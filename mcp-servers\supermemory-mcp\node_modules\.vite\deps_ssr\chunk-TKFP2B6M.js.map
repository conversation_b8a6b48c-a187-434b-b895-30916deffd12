{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/crypto/constants.mjs"], "sourcesContent": ["export const SSL_OP_ALL = 2147485776;\nexport const SSL_OP_ALLOW_NO_DHE_KEX = 1024;\nexport const SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION = 262144;\nexport const SSL_OP_CIPHER_SERVER_PREFERENCE = 4194304;\nexport const SSL_OP_CISCO_ANYCONNECT = 32768;\nexport const SSL_OP_COOKIE_EXCHANGE = 8192;\nexport const SSL_OP_CRYPTOPRO_TLSEXT_BUG = 2147483648;\nexport const SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS = 2048;\nexport const SSL_OP_LEGACY_SERVER_CONNECT = 4;\nexport const SSL_OP_NO_COMPRESSION = 131072;\nexport const SSL_OP_NO_ENCRYPT_THEN_MAC = 524288;\nexport const SSL_OP_NO_QUERY_MTU = 4096;\nexport const SSL_OP_NO_RENEGOTIATION = 1073741824;\nexport const SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION = 65536;\nexport const SSL_OP_NO_SSLv2 = 0;\nexport const SSL_OP_NO_SSLv3 = 33554432;\nexport const SSL_OP_NO_TICKET = 16384;\nexport const SSL_OP_NO_TLSv1 = 67108864;\nexport const SSL_OP_NO_TLSv1_1 = 268435456;\nexport const SSL_OP_NO_TLSv1_2 = 134217728;\nexport const SSL_OP_NO_TLSv1_3 = 536870912;\nexport const SSL_OP_PRIORITIZE_CHACHA = 2097152;\nexport const SSL_OP_TLS_ROLLBACK_BUG = 8388608;\nexport const ENGINE_METHOD_RSA = 1;\nexport const ENGINE_METHOD_DSA = 2;\nexport const ENGINE_METHOD_DH = 4;\nexport const ENGINE_METHOD_RAND = 8;\nexport const ENGINE_METHOD_EC = 2048;\nexport const ENGINE_METHOD_CIPHERS = 64;\nexport const ENGINE_METHOD_DIGESTS = 128;\nexport const ENGINE_METHOD_PKEY_METHS = 512;\nexport const ENGINE_METHOD_PKEY_ASN1_METHS = 1024;\nexport const ENGINE_METHOD_ALL = 65535;\nexport const ENGINE_METHOD_NONE = 0;\nexport const DH_CHECK_P_NOT_SAFE_PRIME = 2;\nexport const DH_CHECK_P_NOT_PRIME = 1;\nexport const DH_UNABLE_TO_CHECK_GENERATOR = 4;\nexport const DH_NOT_SUITABLE_GENERATOR = 8;\nexport const RSA_PKCS1_PADDING = 1;\nexport const RSA_NO_PADDING = 3;\nexport const RSA_PKCS1_OAEP_PADDING = 4;\nexport const RSA_X931_PADDING = 5;\nexport const RSA_PKCS1_PSS_PADDING = 6;\nexport const RSA_PSS_SALTLEN_DIGEST = -1;\nexport const RSA_PSS_SALTLEN_MAX_SIGN = -2;\nexport const RSA_PSS_SALTLEN_AUTO = -2;\nexport const POINT_CONVERSION_COMPRESSED = 2;\nexport const POINT_CONVERSION_UNCOMPRESSED = 4;\nexport const POINT_CONVERSION_HYBRID = 6;\nexport const defaultCoreCipherList = \"\";\nexport const defaultCipherList = \"\";\nexport const OPENSSL_VERSION_NUMBER = 0;\nexport const TLS1_VERSION = 0;\nexport const TLS1_1_VERSION = 0;\nexport const TLS1_2_VERSION = 0;\nexport const TLS1_3_VERSION = 0;\n"], "mappings": ";AAAO,IAAM,aAAa;AACnB,IAAM,0BAA0B;AAChC,IAAM,2CAA2C;AACjD,IAAM,kCAAkC;AACxC,IAAM,0BAA0B;AAChC,IAAM,yBAAyB;AAC/B,IAAM,8BAA8B;AACpC,IAAM,qCAAqC;AAC3C,IAAM,+BAA+B;AACrC,IAAM,wBAAwB;AAC9B,IAAM,6BAA6B;AACnC,IAAM,sBAAsB;AAC5B,IAAM,0BAA0B;AAChC,IAAM,gDAAgD;AACtD,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AACxB,IAAM,oBAAoB;AAC1B,IAAM,oBAAoB;AAC1B,IAAM,oBAAoB;AAC1B,IAAM,2BAA2B;AACjC,IAAM,0BAA0B;AAChC,IAAM,oBAAoB;AAC1B,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAC3B,IAAM,mBAAmB;AACzB,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAC9B,IAAM,2BAA2B;AACjC,IAAM,gCAAgC;AACtC,IAAM,oBAAoB;AAC1B,IAAM,qBAAqB;AAC3B,IAAM,4BAA4B;AAClC,IAAM,uBAAuB;AAC7B,IAAM,+BAA+B;AACrC,IAAM,4BAA4B;AAClC,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB;AACvB,IAAM,yBAAyB;AAC/B,IAAM,mBAAmB;AACzB,IAAM,wBAAwB;AAC9B,IAAM,yBAAyB;AAC/B,IAAM,2BAA2B;AACjC,IAAM,uBAAuB;AAC7B,IAAM,8BAA8B;AACpC,IAAM,gCAAgC;AACtC,IAAM,0BAA0B;AAChC,IAAM,wBAAwB;AAC9B,IAAM,oBAAoB;AAC1B,IAAM,yBAAyB;AAC/B,IAAM,eAAe;AACrB,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;", "names": []}