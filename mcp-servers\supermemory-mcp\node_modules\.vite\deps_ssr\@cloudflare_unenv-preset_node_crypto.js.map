{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/crypto/web.mjs", "../../unenv/dist/runtime/node/internal/crypto/node.mjs", "../../unenv/dist/runtime/node/crypto.mjs", "../../@cloudflare/unenv-preset/dist/runtime/node/crypto.mjs"], "sourcesContent": ["export const subtle = globalThis.crypto?.subtle;\nexport const randomUUID = () => {\n\treturn globalThis.crypto?.randomUUID();\n};\nexport const getRandomValues = (array) => {\n\treturn globalThis.crypto?.getRandomValues(array);\n};\n", "import { notImplemented, notImplementedClass } from \"../../../_internal/utils.mjs\";\nimport { getRandomValues } from \"./web.mjs\";\nconst MAX_RANDOM_VALUE_BYTES = 65536;\nexport const webcrypto = new Proxy(globalThis.crypto, { get(_, key) {\n\tif (key === \"CryptoKey\") {\n\t\treturn globalThis.CryptoKey;\n\t}\n\tif (typeof globalThis.crypto[key] === \"function\") {\n\t\treturn globalThis.crypto[key].bind(globalThis.crypto);\n\t}\n\treturn globalThis.crypto[key];\n} });\nexport const randomBytes = (size, cb) => {\n\tconst bytes = Buffer.alloc(size, 0, undefined);\n\tfor (let generated = 0; generated < size; generated += MAX_RANDOM_VALUE_BYTES) {\n\t\tgetRandomValues(\n\t\t\t// Use subarray to get a view of the buffer\n\t\t\tUint8Array.prototype.subarray.call(bytes, generated, generated + MAX_RANDOM_VALUE_BYTES)\n);\n\t}\n\tif (typeof cb === \"function\") {\n\t\tcb(null, bytes);\n\t\treturn undefined;\n\t}\n\treturn bytes;\n};\nexport const rng = randomBytes;\nexport const prng = randomBytes;\nexport const fips = false;\nexport const checkPrime = /* @__PURE__ */ notImplemented(\"crypto.checkPrime\");\nexport const checkPrimeSync = /* @__PURE__ */ notImplemented(\"crypto.checkPrimeSync\");\n/** @deprecated */\nexport const createCipher = /* @__PURE__ */ notImplemented(\"crypto.createCipher\");\n/** @deprecated */\nexport const createDecipher = /* @__PURE__ */ notImplemented(\"crypto.createDecipher\");\nexport const pseudoRandomBytes = /* @__PURE__ */ notImplemented(\"crypto.pseudoRandomBytes\");\nexport const createCipheriv = /* @__PURE__ */ notImplemented(\"crypto.createCipheriv\");\nexport const createDecipheriv = /* @__PURE__ */ notImplemented(\"crypto.createDecipheriv\");\nexport const createDiffieHellman = /* @__PURE__ */ notImplemented(\"crypto.createDiffieHellman\");\nexport const createDiffieHellmanGroup = /* @__PURE__ */ notImplemented(\"crypto.createDiffieHellmanGroup\");\nexport const createECDH = /* @__PURE__ */ notImplemented(\"crypto.createECDH\");\nexport const createHash = /* @__PURE__ */ notImplemented(\"crypto.createHash\");\nexport const createHmac = /* @__PURE__ */ notImplemented(\"crypto.createHmac\");\nexport const createPrivateKey = /* @__PURE__ */ notImplemented(\"crypto.createPrivateKey\");\nexport const createPublicKey = /* @__PURE__ */ notImplemented(\"crypto.createPublicKey\");\nexport const createSecretKey = /* @__PURE__ */ notImplemented(\"crypto.createSecretKey\");\nexport const createSign = /* @__PURE__ */ notImplemented(\"crypto.createSign\");\nexport const createVerify = /* @__PURE__ */ notImplemented(\"crypto.createVerify\");\nexport const diffieHellman = /* @__PURE__ */ notImplemented(\"crypto.diffieHellman\");\nexport const generatePrime = /* @__PURE__ */ notImplemented(\"crypto.generatePrime\");\nexport const generatePrimeSync = /* @__PURE__ */ notImplemented(\"crypto.generatePrimeSync\");\nexport const getCiphers = /* @__PURE__ */ notImplemented(\"crypto.getCiphers\");\nexport const getCipherInfo = /* @__PURE__ */ notImplemented(\"crypto.getCipherInfo\");\nexport const getCurves = /* @__PURE__ */ notImplemented(\"crypto.getCurves\");\nexport const getDiffieHellman = /* @__PURE__ */ notImplemented(\"crypto.getDiffieHellman\");\nexport const getHashes = /* @__PURE__ */ notImplemented(\"crypto.getHashes\");\nexport const hkdf = /* @__PURE__ */ notImplemented(\"crypto.hkdf\");\nexport const hkdfSync = /* @__PURE__ */ notImplemented(\"crypto.hkdfSync\");\nexport const pbkdf2 = /* @__PURE__ */ notImplemented(\"crypto.pbkdf2\");\nexport const pbkdf2Sync = /* @__PURE__ */ notImplemented(\"crypto.pbkdf2Sync\");\nexport const generateKeyPair = /* @__PURE__ */ notImplemented(\"crypto.generateKeyPair\");\nexport const generateKeyPairSync = /* @__PURE__ */ notImplemented(\"crypto.generateKeyPairSync\");\nexport const generateKey = /* @__PURE__ */ notImplemented(\"crypto.generateKey\");\nexport const generateKeySync = /* @__PURE__ */ notImplemented(\"crypto.generateKeySync\");\nexport const privateDecrypt = /* @__PURE__ */ notImplemented(\"crypto.privateDecrypt\");\nexport const privateEncrypt = /* @__PURE__ */ notImplemented(\"crypto.privateEncrypt\");\nexport const publicDecrypt = /* @__PURE__ */ notImplemented(\"crypto.publicDecrypt\");\nexport const publicEncrypt = /* @__PURE__ */ notImplemented(\"crypto.publicEncrypt\");\nexport const randomFill = /* @__PURE__ */ notImplemented(\"crypto.randomFill\");\nexport const randomFillSync = /* @__PURE__ */ notImplemented(\"crypto.randomFillSync\");\nexport const randomInt = /* @__PURE__ */ notImplemented(\"crypto.randomInt\");\nexport const scrypt = /* @__PURE__ */ notImplemented(\"crypto.scrypt\");\nexport const scryptSync = /* @__PURE__ */ notImplemented(\"crypto.scryptSync\");\nexport const sign = /* @__PURE__ */ notImplemented(\"crypto.sign\");\nexport const setEngine = /* @__PURE__ */ notImplemented(\"crypto.setEngine\");\nexport const timingSafeEqual = /* @__PURE__ */ notImplemented(\"crypto.timingSafeEqual\");\nexport const getFips = /* @__PURE__ */ notImplemented(\"crypto.getFips\");\nexport const setFips = /* @__PURE__ */ notImplemented(\"crypto.setFips\");\nexport const verify = /* @__PURE__ */ notImplemented(\"crypto.verify\");\nexport const secureHeapUsed = /* @__PURE__ */ notImplemented(\"crypto.secureHeapUsed\");\nexport const hash = /* @__PURE__ */ notImplemented(\"crypto.hash\");\nexport const Certificate = /* @__PURE__ */ notImplementedClass(\"crypto.Certificate\");\nexport const Cipher = /* @__PURE__ */ notImplementedClass(\"crypto.Cipher\");\nexport const Cipheriv = /* @__PURE__ */ notImplementedClass(\n\t\"crypto.Cipheriv\"\n\t// @ts-expect-error not typed yet\n);\nexport const Decipher = /* @__PURE__ */ notImplementedClass(\"crypto.Decipher\");\nexport const Decipheriv = /* @__PURE__ */ notImplementedClass(\n\t\"crypto.Decipheriv\"\n\t// @ts-expect-error not typed yet\n);\nexport const DiffieHellman = /* @__PURE__ */ notImplementedClass(\"crypto.DiffieHellman\");\nexport const DiffieHellmanGroup = /* @__PURE__ */ notImplementedClass(\"crypto.DiffieHellmanGroup\");\nexport const ECDH = /* @__PURE__ */ notImplementedClass(\"crypto.ECDH\");\nexport const Hash = /* @__PURE__ */ notImplementedClass(\"crypto.Hash\");\nexport const Hmac = /* @__PURE__ */ notImplementedClass(\"crypto.Hmac\");\nexport const KeyObject = /* @__PURE__ */ notImplementedClass(\"crypto.KeyObject\");\nexport const Sign = /* @__PURE__ */ notImplementedClass(\"crypto.Sign\");\nexport const Verify = /* @__PURE__ */ notImplementedClass(\"crypto.Verify\");\nexport const X509Certificate = /* @__PURE__ */ notImplementedClass(\"crypto.X509Certificate\");\n", "import { getRandomValues, randomUUID, subtle } from \"./internal/crypto/web.mjs\";\nimport { Certificate, Cipher, Cipheriv, Decipher, Dec<PERSON>her<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>manGroup, ECDH, Hash, Hmac, KeyObject, Sign, Verify, X509Certificate, checkPrime, checkPrimeSync, createCipheriv, createDecipheriv, createD<PERSON><PERSON><PERSON>ellman, createDiffieHellmanGroup, createECDH, createHash, createHmac, createPrivate<PERSON><PERSON>, createPublic<PERSON>ey, createS<PERSON>ret<PERSON><PERSON>, createSign, createVerify, diffie<PERSON><PERSON>man, fips, generateKey, generateKeyPair, generateKeyPairSync, generateKeySync, generatePrime, generatePrimeSync, getCipherInfo, getCiphers, getCurves, getDiffieHellman, getFips, getHashes, hash, hkdf, hkdfSync, pbkdf2, pbkdf2Sync, privateDecrypt, privateEncrypt, pseudoRandomBytes, publicDecrypt, prng, publicEncrypt, randomBytes, randomFill, randomFillSync, randomInt, rng, scrypt, scryptSync, secureHeapUsed, setEngine, setFips, sign, timingSafeEqual, verify, webcrypto } from \"./internal/crypto/node.mjs\";\nimport { OPENSSL_VERSION_NUMBER, SSL_OP_ALL, SSL_OP_ALLOW_NO_DHE_KEX, SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION, SSL_OP_CIPHER_SERVER_PREFERENCE, SSL_OP_CISCO_ANYCONNECT, SSL_OP_COOKIE_EXCHANGE, SSL_OP_CRYPTOPRO_TLSEXT_BUG, SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS, SSL_OP_LEGACY_SERVER_CONNECT, SSL_OP_NO_COMPRESSION, SSL_OP_NO_ENCRYPT_THEN_MAC, SSL_OP_NO_QUERY_MTU, SSL_OP_NO_RENEGOTIATION, SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION, SSL_OP_NO_SSLv2, SSL_OP_NO_SSLv3, SSL_OP_NO_TICKET, SSL_OP_NO_TLSv1, SSL_OP_NO_TLSv1_1, SSL_OP_NO_TLSv1_2, SSL_OP_NO_TLSv1_3, SSL_OP_PRIORITIZE_CHACHA, SSL_OP_TLS_ROLLBACK_BUG, ENGINE_METHOD_RSA, ENGINE_METHOD_DSA, ENGINE_METHOD_DH, ENGINE_METHOD_RAND, ENGINE_METHOD_EC, ENGINE_METHOD_CIPHERS, ENGINE_METHOD_DIGESTS, ENGINE_METHOD_PKEY_METHS, ENGINE_METHOD_PKEY_ASN1_METHS, ENGINE_METHOD_ALL, ENGINE_METHOD_NONE, DH_CHECK_P_NOT_SAFE_PRIME, DH_CHECK_P_NOT_PRIME, DH_UNABLE_TO_CHECK_GENERATOR, DH_NOT_SUITABLE_GENERATOR, RSA_PKCS1_PADDING, RSA_NO_PADDING, RSA_PKCS1_OAEP_PADDING, RSA_X931_PADDING, RSA_PKCS1_PSS_PADDING, RSA_PSS_SALTLEN_DIGEST, RSA_PSS_SALTLEN_MAX_SIGN, RSA_PSS_SALTLEN_AUTO, defaultCoreCipherList, TLS1_VERSION, TLS1_1_VERSION, TLS1_2_VERSION, TLS1_3_VERSION, POINT_CONVERSION_COMPRESSED, POINT_CONVERSION_UNCOMPRESSED, POINT_CONVERSION_HYBRID, defaultCipherList } from \"./internal/crypto/constants.mjs\";\nexport * from \"./internal/crypto/web.mjs\";\nexport * from \"./internal/crypto/node.mjs\";\nexport const constants = {\n\tOPENSSL_VERSION_NUMBER,\n\tSSL_OP_ALL,\n\tSSL_OP_ALLOW_NO_DHE_KEX,\n\tSSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION,\n\tSSL_OP_CIPHER_SERVER_PREFERENCE,\n\tSSL_OP_CISCO_ANYCONNECT,\n\tSSL_OP_COOKIE_EXCHANGE,\n\tSSL_OP_CRYPTOPRO_TLSEXT_BUG,\n\tSSL_OP_DONT_INSERT_EMPTY_FRAGMENTS,\n\tSSL_OP_LEGACY_SERVER_CONNECT,\n\tSSL_OP_NO_COMPRESSION,\n\tSSL_OP_NO_ENCRYPT_THEN_MAC,\n\tSSL_OP_NO_QUERY_MTU,\n\tSSL_OP_NO_RENEGOTIATION,\n\tSSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION,\n\tSSL_OP_NO_SSLv2,\n\tSSL_OP_NO_SSLv3,\n\tSSL_OP_NO_TICKET,\n\tSSL_OP_NO_TLSv1,\n\tSSL_OP_NO_TLSv1_1,\n\tSSL_OP_NO_TLSv1_2,\n\tSSL_OP_NO_TLSv1_3,\n\tSSL_OP_PRIORITIZE_CHACHA,\n\tSSL_OP_TLS_ROLLBACK_BUG,\n\tENGINE_METHOD_RSA,\n\tENGINE_METHOD_DSA,\n\tENGINE_METHOD_DH,\n\tENGINE_METHOD_RAND,\n\tENGINE_METHOD_EC,\n\tENGINE_METHOD_CIPHERS,\n\tENGINE_METHOD_DIGESTS,\n\tENGINE_METHOD_PKEY_METHS,\n\tENGINE_METHOD_PKEY_ASN1_METHS,\n\tENGINE_METHOD_ALL,\n\tENGINE_METHOD_NONE,\n\tDH_CHECK_P_NOT_SAFE_PRIME,\n\tDH_CHECK_P_NOT_PRIME,\n\tDH_UNABLE_TO_CHECK_GENERATOR,\n\tDH_NOT_SUITABLE_GENERATOR,\n\tRSA_PKCS1_PADDING,\n\tRSA_NO_PADDING,\n\tRSA_PKCS1_OAEP_PADDING,\n\tRSA_X931_PADDING,\n\tRSA_PKCS1_PSS_PADDING,\n\tRSA_PSS_SALTLEN_DIGEST,\n\tRSA_PSS_SALTLEN_MAX_SIGN,\n\tRSA_PSS_SALTLEN_AUTO,\n\tdefaultCoreCipherList,\n\tTLS1_VERSION,\n\tTLS1_1_VERSION,\n\tTLS1_2_VERSION,\n\tTLS1_3_VERSION,\n\tPOINT_CONVERSION_COMPRESSED,\n\tPOINT_CONVERSION_UNCOMPRESSED,\n\tPOINT_CONVERSION_HYBRID,\n\tdefaultCipherList\n};\nexport default {\n\tconstants,\n\tgetRandomValues,\n\trandomUUID,\n\tsubtle,\n\tCertificate,\n\tCipher,\n\tCipheriv,\n\tDecipher,\n\tDecipheriv,\n\tDiffieHellman,\n\tDiffieHellmanGroup,\n\tECDH,\n\tHash,\n\tHmac,\n\tKeyObject,\n\tSign,\n\tVerify,\n\tX509Certificate,\n\tcheckPrime,\n\tcheckPrimeSync,\n\tcreateCipheriv,\n\tcreateDecipheriv,\n\tcreateDiffieHellman,\n\tcreateDiffieHellmanGroup,\n\tcreateECDH,\n\tcreateHash,\n\tcreateHmac,\n\tcreatePrivateKey,\n\tcreatePublicKey,\n\tcreateSecretKey,\n\tcreateSign,\n\tcreateVerify,\n\tdiffieHellman,\n\tfips,\n\tgenerateKey,\n\tgenerateKeyPair,\n\tgenerateKeyPairSync,\n\tgenerateKeySync,\n\tgeneratePrime,\n\tgeneratePrimeSync,\n\tgetCipherInfo,\n\tgetCiphers,\n\tgetCurves,\n\tgetDiffieHellman,\n\tgetFips,\n\tgetHashes,\n\thash,\n\thkdf,\n\thkdfSync,\n\tpbkdf2,\n\tpbkdf2Sync,\n\tprivateDecrypt,\n\tprivateEncrypt,\n\tpseudoRandomBytes,\n\tpublicDecrypt,\n\tprng,\n\tpublicEncrypt,\n\trandomBytes,\n\trandomFill,\n\trandomFillSync,\n\trandomInt,\n\trng,\n\tscrypt,\n\tscryptSync,\n\tsecureHeapUsed,\n\tsetEngine,\n\tsetFips,\n\tsign,\n\ttimingSafeEqual,\n\tverify,\n\twebcrypto\n};\n", "import {\n  Cipher,\n  constants,\n  createCipher,\n  createD<PERSON><PERSON>her,\n  Decipher,\n  pseudoRandomBytes,\n  webcrypto as unenvCryptoWebcrypto\n} from \"unenv/node/crypto\";\nexport { Cipher, constants, Decipher } from \"unenv/node/crypto\";\nconst workerdCrypto = process.getBuiltinModule(\"node:crypto\");\nexport const {\n  Certificate,\n  checkPrime,\n  checkPrimeSync,\n  // @ts-expect-error\n  Cipheriv,\n  createCipher<PERSON>,\n  createDecipheriv,\n  createD<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  createDiffieHellmanGroup,\n  createECDH,\n  createHash,\n  createHmac,\n  createPrivate<PERSON><PERSON>,\n  createPublic<PERSON><PERSON>,\n  createSecretKey,\n  createSign,\n  createVerify,\n  // @ts-expect-error\n  Decipheriv,\n  diff<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>HellmanGroup,\n  ECDH,\n  fips,\n  generateKey,\n  generateKeyPair,\n  generateKeyPairSync,\n  generateKeySync,\n  generatePrime,\n  generatePrimeSync,\n  getCipherInfo,\n  getCip<PERSON>,\n  getCurves,\n  getD<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  getF<PERSON>s,\n  getHashes,\n  getRandomValues,\n  hash,\n  Hash,\n  hkdf,\n  hkdfSync,\n  Hmac,\n  KeyObject,\n  pbkdf2,\n  pbkdf2Sync,\n  privateDecrypt,\n  privateEncrypt,\n  publicDecrypt,\n  publicEncrypt,\n  randomBytes,\n  randomFill,\n  randomFillSync,\n  randomInt,\n  randomUUID,\n  scrypt,\n  scryptSync,\n  secureHeapUsed,\n  setEngine,\n  setFips,\n  sign,\n  Sign,\n  subtle,\n  timingSafeEqual,\n  verify,\n  Verify,\n  X509Certificate\n} = workerdCrypto;\nexport const webcrypto = {\n  // @ts-expect-error\n  CryptoKey: unenvCryptoWebcrypto.CryptoKey,\n  getRandomValues,\n  randomUUID,\n  subtle\n};\nexport default {\n  /**\n   * manually unroll unenv-polyfilled-symbols to make it tree-shakeable\n   */\n  Certificate,\n  Cipher,\n  Cipheriv,\n  Decipher,\n  Decipheriv,\n  ECDH,\n  Sign,\n  Verify,\n  X509Certificate,\n  // @ts-expect-error @types/node is out of date - this is a bug in typings\n  constants,\n  createCipheriv,\n  createDecipheriv,\n  createECDH,\n  createSign,\n  createVerify,\n  diffieHellman,\n  getCipherInfo,\n  hash,\n  privateDecrypt,\n  privateEncrypt,\n  publicDecrypt,\n  publicEncrypt,\n  scrypt,\n  scryptSync,\n  sign,\n  verify,\n  // default-only export from unenv\n  // @ts-expect-error unenv has unknown type\n  createCipher,\n  // @ts-expect-error unenv has unknown type\n  createDecipher,\n  // @ts-expect-error unenv has unknown type\n  pseudoRandomBytes,\n  /**\n   * manually unroll workerd-polyfilled-symbols to make it tree-shakeable\n   */\n  DiffieHellman,\n  DiffieHellmanGroup,\n  Hash,\n  Hmac,\n  KeyObject,\n  checkPrime,\n  checkPrimeSync,\n  createDiffieHellman,\n  createDiffieHellmanGroup,\n  createHash,\n  createHmac,\n  createPrivateKey,\n  createPublicKey,\n  createSecretKey,\n  generateKey,\n  generateKeyPair,\n  generateKeyPairSync,\n  generateKeySync,\n  generatePrime,\n  generatePrimeSync,\n  getCiphers,\n  getCurves,\n  getDiffieHellman,\n  getFips,\n  getHashes,\n  getRandomValues,\n  hkdf,\n  hkdfSync,\n  pbkdf2,\n  pbkdf2Sync,\n  randomBytes,\n  randomFill,\n  randomFillSync,\n  randomInt,\n  randomUUID,\n  secureHeapUsed,\n  setEngine,\n  setFips,\n  subtle,\n  timingSafeEqual,\n  // default-only export from workerd\n  fips,\n  // special-cased deep merged symbols\n  webcrypto\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAM,SAAS,WAAW,QAAQ;;;ACGlC,IAAM,YAAY,IAAI,MAAM,WAAW,QAAQ,EAAE,IAAI,GAAG,KAAK;AACnE,MAAI,QAAQ,aAAa;AACxB,WAAO,WAAW;AAAA,EACnB;AACA,MAAI,OAAO,WAAW,OAAO,GAAG,MAAM,YAAY;AACjD,WAAO,WAAW,OAAO,GAAG,EAAE,KAAK,WAAW,MAAM;AAAA,EACrD;AACA,SAAO,WAAW,OAAO,GAAG;AAC7B,EAAE,CAAC;AAkBI,IAAM,aAA6B,eAAe,mBAAmB;AACrE,IAAM,iBAAiC,eAAe,uBAAuB;AAE7E,IAAM,eAA+B,eAAe,qBAAqB;AAEzE,IAAM,iBAAiC,eAAe,uBAAuB;AAC7E,IAAM,oBAAoC,eAAe,0BAA0B;AACnF,IAAM,iBAAiC,eAAe,uBAAuB;AAC7E,IAAM,mBAAmC,eAAe,yBAAyB;AACjF,IAAM,sBAAsC,eAAe,4BAA4B;AACvF,IAAM,2BAA2C,eAAe,iCAAiC;AACjG,IAAM,aAA6B,eAAe,mBAAmB;AACrE,IAAM,aAA6B,eAAe,mBAAmB;AACrE,IAAM,aAA6B,eAAe,mBAAmB;AACrE,IAAM,mBAAmC,eAAe,yBAAyB;AACjF,IAAM,kBAAkC,eAAe,wBAAwB;AAC/E,IAAM,kBAAkC,eAAe,wBAAwB;AAC/E,IAAM,aAA6B,eAAe,mBAAmB;AACrE,IAAM,eAA+B,eAAe,qBAAqB;AACzE,IAAM,gBAAgC,eAAe,sBAAsB;AAC3E,IAAM,gBAAgC,eAAe,sBAAsB;AAC3E,IAAM,oBAAoC,eAAe,0BAA0B;AACnF,IAAM,aAA6B,eAAe,mBAAmB;AACrE,IAAM,gBAAgC,eAAe,sBAAsB;AAC3E,IAAM,YAA4B,eAAe,kBAAkB;AACnE,IAAM,mBAAmC,eAAe,yBAAyB;AACjF,IAAM,YAA4B,eAAe,kBAAkB;AACnE,IAAM,OAAuB,eAAe,aAAa;AACzD,IAAM,WAA2B,eAAe,iBAAiB;AACjE,IAAM,SAAyB,eAAe,eAAe;AAC7D,IAAM,aAA6B,eAAe,mBAAmB;AACrE,IAAM,kBAAkC,eAAe,wBAAwB;AAC/E,IAAM,sBAAsC,eAAe,4BAA4B;AACvF,IAAM,cAA8B,eAAe,oBAAoB;AACvE,IAAM,kBAAkC,eAAe,wBAAwB;AAC/E,IAAM,iBAAiC,eAAe,uBAAuB;AAC7E,IAAM,iBAAiC,eAAe,uBAAuB;AAC7E,IAAM,gBAAgC,eAAe,sBAAsB;AAC3E,IAAM,gBAAgC,eAAe,sBAAsB;AAC3E,IAAM,aAA6B,eAAe,mBAAmB;AACrE,IAAM,iBAAiC,eAAe,uBAAuB;AAC7E,IAAM,YAA4B,eAAe,kBAAkB;AACnE,IAAM,SAAyB,eAAe,eAAe;AAC7D,IAAM,aAA6B,eAAe,mBAAmB;AACrE,IAAM,OAAuB,eAAe,aAAa;AACzD,IAAM,YAA4B,eAAe,kBAAkB;AACnE,IAAM,kBAAkC,eAAe,wBAAwB;AAC/E,IAAM,UAA0B,eAAe,gBAAgB;AAC/D,IAAM,UAA0B,eAAe,gBAAgB;AAC/D,IAAM,SAAyB,eAAe,eAAe;AAC7D,IAAM,iBAAiC,eAAe,uBAAuB;AAC7E,IAAM,OAAuB,eAAe,aAAa;AACzD,IAAM,cAA8B,oBAAoB,oBAAoB;AAC5E,IAAM,SAAyB,oBAAoB,eAAe;AAClE,IAAM,WAA2B;AAAA,EACvC;AAAA;AAED;AACO,IAAM,WAA2B,oBAAoB,iBAAiB;AACtE,IAAM,aAA6B;AAAA,EACzC;AAAA;AAED;AACO,IAAM,gBAAgC,oBAAoB,sBAAsB;AAChF,IAAM,qBAAqC,oBAAoB,2BAA2B;AAC1F,IAAM,OAAuB,oBAAoB,aAAa;AAC9D,IAAM,OAAuB,oBAAoB,aAAa;AAC9D,IAAM,OAAuB,oBAAoB,aAAa;AAC9D,IAAM,YAA4B,oBAAoB,kBAAkB;AACxE,IAAM,OAAuB,oBAAoB,aAAa;AAC9D,IAAM,SAAyB,oBAAoB,eAAe;AAClE,IAAM,kBAAkC,oBAAoB,wBAAwB;;;AC/FpF,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;;;ACpDA,IAAM,gBAAgB,QAAQ,iBAAiB,aAAa;AACrD,IAAM;AAAA,EACX,aAAAA;AAAA,EACA,YAAAC;AAAA,EACA,gBAAAC;AAAA;AAAA,EAEA,UAAAC;AAAA,EACA,gBAAAC;AAAA,EACA,kBAAAC;AAAA,EACA,qBAAAC;AAAA,EACA,0BAAAC;AAAA,EACA,YAAAC;AAAA,EACA,YAAAC;AAAA,EACA,YAAAC;AAAA,EACA,kBAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,YAAAC;AAAA,EACA,cAAAC;AAAA;AAAA,EAEA,YAAAC;AAAA,EACA,eAAAC;AAAA,EACA,eAAAC;AAAA,EACA,oBAAAC;AAAA,EACA,MAAAC;AAAA,EACA,MAAAC;AAAA,EACA,aAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,qBAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,eAAAC;AAAA,EACA,mBAAAC;AAAA,EACA,eAAAC;AAAA,EACA,YAAAC;AAAA,EACA,WAAAC;AAAA,EACA,kBAAAC;AAAA,EACA,SAAAC;AAAA,EACA,WAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,MAAAC;AAAA,EACA,MAAAC;AAAA,EACA,MAAAC;AAAA,EACA,UAAAC;AAAA,EACA,MAAAC;AAAA,EACA,WAAAC;AAAA,EACA,QAAAC;AAAA,EACA,YAAAC;AAAA,EACA,gBAAAC;AAAA,EACA,gBAAAC;AAAA,EACA,eAAAC;AAAA,EACA,eAAAC;AAAA,EACA,aAAAC;AAAA,EACA,YAAAC;AAAA,EACA,gBAAAC;AAAA,EACA,WAAAC;AAAA,EACA,YAAAC;AAAA,EACA,QAAAC;AAAA,EACA,YAAAC;AAAA,EACA,gBAAAC;AAAA,EACA,WAAAC;AAAA,EACA,SAAAC;AAAA,EACA,MAAAC;AAAA,EACA,MAAAC;AAAA,EACA,QAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,QAAAC;AAAA,EACA,QAAAC;AAAA,EACA,iBAAAC;AACF,IAAI;AACG,IAAMC,aAAY;AAAA;AAAA,EAEvB,WAAW,UAAqB;AAAA,EAChC,iBAAA9B;AAAA,EACA,YAAAiB;AAAA,EACA,QAAAQ;AACF;AACA,IAAO,iBAAQ;AAAA;AAAA;AAAA;AAAA,EAIb,aAAA3D;AAAA,EACA;AAAA,EACA,UAAAG;AAAA,EACA;AAAA,EACA,YAAAa;AAAA,EACA,MAAAI;AAAA,EACA,MAAAsC;AAAA,EACA,QAAAI;AAAA,EACA,iBAAAC;AAAA;AAAA,EAEA;AAAA,EACA,gBAAA3D;AAAA,EACA,kBAAAC;AAAA,EACA,YAAAG;AAAA,EACA,YAAAM;AAAA,EACA,cAAAC;AAAA,EACA,eAAAE;AAAA,EACA,eAAAW;AAAA,EACA,MAAAO;AAAA,EACA,gBAAAQ;AAAA,EACA,gBAAAC;AAAA,EACA,eAAAC;AAAA,EACA,eAAAC;AAAA,EACA,QAAAM;AAAA,EACA,YAAAC;AAAA,EACA,MAAAI;AAAA,EACA,QAAAI;AAAA;AAAA;AAAA,EAGA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA,EAIA,eAAA3C;AAAA,EACA,oBAAAC;AAAA,EACA,MAAAiB;AAAA,EACA,MAAAG;AAAA,EACA,WAAAC;AAAA,EACA,YAAAvC;AAAA,EACA,gBAAAC;AAAA,EACA,qBAAAI;AAAA,EACA,0BAAAC;AAAA,EACA,YAAAE;AAAA,EACA,YAAAC;AAAA,EACA,kBAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,aAAAS;AAAA,EACA,iBAAAC;AAAA,EACA,qBAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,eAAAC;AAAA,EACA,mBAAAC;AAAA,EACA,YAAAE;AAAA,EACA,WAAAC;AAAA,EACA,kBAAAC;AAAA,EACA,SAAAC;AAAA,EACA,WAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,MAAAG;AAAA,EACA,UAAAC;AAAA,EACA,QAAAG;AAAA,EACA,YAAAC;AAAA,EACA,aAAAK;AAAA,EACA,YAAAC;AAAA,EACA,gBAAAC;AAAA,EACA,WAAAC;AAAA,EACA,YAAAC;AAAA,EACA,gBAAAG;AAAA,EACA,WAAAC;AAAA,EACA,SAAAC;AAAA,EACA,QAAAG;AAAA,EACA,iBAAAC;AAAA;AAAA,EAEA,MAAAvC;AAAA;AAAA,EAEA,WAAA2C;AACF;", "names": ["Certificate", "checkPrime", "checkPrimeSync", "Cipheriv", "createCipheriv", "createDecipheriv", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createDiffieHellmanGroup", "createECDH", "createHash", "createHmac", "createPrivateKey", "createPublicKey", "createSecretKey", "createSign", "createVerify", "Decipheriv", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DiffieHellmanGroup", "ECDH", "fips", "<PERSON><PERSON>ey", "generateKeyPair", "generateKeyPairSync", "generateKeySync", "generatePrime", "generatePrimeSync", "getCipherInfo", "getCiphers", "getCurves", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getFips", "getHashes", "getRandomValues", "hash", "Hash", "hkdf", "hkdfSync", "Hmac", "KeyObject", "pbkdf2", "pbkdf2Sync", "privateDecrypt", "privateEncrypt", "publicDecrypt", "publicEncrypt", "randomBytes", "randomFill", "randomFillSync", "randomInt", "randomUUID", "scrypt", "scryptSync", "secureHeapUsed", "setEngine", "setFips", "sign", "Sign", "subtle", "timingSafeEqual", "verify", "Verify", "X509Certificate", "webcrypto"]}