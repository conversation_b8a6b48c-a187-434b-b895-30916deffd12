{"version": 3, "names": ["React", "Easing", "Animated", "AnimatedPoint", "SymbolLayer", "jsx", "_jsx", "Annotation", "Component", "defaultProps", "animated", "animationDuration", "animationEasingFunction", "linear", "constructor", "props", "shape", "_getShapeFromProps", "state", "onPress", "bind", "componentDidUpdate", "prevProps", "Array", "isArray", "coordinates", "setState", "haveCoordinatesChanged", "stopAnimation", "timing", "easing", "duration", "start", "event", "lng", "lat", "type", "symbolStyle", "icon", "undefined", "Object", "assign", "style", "iconImage", "render", "children", "push", "id", "ShapeSource"], "sourceRoot": "../../../src", "sources": ["components/Annotation.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAwB,OAAO;AAC3C,SAAiCC,MAAM,QAAQ,cAAc;AAG7D,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,SAASC,aAAa,QAAQ,YAAY;AAI1C,SAASC,WAAW,QAAQ,eAAe;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAkB5C,MAAMC,UAAU,SAASP,KAAK,CAACQ,SAAS,CAAyB;EAC/D,OAAOC,YAAY,GAAG;IACpBC,QAAQ,EAAE,KAAK;IACfC,iBAAiB,EAAE,IAAI;IACvBC,uBAAuB,EAAEX,MAAM,CAACY;EAClC,CAAC;EAEDC,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IAEZ,MAAMC,KAAK,GAAG,IAAI,CAACC,kBAAkB,CAACF,KAAK,CAAC;IAE5C,IAAI,CAACG,KAAK,GAAG;MACXF,KAAK,EAAED,KAAK,CAACL,QAAQ,GAAG,IAAIP,aAAa,CAACa,KAAK,CAAC,GAAGA;IACrD,CAAC;IAED,IAAI,CAACG,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;EACxC;EAEAC,kBAAkBA,CAACC,SAAgB,EAAE;IACnC,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACT,KAAK,CAACU,WAAW,CAAC,EAAE;MAC1C,IAAI,CAACC,QAAQ,CAAC;QAAEV,KAAK,EAAE;MAAK,CAAC,CAAC;MAC9B;IACF;IAEA,MAAMW,sBAAsB,GAC1BL,SAAS,CAACG,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,CAACV,KAAK,CAACU,WAAW,CAAC,CAAC,CAAC,IACtDH,SAAS,CAACG,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,CAACV,KAAK,CAACU,WAAW,CAAC,CAAC,CAAC;IAExD,IACEH,SAAS,CAACZ,QAAQ,KAAK,IAAI,CAACK,KAAK,CAACL,QAAQ,IACzCiB,sBAAsB,KAAK,CAAC,IAAI,CAACT,KAAK,CAACF,KAAK,IAAI,CAAC,IAAI,CAACD,KAAK,CAACL,QAAQ,CAAE,EACvE;MACA,MAAMM,KAAK,GAAG,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACF,KAAK,CAAC;MAEjD,IAAI,CAACW,QAAQ,CAAC;QACZV,KAAK,EAAE,IAAI,CAACD,KAAK,CAACL,QAAQ,GAAG,IAAIP,aAAa,CAACa,KAAK,CAAC,GAAGA;MAC1D,CAAC,CAAC;IACJ,CAAC,MAAM,IACLW,sBAAsB,IACtB,IAAI,CAACZ,KAAK,CAACL,QAAQ,IACnB,IAAI,CAACQ,KAAK,CAACF,KAAK,EAChB;MACA;MACC,IAAI,CAACE,KAAK,CAACF,KAAK,CAAmBY,aAAa,CAAC,CAAC;MAElD,IAAI,CAACV,KAAK,CAACF,KAAK,CACda,MAAM,CAAC;QACNJ,WAAW,EAAE,IAAI,CAACV,KAAK,CAACU,WAAW;QACnCK,MAAM,EAAE,IAAI,CAACf,KAAK,CAACH,uBAAuB;QAC1CmB,QAAQ,EAAE,IAAI,CAAChB,KAAK,CAACJ;MACvB,CAAC,CAAC,CACDqB,KAAK,CAAC,CAAC;IACZ;EACF;EAEAb,OAAOA,CAACc,KAAmB,EAAE;IAC3B,IAAI,IAAI,CAAClB,KAAK,CAACI,OAAO,EAAE;MACtB,IAAI,CAACJ,KAAK,CAACI,OAAO,CAACc,KAAK,CAAC;IAC3B;EACF;EAEAhB,kBAAkBA,CAACF,KAAqB,GAAG,CAAC,CAAC,EAAS;IACpD,MAAMmB,GAAG,GAAGnB,KAAK,CAACU,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC;IACvC,MAAMU,GAAG,GAAGpB,KAAK,CAACU,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC;IACvC,OAAO;MAAEW,IAAI,EAAE,OAAO;MAAEX,WAAW,EAAE,CAACS,GAAG,EAAEC,GAAG;IAAE,CAAC;EACnD;EAEA,IAAIE,WAAWA,CAAA,EAAiC;IAC9C,IAAI,CAAC,IAAI,CAACtB,KAAK,CAACuB,IAAI,EAAE;MACpB,OAAOC,SAAS;IAClB;IACA,OAAOC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC1B,KAAK,CAAC2B,KAAK,EAAE;MACzCC,SAAS,EAAE,IAAI,CAAC5B,KAAK,CAACuB;IACxB,CAAC,CAAC;EACJ;EAEAM,MAAMA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAC7B,KAAK,CAACU,WAAW,EAAE;MAC3B,OAAO,IAAI;IACb;IAEA,MAAMoB,QAAQ,GAAG,EAAE;IAEnB,IAAI,IAAI,CAACR,WAAW,EAAE;MACpBQ,QAAQ,CAACC,IAAI,cACXxC,IAAA,CAACF,WAAW;QACV2C,EAAE,EAAE,GAAG,IAAI,CAAChC,KAAK,CAACgC,EAAE,SAAU;QAC9BL,KAAK,EAAE,IAAI,CAACL;MAAgC,CAC7C,CACH,CAAC;IACH;IAEA,IAAI,IAAI,CAACtB,KAAK,CAAC8B,QAAQ,EAAE;MACvB,IAAItB,KAAK,CAACC,OAAO,CAAC,IAAI,CAACT,KAAK,CAAC8B,QAAQ,CAAC,EAAE;QACtCA,QAAQ,CAACC,IAAI,CAAC,GAAG,IAAI,CAAC/B,KAAK,CAAC8B,QAAQ,CAAC;MACvC,CAAC,MAAM;QACLA,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC/B,KAAK,CAAC8B,QAAQ,CAAC;MACpC;IACF;IAEA,oBACEvC,IAAA,CAACJ,QAAQ,CAAC8C,WAAW;MACnBD,EAAE,EAAE,IAAI,CAAChC,KAAK,CAACgC,EAAG;MAClB5B,OAAO,EAAE,IAAI,CAACA,OAAQ;MACtBH,KAAK,EAAE,IAAI,CAACE,KAAK,CAACF,KAA8C;MAAA6B,QAAA,EAE/DA;IAAQ,CACW,CAAC;EAE3B;AACF;AAEA,eAAetC,UAAU", "ignoreList": []}