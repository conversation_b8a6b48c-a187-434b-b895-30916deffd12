import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { ClientStackParamList } from '../types';
import { colors, dimensions, typography, spacing } from '../constants/theme';
import { useCartStore } from '../store/cartStore';

// Écrans clients
import HomeScreen from '../screens/client/HomeScreen';
import SearchScreen from '../screens/client/SearchScreen';
import OrdersScreen from '../screens/client/OrdersScreen';
import ProfileScreen from '../screens/client/ProfileScreen';
import LocationScreen from '../screens/client/LocationScreenMapboxWeb';
import LocationScreenExact from '../screens/client/LocationScreenExact';


const Tab = createBottomTabNavigator();
const Stack = createStackNavigator<ClientStackParamList>();

// Composant Badge pour les notifications
const TabBarBadge: React.FC<{ count: number }> = ({ count }) => {
  if (count === 0) return null;
  
  return (
    <View style={styles.badge}>
      <Text style={styles.badgeText}>
        {count > 99 ? '99+' : count.toString()}
      </Text>
    </View>
  );
};

// Navigation par onglets
const TabNavigator: React.FC = () => {
  const { getTotalItems } = useCartStore();
  const cartItemsCount = getTotalItems();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Search':
              iconName = focused ? 'search' : 'search-outline';
              break;
            case 'Orders':
              iconName = focused ? 'receipt' : 'receipt-outline';
              break;
            case 'Profile':
              iconName = focused ? 'person' : 'person-outline';
              break;
            default:
              iconName = 'home-outline';
          }

          return (
            <View style={styles.tabIconContainer}>
              <Ionicons name={iconName} size={size} color={color} />
              {route.name === 'Home' && cartItemsCount > 0 && (
                <TabBarBadge count={cartItemsCount} />
              )}
            </View>
          );
        },
        tabBarActiveTintColor: colors.primary[500],
        tabBarInactiveTintColor: colors.neutral[500],
        tabBarStyle: styles.tabBar,
        tabBarLabelStyle: styles.tabBarLabel,
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: 'Accueil',
          tabBarLabel: 'Accueil'
        }}
      />
      <Tab.Screen 
        name="Search" 
        component={SearchScreen}
        options={{ 
          title: 'Rechercher',
          tabBarLabel: 'Rechercher'
        }}
      />
      <Tab.Screen 
        name="Orders" 
        component={OrdersScreen}
        options={{ 
          title: 'Commandes',
          tabBarLabel: 'Commandes'
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: 'Profil',
          tabBarLabel: 'Profil'
        }}
      />
      <Tab.Screen
        name="LocationExact"
        component={LocationScreenExact}
        options={{
          title: 'Interface Exacte',
          tabBarLabel: 'Test',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="location" size={size} color={color} />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

// Navigation principale avec pile pour les détails
const ClientNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary[500],
          elevation: 0,
          shadowOpacity: 0,
        },
        headerTintColor: colors.text.inverse,
        headerTitleStyle: {
          fontWeight: typography.fontWeight.bold,
          fontSize: typography.fontSize.lg,
        },
        headerBackTitleVisible: false,
        cardStyle: { backgroundColor: colors.background.primary },
      }}
    >
      {/* Navigation par onglets comme écran principal */}
      <Stack.Screen 
        name="HomeTabs" 
        component={TabNavigator}
        options={{ headerShown: false }}
      />
      
      {/* Écrans de détail avec navigation en pile */}
      <Stack.Screen 
        name="Establishment" 
        component={SearchScreen} // À remplacer par EstablishmentScreen
        options={{ 
          title: 'Restaurant',
          headerShown: true,
        }}
      />
      
      <Stack.Screen 
        name="Product" 
        component={SearchScreen} // À remplacer par ProductScreen
        options={{ 
          title: 'Produit',
          headerShown: true,
        }}
      />
      
      <Stack.Screen 
        name="Cart" 
        component={SearchScreen} // À remplacer par CartScreen
        options={{ 
          title: 'Panier',
          headerShown: true,
        }}
      />
      
      <Stack.Screen 
        name="Checkout" 
        component={SearchScreen} // À remplacer par CheckoutScreen
        options={{ 
          title: 'Commande',
          headerShown: true,
        }}
      />
      
      <Stack.Screen 
        name="OrderTracking" 
        component={SearchScreen} // À remplacer par OrderTrackingScreen
        options={{ 
          title: 'Suivi de commande',
          headerShown: true,
        }}
      />
      
      <Stack.Screen
        name="Location"
        component={LocationScreen}
        options={{
          title: 'Localisation',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="LocationExact"
        component={LocationScreenExact}
        options={{
          title: 'Votre localisation',
          headerShown: false,
        }}
      />



      <Stack.Screen
        name="Addresses"
        component={SearchScreen} // À remplacer par AddressesScreen
        options={{
          title: 'Mes adresses',
          headerShown: true,
        }}
      />

      <Stack.Screen
        name="AddAddress"
        component={SearchScreen} // À remplacer par AddAddressScreen
        options={{
          title: 'Ajouter une adresse',
          headerShown: true,
        }}
      />
      
      <Stack.Screen 
        name="Notifications" 
        component={SearchScreen} // À remplacer par NotificationsScreen
        options={{ 
          title: 'Notifications',
          headerShown: true,
        }}
      />
      
      <Stack.Screen 
        name="Help" 
        component={SearchScreen} // À remplacer par HelpScreen
        options={{ 
          title: 'Aide',
          headerShown: true,
        }}
      />
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: colors.surface.primary,
    borderTopColor: colors.border.light,
    borderTopWidth: 1,
    height: dimensions.tabBarHeight,
    paddingBottom: spacing.sm,
    paddingTop: spacing.sm,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },

  tabBarLabel: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    marginTop: 2,
  },

  tabIconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },

  badge: {
    position: 'absolute',
    top: -6,
    right: -12,
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },

  badgeText: {
    color: colors.text.inverse,
    fontSize: 10,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
  },
});

export default ClientNavigator; 