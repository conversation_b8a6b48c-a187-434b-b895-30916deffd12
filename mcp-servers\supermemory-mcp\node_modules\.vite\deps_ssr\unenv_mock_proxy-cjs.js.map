{"version": 3, "sources": ["../../unenv/lib/mock.cjs"], "sourcesContent": ["Object.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nfunction createMock(name, overrides = {}) {\n  const proxyFn = function () { /* noop */ };\n  proxyFn.prototype.name = name;\n  const props = {};\n  const proxy = new Proxy(proxyFn, {\n    get(_target, prop) {\n      if (prop === \"caller\") {\n        return null;\n      }\n      if (prop === \"__createMock__\") {\n        return createMock;\n      }\n      if (prop === \"__unenv__\") {\n        return true;\n      }\n      if (prop in overrides) {\n        return overrides[prop];\n      }\n      if (prop === \"then\") {\n        return (fn) => Promise.resolve(fn());\n      }\n      if (prop === \"catch\") {\n        return (fn) => Promise.resolve();\n      }\n      if (prop === \"finally\") {\n        return (fn) => Promise.resolve(fn());\n      }\n      return props[prop] = props[prop] || createMock(`${name}.${prop.toString()}`);\n    },\n    apply(_target, _this, _args) {\n      return createMock(`${name}()`);\n    },\n    construct(_target, _args, _newT) {\n      return createMock(`[${name}]`);\n    },\n    enumerate() {\n      return [];\n    }\n  });\n  return proxy;\n}\n\nmodule.exports = createMock(\"mock\");\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,aAAS,WAAW,MAAM,YAAY,CAAC,GAAG;AACxC,YAAM,UAAU,WAAY;AAAA,MAAa;AACzC,cAAQ,UAAU,OAAO;AACzB,YAAM,QAAQ,CAAC;AACf,YAAM,QAAQ,IAAI,MAAM,SAAS;AAAA,QAC/B,IAAI,SAAS,MAAM;AACjB,cAAI,SAAS,UAAU;AACrB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,kBAAkB;AAC7B,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,aAAa;AACxB,mBAAO;AAAA,UACT;AACA,cAAI,QAAQ,WAAW;AACrB,mBAAO,UAAU,IAAI;AAAA,UACvB;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO,CAAC,OAAO,QAAQ,QAAQ,GAAG,CAAC;AAAA,UACrC;AACA,cAAI,SAAS,SAAS;AACpB,mBAAO,CAAC,OAAO,QAAQ,QAAQ;AAAA,UACjC;AACA,cAAI,SAAS,WAAW;AACtB,mBAAO,CAAC,OAAO,QAAQ,QAAQ,GAAG,CAAC;AAAA,UACrC;AACA,iBAAO,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,WAAW,GAAG,IAAI,IAAI,KAAK,SAAS,CAAC,EAAE;AAAA,QAC7E;AAAA,QACA,MAAM,SAAS,OAAO,OAAO;AAC3B,iBAAO,WAAW,GAAG,IAAI,IAAI;AAAA,QAC/B;AAAA,QACA,UAAU,SAAS,OAAO,OAAO;AAC/B,iBAAO,WAAW,IAAI,IAAI,GAAG;AAAA,QAC/B;AAAA,QACA,YAAY;AACV,iBAAO,CAAC;AAAA,QACV;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,WAAW,MAAM;AAAA;AAAA;", "names": []}