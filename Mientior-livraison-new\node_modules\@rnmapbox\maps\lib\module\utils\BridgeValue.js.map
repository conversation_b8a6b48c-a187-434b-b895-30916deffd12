{"version": 3, "names": ["isBoolean", "isNumber", "isString", "BridgeValue", "constructor", "rawValue", "type", "Array", "isArray", "Error", "value", "innerRawValue", "bridgeValue", "push", "toJSON", "stringKeys", "Object", "keys", "<PERSON><PERSON><PERSON>", "formatter"], "sourceRoot": "../../../src", "sources": ["utils/BridgeValue.ts"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,SAAS;AAkBvD,eAAe,MAAMC,WAAW,CAAC;EAG/BC,WAAWA,CAACC,QAAsB,EAAE;IAClC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;EAEA,IAAIC,IAAIA,CAAA,EAAoB;IAC1B,IAAIC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACH,QAAQ,CAAC,EAAE;MAChC,OAAO,OAAO;IAChB;IACA,IAAIL,SAAS,CAAC,IAAI,CAACK,QAAQ,CAAC,EAAE;MAC5B,OAAO,SAAS;IAClB;IACA,IAAIJ,QAAQ,CAAC,IAAI,CAACI,QAAQ,CAAC,EAAE;MAC3B,OAAO,QAAQ;IACjB;IACA,IAAIH,QAAQ,CAAC,IAAI,CAACG,QAAQ,CAAC,EAAE;MAC3B,OAAO,QAAQ;IACjB;IACA,IAAI,IAAI,CAACA,QAAQ,IAAI,OAAO,IAAI,CAACA,QAAQ,KAAK,QAAQ,EAAE;MACtD,OAAO,SAAS;IAClB;IACA,MAAM,IAAII,KAAK,CACb,WAAW,IAAI,CAACJ,QAAQ,gDAC1B,CAAC;EACH;EAEA,IAAIK,KAAKA,CAAA,EAAG;IACV,MAAM;MAAEJ;IAAK,CAAC,GAAG,IAAI;IAErB,IAAII,KAAK;IAET,IAAIJ,IAAI,KAAK,OAAO,EAAE;MACpBI,KAAK,GAAG,EAAE;MAEV,MAAML,QAAQ,GAAG,IAAI,CAACA,QAA0B;MAChD,KAAK,MAAMM,aAAa,IAAIN,QAAQ,EAAE;QACpC,MAAMO,WAAW,GAAG,IAAIT,WAAW,CAACQ,aAAa,CAAC;QAClDD,KAAK,CAACG,IAAI,CAACD,WAAW,CAACE,MAAM,CAAC,CAAC,CAAC;MAClC;IACF,CAAC,MAAM,IAAIR,IAAI,KAAK,SAAS,EAAE;MAC7BI,KAAK,GAAG,EAAE;MAEV,MAAML,QAAQ,GAAG,IAAI,CAACA,QAA2C;MACjE,MAAMU,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACZ,QAAQ,CAAC;MACxC,KAAK,MAAMa,SAAS,IAAIH,UAAU,EAAE;QAClCL,KAAK,CAACG,IAAI,CAAC,CACT,IAAIV,WAAW,CAACe,SAAS,CAAC,CAACJ,MAAM,CAAC,CAAC,EACnC,IAAIX,WAAW,CAACE,QAAQ,CAACa,SAAS,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAC9C,CAAC;MACJ;IACF,CAAC,MAAM,IAAIR,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACvEI,KAAK,GAAG,IAAI,CAACL,QAAQ;IACvB,CAAC,MAAM;MACL,MAAM,IAAII,KAAK,CACb,YAAY,IAAI,CAACJ,QAAQ,gDAC3B,CAAC;IACH;IAEA,OAAOK,KAAK;EACd;EAEAI,MAAMA,CAACK,SAA6B,EAAkB;IACpD,OAAO;MACLb,IAAI,EAAE,IAAI,CAACA,IAAI;MACfI,KAAK,EACH,OAAOS,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC,IAAI,CAACT,KAAK,CAAC,GAAG,IAAI,CAACA;IACnE,CAAC;EACH;AACF", "ignoreList": []}