internal extension Encodable {
  func toJSON() throws -> Any {
      return try JSONSerialization.jsonObject(with: <PERSON><PERSON><PERSON><PERSON><PERSON>().encode(self))
  }
  
  func toJSON() throws -> [String:Any] {
    let result : Any = try toJ<PERSON><PERSON>()
    guard let result = result as? [String:Any] else {
      throw RNMBXError.paramError("Expected object but got something else: \(result)")
    }
    return result
  }
}
