{"version": 3, "names": ["React", "View", "Text", "Animated", "StyleSheet", "RNMBXCalloutNativeComponent", "jsx", "_jsx", "jsxs", "_jsxs", "styles", "create", "container", "alignItems", "justifyContent", "width", "zIndex", "tip", "marginTop", "elevation", "backgroundColor", "borderTopWidth", "borderRightWidth", "borderBottomWidth", "borderLeftWidth", "borderTopColor", "borderRightColor", "borderBottomColor", "borderLeftColor", "content", "position", "padding", "flex", "borderRadius", "borderWidth", "borderColor", "title", "color", "textAlign", "Callout", "PureComponent", "_containerStyle", "style", "props", "containerStyle", "push", "_has<PERSON><PERSON><PERSON>n", "Children", "count", "children", "_renderDefaultCallout", "contentStyle", "textStyle", "tipStyle", "_renderCustomCallout", "render", "callout<PERSON><PERSON><PERSON>"], "sourceRoot": "../../../src", "sources": ["components/Callout.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SACEC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,UAAU,QAGL,cAAc;AAErB,OAAOC,2BAA2B,MAAM,sCAAsC;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE/E,MAAMC,MAAM,GAAGN,UAAU,CAACO,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE;EACV,CAAC;EACDC,GAAG,EAAE;IACHD,MAAM,EAAE,IAAI;IACZE,SAAS,EAAE,CAAC,CAAC;IACbC,SAAS,EAAE,CAAC;IACZC,eAAe,EAAE,aAAa;IAC9BC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,iBAAiB,EAAE,CAAC;IACpBC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE,OAAO;IACvBC,gBAAgB,EAAE,aAAa;IAC/BC,iBAAiB,EAAE,aAAa;IAChCC,eAAe,EAAE;EACnB,CAAC;EACDC,OAAO,EAAE;IACPC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,CAAC;IACVC,IAAI,EAAE,CAAC;IACPC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,oBAAoB;IACjCf,eAAe,EAAE;EACnB,CAAC;EACDgB,KAAK,EAAE;IACLC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAkCF;AACA;AACA;AACA,MAAMC,OAAO,SAASvC,KAAK,CAACwC,aAAa,CAAQ;EAC/C,IAAIC,eAAeA,CAAA,EAAG;IACpB,MAAMC,KAAK,GAAG,CACZ;MACEZ,QAAQ,EAAE,UAAU;MACpBd,MAAM,EAAE,GAAG;MACXI,eAAe,EAAE;IACnB,CAAC,CACF;IAED,IAAI,IAAI,CAACuB,KAAK,CAACC,cAAc,EAAE;MAC7BF,KAAK,CAACG,IAAI,CAAC,IAAI,CAACF,KAAK,CAACC,cAAc,CAAC;IACvC;IAEA,OAAOF,KAAK;EACd;EAEA,IAAII,YAAYA,CAAA,EAAG;IACjB,OAAO9C,KAAK,CAAC+C,QAAQ,CAACC,KAAK,CAAC,IAAI,CAACL,KAAK,CAACM,QAAQ,CAAC,GAAG,CAAC;EACtD;EAEAC,qBAAqBA,CAAA,EAAc;IACjC,oBACEzC,KAAA,CAACN,QAAQ,CAACF,IAAI;MAACyC,KAAK,EAAE,CAAChC,MAAM,CAACE,SAAS,EAAE,IAAI,CAAC+B,KAAK,CAACD,KAAK,CAAE;MAAAO,QAAA,gBACzD1C,IAAA,CAACN,IAAI;QAACyC,KAAK,EAAE,CAAChC,MAAM,CAACmB,OAAO,EAAE,IAAI,CAACc,KAAK,CAACQ,YAAY,CAAE;QAAAF,QAAA,eACrD1C,IAAA,CAACL,IAAI;UAACwC,KAAK,EAAE,CAAChC,MAAM,CAAC0B,KAAK,EAAE,IAAI,CAACO,KAAK,CAACS,SAAS,CAAE;UAAAH,QAAA,EAC/C,IAAI,CAACN,KAAK,CAACP;QAAK,CACb;MAAC,CACH,CAAC,eACP7B,IAAA,CAACN,IAAI;QAACyC,KAAK,EAAE,CAAChC,MAAM,CAACO,GAAG,EAAE,IAAI,CAAC0B,KAAK,CAACU,QAAQ;MAAE,CAAE,CAAC;IAAA,CACrC,CAAC;EAEpB;EAEAC,oBAAoBA,CAAA,EAAc;IAChC,oBACE/C,IAAA,CAACJ,QAAQ,CAACF,IAAI;MAAA,GAAK,IAAI,CAAC0C,KAAK;MAAED,KAAK,EAAE,IAAI,CAACC,KAAK,CAACD,KAAM;MAAAO,QAAA,EACpD,IAAI,CAACN,KAAK,CAACM;IAAQ,CACP,CAAC;EAEpB;EAEAM,MAAMA,CAAA,EAAG;IACP,MAAMC,cAAc,GAAG,IAAI,CAACV,YAAY,GACpC,IAAI,CAACQ,oBAAoB,CAAC,CAAC,GAC3B,IAAI,CAACJ,qBAAqB,CAAC,CAAC;IAChC,oBACE3C,IAAA,CAACF,2BAA2B;MAACqC,KAAK,EAAE,IAAI,CAACD,eAAgB;MAAAQ,QAAA,EACtDO;IAAc,CACY,CAAC;EAElC;AACF;AAEA,eAAejB,OAAO", "ignoreList": []}