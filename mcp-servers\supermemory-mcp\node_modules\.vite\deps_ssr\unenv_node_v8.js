import {
  noop_default
} from "./chunk-DQN6H3OM.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/v8.mjs
import { Readable } from "node:stream";

// node_modules/unenv/dist/runtime/node/internal/v8/deserializer.mjs
var Deserializer = class {
  readHeader() {
    return false;
  }
  readValue() {
  }
  transferArrayBuffer(id, arrayBuffer) {
  }
  getWireFormatVersion() {
    return 0;
  }
  readUint32() {
    return 0;
  }
  readUint64() {
    return [0, 0];
  }
  readDouble() {
    return 0;
  }
  readRawBytes(length) {
    return Buffer.from("");
  }
};
var DefaultDeserializer = class extends Deserializer {
};

// node_modules/unenv/dist/runtime/node/internal/v8/serializer.mjs
var Serializer = class {
  writeHeader() {
  }
  writeValue(val) {
    return false;
  }
  releaseBuffer() {
    return Buffer.from("");
  }
  transferArrayBuffer(id, arrayBuffer) {
  }
  writeDouble(value) {
  }
  writeUint32(value) {
  }
  writeUint64(hi, lo) {
  }
  writeRawBytes(buffer) {
  }
};
var DefaultSerializer = class extends Serializer {
};

// node_modules/unenv/dist/runtime/node/internal/v8/profiler.mjs
var GCProfiler = class {
  start() {
  }
  stop() {
    return {
      version: 1,
      startTime: 0,
      endTime: 0,
      statistics: []
    };
  }
};

// node_modules/unenv/dist/runtime/node/v8.mjs
var getMockHeapSpaceStats = (name) => ({
  space_name: name,
  space_size: 0,
  space_used_size: 0,
  space_available_size: 0,
  physical_space_size: 0
});
var cachedDataVersionTag = () => 0;
var deserialize = noop_default;
var getHeapCodeStatistics = () => ({
  code_and_metadata_size: 0,
  bytecode_and_metadata_size: 0,
  external_script_source_size: 0,
  cpu_profiler_metadata_size: 0
});
var getHeapSpaceStatistics = () => [
  "read_only_space",
  "new_space",
  "old_space",
  "code_space",
  "map_space",
  "large_object_space",
  "code_large_object_space",
  "new_large_object_space"
].map((space) => getMockHeapSpaceStats(space));
var getHeapStatistics = () => ({
  total_heap_size: 0,
  total_heap_size_executable: 0,
  total_physical_size: 0,
  total_available_size: 0,
  used_heap_size: 0,
  heap_size_limit: 0,
  malloced_memory: 0,
  peak_malloced_memory: 0,
  does_zap_garbage: 0,
  number_of_native_contexts: 0,
  number_of_detached_contexts: 0,
  total_global_handles_size: 0,
  used_global_handles_size: 0,
  external_memory: 0
});
var getHeapSnapshot = () => {
  return Readable.from(`{
    snapshot: {},
    nodes: [],
    edges: [],
    trace_function_infos: [],
    trace_tree: [],
    samples: [],
    locations: [],
    strings: [],
  }`);
};
var promiseHooks = {
  onInit: () => noop_default,
  onSettled: () => noop_default,
  onBefore: () => noop_default,
  onAfter: () => noop_default,
  createHook: () => noop_default
};
var serialize = (value) => Buffer.from(value);
var setFlagsFromString = noop_default;
var setHeapSnapshotNearHeapLimit = noop_default;
var startupSnapshot = {
  addDeserializeCallback: noop_default,
  addSerializeCallback: noop_default,
  setDeserializeMainFunction: noop_default,
  isBuildingSnapshot: () => false
};
var stopCoverage = noop_default;
var takeCoverage = noop_default;
var writeHeapSnapshot = () => "";
function queryObjects(_ctor, options) {
  if (options?.format === "count") {
    return 0;
  }
  return [];
}
var v8_default = {
  DefaultDeserializer,
  Deserializer,
  GCProfiler,
  DefaultSerializer,
  Serializer,
  cachedDataVersionTag,
  deserialize,
  getHeapCodeStatistics,
  getHeapSnapshot,
  getHeapSpaceStatistics,
  getHeapStatistics,
  promiseHooks,
  serialize,
  setFlagsFromString,
  setHeapSnapshotNearHeapLimit,
  startupSnapshot,
  stopCoverage,
  takeCoverage,
  writeHeapSnapshot,
  queryObjects
};
export {
  DefaultDeserializer,
  DefaultSerializer,
  Deserializer,
  GCProfiler,
  Serializer,
  cachedDataVersionTag,
  v8_default as default,
  deserialize,
  getHeapCodeStatistics,
  getHeapSnapshot,
  getHeapSpaceStatistics,
  getHeapStatistics,
  promiseHooks,
  queryObjects,
  serialize,
  setFlagsFromString,
  setHeapSnapshotNearHeapLimit,
  startupSnapshot,
  stopCoverage,
  takeCoverage,
  writeHeapSnapshot
};
//# sourceMappingURL=unenv_node_v8.js.map
