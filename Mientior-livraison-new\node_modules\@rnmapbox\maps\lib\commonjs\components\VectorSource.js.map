{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_RNMBXVectorSourceNativeComponent", "_utils", "_deprecation", "_AbstractSource", "_jsxRuntime", "e", "__esModule", "default", "MapboxGL", "NativeModules", "RNMBXModule", "NATIVE_MODULE_NAME", "exports", "VectorSource", "AbstractSource", "defaultProps", "id", "StyleSource", "DefaultSourceID", "constructor", "props", "_decodePayload", "payload", "JSON", "parse", "onPress", "event", "nativeEvent", "features", "coordinates", "point", "newEvent", "copyPropertiesAsDeprecated", "key", "console", "warn", "origNativeEvent", "render", "existing", "url", "tileUrlTemplates", "minZoomLevel", "maxZoomLevel", "tms", "attribution", "hitbox", "hasPressListener", "isFunction", "onMapboxVectorSourcePress", "bind", "undefined", "jsx", "ref", "setNativeRef", "children", "cloneReactChildrenWithProps", "sourceID", "_default"], "sourceRoot": "../../../src", "sources": ["components/VectorSource.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,iCAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AAGA,IAAAK,eAAA,GAAAN,sBAAA,CAAAC,OAAA;AAA8C,IAAAM,WAAA,GAAAN,OAAA;AAAA,SAAAD,uBAAAQ,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE9C,MAAMG,QAAQ,GAAGC,0BAAa,CAACC,WAAW;AAEnC,MAAMC,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG,mBAAmB;;AA6ErD;;AAC0B;AAC1B;AACA;AACA;AACA;AACA,MAAME,YAAY,SAASC,uBAAc,CAAqB;EAC5D,OAAOC,YAAY,GAAG;IACpBC,EAAE,EAAER,QAAQ,CAACS,WAAW,CAACC;EAC3B,CAAC;EAEDC,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;EACd;EAEAC,cAAcA,CAACC,OAA8B,EAAgB;IAC3D;IACA;IACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,OAAOC,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;IAC5B,CAAC,MAAM;MACL,OAAOA,OAAO;IAChB;EACF;EAEAG,OAAOA,CACLC,KAEE,EACF;IACA,MAAMJ,OAAO,GAAG,IAAI,CAACD,cAAc,CAACK,KAAK,CAACC,WAAW,CAACL,OAAO,CAAC;IAC9D,MAAM;MAAEM,QAAQ;MAAEC,WAAW;MAAEC;IAAM,CAAC,GAAGR,OAAO;IAChD,IAAIS,QAAQ,GAAG;MACbH,QAAQ;MACRC,WAAW;MACXC;IACF,CAAC;IACDC,QAAQ,GAAG,IAAAC,uCAA0B,EACnCN,KAAK,EACLK,QAAQ,EACPE,GAAG,IAAK;MACPC,OAAO,CAACC,IAAI,CACV,SAASF,GAAG,mEACd,CAAC;IACH,CAAC,EACD;MACEN,WAAW,EAAGS,eAAwB,KAAM;QAC1C,GAAIA,eAAgC;QACpCd,OAAO,EAAEM,QAAQ,CAAC,CAAC;MACrB,CAAC;IACH,CACF,CAAC;IACD,MAAM;MAAEH;IAAQ,CAAC,GAAG,IAAI,CAACL,KAAK;IAC9B,IAAIK,OAAO,EAAE;MACXA,OAAO,CAACM,QAAQ,CAAC;IACnB;EACF;EAEAM,MAAMA,CAAA,EAAG;IACP,MAAMjB,KAAK,GAAG;MACZJ,EAAE,EAAE,IAAI,CAACI,KAAK,CAACJ,EAAE;MACjBsB,QAAQ,EAAE,IAAI,CAAClB,KAAK,CAACkB,QAAQ;MAC7BC,GAAG,EAAE,IAAI,CAACnB,KAAK,CAACmB,GAAG;MACnBC,gBAAgB,EAAE,IAAI,CAACpB,KAAK,CAACoB,gBAAgB;MAC7CC,YAAY,EAAE,IAAI,CAACrB,KAAK,CAACqB,YAAY;MACrCC,YAAY,EAAE,IAAI,CAACtB,KAAK,CAACsB,YAAY;MACrCC,GAAG,EAAE,IAAI,CAACvB,KAAK,CAACuB,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACxB,KAAK,CAACwB,WAAW;MACnCC,MAAM,EAAE,IAAI,CAACzB,KAAK,CAACyB,MAAM;MACzBC,gBAAgB,EAAE,IAAAC,iBAAU,EAAC,IAAI,CAAC3B,KAAK,CAACK,OAAO,CAAC;MAChDuB,yBAAyB,EAAE,IAAI,CAACvB,OAAO,CAACwB,IAAI,CAAC,IAAI,CAAC;MAClDxB,OAAO,EAAEyB;IACX,CAAC;IACD;MAAA;MACE;MACA,IAAA9C,WAAA,CAAA+C,GAAA,EAACnD,iCAAA,CAAAO,OAAgC;QAAC6C,GAAG,EAAE,IAAI,CAACC,YAAa;QAAA,GAAKjC,KAAK;QAAAkC,QAAA,EAChE,IAAAC,kCAA2B,EAAC,IAAI,CAACnC,KAAK,CAACkC,QAAQ,EAAE;UAChDE,QAAQ,EAAE,IAAI,CAACpC,KAAK,CAACJ;QACvB,CAAC;MAAC,CAC8B;IAAC;EAEvC;AACF;AAAC,IAAAyC,QAAA,GAAA7C,OAAA,CAAAL,OAAA,GAEcM,YAAY", "ignoreList": []}