{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_RNMBXShapeSourceNativeComponent", "_NativeRNMBXShapeSourceModule", "_utils", "_deprecation", "_AbstractSource", "_NativeBridgeComponent", "_jsxRuntime", "e", "__esModule", "default", "MapboxGL", "NativeModules", "RNMBXModule", "ShapeSource", "NativeBridgeComponent", "AbstractSource", "NativeRNMBXShapeSourceModule", "NATIVE_ASSETS_KEY", "defaultProps", "id", "StyleSource", "DefaultSourceID", "constructor", "props", "_setNativeRef", "nativeRef", "setNativeRef", "_runPendingNativeMethods", "getClusterExpansionZoom", "feature", "res", "_runNativeMethod", "_nativeRef", "JSON", "stringify", "data", "getClusterLeaves", "limit", "offset", "isAndroid", "parse", "getClusterChildren", "setNativeProps", "shallowProps", "Object", "assign", "shape", "_getShape", "toJSONString", "_decodePayload", "payload", "onPress", "event", "nativeEvent", "features", "coordinates", "point", "newEvent", "copyPropertiesAsDeprecated", "key", "console", "warn", "origNativeEvent", "render", "existing", "url", "hitbox", "hasPressListener", "isFunction", "onMapboxShapeSourcePress", "bind", "cluster", "clusterRadius", "clusterMaxZoomLevel", "clusterProperties", "maxZoomLevel", "buffer", "tolerance", "lineMetrics", "undefined", "ref", "jsx", "children", "cloneReactChildrenWithProps", "sourceID", "exports"], "sourceRoot": "../../../src", "sources": ["components/ShapeSource.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAMA,IAAAE,gCAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,6BAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAMA,IAAAK,YAAA,GAAAL,OAAA;AAIA,IAAAM,eAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,sBAAA,GAAAR,sBAAA,CAAAC,OAAA;AAA4D,IAAAQ,WAAA,GAAAR,OAAA;AAAA,SAAAD,uBAAAU,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE5D,MAAMG,QAAQ,GAAGC,0BAAa,CAACC,WAAW;AA4H1C;AACA;AACA;AACA;AACO,MAAMC,WAAW,SAAS,IAAAC,8BAAqB,EACpDC,uBAAc,EACdC,qCACF,CAAC,CAAC;EACA,OAAOC,iBAAiB,GAAG,QAAQ;EAEnC,OAAOC,YAAY,GAAG;IACpBC,EAAE,EAAET,QAAQ,CAACU,WAAW,CAACC;EAC3B,CAAC;EAEDC,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;EACd;EAEAC,aAAaA,CACXC,SAAiE,EACjE;IACA,IAAI,CAACC,YAAY,CAACD,SAAS,CAAC;IAC5B,KAAK,CAACE,wBAAwB,CAACF,SAAS,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMG,uBAAuBA,CAC3BC,OAAiC,EAChB;IACjB,MAAMC,GAAqB,GAAG,MAAM,IAAI,CAACC,gBAAgB,CACvD,yBAAyB,EACzB,IAAI,CAACC,UAAU,EACf,CAACC,IAAI,CAACC,SAAS,CAACL,OAAO,CAAC,CAC1B,CAAC;IACD,OAAOC,GAAG,CAACK,IAAI;EACjB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,gBAAgBA,CACpBP,OAAiC,EACjCQ,KAAa,EACbC,MAAc,EACd;IACA,MAAMR,GAAqB,GAAG,MAAM,IAAI,CAACC,gBAAgB,CACvD,kBAAkB,EAClB,IAAI,CAACC,UAAU,EACf,CAACC,IAAI,CAACC,SAAS,CAACL,OAAO,CAAC,EAAEQ,KAAK,EAAEC,MAAM,CACzC,CAAC;IAED,IAAI,IAAAC,gBAAS,EAAC,CAAC,EAAE;MACf,OAAON,IAAI,CAACO,KAAK,CAACV,GAAG,CAACK,IAAI,CAAC;IAC7B;IACA,OAAOL,GAAG,CAACK,IAAI;EACjB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMM,kBAAkBA,CAACZ,OAAiC,EAAE;IAC1D,MAAMC,GAAqB,GAAG,MAAM,IAAI,CAACC,gBAAgB,CACvD,oBAAoB,EACpB,IAAI,CAACC,UAAU,EACf,CAACC,IAAI,CAACC,SAAS,CAACL,OAAO,CAAC,CAC1B,CAAC;IAED,IAAI,IAAAU,gBAAS,EAAC,CAAC,EAAE;MACf,OAAON,IAAI,CAACO,KAAK,CAACV,GAAG,CAACK,IAAI,CAAC;IAC7B;IACA,OAAOL,GAAG,CAACK,IAAI;EACjB;EAEAO,cAAcA,CAACnB,KAAkB,EAAE;IACjC,MAAMoB,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtB,KAAK,CAAC;;IAE7C;IACA,IAAIoB,YAAY,CAACG,KAAK,IAAI,OAAOH,YAAY,CAACG,KAAK,KAAK,QAAQ,EAAE;MAChEH,YAAY,CAACG,KAAK,GAAGb,IAAI,CAACC,SAAS,CAACS,YAAY,CAACG,KAAK,CAAC;IACzD;IAEA,KAAK,CAACJ,cAAc,CAACC,YAAY,CAAC;EACpC;EAEAI,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACxB,KAAK,CAACuB,KAAK,EAAE;MACrB;IACF;IACA,OAAO,IAAAE,mBAAY,EAAC,IAAI,CAACzB,KAAK,CAACuB,KAAK,CAAC;EACvC;EAEAG,cAAcA,CAACC,OAA8B,EAAgB;IAC3D;IACA;IACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,OAAOjB,IAAI,CAACO,KAAK,CAACU,OAAO,CAAC;IAC5B,CAAC,MAAM;MACL,OAAOA,OAAO;IAChB;EACF;EAEAC,OAAOA,CACLC,KAEE,EACF;IACA,MAAMF,OAAO,GAAG,IAAI,CAACD,cAAc,CAACG,KAAK,CAACC,WAAW,CAACH,OAAO,CAAC;IAC9D,MAAM;MAAEI,QAAQ;MAAEC,WAAW;MAAEC;IAAM,CAAC,GAAGN,OAAO;IAChD,IAAIO,QAAgC,GAAG;MACrCH,QAAQ;MACRC,WAAW;MACXC;IACF,CAAC;IAEDC,QAAQ,GAAG,IAAAC,uCAA0B,EACnCN,KAAK,EACLK,QAAQ,EACPE,GAAG,IAAK;MACPC,OAAO,CAACC,IAAI,CACV,SAASF,GAAG,kEACd,CAAC;IACH,CAAC,EACD;MACEN,WAAW,EAAGS,eAAwB,KAAM;QAC1C,GAAIA,eAAgC;QACpCZ,OAAO,EAAEI,QAAQ,CAAC,CAAC;MACrB,CAAC;IACH,CACF,CAAC;IACD,IAAI,CAAC/B,KAAK,CAAC4B,OAAO,GAAGM,QAAQ,CAAC;EAChC;EAEAM,MAAMA,CAAA,EAAG;IACP,MAAMxC,KAAK,GAAG;MACZJ,EAAE,EAAE,IAAI,CAACI,KAAK,CAACJ,EAAE;MACjB6C,QAAQ,EAAE,IAAI,CAACzC,KAAK,CAACyC,QAAQ;MAC7BC,GAAG,EAAE,IAAI,CAAC1C,KAAK,CAAC0C,GAAG;MACnBnB,KAAK,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC;MACvBmB,MAAM,EAAE,IAAI,CAAC3C,KAAK,CAAC2C,MAAM;MACzBC,gBAAgB,EAAE,IAAAC,iBAAU,EAAC,IAAI,CAAC7C,KAAK,CAAC4B,OAAO,CAAC;MAChDkB,wBAAwB,EAAE,IAAI,CAAClB,OAAO,CAACmB,IAAI,CAAC,IAAI,CAAC;MACjDC,OAAO,EAAE,IAAI,CAAChD,KAAK,CAACgD,OAAO,GAAG,CAAC,GAAG,CAAC;MACnCC,aAAa,EAAE,IAAI,CAACjD,KAAK,CAACiD,aAAa;MACvCC,mBAAmB,EAAE,IAAI,CAAClD,KAAK,CAACkD,mBAAmB;MACnDC,iBAAiB,EAAE,IAAI,CAACnD,KAAK,CAACmD,iBAAiB;MAC/CC,YAAY,EAAE,IAAI,CAACpD,KAAK,CAACoD,YAAY;MACrCC,MAAM,EAAE,IAAI,CAACrD,KAAK,CAACqD,MAAM;MACzBC,SAAS,EAAE,IAAI,CAACtD,KAAK,CAACsD,SAAS;MAC/BC,WAAW,EAAE,IAAI,CAACvD,KAAK,CAACuD,WAAW;MACnC3B,OAAO,EAAE4B,SAAS;MAClBC,GAAG,EACDvD,SAAiE,IAC9D,IAAI,CAACD,aAAa,CAACC,SAAS;IACnC,CAAC;IAED;MAAA;MACE;MACA,IAAAnB,WAAA,CAAA2E,GAAA,EAACjF,gCAAA,CAAAS,OAA+B;QAAA,GAAKc,KAAK;QAAA2D,QAAA,EACvC,IAAAC,kCAA2B,EAAC,IAAI,CAAC5D,KAAK,CAAC2D,QAAQ,EAAE;UAChDE,QAAQ,EAAE,IAAI,CAAC7D,KAAK,CAACJ;QACvB,CAAC;MAAC,CAC6B;IAAC;EAEtC;AACF;AAACkE,OAAA,CAAAxE,WAAA,GAAAA,WAAA", "ignoreList": []}