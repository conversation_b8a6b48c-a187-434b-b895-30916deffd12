{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/dgram/socket.mjs", "../../unenv/dist/runtime/node/dgram.mjs"], "sourcesContent": ["import { EventEmitter } from \"node:events\";\nexport class Socket extends EventEmitter {\n\t__unenv__ = true;\n\tbind() {\n\t\treturn this;\n\t}\n\tclose() {\n\t\treturn this;\n\t}\n\tref() {\n\t\treturn this;\n\t}\n\tunref() {\n\t\treturn this;\n\t}\n\tgetRecvBufferSize() {\n\t\treturn 1e5;\n\t}\n\tgetSendBufferSize() {\n\t\treturn 1e4;\n\t}\n\tgetSendQueueSize() {\n\t\treturn 0;\n\t}\n\tgetSendQueueCount() {\n\t\treturn 0;\n\t}\n\tsetMulticastLoopback() {\n\t\treturn false;\n\t}\n\tsetMulticastTTL() {\n\t\treturn 1;\n\t}\n\tsetTTL() {\n\t\treturn 1;\n\t}\n\taddress() {\n\t\treturn {\n\t\t\taddress: \"127.0.0.1\",\n\t\t\tfamily: \"IPv4\",\n\t\t\tport: 1234\n\t\t};\n\t}\n\tremoteAddress() {\n\t\tthrow new Error(\"ERR_SOCKET_DGRAM_NOT_CONNECTED\");\n\t}\n\t[Symbol.asyncDispose]() {\n\t\treturn Promise.resolve();\n\t}\n\taddMembership() {}\n\taddSourceSpecificMembership() {}\n\tconnect() {}\n\tdisconnect() {}\n\tdropMembership() {}\n\tdropSourceSpecificMembership() {}\n\tsend() {}\n\tsetSendBufferSize() {}\n\tsetBroadcast() {}\n\tsetRecvBufferSize() {}\n\tsetMulticastInterface() {}\n}\n", "import noop from \"../mock/noop.mjs\";\nimport { Socket } from \"./internal/dgram/socket.mjs\";\nexport { Socket } from \"./internal/dgram/socket.mjs\";\nexport const _createSocketHandle = noop;\nexport const createSocket = function() {\n\treturn new Socket();\n};\nexport default {\n\tSocket,\n\t_createSocketHandle,\n\tcreateSocket\n};\n"], "mappings": ";;;;;;AAAA,SAAS,oBAAoB;AACtB,IAAM,SAAN,cAAqB,aAAa;AAAA,EACxC,YAAY;AAAA,EACZ,OAAO;AACN,WAAO;AAAA,EACR;AAAA,EACA,QAAQ;AACP,WAAO;AAAA,EACR;AAAA,EACA,MAAM;AACL,WAAO;AAAA,EACR;AAAA,EACA,QAAQ;AACP,WAAO;AAAA,EACR;AAAA,EACA,oBAAoB;AACnB,WAAO;AAAA,EACR;AAAA,EACA,oBAAoB;AACnB,WAAO;AAAA,EACR;AAAA,EACA,mBAAmB;AAClB,WAAO;AAAA,EACR;AAAA,EACA,oBAAoB;AACnB,WAAO;AAAA,EACR;AAAA,EACA,uBAAuB;AACtB,WAAO;AAAA,EACR;AAAA,EACA,kBAAkB;AACjB,WAAO;AAAA,EACR;AAAA,EACA,SAAS;AACR,WAAO;AAAA,EACR;AAAA,EACA,UAAU;AACT,WAAO;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACP;AAAA,EACD;AAAA,EACA,gBAAgB;AACf,UAAM,IAAI,MAAM,gCAAgC;AAAA,EACjD;AAAA,EACA,CAAC,OAAO,YAAY,IAAI;AACvB,WAAO,QAAQ,QAAQ;AAAA,EACxB;AAAA,EACA,gBAAgB;AAAA,EAAC;AAAA,EACjB,8BAA8B;AAAA,EAAC;AAAA,EAC/B,UAAU;AAAA,EAAC;AAAA,EACX,aAAa;AAAA,EAAC;AAAA,EACd,iBAAiB;AAAA,EAAC;AAAA,EAClB,+BAA+B;AAAA,EAAC;AAAA,EAChC,OAAO;AAAA,EAAC;AAAA,EACR,oBAAoB;AAAA,EAAC;AAAA,EACrB,eAAe;AAAA,EAAC;AAAA,EAChB,oBAAoB;AAAA,EAAC;AAAA,EACrB,wBAAwB;AAAA,EAAC;AAC1B;;;ACzDO,IAAM,sBAAsB;AAC5B,IAAM,eAAe,WAAW;AACtC,SAAO,IAAI,OAAO;AACnB;AACA,IAAO,gBAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AACD;", "names": []}