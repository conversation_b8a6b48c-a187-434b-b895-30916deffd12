{"version": 3, "names": ["<PERSON><PERSON>", "instance", "level", "startedCount", "sharedInstance", "constructor", "logCallback", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setLogLevel", "start", "stop", "subscribe", "unsubscribe", "effectiveLevel", "log", "message", "tag", "startsWith", "onLog", "console", "error", "warn"], "sourceRoot": "../../../../src", "sources": ["web/utils/Logger.ts"], "mappings": ";;AAAA;;AASA,MAAMA,MAAM,CAAC;EACX,OAAOC,QAAQ,GAAkB,IAAI;EAErCC,KAAK,GAAa,MAAM;EAExBC,YAAY,GAAG,CAAC;EAEhB,OAAOC,cAAcA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACH,QAAQ,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACA,QAAQ,GAAG,IAAID,MAAM,CAAC,CAAC;IAC9B;IACA,OAAO,IAAI,CAACC,QAAQ;EACtB;EAEAI,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,WAAW,GAAG,IAAI;EACzB;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOC,cAAcA,CAACD,WAAwB,EAAE;IAC9C,IAAI,CAACF,cAAc,CAAC,CAAC,CAACG,cAAc,CAACD,WAAW,CAAC;EACnD;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAACD,WAAwB,EAAE;IACvC,IAAI,CAACA,WAAW,GAAGA,WAAW;EAChC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE;AACF;AACA;AACA;EACE,OAAOE,WAAWA,CAACN,KAAe,EAAE;IAClC,IAAI,CAACE,cAAc,CAAC,CAAC,CAACF,KAAK,GAAGA,KAAK;EACrC;EAEAO,KAAKA,CAAA,EAAG,CAAC;EAETC,IAAIA,CAAA,EAAG,CAAC;EAERC,SAASA,CAAA,EAAG;IACV;EAAA;EAGFC,WAAWA,CAAA,EAAG;IACZ;EAAA;EAGFC,cAAcA,CAACC,GAAe,EAAY;IACxC,MAAM;MAAEZ,KAAK;MAAEa,OAAO;MAAEC;IAAI,CAAC,GAAGF,GAAG;IAEnC,IAAIZ,KAAK,KAAK,SAAS,EAAE;MACvB,IACEc,GAAG,KAAK,kBAAkB,IAC1BD,OAAO,CAACE,UAAU,CAAC,mDAAmD,CAAC,EACvE;QACA;QACA,OAAO,MAAM;MACf;IACF;IACA,OAAOf,KAAK;EACd;EAEAgB,KAAKA,CAACJ,GAAe,EAAE;IACrB,IAAI,CAAC,IAAI,CAACR,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACQ,GAAG,CAAC,EAAE;MAC/C,MAAM;QAAEC;MAAQ,CAAC,GAAGD,GAAG;MACvB,MAAMZ,KAAK,GAAG,IAAI,CAACW,cAAc,CAACC,GAAG,CAAC;MACtC,IAAIZ,KAAK,KAAK,OAAO,EAAE;QACrBiB,OAAO,CAACC,KAAK,CAAC,cAAc,EAAEL,OAAO,EAAED,GAAG,CAAC;MAC7C,CAAC,MAAM,IAAIZ,KAAK,KAAK,SAAS,EAAE;QAC9BiB,OAAO,CAACE,IAAI,CAAC,gBAAgB,EAAEN,OAAO,EAAED,GAAG,CAAC;MAC9C,CAAC,MAAM;QACLK,OAAO,CAACL,GAAG,CAAC,WAAWZ,KAAK,GAAG,EAAEa,OAAO,EAAED,GAAG,CAAC;MAChD;IACF;EACF;AACF;AAEAd,MAAM,CAACI,cAAc,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC;AAE/B,eAAeT,MAAM", "ignoreList": []}