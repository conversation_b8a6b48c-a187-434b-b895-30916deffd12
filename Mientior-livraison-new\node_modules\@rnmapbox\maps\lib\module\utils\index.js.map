{"version": 3, "names": ["React", "findNodeHandle", "Platform", "Image", "isAndroid", "OS", "existenceChange", "cur", "next", "isFunction", "fn", "isNumber", "num", "Number", "isNaN", "isUndefined", "obj", "isString", "str", "isBoolean", "bool", "isPrimitive", "value", "runNativeMethod", "turboModule", "name", "nativeRef", "args", "handle", "Error", "module", "cloneReactChildrenWithProps", "children", "propsToAdd", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "child", "Children", "map", "cloneElement", "resolveImagePath", "imageRef", "res", "resolveAssetSource", "uri", "toJSONString", "json", "JSON", "stringify"], "sourceRoot": "../../../src", "sources": ["utils/index.ts"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,cAAc,EACdC,QAAQ,EACRC,KAAK,QAGA,cAAc;AAErB,OAAO,SAASC,SAASA,CAAA,EAAY;EACnC,OAAOF,QAAQ,CAACG,EAAE,KAAK,SAAS;AAClC;AAEA,OAAO,SAASC,eAAeA,CAACC,GAAY,EAAEC,IAAa,EAAW;EACpE,IAAI,CAACD,GAAG,IAAI,CAACC,IAAI,EAAE;IACjB,OAAO,KAAK;EACd;EACA,OAAQ,CAACD,GAAG,IAAIC,IAAI,IAAMD,GAAG,IAAI,CAACC,IAAK;AACzC;AAEA,OAAO,SAASC,UAAUA,CAACC,EAAW,EAAiB;EACrD,OAAO,OAAOA,EAAE,KAAK,UAAU;AACjC;AAEA,OAAO,SAASC,QAAQA,CAACC,GAAY,EAAiB;EACpD,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,GAAG,CAAC;AACtD;AAEA,OAAO,SAASG,WAAWA,CAACC,GAAY,EAAoB;EAC1D,OAAO,OAAOA,GAAG,KAAK,WAAW;AACnC;AAEA,OAAO,SAASC,QAAQA,CAACC,GAAY,EAAiB;EACpD,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAChC;AAEA,OAAO,SAASC,SAASA,CAACC,IAAa,EAAmB;EACxD,OAAO,OAAOA,IAAI,KAAK,SAAS;AAClC;AAEA,OAAO,SAASC,WAAWA,CACzBC,KAAc,EACsB;EACpC,OAAOL,QAAQ,CAACK,KAAK,CAAC,IAAIX,QAAQ,CAACW,KAAK,CAAC,IAAIH,SAAS,CAACG,KAAK,CAAC;AAC/D;AAUA,OAAO,SAASC,eAAeA,CAC7BC,WAAwB,EACxBC,IAAY,EACZC,SAAc,EACdC,IAAiB,EACI;EACrB,MAAMC,MAAM,GAAG3B,cAAc,CAACyB,SAAS,CAAC;EACxC,IAAI,CAACE,MAAM,EAAE;IACX,MAAM,IAAIC,KAAK,CAAC,wCAAwCC,MAAM,IAAIL,IAAI,EAAE,CAAC;EAC3E;;EAEA;EACA;EACA,OAAOD,WAAW,CAACC,IAAI,CAAC,CAACG,MAAM,EAAE,GAAGD,IAAI,CAAC;AAC3C;AAEA,OAAO,SAASI,2BAA2BA,CACzCC,QAAkD,EAClDC,UAAqC,GAAG,CAAC,CAAC,EAC1C;EACA,IAAI,CAACD,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,IAAIE,aAAa,GAAG,IAAI;EAExB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;IAC5BE,aAAa,GAAG,CAACF,QAAQ,CAAC;EAC5B,CAAC,MAAM;IACLE,aAAa,GAAGF,QAAQ;EAC1B;EAEA,MAAMK,gBAAgB,GAAGH,aAAa,CAACI,MAAM,CAAEC,KAAK,IAAK,CAAC,CAACA,KAAK,CAAC,CAAC,CAAC;EACnE,OAAOvC,KAAK,CAACwC,QAAQ,CAACC,GAAG,CAACJ,gBAAgB,EAAGE,KAAK,iBAChDvC,KAAK,CAAC0C,YAAY,CAACH,KAAK,EAAEN,UAAU,CACtC,CAAC;AACH;AAEA,OAAO,SAASU,gBAAgBA,CAACC,QAA6B,EAAU;EACtE,MAAMC,GAAG,GAAG1C,KAAK,CAAC2C,kBAAkB,CAACF,QAAQ,CAAC;EAC9C,OAAOC,GAAG,CAACE,GAAG;AAChB;AAEA,OAAO,SAASC,YAAYA,CAACC,IAAS,GAAG,EAAE,EAAE;EAC3C,OAAOC,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC;AAC7B", "ignoreList": []}