import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Platform,
  Dimensions,
  StatusBar,
  Animated,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
}

const LocationScreenMapboxWeb: React.FC = () => {
  const navigation = useNavigation();

  // État local
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [mapReady, setMapReady] = useState(true);

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    initializeScreen();
  }, []);

  const initializeScreen = async () => {
    console.log('🗺️ Initialisation Mapbox Web...');
    
    // Animations d'entrée
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Demander les permissions
    await requestLocationPermission();
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission requise',
          'L\'accès à la localisation est nécessaire pour cette fonctionnalité.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.log('Erreur permission:', error);
    }
  };

  const handleMapPress = () => {
    // Simulation d'une sélection de position avec style Mapbox
    const simulatedLocation = {
      latitude: 5.348 + (Math.random() - 0.5) * 0.1,
      longitude: -4.007 + (Math.random() - 0.5) * 0.1,
    };
    
    console.log('🗺️ Position sélectionnée sur Mapbox Web:', simulatedLocation);
    setSelectedLocation(simulatedLocation);
    getAddressFromCoordinates(simulatedLocation.latitude, simulatedLocation.longitude);
  };

  const getAddressFromCoordinates = async (latitude: number, longitude: number) => {
    try {
      const result = await Location.reverseGeocodeAsync({ latitude, longitude });
      if (result.length > 0) {
        const address = result[0];
        const formattedAddress = `${address.street || ''} ${address.name || ''}, ${address.city || ''}, ${address.country || ''}`.trim();
        console.log('📍 Adresse trouvée:', formattedAddress);
        setSelectedLocation(prev => prev ? { ...prev, address: formattedAddress } : null);
      }
    } catch (error) {
      console.log('Erreur géocodage inverse:', error);
    }
  };

  const handleCurrentLocationPress = async () => {
    setLoading(true);
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission requise',
          'L\'accès à la localisation est nécessaire pour cette fonctionnalité.',
          [{ text: 'OK' }]
        );
        return;
      }

      console.log('📍 Récupération de la position GPS...');
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const newLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      console.log('✅ Position GPS obtenue:', newLocation);
      setSelectedLocation(newLocation);

      // Obtenir l'adresse
      getAddressFromCoordinates(location.coords.latitude, location.coords.longitude);

    } catch (error) {
      console.log('❌ Erreur localisation:', error);
      Alert.alert(
        'Erreur de localisation',
        'Impossible d\'obtenir votre position actuelle. Vérifiez que le GPS est activé.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmLocation = () => {
    if (!selectedLocation) {
      Alert.alert('Aucune position sélectionnée', 'Veuillez sélectionner une position sur la carte.');
      return;
    }

    console.log('✅ Position confirmée:', selectedLocation);
    navigation.goBack();
  };

  const formatAddress = (address: string) => {
    if (address.length > 40) {
      return address.substring(0, 40) + '...';
    }
    return address;
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      {/* Header */}
      <Animated.View style={[
        styles.header,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>🗺️ Mapbox Interactive</Text>
      </Animated.View>

      {/* Map Container */}
      <Animated.View style={[
        styles.mapContainer,
        {
          opacity: fadeAnim,
        }
      ]}>
        {/* Mapbox Style Interface */}
        <TouchableOpacity style={styles.mapboxInterface} onPress={handleMapPress}>
          {/* Mapbox Background Pattern */}
          <View style={styles.mapboxBackground}>
            {/* Streets Pattern */}
            <View style={styles.streetsPattern}>
              {Array.from({ length: 12 }, (_, i) => (
                <View key={`h${i}`} style={[styles.street, styles.horizontalStreet, { top: `${i * 8.33}%` }]} />
              ))}
              {Array.from({ length: 8 }, (_, i) => (
                <View key={`v${i}`} style={[styles.street, styles.verticalStreet, { left: `${i * 12.5}%` }]} />
              ))}
            </View>

            {/* Buildings */}
            <View style={styles.buildings}>
              {Array.from({ length: 20 }, (_, i) => (
                <View
                  key={i}
                  style={[
                    styles.building,
                    {
                      left: `${Math.random() * 80 + 10}%`,
                      top: `${Math.random() * 70 + 15}%`,
                      width: Math.random() * 30 + 20,
                      height: Math.random() * 40 + 30,
                    }
                  ]}
                />
              ))}
            </View>

            {/* Parks/Green Areas */}
            <View style={styles.parks}>
              {Array.from({ length: 5 }, (_, i) => (
                <View
                  key={i}
                  style={[
                    styles.park,
                    {
                      left: `${Math.random() * 60 + 20}%`,
                      top: `${Math.random() * 50 + 25}%`,
                      width: Math.random() * 60 + 40,
                      height: Math.random() * 40 + 30,
                    }
                  ]}
                />
              ))}
            </View>
          </View>

          {/* Selected Location Marker */}
          {selectedLocation && (
            <View style={styles.selectedMarker}>
              <View style={styles.markerPin}>
                <Ionicons name="location" size={24} color="#FFFFFF" />
              </View>
              <View style={styles.markerShadow} />
            </View>
          )}

          {/* Mapbox Logo */}
          <View style={styles.mapboxLogo}>
            <Text style={styles.mapboxText}>mapbox</Text>
          </View>

          {/* Interaction Hint */}
          <View style={styles.interactionHint}>
            <Text style={styles.hintText}>🎯 Touchez pour sélectionner une position</Text>
          </View>
        </TouchableOpacity>

        {/* Current Location Button */}
        <TouchableOpacity
          style={styles.currentLocationButton}
          onPress={handleCurrentLocationPress}
          disabled={loading}
        >
          <Ionicons
            name="locate"
            size={24}
            color={loading ? "#9CA3AF" : "#0DCAA8"}
          />
        </TouchableOpacity>

        {/* Map Type Info */}
        <View style={styles.mapTypeInfo}>
          <Text style={styles.mapTypeText}>🗺️ Mapbox Style</Text>
        </View>
      </Animated.View>

      {/* Bottom Sheet */}
      <Animated.View style={[
        styles.bottomSheet,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        {/* Selected Location Info */}
        {selectedLocation && (
          <View style={styles.locationInfo}>
            <View style={styles.locationHeader}>
              <Ionicons name="location" size={24} color="#0DCAA8" />
              <View style={styles.locationDetails}>
                <Text style={styles.locationTitle}>
                  {selectedLocation.address ? formatAddress(selectedLocation.address) : 'Position sélectionnée'}
                </Text>
                <Text style={styles.locationCoordinates}>
                  📍 Lat: {selectedLocation.latitude.toFixed(6)}, Long: {selectedLocation.longitude.toFixed(6)}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Instructions */}
        {!selectedLocation && (
          <View style={styles.instructions}>
            <Text style={styles.instructionsText}>
              🎯 Touchez la carte pour sélectionner une position ou utilisez le bouton GPS
            </Text>
          </View>
        )}

        {/* Mapbox Info */}
        <View style={styles.mapboxInfo}>
          <Text style={styles.mapboxInfoText}>
            🗺️ Interface Mapbox avec style moderne et interactif. Géolocalisation précise et design professionnel.
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.confirmButton,
              !selectedLocation && styles.confirmButtonDisabled
            ]}
            onPress={handleConfirmLocation}
            disabled={!selectedLocation}
          >
            <Text style={styles.confirmButtonText}>
              {selectedLocation ? '✅ Confirmer cette position' : 'Sélectionnez une position'}
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* Bottom Indicator */}
      <View style={styles.bottomIndicator}>
        <View style={styles.indicator} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  mapboxInterface: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    position: 'relative',
    overflow: 'hidden',
  },
  mapboxBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  streetsPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  street: {
    position: 'absolute',
    backgroundColor: '#E5E7EB',
  },
  horizontalStreet: {
    height: 2,
    width: '100%',
  },
  verticalStreet: {
    width: 2,
    height: '100%',
  },
  buildings: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  building: {
    position: 'absolute',
    backgroundColor: '#D1D5DB',
    borderRadius: 2,
    shadowColor: '#000000',
    shadowOffset: {
      width: 1,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  parks: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  park: {
    position: 'absolute',
    backgroundColor: '#10B981',
    borderRadius: 8,
    opacity: 0.6,
  },
  selectedMarker: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginTop: -20,
    marginLeft: -15,
    alignItems: 'center',
    zIndex: 10,
  },
  markerPin: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#0DCAA8',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  markerShadow: {
    width: 10,
    height: 5,
    borderRadius: 5,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    marginTop: 2,
  },
  mapboxLogo: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  mapboxText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#1F2937',
    letterSpacing: 1,
  },
  interactionHint: {
    position: 'absolute',
    bottom: 60,
    left: 16,
    right: 16,
    backgroundColor: 'rgba(13, 202, 168, 0.9)',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  hintText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  currentLocationButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  mapTypeInfo: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: 'rgba(13, 202, 168, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  mapTypeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  bottomSheet: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 8,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    maxHeight: screenHeight * 0.5,
  },
  locationInfo: {
    marginBottom: 16,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  locationDetails: {
    flex: 1,
    marginLeft: 12,
  },
  locationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  locationCoordinates: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  instructions: {
    backgroundColor: '#F0FDF4',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#0DCAA8',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
  },
  instructionsText: {
    fontSize: 16,
    color: '#0DCAA8',
    textAlign: 'center',
    fontWeight: '500',
  },
  mapboxInfo: {
    backgroundColor: '#1F2937',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 20,
  },
  mapboxInfoText: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: 20,
  },
  actionButtons: {
    marginBottom: 24,
  },
  confirmButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    shadowColor: '#0DCAA8',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  confirmButtonDisabled: {
    backgroundColor: '#9CA3AF',
    shadowOpacity: 0.1,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  bottomIndicator: {
    alignItems: 'center',
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    paddingTop: 8,
    backgroundColor: '#FFFFFF',
  },
  indicator: {
    width: 134,
    height: 5,
    backgroundColor: '#000000',
    borderRadius: 3,
  },
});

export default LocationScreenMapboxWeb;
