/// <reference types="react-native/types/modules/Codegen" />
import type { TurboModule } from 'react-native/Libraries/TurboModule/RCTExport';
import { Int32 } from 'react-native/Libraries/Types/CodegenTypes';
export interface Spec extends TurboModule {
    refresh: (viewRef: Int32 | null) => Promise<Object>;
}
declare const _default: Spec;
export default _default;
//# sourceMappingURL=NativeRNMBXImageModule.d.ts.map