import * as Location from 'expo-location';
import { Alert } from 'react-native';
import { Location as LocationType, Address } from '../types';

export interface LocationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: Location.PermissionStatus;
}

export interface AddressSuggestion {
  id: string;
  description: string;
  place_id?: string;
  latitude?: number;
  longitude?: number;
  structured_formatting?: {
    main_text: string;
    secondary_text: string;
  };
}

export interface GeocodingResult {
  latitude: number;
  longitude: number;
  address: string;
  city?: string;
  country?: string;
  postalCode?: string;
  region?: string;
}

class LocationService {
  private watchId: Location.LocationSubscription | null = null;
  private lastKnownLocation: LocationType | null = null;

  /**
   * Demande les permissions de géolocalisation
   */
  async requestLocationPermission(): Promise<LocationPermissionStatus> {
    try {
      // Vérifier d'abord les permissions actuelles
      const { status: existingStatus } = await Location.getForegroundPermissionsAsync();
      
      if (existingStatus === 'granted') {
        return {
          granted: true,
          canAskAgain: true,
          status: existingStatus
        };
      }

      // Demander les permissions si pas encore accordées
      const { status, canAskAgain } = await Location.requestForegroundPermissionsAsync();
      
      return {
        granted: status === 'granted',
        canAskAgain,
        status
      };
    } catch (error) {
      console.error('Erreur demande permission localisation:', error);
      return {
        granted: false,
        canAskAgain: false,
        status: Location.PermissionStatus.DENIED
      };
    }
  }

  /**
   * Obtient la position actuelle de l'utilisateur
   */
  async getCurrentLocation(options?: {
    accuracy?: Location.Accuracy;
    timeout?: number;
    maximumAge?: number;
  }): Promise<LocationType> {
    try {
      const permission = await this.requestLocationPermission();

      if (!permission.granted) {
        throw new Error('Permission de géolocalisation refusée');
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: options?.accuracy || Location.Accuracy.Balanced,
        timeInterval: options?.timeout || 10000,
        distanceInterval: 0,
      });

      // Valider que les coordonnées sont valides
      if (!location || !location.coords ||
          typeof location.coords.latitude !== 'number' ||
          typeof location.coords.longitude !== 'number' ||
          isNaN(location.coords.latitude) ||
          isNaN(location.coords.longitude)) {
        throw new Error('Coordonnées de localisation invalides');
      }

      const result: LocationType = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        altitude: location.coords.altitude || undefined,
        accuracy: location.coords.accuracy || undefined,
        timestamp: location.timestamp,
      };

      this.lastKnownLocation = result;
      return result;
    } catch (error) {
      console.error('Erreur obtention position:', error);

      // Fallback vers la dernière position connue si elle est valide
      if (this.lastKnownLocation &&
          typeof this.lastKnownLocation.latitude === 'number' &&
          typeof this.lastKnownLocation.longitude === 'number') {
        console.log('🔄 Utilisation de la dernière position connue');
        return this.lastKnownLocation;
      }

      // Fallback vers position par défaut (Abidjan, Côte d'Ivoire)
      console.log('🌍 Utilisation de la position par défaut (Abidjan)');
      return {
        latitude: 5.3600,
        longitude: -4.0083,
        accuracy: 1000,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Surveille les changements de position
   */
  async watchLocation(
    callback: (location: LocationType) => void,
    options?: {
      accuracy?: Location.Accuracy;
      timeInterval?: number;
      distanceInterval?: number;
    }
  ): Promise<boolean> {
    try {
      const permission = await this.requestLocationPermission();
      
      if (!permission.granted) {
        return false;
      }

      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: options?.accuracy || Location.Accuracy.Balanced,
          timeInterval: options?.timeInterval || 5000,
          distanceInterval: options?.distanceInterval || 10,
        },
        (location) => {
          const result: LocationType = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            altitude: location.coords.altitude || undefined,
            accuracy: location.coords.accuracy || undefined,
            timestamp: location.timestamp,
          };
          
          this.lastKnownLocation = result;
          callback(result);
        }
      );

      return true;
    } catch (error) {
      console.error('Erreur surveillance position:', error);
      return false;
    }
  }

  /**
   * Arrête la surveillance de position
   */
  stopWatchingLocation(): void {
    if (this.watchId) {
      this.watchId.remove();
      this.watchId = null;
    }
  }

  /**
   * Géocodage inverse - obtient l'adresse à partir des coordonnées
   */
  async reverseGeocode(latitude: number, longitude: number): Promise<GeocodingResult> {
    try {
      // Valider les coordonnées d'entrée
      if (typeof latitude !== 'number' || typeof longitude !== 'number' ||
          isNaN(latitude) || isNaN(longitude) ||
          latitude < -90 || latitude > 90 ||
          longitude < -180 || longitude > 180) {
        throw new Error('Coordonnées invalides pour le géocodage inverse');
      }

      const results = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (results && results.length > 0) {
        const result = results[0];
        return {
          latitude,
          longitude,
          address: this.formatAddress(result),
          city: result.city || undefined,
          country: result.country || undefined,
          postalCode: result.postalCode || undefined,
          region: result.region || undefined,
        };
      }

      // Fallback si pas de résultat
      return {
        latitude,
        longitude,
        address: `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`,
      };
    } catch (error) {
      console.error('Erreur géocodage inverse:', error);
      return {
        latitude: latitude || 0,
        longitude: longitude || 0,
        address: `${(latitude || 0).toFixed(4)}, ${(longitude || 0).toFixed(4)}`,
      };
    }
  }

  /**
   * Géocodage - obtient les coordonnées à partir d'une adresse
   */
  async geocode(address: string): Promise<GeocodingResult[]> {
    try {
      const results = await Location.geocodeAsync(address);
      
      return results.map(result => ({
        latitude: result.latitude,
        longitude: result.longitude,
        address: address,
      }));
    } catch (error) {
      console.error('Erreur géocodage:', error);
      return [];
    }
  }

  /**
   * Calcule la distance entre deux points (en kilomètres)
   */
  calculateDistance(point1: LocationType, point2: LocationType): number {
    const R = 6371; // Rayon de la Terre en km
    const dLat = this.toRad(point2.latitude - point1.latitude);
    const dLon = this.toRad(point2.longitude - point1.longitude);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRad(point1.latitude)) * Math.cos(this.toRad(point2.latitude)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Vérifie si un point est dans un rayon donné
   */
  isWithinRadius(center: LocationType, point: LocationType, radiusKm: number): boolean {
    const distance = this.calculateDistance(center, point);
    return distance <= radiusKm;
  }

  /**
   * Obtient la dernière position connue
   */
  getLastKnownLocation(): LocationType | null {
    return this.lastKnownLocation;
  }

  /**
   * Formate une adresse à partir du résultat de géocodage
   */
  private formatAddress(result: Location.LocationGeocodedAddress): string {
    const parts = [];
    
    if (result.streetNumber) parts.push(result.streetNumber);
    if (result.street) parts.push(result.street);
    if (result.district) parts.push(result.district);
    if (result.city) parts.push(result.city);
    if (result.region) parts.push(result.region);
    if (result.country) parts.push(result.country);
    
    return parts.join(', ') || 'Adresse inconnue';
  }

  /**
   * Convertit les degrés en radians
   */
  private toRad(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Gère les erreurs de permission avec des messages utilisateur appropriés
   */
  handleLocationError(error: any): void {
    if (error.message?.includes('Permission')) {
      Alert.alert(
        'Permission requise',
        'L\'application a besoin d\'accéder à votre localisation pour vous proposer les restaurants les plus proches.',
        [
          { text: 'Annuler', style: 'cancel' },
          { text: 'Paramètres', onPress: () => Location.enableNetworkProviderAsync() }
        ]
      );
    } else {
      Alert.alert(
        'Erreur de localisation',
        'Impossible d\'obtenir votre position. Veuillez vérifier que le GPS est activé.',
        [{ text: 'OK' }]
      );
    }
  }
}

export const locationService = new LocationService();
export default locationService;
