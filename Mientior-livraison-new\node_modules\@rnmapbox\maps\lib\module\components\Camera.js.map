{"version": 3, "names": ["React", "forwardRef", "memo", "useCallback", "useEffect", "useImperativeHandle", "useMemo", "useRef", "NativeModules", "makeLatLngBounds", "makePoint", "NativeCameraView", "RNMBXCameraModule", "NativeCommands", "jsx", "_jsx", "NativeModule", "RNMBXModule", "UserTrackingMode", "nativeAnimationMode", "mode", "NativeCameraModes", "CameraModes", "Flight", "Ease", "Linear", "Move", "None", "Camera", "props", "ref", "centerCoordinate", "bounds", "heading", "pitch", "zoomLevel", "padding", "animationDuration", "animationMode", "minZoomLevel", "maxZoomLevel", "maxBounds", "followUserLocation", "followUserMode", "followZoomLevel", "follow<PERSON><PERSON>", "followHeading", "followPadding", "defaultSettings", "allowUpdates", "onUserTrackingModeChange", "nativeCamera", "commands", "current", "setNativeRef", "buildNativeStop", "stop", "ignoreFollowUserLocation", "type", "_nativeStop", "undefined", "zoom", "duration", "JSON", "stringify", "ne", "sw", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "_onUserTrackingModeChange", "event", "nativeEvent", "payload", "payloadRenamed", "nativeDefaultStop", "nativeStop", "nativeMaxBounds", "_setCamera", "config", "stops", "_stop", "_nativeStops", "call", "setCamera", "_fitBounds", "paddingConfig", "_animationDuration", "_padding", "length", "fitBounds", "_flyTo", "_centerCoordinate", "flyTo", "_moveTo", "moveTo", "_zoomTo", "_zoomLevel", "zoomTo", "RNMBXCamera", "testID", "defaultStop"], "sourceRoot": "../../../src", "sources": ["components/Camera.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,IACVC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,mBAAmB,EACnBC,OAAO,EACPC,MAAM,QACD,OAAO;AACd,SAASC,aAAa,QAAQ,cAAc;AAI5C,SAASC,gBAAgB,EAAEC,SAAS,QAAQ,mBAAmB;AAE/D,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,SAASC,cAAc,QAAwB,yBAAyB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEzE,MAAMC,YAAY,GAAGR,aAAa,CAACS,WAAW;AAE9C,WAAYC,gBAAgB,0BAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAA,OAAhBA,gBAAgB;AAAA;AAgB5B;AACA;AACA;AACA,MAAMC,mBAAmB,GACvBC,IAA0B,IACF;EACxB,MAAMC,iBAAiB,GAAGL,YAAY,CAACM,WAAW;EAElD,QAAQF,IAAI;IACV,KAAK,OAAO;MACV,OAAOC,iBAAiB,CAACE,MAAM;IACjC,KAAK,QAAQ;MACX,OAAOF,iBAAiB,CAACG,IAAI;IAC/B,KAAK,UAAU;MACb,OAAOH,iBAAiB,CAACI,MAAM;IACjC,KAAK,QAAQ;MACX,OAAOJ,iBAAiB,CAACK,IAAI;IAC/B,KAAK,MAAM;MACT,OAAOL,iBAAiB,CAACM,IAAI;IAC/B;MACE,OAAON,iBAAiB,CAACG,IAAI;EACjC;AACF,CAAC;;AAED;;AAiJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,MAAM,gBAAG1B,IAAI,cACxBD,UAAU,CACR,CAAC4B,KAAkB,EAAEC,GAAkC,KAAK;EAC1D,MAAM;IACJC,gBAAgB;IAChBC,MAAM;IACNC,OAAO;IACPC,KAAK;IACLC,SAAS;IACTC,OAAO;IACPC,iBAAiB;IACjBC,aAAa;IACbC,YAAY;IACZC,YAAY;IACZC,SAAS;IACTC,kBAAkB;IAClBC,cAAc;IACdC,eAAe;IACfC,WAAW;IACXC,aAAa;IACbC,aAAa;IACbC,eAAe;IACfC,YAAY,GAAG,IAAI;IACnBC;EACF,CAAC,GAAGrB,KAAK;EAET,MAAMsB,YAAY,GAAG5C,MAAM,CACzB,IACF,CAAqC;EAErC,MAAM6C,QAAQ,GAAG9C,OAAO,CAAC,MAAM,IAAIO,cAAc,CAACD,iBAAiB,CAAC,EAAE,EAAE,CAAC;EAEzER,SAAS,CAAC,MAAM;IACd,IAAI+C,YAAY,CAACE,OAAO,EAAE;MACxBD,QAAQ,CAACE,YAAY,CAACH,YAAY,CAACE,OAAO,CAAC;IAC7C;IACA;EACF,CAAC,EAAE,CAACD,QAAQ,EAAED,YAAY,CAACE,OAAO,CAAC,CAAC;EAEpC,MAAME,eAAe,GAAGpD,WAAW,CACjC,CACEqD,IAAgB,EAChBC,wBAAwB,GAAG,KAAK,KACJ;IAC5BD,IAAI,GAAG;MACL,GAAGA,IAAI;MACPE,IAAI,EAAE;IACR,CAAC;IAED,IAAI7B,KAAK,CAACa,kBAAkB,IAAI,CAACe,wBAAwB,EAAE;MACzD,OAAO,IAAI;IACb;IAEA,MAAME,WAA6B,GAAG,CAAC,CAAC;IAExC,IAAIH,IAAI,CAACtB,KAAK,KAAK0B,SAAS,EAAED,WAAW,CAACzB,KAAK,GAAGsB,IAAI,CAACtB,KAAK;IAC5D,IAAIsB,IAAI,CAACvB,OAAO,KAAK2B,SAAS,EAAED,WAAW,CAAC1B,OAAO,GAAGuB,IAAI,CAACvB,OAAO;IAClE,IAAIuB,IAAI,CAACrB,SAAS,KAAKyB,SAAS,EAAED,WAAW,CAACE,IAAI,GAAGL,IAAI,CAACrB,SAAS;IACnE,IAAIqB,IAAI,CAAClB,aAAa,KAAKsB,SAAS,EAClCD,WAAW,CAACvC,IAAI,GAAGD,mBAAmB,CAACqC,IAAI,CAAClB,aAAa,CAAC;IAC5D,IAAIkB,IAAI,CAACnB,iBAAiB,KAAKuB,SAAS,EACtCD,WAAW,CAACG,QAAQ,GAAGN,IAAI,CAACnB,iBAAiB;IAE/C,IAAImB,IAAI,CAACzB,gBAAgB,EAAE;MACzB4B,WAAW,CAAC5B,gBAAgB,GAAGgC,IAAI,CAACC,SAAS,CAC3CtD,SAAS,CAAC8C,IAAI,CAACzB,gBAAgB,CACjC,CAAC;IACH;IAEA,IAAIyB,IAAI,CAACxB,MAAM,IAAIwB,IAAI,CAACxB,MAAM,CAACiC,EAAE,IAAIT,IAAI,CAACxB,MAAM,CAACkC,EAAE,EAAE;MACnD,MAAM;QAAED,EAAE;QAAEC;MAAG,CAAC,GAAGV,IAAI,CAACxB,MAAM;MAC9B2B,WAAW,CAAC3B,MAAM,GAAG+B,IAAI,CAACC,SAAS,CAACvD,gBAAgB,CAACwD,EAAE,EAAEC,EAAE,CAAC,CAAC;IAC/D;IAEA,MAAMC,UAAU,GACdX,IAAI,CAACpB,OAAO,EAAE+B,UAAU,IAAIX,IAAI,CAACxB,MAAM,EAAEmC,UAAU;IACrD,IAAIA,UAAU,KAAKP,SAAS,EAAE;MAC5BD,WAAW,CAACQ,UAAU,GAAGA,UAAU;IACrC;IAEA,MAAMC,YAAY,GAChBZ,IAAI,CAACpB,OAAO,EAAEgC,YAAY,IAAIZ,IAAI,CAACxB,MAAM,EAAEoC,YAAY;IACzD,IAAIA,YAAY,KAAKR,SAAS,EAAE;MAC9BD,WAAW,CAACS,YAAY,GAAGA,YAAY;IACzC;IAEA,MAAMC,aAAa,GACjBb,IAAI,CAACpB,OAAO,EAAEiC,aAAa,IAAIb,IAAI,CAACxB,MAAM,EAAEqC,aAAa;IAC3D,IAAIA,aAAa,IAAIT,SAAS,EAAE;MAC9BD,WAAW,CAACU,aAAa,GAAGA,aAAa;IAC3C;IAEA,MAAMC,WAAW,GACfd,IAAI,CAACpB,OAAO,EAAEkC,WAAW,IAAId,IAAI,CAACxB,MAAM,EAAEsC,WAAW;IACvD,IAAIA,WAAW,KAAKV,SAAS,EAAE;MAC7BD,WAAW,CAACW,WAAW,GAAGA,WAAW;IACvC;IAEA,OAAOX,WAAW;EACpB,CAAC,EACD,CAAC9B,KAAK,CAACa,kBAAkB,CAC3B,CAAC;;EAED;EACA;EACA,MAAM6B,yBAAyB,GAAGpE,WAAW,CAEzCqE,KAMC,IACE;IACH,IAAItB,wBAAwB,EAAE;MAC5B,IAAI,CAACsB,KAAK,CAACC,WAAW,CAACC,OAAO,EAAE;QAC9B;QACAF,KAAK,CAACC,WAAW,CAACC,OAAO,GAAGF,KAAK,CAACC,WAAW,CAACE,cAAc;MAC9D;MACAzB,wBAAwB,CAACsB,KAAK,CAAC;IACjC;EACF,CAAC,EACD,CAACtB,wBAAwB,CAC3B,CAAC;EAED,MAAM0B,iBAAiB,GAAGtE,OAAO,CAAC,MAA+B;IAC/D,IAAI,CAAC0C,eAAe,EAAE;MACpB,OAAO,IAAI;IACb;IACA,OAAOO,eAAe,CAACP,eAAe,CAAC;EACzC,CAAC,EAAE,CAACA,eAAe,EAAEO,eAAe,CAAC,CAAC;EAEtC,MAAMsB,UAAU,GAAGvE,OAAO,CAAC,MAAM;IAC/B,OAAOiD,eAAe,CAAC;MACrBG,IAAI,EAAE,YAAY;MAClB3B,gBAAgB;MAChBC,MAAM;MACNC,OAAO;MACPC,KAAK;MACLC,SAAS;MACTC,OAAO;MACPC,iBAAiB;MACjBC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CACDP,gBAAgB,EAChBC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,SAAS,EACTC,OAAO,EACPC,iBAAiB,EACjBC,aAAa,EACbiB,eAAe,CAChB,CAAC;EAEF,MAAMuB,eAAe,GAAGxE,OAAO,CAAC,MAAM;IACpC,IAAI,CAACmC,SAAS,EAAEwB,EAAE,IAAI,CAACxB,SAAS,EAAEyB,EAAE,EAAE;MACpC,OAAO,IAAI;IACb;IACA,OAAOH,IAAI,CAACC,SAAS,CAACvD,gBAAgB,CAACgC,SAAS,CAACwB,EAAE,EAAExB,SAAS,CAACyB,EAAE,CAAC,CAAC;EACrE,CAAC,EAAE,CAACzB,SAAS,CAAC,CAAC;EAEf,MAAMsC,UAAkC,GAAIC,MAAM,IAAK;IACrD,IAAI,CAAC/B,YAAY,EAAE;MACjB;IACF;IAEA,IAAI,CAAC+B,MAAM,CAACtB,IAAI;MACd;MACA;MACAsB,MAAM,GAAG;QACP,GAAGA,MAAM;QACT;QACA;QACAtB,IAAI,EAAEsB,MAAM,CAACC,KAAK,GAAG,aAAa,GAAG;MACvC,CAAC;IAEH,IAAID,MAAM,CAACtB,IAAI,KAAK,aAAa,EAAE;MACjC,KAAK,MAAMwB,KAAK,IAAIF,MAAM,CAACC,KAAK,EAAE;QAChC,IAAIE,YAAgC,GAAG,EAAE;QACzC,MAAMxB,WAAW,GAAGJ,eAAe,CAAC2B,KAAK,CAAC;QAC1C,IAAIvB,WAAW,EAAE;UACfwB,YAAY,GAAG,CAAC,GAAGA,YAAY,EAAExB,WAAW,CAAC;QAC/C;QAEAP,QAAQ,CAACgC,IAAI,CAAO,kBAAkB,EAAE,CACtC;UACEH,KAAK,EAAEE;QACT,CAAC,CACF,CAAC;MACJ;IACF,CAAC,MAAM,IAAIH,MAAM,CAACtB,IAAI,KAAK,YAAY,EAAE;MACvC,MAAMC,WAAW,GAAGJ,eAAe,CAACyB,MAAM,CAAC;MAC3C,IAAIrB,WAAW,EAAE;QACfP,QAAQ,CAACgC,IAAI,CAAO,kBAAkB,EAAE,CACtCzB,WAAW,CACZ,CAAC;MACJ;IACF;EACF,CAAC;EACD,MAAM0B,SAAS,GAAGlF,WAAW,CAAC4E,UAAU,EAAE,CACxC9B,YAAY,EACZM,eAAe,EACfH,QAAQ,CACT,CAAC;EAEF,MAAMkC,UAAkC,GAAGA,CACzCrB,EAAE,EACFC,EAAE,EACFqB,aAAa,GAAG,CAAC,EACjBC,kBAAkB,GAAG,CAAC,KACnB;IACH,IAAIC,QAAQ,GAAG;MACbtB,UAAU,EAAE,CAAC;MACbE,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE,CAAC;MACdF,YAAY,EAAE;IAChB,CAAC;IAED,IAAI,OAAOmB,aAAa,KAAK,QAAQ,EAAE;MACrC,IAAIA,aAAa,CAACG,MAAM,KAAK,CAAC,EAAE;QAC9BD,QAAQ,GAAG;UACTtB,UAAU,EAAEoB,aAAa,CAAC,CAAC,CAAC;UAC5BlB,aAAa,EAAEkB,aAAa,CAAC,CAAC,CAAC;UAC/BjB,WAAW,EAAEiB,aAAa,CAAC,CAAC,CAAC;UAC7BnB,YAAY,EAAEmB,aAAa,CAAC,CAAC;QAC/B,CAAC;MACH,CAAC,MAAM,IAAIA,aAAa,CAACG,MAAM,KAAK,CAAC,EAAE;QACrCD,QAAQ,GAAG;UACTtB,UAAU,EAAEoB,aAAa,CAAC,CAAC,CAAC;UAC5BlB,aAAa,EAAEkB,aAAa,CAAC,CAAC,CAAC;UAC/BjB,WAAW,EAAEiB,aAAa,CAAC,CAAC,CAAC;UAC7BnB,YAAY,EAAEmB,aAAa,CAAC,CAAC;QAC/B,CAAC;MACH;IACF,CAAC,MAAM,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MAC5CE,QAAQ,GAAG;QACTtB,UAAU,EAAEoB,aAAa;QACzBlB,aAAa,EAAEkB,aAAa;QAC5BjB,WAAW,EAAEiB,aAAa;QAC1BnB,YAAY,EAAEmB;MAChB,CAAC;IACH;IAEAF,SAAS,CAAC;MACR3B,IAAI,EAAE,YAAY;MAClB1B,MAAM,EAAE;QACNiC,EAAE;QACFC;MACF,CAAC;MACD9B,OAAO,EAAEqD,QAAQ;MACjBpD,iBAAiB,EAAEmD,kBAAkB;MACrClD,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMqD,SAAS,GAAGxF,WAAW,CAACmF,UAAU,EAAE,CAACD,SAAS,CAAC,CAAC;EAEtD,MAAMO,MAA0B,GAAGA,CACjCC,iBAAiB,EACjBL,kBAAkB,GAAG,IAAI,KACtB;IACHH,SAAS,CAAC;MACR3B,IAAI,EAAE,YAAY;MAClB3B,gBAAgB,EAAE8D,iBAAiB;MACnCxD,iBAAiB,EAAEmD;IACrB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMM,KAAK,GAAG3F,WAAW,CAACyF,MAAM,EAAE,CAACP,SAAS,CAAC,CAAC;EAE9C,MAAMU,OAA4B,GAAGA,CACnCF,iBAAiB,EACjBL,kBAAkB,GAAG,CAAC,KACnB;IACHH,SAAS,CAAC;MACR3B,IAAI,EAAE,YAAY;MAClB3B,gBAAgB,EAAE8D,iBAAiB;MACnCxD,iBAAiB,EAAEmD,kBAAkB;MACrClD,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC;EACD,MAAM0D,MAAM,GAAG7F,WAAW,CAAC4F,OAAO,EAAE,CAACV,SAAS,CAAC,CAAC;EAEhD,MAAMY,OAA4B,GAAGA,CACnCC,UAAU,EACVV,kBAAkB,GAAG,IAAI,KACtB;IACHH,SAAS,CAAC;MACR3B,IAAI,EAAE,YAAY;MAClBvB,SAAS,EAAE+D,UAAU;MACrB7D,iBAAiB,EAAEmD,kBAAkB;MACrClD,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC;EACD,MAAM6D,MAAM,GAAGhG,WAAW,CAAC8F,OAAO,EAAE,CAACZ,SAAS,CAAC,CAAC;EAEhDhF,mBAAmB,CAACyB,GAAG,EAAE,OAAO;IAC9B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQuD,SAAS;IACT;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQM,SAAS;IACT;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQG,KAAK;IACL;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQE,MAAM;IACN;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQG;EACF,CAAC,CAAC,CAAC;EAEH,oBACEpF,IAAA,CAACqF,WAAW;IACVC,MAAM,EAAE;IACR;IAAA;IACAvE,GAAG,EAAEqB,YAAa;IAClBK,IAAI,EAAEqB,UAAW;IACjBxC,iBAAiB,EAAEA,iBAAkB;IACrCC,aAAa,EAAEA,aAAc;IAC7BgE,WAAW,EAAE1B,iBAAkB;IAC/BlC,kBAAkB,EAAEA,kBAAmB;IACvCC,cAAc,EAAEA,cAAe;IAC/BC,eAAe,EAAEA,eAAgB;IACjCC,WAAW,EAAEA,WAAY;IACzBC,aAAa,EAAEA,aAAc;IAC7BC,aAAa,EAAEA,aAAc;IAC7BR,YAAY,EAAEA,YAAa;IAC3BC,YAAY,EAAEA,YAAa;IAC3BC,SAAS,EAAEqC;IACX;IAAA;IACA5B,wBAAwB,EAAEqB;EAA0B,CACrD,CAAC;AAEN,CACF,CACF,CAAC;AAED,MAAM6B,WAAW,GAAGzF,gBAAgB", "ignoreList": []}