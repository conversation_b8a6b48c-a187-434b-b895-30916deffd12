{"version": 3, "names": ["Animated", "AnimatedWithChildren", "Object", "getPrototypeOf", "ValueXY", "DEFAULT_COORD", "DEFAULT_POINT", "type", "coordinates", "uniqueID", "AnimatedPoint", "constructor", "point", "longitude", "latitude", "Value", "_listeners", "setValue", "setOffset", "flattenOffset", "stopAnimation", "cb", "__getValue", "addListener", "id", "String", "Date", "now", "completeCB", "removeListener", "spring", "config", "parallel", "toValue", "useNativeDriver", "timing", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d"], "sourceRoot": "../../../src", "sources": ["classes/AnimatedPoint.js"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,cAAc;;AAEvC;AACA;AACA,MAAMC,oBAAoB,GAAGC,MAAM,CAACC,cAAc,CAACH,QAAQ,CAACI,OAAO,CAAC;AAEpE,MAAMC,aAAa,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5B,MAAMC,aAAa,GAAG;EAAEC,IAAI,EAAE,OAAO;EAAEC,WAAW,EAAEH;AAAc,CAAC;AAEnE,IAAII,QAAQ,GAAG,CAAC;AAEhB,OAAO,MAAMC,aAAa,SAAST,oBAAoB,CAAC;EACtDU,WAAWA,CAACC,KAAK,GAAGN,aAAa,EAAE;IACjC,KAAK,CAAC,CAAC;IAEP,IAAI,CAACO,SAAS,GAAGD,KAAK,CAACJ,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACM,QAAQ,GAAGF,KAAK,CAACJ,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;IAEzC,IAAI,EAAE,IAAI,CAACK,SAAS,YAAYb,QAAQ,CAACe,KAAK,CAAC,EAAE;MAC/C,IAAI,CAACF,SAAS,GAAG,IAAIb,QAAQ,CAACe,KAAK,CAAC,IAAI,CAACF,SAAS,CAAC;IACrD;IAEA,IAAI,EAAE,IAAI,CAACC,QAAQ,YAAYd,QAAQ,CAACe,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACD,QAAQ,GAAG,IAAId,QAAQ,CAACe,KAAK,CAAC,IAAI,CAACD,QAAQ,CAAC;IACnD;IAEA,IAAI,CAACE,UAAU,GAAG,CAAC,CAAC;EACtB;EAEAC,QAAQA,CAACL,KAAK,GAAGN,aAAa,EAAE;IAC9B,IAAI,CAACO,SAAS,CAACI,QAAQ,CAACL,KAAK,CAACJ,WAAW,CAAC,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACM,QAAQ,CAACG,QAAQ,CAACL,KAAK,CAACJ,WAAW,CAAC,CAAC,CAAC,CAAC;EAC9C;EAEAU,SAASA,CAACN,KAAK,GAAGN,aAAa,EAAE;IAC/B,IAAI,CAACO,SAAS,CAACK,SAAS,CAACN,KAAK,CAACJ,WAAW,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACM,QAAQ,CAACI,SAAS,CAACN,KAAK,CAACJ,WAAW,CAAC,CAAC,CAAC,CAAC;EAC/C;EAEAW,aAAaA,CAAA,EAAG;IACd,IAAI,CAACN,SAAS,CAACM,aAAa,CAAC,CAAC;IAC9B,IAAI,CAACL,QAAQ,CAACK,aAAa,CAAC,CAAC;EAC/B;EAEAC,aAAaA,CAACC,EAAE,EAAE;IAChB,IAAI,CAACR,SAAS,CAACO,aAAa,CAAC,CAAC;IAC9B,IAAI,CAACN,QAAQ,CAACM,aAAa,CAAC,CAAC;IAE7B,IAAI,OAAOC,EAAE,KAAK,UAAU,EAAE;MAC5BA,EAAE,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IACvB;EACF;EAEAC,WAAWA,CAACF,EAAE,EAAE;IACdZ,QAAQ,IAAI,CAAC;IACb,MAAMe,EAAE,GAAG,GAAGC,MAAM,CAAChB,QAAQ,CAAC,IAAIgB,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAAE;IAEtD,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI,OAAOP,EAAE,KAAK,UAAU,EAAE;QAC5BA,EAAE,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IAED,IAAI,CAACN,UAAU,CAACQ,EAAE,CAAC,GAAG;MACpBX,SAAS,EAAE,IAAI,CAACA,SAAS,CAACU,WAAW,CAACK,UAAU,CAAC;MACjDd,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACS,WAAW,CAACK,UAAU;IAChD,CAAC;IAED,OAAOJ,EAAE;EACX;EAEAK,cAAcA,CAACL,EAAE,EAAE;IACjB,IAAI,CAACX,SAAS,CAACgB,cAAc,CAAC,IAAI,CAACb,UAAU,CAACQ,EAAE,CAAC,CAACX,SAAS,CAAC;IAC5D,IAAI,CAACC,QAAQ,CAACe,cAAc,CAAC,IAAI,CAACb,UAAU,CAACQ,EAAE,CAAC,CAACV,QAAQ,CAAC;IAC1D,OAAO,IAAI,CAACE,UAAU,CAACQ,EAAE,CAAC;EAC5B;EAEAM,MAAMA,CAACC,MAAM,GAAG;IAAEvB,WAAW,EAAEH;EAAc,CAAC,EAAE;IAC9C,OAAOL,QAAQ,CAACgC,QAAQ,CAAC,CACvBhC,QAAQ,CAAC8B,MAAM,CAAC,IAAI,CAACjB,SAAS,EAAE;MAC9B,GAAGkB,MAAM;MACTE,OAAO,EAAEF,MAAM,CAACvB,WAAW,CAAC,CAAC,CAAC;MAC9B0B,eAAe,EAAE;IACnB,CAAC,CAAC,EACFlC,QAAQ,CAAC8B,MAAM,CAAC,IAAI,CAAChB,QAAQ,EAAE;MAC7B,GAAGiB,MAAM;MACTE,OAAO,EAAEF,MAAM,CAACvB,WAAW,CAAC,CAAC,CAAC;MAC9B0B,eAAe,EAAE;IACnB,CAAC,CAAC,CACH,CAAC;EACJ;EAEAC,MAAMA,CAACJ,MAAM,GAAG;IAAEvB,WAAW,EAAEH;EAAc,CAAC,EAAE;IAC9C,OAAOL,QAAQ,CAACgC,QAAQ,CAAC,CACvBhC,QAAQ,CAACmC,MAAM,CAAC,IAAI,CAACtB,SAAS,EAAE;MAC9B,GAAGkB,MAAM;MACTE,OAAO,EAAEF,MAAM,CAACvB,WAAW,CAAC,CAAC,CAAC;MAC9B0B,eAAe,EAAE;IACnB,CAAC,CAAC,EACFlC,QAAQ,CAACmC,MAAM,CAAC,IAAI,CAACrB,QAAQ,EAAE;MAC7B,GAAGiB,MAAM;MACTE,OAAO,EAAEF,MAAM,CAACvB,WAAW,CAAC,CAAC,CAAC;MAC9B0B,eAAe,EAAE;IACnB,CAAC,CAAC,CACH,CAAC;EACJ;EAEAZ,UAAUA,CAAA,EAAG;IACX,OAAO;MACLf,IAAI,EAAE,OAAO;MACbC,WAAW,EAAE,CAAC,IAAI,CAACK,SAAS,CAACS,UAAU,CAAC,CAAC,EAAE,IAAI,CAACR,QAAQ,CAACQ,UAAU,CAAC,CAAC;IACvE,CAAC;EACH;EAEAc,QAAQA,CAAA,EAAG;IACT,IAAI,CAACvB,SAAS,CAACwB,UAAU,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACvB,QAAQ,CAACuB,UAAU,CAAC,IAAI,CAAC;EAChC;EAEAC,QAAQA,CAAA,EAAG;IACT,IAAI,CAACzB,SAAS,CAAC0B,aAAa,CAAC,IAAI,CAAC;IAClC,IAAI,CAACzB,QAAQ,CAACyB,aAAa,CAAC,IAAI,CAAC;EACnC;AACF;AAEA,eAAe7B,aAAa", "ignoreList": []}