{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/domain/domain.mjs", "../../unenv/dist/runtime/node/domain.mjs"], "sourcesContent": ["import { createNotImplementedError } from \"../../../_internal/utils.mjs\";\nimport { EventEmitter } from \"node:events\";\nexport class Domain extends EventEmitter {\n\t__unenv__ = true;\n\tmembers = [];\n\tadd() {}\n\tenter() {}\n\texit() {}\n\tremove() {}\n\tbind(callback) {\n\t\tthrow createNotImplementedError(\"Domain.bind\");\n\t}\n\tintercept(callback) {\n\t\tthrow createNotImplementedError(\"Domain.intercept\");\n\t}\n\trun(fn, ...args) {\n\t\tthrow createNotImplementedError(\"Domain.run\");\n\t}\n}\n", "import { Domain } from \"./internal/domain/domain.mjs\";\nexport { Domain } from \"./internal/domain/domain.mjs\";\nexport const create = function() {\n\treturn new Domain();\n};\nexport const createDomain = create;\nconst _domain = create();\nexport const active = () => _domain;\nexport const _stack = [];\nexport default {\n\tDomain,\n\t_stack,\n\tactive,\n\tcreate,\n\tcreateDomain\n};\n"], "mappings": ";;;;;;AACA,SAAS,oBAAoB;AACtB,IAAM,SAAN,cAAqB,aAAa;AAAA,EACxC,YAAY;AAAA,EACZ,UAAU,CAAC;AAAA,EACX,MAAM;AAAA,EAAC;AAAA,EACP,QAAQ;AAAA,EAAC;AAAA,EACT,OAAO;AAAA,EAAC;AAAA,EACR,SAAS;AAAA,EAAC;AAAA,EACV,KAAK,UAAU;AACd,UAAM,0BAA0B,aAAa;AAAA,EAC9C;AAAA,EACA,UAAU,UAAU;AACnB,UAAM,0BAA0B,kBAAkB;AAAA,EACnD;AAAA,EACA,IAAI,OAAO,MAAM;AAChB,UAAM,0BAA0B,YAAY;AAAA,EAC7C;AACD;;;AChBO,IAAM,SAAS,WAAW;AAChC,SAAO,IAAI,OAAO;AACnB;AACO,IAAM,eAAe;AAC5B,IAAM,UAAU,OAAO;AAChB,IAAM,SAAS,MAAM;AACrB,IAAM,SAAS,CAAC;AACvB,IAAO,iBAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}