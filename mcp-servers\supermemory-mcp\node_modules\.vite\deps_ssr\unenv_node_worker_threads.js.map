{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/worker_threads/broadcast-channel.mjs", "../../unenv/dist/runtime/node/internal/worker_threads/message-port.mjs", "../../unenv/dist/runtime/node/internal/worker_threads/message-channel.mjs", "../../unenv/dist/runtime/node/internal/worker_threads/worker.mjs", "../../unenv/dist/runtime/node/worker_threads.mjs"], "sourcesContent": ["export class BroadcastChannel {\n\tname = \"\";\n\tonmessage = (message) => {};\n\tonmessageerror = (message) => {};\n\tclose() {}\n\tpostMessage(message) {}\n\tref() {\n\t\treturn this;\n\t}\n\tunref() {\n\t\treturn this;\n\t}\n}\n", "import { EventEmitter } from \"node:events\";\nexport class MessagePort extends EventEmitter {\n\tclose() {}\n\tpostMessage(value, transferList) {}\n\tref() {}\n\tunref() {}\n\tstart() {}\n\taddEventListener(type, listener) {\n\t\tthis.on(type, listener);\n\t}\n\tremoveEventListener(type, listener) {\n\t\tthis.off(type, listener);\n\t}\n\tdispatchEvent(event) {\n\t\treturn this.emit(event.type, event);\n\t}\n}\n", "import { MessagePort } from \"./message-port.mjs\";\nexport class MessageChannel {\n\tport1 = new MessagePort();\n\tport2 = new MessagePort();\n}\n", "import { EventEmitter } from \"node:events\";\nimport { Readable } from \"node:stream\";\nexport class Worker extends EventEmitter {\n\tstdin = null;\n\tstdout = new Readable();\n\tstderr = new Readable();\n\tthreadId = 0;\n\tperformance = { eventLoopUtilization: () => ({\n\t\tidle: 0,\n\t\tactive: 0,\n\t\tutilization: 0\n\t}) };\n\tpostMessage(_value, _transferList) {}\n\tpostMessageToThread(_threadId, _value, _transferList, _timeout) {\n\t\treturn Promise.resolve();\n\t}\n\tref() {}\n\tunref() {}\n\tterminate() {\n\t\treturn Promise.resolve(0);\n\t}\n\tgetHeapSnapshot() {\n\t\treturn Promise.resolve(new Readable());\n\t}\n}\n", "import { BroadcastChannel } from \"./internal/worker_threads/broadcast-channel.mjs\";\nimport { MessageChannel } from \"./internal/worker_threads/message-channel.mjs\";\nimport { MessagePort } from \"./internal/worker_threads/message-port.mjs\";\nimport { Worker } from \"./internal/worker_threads/worker.mjs\";\nimport { notImplemented } from \"../_internal/utils.mjs\";\nexport { BroadcastChannel } from \"./internal/worker_threads/broadcast-channel.mjs\";\nexport { MessageChannel } from \"./internal/worker_threads/message-channel.mjs\";\nexport { MessagePort } from \"./internal/worker_threads/message-port.mjs\";\nexport { Worker } from \"./internal/worker_threads/worker.mjs\";\nconst _environmentData = new Map();\nexport const getEnvironmentData = function getEnvironmentData(key) {\n\treturn _environmentData.get(key);\n};\nexport const setEnvironmentData = function setEnvironmentData(key, value) {\n\t_environmentData.set(key, value);\n};\nexport const isMainThread = true;\nexport const isMarkedAsUntransferable = () => false;\nexport const markAsUntransferable = function markAsUntransferable(value) {};\nexport const markAsUncloneable = () => {};\nexport const moveMessagePortToContext = () => new MessagePort();\nexport const parentPort = null;\nexport const receiveMessageOnPort = () => undefined;\nexport const SHARE_ENV = /* @__PURE__ */ Symbol.for(\"nodejs.worker_threads.SHARE_ENV\");\nexport const resourceLimits = {};\nexport const threadId = 0;\nexport const workerData = null;\nexport const postMessageToThread = /* @__PURE__ */ notImplemented(\"worker_threads.postMessageToThread\");\nexport const isInternalThread = false;\nexport default {\n\tBroadcastChannel,\n\tMessageChannel,\n\tMessagePort,\n\tWorker,\n\tSHARE_ENV,\n\tgetEnvironmentData,\n\tisMainThread,\n\tisMarkedAsUntransferable,\n\tmarkAsUntransferable,\n\tmarkAsUncloneable,\n\tmoveMessagePortToContext,\n\tparentPort,\n\treceiveMessageOnPort,\n\tresourceLimits,\n\tsetEnvironmentData,\n\tpostMessageToThread,\n\tthreadId,\n\tworkerData,\n\tisInternalThread\n};\n"], "mappings": ";;;;;;AAAO,IAAM,mBAAN,MAAuB;AAAA,EAC7B,OAAO;AAAA,EACP,YAAY,CAAC,YAAY;AAAA,EAAC;AAAA,EAC1B,iBAAiB,CAAC,YAAY;AAAA,EAAC;AAAA,EAC/B,QAAQ;AAAA,EAAC;AAAA,EACT,YAAY,SAAS;AAAA,EAAC;AAAA,EACtB,MAAM;AACL,WAAO;AAAA,EACR;AAAA,EACA,QAAQ;AACP,WAAO;AAAA,EACR;AACD;;;ACZA,SAAS,oBAAoB;AACtB,IAAM,cAAN,cAA0B,aAAa;AAAA,EAC7C,QAAQ;AAAA,EAAC;AAAA,EACT,YAAY,OAAO,cAAc;AAAA,EAAC;AAAA,EAClC,MAAM;AAAA,EAAC;AAAA,EACP,QAAQ;AAAA,EAAC;AAAA,EACT,QAAQ;AAAA,EAAC;AAAA,EACT,iBAAiB,MAAM,UAAU;AAChC,SAAK,GAAG,MAAM,QAAQ;AAAA,EACvB;AAAA,EACA,oBAAoB,MAAM,UAAU;AACnC,SAAK,IAAI,MAAM,QAAQ;AAAA,EACxB;AAAA,EACA,cAAc,OAAO;AACpB,WAAO,KAAK,KAAK,MAAM,MAAM,KAAK;AAAA,EACnC;AACD;;;ACfO,IAAM,iBAAN,MAAqB;AAAA,EAC3B,QAAQ,IAAI,YAAY;AAAA,EACxB,QAAQ,IAAI,YAAY;AACzB;;;ACJA,SAAS,gBAAAA,qBAAoB;AAC7B,SAAS,gBAAgB;AAClB,IAAM,SAAN,cAAqBA,cAAa;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS,IAAI,SAAS;AAAA,EACtB,SAAS,IAAI,SAAS;AAAA,EACtB,WAAW;AAAA,EACX,cAAc,EAAE,sBAAsB,OAAO;AAAA,IAC5C,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,aAAa;AAAA,EACd,GAAG;AAAA,EACH,YAAY,QAAQ,eAAe;AAAA,EAAC;AAAA,EACpC,oBAAoB,WAAW,QAAQ,eAAe,UAAU;AAC/D,WAAO,QAAQ,QAAQ;AAAA,EACxB;AAAA,EACA,MAAM;AAAA,EAAC;AAAA,EACP,QAAQ;AAAA,EAAC;AAAA,EACT,YAAY;AACX,WAAO,QAAQ,QAAQ,CAAC;AAAA,EACzB;AAAA,EACA,kBAAkB;AACjB,WAAO,QAAQ,QAAQ,IAAI,SAAS,CAAC;AAAA,EACtC;AACD;;;ACfA,IAAM,mBAAmB,oBAAI,IAAI;AAC1B,IAAM,qBAAqB,SAASC,oBAAmB,KAAK;AAClE,SAAO,iBAAiB,IAAI,GAAG;AAChC;AACO,IAAM,qBAAqB,SAASC,oBAAmB,KAAK,OAAO;AACzE,mBAAiB,IAAI,KAAK,KAAK;AAChC;AACO,IAAM,eAAe;AACrB,IAAM,2BAA2B,MAAM;AACvC,IAAM,uBAAuB,SAASC,sBAAqB,OAAO;AAAC;AACnE,IAAM,oBAAoB,MAAM;AAAC;AACjC,IAAM,2BAA2B,MAAM,IAAI,YAAY;AACvD,IAAM,aAAa;AACnB,IAAM,uBAAuB,MAAM;AACnC,IAAM,YAA4B,OAAO,IAAI,iCAAiC;AAC9E,IAAM,iBAAiB,CAAC;AACxB,IAAM,WAAW;AACjB,IAAM,aAAa;AACnB,IAAM,sBAAsC,eAAe,oCAAoC;AAC/F,IAAM,mBAAmB;AAChC,IAAO,yBAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": ["EventEmitter", "getEnvironmentData", "setEnvironmentData", "markAsUntransferable"]}