{"version": 3, "sources": ["../../unenv/dist/runtime/node/fs/promises.mjs"], "sourcesContent": ["import { access, appendFile, chmod, chown, copyFile, cp, glob, lchmod, lchown, link, lstat, lutimes, mkdir, mkdtemp, open, opendir, readFile, readdir, readlink, realpath, rename, rm, rmdir, stat, statfs, symlink, truncate, unlink, utimes, watch, writeFile } from \"../internal/fs/promises.mjs\";\nimport * as constants from \"../internal/fs/constants.mjs\";\nexport { constants };\nexport * from \"../internal/fs/promises.mjs\";\nexport default {\n\tconstants,\n\taccess,\n\tappendFile,\n\tchmod,\n\tchown,\n\tcopyFile,\n\tcp,\n\tglob,\n\tlchmod,\n\tlchown,\n\tlink,\n\tlstat,\n\tlutimes,\n\tmkdir,\n\tmkdtemp,\n\topen,\n\topendir,\n\treadFile,\n\treaddir,\n\treadlink,\n\trealpath,\n\trename,\n\trm,\n\trmdir,\n\tstat,\n\tstatfs,\n\tsymlink,\n\ttruncate,\n\tunlink,\n\tutimes,\n\twatch,\n\twriteFile\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAO,mBAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}