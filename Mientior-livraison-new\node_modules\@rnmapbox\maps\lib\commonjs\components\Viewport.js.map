{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_RNMBXViewportNativeComponent", "_interopRequireDefault", "_NativeRNMBXViewportModule", "_NativeCommands", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "Viewport", "exports", "memo", "forwardRef", "props", "ref", "commands", "useMemo", "NativeCommands", "RNMBXViewportModule", "nativeViewport", "useRef", "useEffect", "current", "setNativeRef", "useImperativeHandle", "getState", "console", "log", "idle", "transitionTo", "state", "transition", "onStatusChangedNative", "propsOnS<PERSON><PERSON><PERSON><PERSON><PERSON>", "onStatusChanged", "event", "nativeEvent", "payload", "undefined", "jsx", "RNMBXViewport", "hasStatus<PERSON><PERSON>ed", "NativeViewport"], "sourceRoot": "../../../src", "sources": ["components/Viewport.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAWA,IAAAC,6BAAA,GAAAC,sBAAA,CAAAF,OAAA;AAIA,IAAAG,0BAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,eAAA,GAAAJ,OAAA;AAAyD,IAAAK,WAAA,GAAAL,OAAA;AAAA,SAAAE,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,CAAAO,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAuJzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMgB,QAAQ,GAAAC,OAAA,CAAAD,QAAA,gBAAG,IAAAE,WAAI,eAC1B,IAAAC,iBAAU,EAAa,CAACC,KAAY,EAAEC,GAA4B,KAAK;EACrE,MAAMC,QAAQ,GAAG,IAAAC,cAAO,EAAC,MAAM,IAAIC,8BAAc,CAACC,kCAAmB,CAAC,EAAE,EAAE,CAAC;EAC3E,MAAMC,cAAc,GAAG,IAAAC,aAAM,EAAuB,IAAI,CAAC;EACzD,IAAAC,gBAAS,EAAC,MAAM;IACd,IAAIF,cAAc,CAACG,OAAO,EAAE;MAC1BP,QAAQ,CAACQ,YAAY,CAACJ,cAAc,CAACG,OAAO,CAAC;IAC/C;IACA;EACF,CAAC,EAAE,CAACP,QAAQ,EAAEI,cAAc,CAACG,OAAO,CAAC,CAAC;EAEtC,IAAAE,0BAAmB,EAACV,GAAG,EAAE,OAAO;IAC9BW,QAAQA,CAAA,EAAG;MACTC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,OAAOZ,QAAQ,CAACV,IAAI,CAAS,UAAU,EAAE,EAAE,CAAC;IAC9C,CAAC;IACD,MAAMuB,IAAIA,CAAA,EAAG;MACX,OAAOb,QAAQ,CAACV,IAAI,CAAO,MAAM,EAAE,EAAE,CAAC;IACxC,CAAC;IACDwB,YAAYA,CAACC,KAAK,EAAEC,UAAU,EAAE;MAC9B,OAAOhB,QAAQ,CAACV,IAAI,CAAU,cAAc,EAAE,CAACyB,KAAK,EAAEC,UAAU,CAAC,CAAC;IACpE;EACF,CAAC,CAAC,CAAC;EAEH,MAAMC,qBAAqB,GAAG,IAAAhB,cAAO,EAAC,MAAM;IAC1C,MAAMiB,oBAAoB,GAAGpB,KAAK,CAACqB,eAAe;IAClD,IAAID,oBAAoB,IAAI,IAAI,EAAE;MAChC,OAAQE,KAAyD,IAAK;QACpEF,oBAAoB,CAACE,KAAK,CAACC,WAAW,CAACC,OAAO,CAAC;MACjD,CAAC;IACH,CAAC,MAAM;MACL,OAAOC,SAAS;IAClB;EACF,CAAC,EAAE,CAACzB,KAAK,CAACqB,eAAe,CAAC,CAAC;EAE3B,oBACE,IAAA7C,WAAA,CAAAkD,GAAA,EAACC,aAAa;IAAA,GACR3B,KAAK;IACT4B,gBAAgB,EAAE5B,KAAK,CAACqB,eAAe,IAAI,IAAK;IAChDA,eAAe,EAAEF,qBAAsB;IACvClB,GAAG,EAAEK;EAAe,CACrB,CAAC;AAEN,CAAC,CACH,CAAC;AAeD,MAAMqB,aAAa,GAAGE,qCAAoC", "ignoreList": []}