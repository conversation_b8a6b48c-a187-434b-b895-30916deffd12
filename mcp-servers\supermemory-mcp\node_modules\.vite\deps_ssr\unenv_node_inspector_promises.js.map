{"version": 3, "sources": ["../../unenv/dist/runtime/node/inspector/promises.mjs"], "sourcesContent": ["import { notImplemented, notImplementedClass } from \"../../_internal/utils.mjs\";\nimport noop from \"../../mock/noop.mjs\";\nexport const console = {\n\tdebug: noop,\n\terror: noop,\n\tinfo: noop,\n\tlog: noop,\n\twarn: noop,\n\tdir: noop,\n\tdirxml: noop,\n\ttable: noop,\n\ttrace: noop,\n\tgroup: noop,\n\tgroupCollapsed: noop,\n\tgroupEnd: noop,\n\tclear: noop,\n\tcount: noop,\n\tcountReset: noop,\n\tassert: noop,\n\tprofile: noop,\n\tprofileEnd: noop,\n\ttime: noop,\n\ttimeLog: noop,\n\ttimeStamp: noop\n};\nexport const Network = /* @__PURE__ */ notImplementedClass(\"inspectorPromises.Network\");\nexport const Session = /* @__PURE__ */ notImplementedClass(\"inspectorPromises.Session\");\nexport const url = /* @__PURE__ */ notImplemented(\"inspectorPromises.url\");\nexport const waitForDebugger = /* @__PURE__ */ notImplemented(\"inspectorPromises.waitForDebugger\");\nexport const open = /* @__PURE__ */ notImplemented(\"inspectorPromises.open\");\nexport const close = /* @__PURE__ */ notImplemented(\"inspectorPromises.close\");\nexport default {\n\tclose,\n\tconsole,\n\tNetwork,\n\topen,\n\tSession,\n\turl,\n\twaitForDebugger\n};\n"], "mappings": ";;;;;;;;;;AAEO,IAAM,UAAU;AAAA,EACtB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AACZ;AACO,IAAM,UAA0B,oBAAoB,2BAA2B;AAC/E,IAAM,UAA0B,oBAAoB,2BAA2B;AAC/E,IAAM,MAAsB,eAAe,uBAAuB;AAClE,IAAM,kBAAkC,eAAe,mCAAmC;AAC1F,IAAM,OAAuB,eAAe,wBAAwB;AACpE,IAAM,QAAwB,eAAe,yBAAyB;AAC7E,IAAO,mBAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}