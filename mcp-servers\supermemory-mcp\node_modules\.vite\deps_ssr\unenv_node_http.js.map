{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/http/request.mjs", "../../unenv/dist/runtime/node/internal/http/response.mjs", "../../unenv/dist/runtime/node/internal/http/constants.mjs", "../../unenv/dist/runtime/node/http.mjs"], "sourcesContent": ["import { Socket } from \"node:net\";\nimport { Readable } from \"node:stream\";\nimport { rawHeaders } from \"../../../_internal/utils.mjs\";\nexport class IncomingMessage extends Readable {\n\t__unenv__ = {};\n\taborted = false;\n\thttpVersion = \"1.1\";\n\thttpVersionMajor = 1;\n\thttpVersionMinor = 1;\n\tcomplete = true;\n\tconnection;\n\tsocket;\n\theaders = {};\n\ttrailers = {};\n\tmethod = \"GET\";\n\turl = \"/\";\n\tstatusCode = 200;\n\tstatusMessage = \"\";\n\tclosed = false;\n\terrored = null;\n\treadable = false;\n\tconstructor(socket) {\n\t\tsuper();\n\t\tthis.socket = this.connection = socket || new Socket();\n\t}\n\tget rawHeaders() {\n\t\treturn rawHeaders(this.headers);\n\t}\n\tget rawTrailers() {\n\t\treturn [];\n\t}\n\tsetTimeout(_msecs, _callback) {\n\t\treturn this;\n\t}\n\tget headersDistinct() {\n\t\treturn _distinct(this.headers);\n\t}\n\tget trailersDistinct() {\n\t\treturn _distinct(this.trailers);\n\t}\n\t_read() {}\n}\nfunction _distinct(obj) {\n\tconst d = {};\n\tfor (const [key, value] of Object.entries(obj)) {\n\t\tif (key) {\n\t\t\td[key] = (Array.isArray(value) ? value : [value]).filter(Boolean);\n\t\t}\n\t}\n\treturn d;\n}\n", "import { Writable } from \"node:stream\";\nexport class ServerResponse extends Writable {\n\t__unenv__ = true;\n\tstatusCode = 200;\n\tstatusMessage = \"\";\n\tupgrading = false;\n\tchunkedEncoding = false;\n\tshouldKeepAlive = false;\n\tuseChunkedEncodingByDefault = false;\n\tsendDate = false;\n\tfinished = false;\n\theadersSent = false;\n\tstrictContentLength = false;\n\tconnection = null;\n\tsocket = null;\n\treq;\n\t_headers = {};\n\tconstructor(req) {\n\t\tsuper();\n\t\tthis.req = req;\n\t}\n\tassignSocket(socket) {\n\t\tsocket._httpMessage = this;\n\t\tthis.socket = socket;\n\t\tthis.connection = socket;\n\t\tthis.emit(\"socket\", socket);\n\t\tthis._flush();\n\t}\n\t_flush() {\n\t\tthis.flushHeaders();\n\t}\n\tdetachSocket(_socket) {}\n\twriteContinue(_callback) {}\n\twriteHead(statusCode, arg1, arg2) {\n\t\tif (statusCode) {\n\t\t\tthis.statusCode = statusCode;\n\t\t}\n\t\tif (typeof arg1 === \"string\") {\n\t\t\tthis.statusMessage = arg1;\n\t\t\targ1 = undefined;\n\t\t}\n\t\tconst headers = arg2 || arg1;\n\t\tif (headers) {\n\t\t\tif (Array.isArray(headers)) {} else {\n\t\t\t\tfor (const key in headers) {\n\t\t\t\t\tthis.setHeader(key, headers[key]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tthis.headersSent = true;\n\t\treturn this;\n\t}\n\twriteProcessing() {}\n\tsetTimeout(_msecs, _callback) {\n\t\treturn this;\n\t}\n\tappendHeader(name, value) {\n\t\tname = name.toLowerCase();\n\t\tconst current = this._headers[name];\n\t\tconst all = [...Array.isArray(current) ? current : [current], ...Array.isArray(value) ? value : [value]].filter(Boolean);\n\t\tthis._headers[name] = all.length > 1 ? all : all[0];\n\t\treturn this;\n\t}\n\tsetHeader(name, value) {\n\t\tthis._headers[name.toLowerCase()] = Array.isArray(value) ? [...value] : value;\n\t\treturn this;\n\t}\n\tsetHeaders(headers) {\n\t\tfor (const [key, value] of headers.entries()) {\n\t\t\tthis.setHeader(key, value);\n\t\t}\n\t\treturn this;\n\t}\n\tgetHeader(name) {\n\t\treturn this._headers[name.toLowerCase()];\n\t}\n\tgetHeaders() {\n\t\treturn this._headers;\n\t}\n\tgetHeaderNames() {\n\t\treturn Object.keys(this._headers);\n\t}\n\thasHeader(name) {\n\t\treturn name.toLowerCase() in this._headers;\n\t}\n\tremoveHeader(name) {\n\t\tdelete this._headers[name.toLowerCase()];\n\t}\n\taddTrailers(_headers) {}\n\tflushHeaders() {}\n\twriteEarlyHints(_headers, cb) {\n\t\tif (typeof cb === \"function\") {\n\t\t\tcb();\n\t\t}\n\t}\n}\n", "export const METHODS = [\n\t\"ACL\",\n\t\"BIND\",\n\t\"CHECKOUT\",\n\t\"CONNECT\",\n\t\"COPY\",\n\t\"DELETE\",\n\t\"GET\",\n\t\"HEAD\",\n\t\"LINK\",\n\t\"LOCK\",\n\t\"M-SEARCH\",\n\t\"<PERSON>R<PERSON>\",\n\t\"<PERSON><PERSON><PERSON>IVI<PERSON>\",\n\t\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n\t\"<PERSON><PERSON><PERSON><PERSON>\",\n\t\"MOVE\",\n\t\"NOTIFY\",\n\t\"OPTIONS\",\n\t\"PATCH\",\n\t\"POST\",\n\t\"PRI\",\n\t\"PROPFI<PERSON>\",\n\t\"PROPPATCH\",\n\t\"PURGE\",\n\t\"PUT\",\n\t\"REBIND\",\n\t\"REPORT\",\n\t\"SEARCH\",\n\t\"SOURCE\",\n\t\"SUBSCRIBE\",\n\t\"TRACE\",\n\t\"UNBIND\",\n\t\"UNLINK\",\n\t\"UNLOCK\",\n\t\"UNSUBSCRIBE\"\n];\nexport const STATUS_CODES = {\n\t100: \"Continue\",\n\t101: \"Switching Protocols\",\n\t102: \"Processing\",\n\t103: \"Early Hints\",\n\t200: \"OK\",\n\t201: \"Created\",\n\t202: \"Accepted\",\n\t203: \"Non-Authoritative Information\",\n\t204: \"No Content\",\n\t205: \"Reset Content\",\n\t206: \"Partial Content\",\n\t207: \"Multi-Status\",\n\t208: \"Already Reported\",\n\t226: \"IM Used\",\n\t300: \"Multiple Choices\",\n\t301: \"Moved Permanently\",\n\t302: \"Found\",\n\t303: \"See Other\",\n\t304: \"Not Modified\",\n\t305: \"Use Proxy\",\n\t307: \"Temporary Redirect\",\n\t308: \"Permanent Redirect\",\n\t400: \"Bad Request\",\n\t401: \"Unauthorized\",\n\t402: \"Payment Required\",\n\t403: \"Forbidden\",\n\t404: \"Not Found\",\n\t405: \"Method Not Allowed\",\n\t406: \"Not Acceptable\",\n\t407: \"Proxy Authentication Required\",\n\t408: \"Request Timeout\",\n\t409: \"Conflict\",\n\t410: \"Gone\",\n\t411: \"Length Required\",\n\t412: \"Precondition Failed\",\n\t413: \"Payload Too Large\",\n\t414: \"URI Too Long\",\n\t415: \"Unsupported Media Type\",\n\t416: \"Range Not Satisfiable\",\n\t417: \"Expectation Failed\",\n\t418: \"I'm a Teapot\",\n\t421: \"Misdirected Request\",\n\t422: \"Unprocessable Entity\",\n\t423: \"Locked\",\n\t424: \"Failed Dependency\",\n\t425: \"Too Early\",\n\t426: \"Upgrade Required\",\n\t428: \"Precondition Required\",\n\t429: \"Too Many Requests\",\n\t431: \"Request Header Fields Too Large\",\n\t451: \"Unavailable For Legal Reasons\",\n\t500: \"Internal Server Error\",\n\t501: \"Not Implemented\",\n\t502: \"Bad Gateway\",\n\t503: \"Service Unavailable\",\n\t504: \"Gateway Timeout\",\n\t505: \"HTTP Version Not Supported\",\n\t506: \"Variant Also Negotiates\",\n\t507: \"Insufficient Storage\",\n\t508: \"Loop Detected\",\n\t509: \"Bandwidth Limit Exceeded\",\n\t510: \"Not Extended\",\n\t511: \"Network Authentication Required\"\n};\nexport const maxHeaderSize = 16384;\n", "import { notImplemented, notImplementedClass } from \"../_internal/utils.mjs\";\nimport { IncomingMessage } from \"./internal/http/request.mjs\";\nimport { ServerResponse } from \"./internal/http/response.mjs\";\nimport { Agent } from \"./internal/http/agent.mjs\";\nimport { METHODS, STATUS_CODES, maxHeaderSize } from \"./internal/http/constants.mjs\";\nexport { METHODS, STATUS_CODES, maxHeaderSize };\nexport * from \"./internal/http/request.mjs\";\nexport * from \"./internal/http/response.mjs\";\nexport { Agent } from \"./internal/http/agent.mjs\";\nexport const createServer = /* @__PURE__ */ notImplemented(\"http.createServer\");\nexport const request = /* @__PURE__ */ notImplemented(\"http.request\");\nexport const get = /* @__PURE__ */ notImplemented(\"http.get\");\nexport const Server = /* @__PURE__ */ notImplementedClass(\"http.Server\");\nexport const OutgoingMessage = /* @__PURE__ */ notImplementedClass(\"http.OutgoingMessage\");\nexport const ClientRequest = /* @__PURE__ */ notImplementedClass(\"http.ClientRequest\");\nexport const globalAgent = new Agent();\nexport const validateHeaderName = /* @__PURE__ */ notImplemented(\"http.validateHeaderName\");\nexport const validateHeaderValue = /* @__PURE__ */ notImplemented(\"http.validateHeaderValue\");\nexport const setMaxIdleHTTPParsers = /* @__PURE__ */ notImplemented(\"http.setMaxIdleHTTPParsers\");\nexport const _connectionListener = /* @__PURE__ */ notImplemented(\"http._connectionListener\");\nexport const WebSocket = globalThis.WebSocket || /* @__PURE__ */ notImplementedClass(\"WebSocket\");\nexport const CloseEvent = globalThis.CloseEvent || /* @__PURE__ */ notImplementedClass(\"CloseEvent\");\nexport const MessageEvent = globalThis.MessageEvent || /* @__PURE__ */ notImplementedClass(\"MessageEvent\");\nexport default {\n\tMETHODS,\n\tSTATUS_CODES,\n\tmaxHeaderSize,\n\tIncomingMessage,\n\tServerResponse,\n\tWebSocket,\n\tCloseEvent,\n\tMessageEvent,\n\tcreateServer,\n\trequest,\n\tget,\n\tServer,\n\tOutgoingMessage,\n\tClientRequest,\n\tAgent,\n\tglobalAgent,\n\tvalidateHeaderName,\n\tvalidateHeaderValue,\n\tsetMaxIdleHTTPParsers,\n\t_connectionListener\n};\n"], "mappings": ";;;;;;;;;;;AAAA,SAAS,cAAc;AACvB,SAAS,gBAAgB;AAElB,IAAM,kBAAN,cAA8B,SAAS;AAAA,EAC7C,YAAY,CAAC;AAAA,EACb,UAAU;AAAA,EACV,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA,UAAU,CAAC;AAAA,EACX,WAAW,CAAC;AAAA,EACZ,SAAS;AAAA,EACT,MAAM;AAAA,EACN,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY,QAAQ;AACnB,UAAM;AACN,SAAK,SAAS,KAAK,aAAa,UAAU,IAAI,OAAO;AAAA,EACtD;AAAA,EACA,IAAI,aAAa;AAChB,WAAO,WAAW,KAAK,OAAO;AAAA,EAC/B;AAAA,EACA,IAAI,cAAc;AACjB,WAAO,CAAC;AAAA,EACT;AAAA,EACA,WAAW,QAAQ,WAAW;AAC7B,WAAO;AAAA,EACR;AAAA,EACA,IAAI,kBAAkB;AACrB,WAAO,UAAU,KAAK,OAAO;AAAA,EAC9B;AAAA,EACA,IAAI,mBAAmB;AACtB,WAAO,UAAU,KAAK,QAAQ;AAAA,EAC/B;AAAA,EACA,QAAQ;AAAA,EAAC;AACV;AACA,SAAS,UAAU,KAAK;AACvB,QAAM,IAAI,CAAC;AACX,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG,GAAG;AAC/C,QAAI,KAAK;AACR,QAAE,GAAG,KAAK,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,GAAG,OAAO,OAAO;AAAA,IACjE;AAAA,EACD;AACA,SAAO;AACR;;;AClDA,SAAS,gBAAgB;AAClB,IAAM,iBAAN,cAA6B,SAAS;AAAA,EAC5C,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAAA,EACd,sBAAsB;AAAA,EACtB,aAAa;AAAA,EACb,SAAS;AAAA,EACT;AAAA,EACA,WAAW,CAAC;AAAA,EACZ,YAAY,KAAK;AAChB,UAAM;AACN,SAAK,MAAM;AAAA,EACZ;AAAA,EACA,aAAa,QAAQ;AACpB,WAAO,eAAe;AACtB,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,KAAK,UAAU,MAAM;AAC1B,SAAK,OAAO;AAAA,EACb;AAAA,EACA,SAAS;AACR,SAAK,aAAa;AAAA,EACnB;AAAA,EACA,aAAa,SAAS;AAAA,EAAC;AAAA,EACvB,cAAc,WAAW;AAAA,EAAC;AAAA,EAC1B,UAAU,YAAY,MAAM,MAAM;AACjC,QAAI,YAAY;AACf,WAAK,aAAa;AAAA,IACnB;AACA,QAAI,OAAO,SAAS,UAAU;AAC7B,WAAK,gBAAgB;AACrB,aAAO;AAAA,IACR;AACA,UAAM,UAAU,QAAQ;AACxB,QAAI,SAAS;AACZ,UAAI,MAAM,QAAQ,OAAO,GAAG;AAAA,MAAC,OAAO;AACnC,mBAAW,OAAO,SAAS;AAC1B,eAAK,UAAU,KAAK,QAAQ,GAAG,CAAC;AAAA,QACjC;AAAA,MACD;AAAA,IACD;AACA,SAAK,cAAc;AACnB,WAAO;AAAA,EACR;AAAA,EACA,kBAAkB;AAAA,EAAC;AAAA,EACnB,WAAW,QAAQ,WAAW;AAC7B,WAAO;AAAA,EACR;AAAA,EACA,aAAa,MAAM,OAAO;AACzB,WAAO,KAAK,YAAY;AACxB,UAAM,UAAU,KAAK,SAAS,IAAI;AAClC,UAAM,MAAM,CAAC,GAAG,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO,GAAG,GAAG,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,OAAO;AACvH,SAAK,SAAS,IAAI,IAAI,IAAI,SAAS,IAAI,MAAM,IAAI,CAAC;AAClD,WAAO;AAAA,EACR;AAAA,EACA,UAAU,MAAM,OAAO;AACtB,SAAK,SAAS,KAAK,YAAY,CAAC,IAAI,MAAM,QAAQ,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI;AACxE,WAAO;AAAA,EACR;AAAA,EACA,WAAW,SAAS;AACnB,eAAW,CAAC,KAAK,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAC7C,WAAK,UAAU,KAAK,KAAK;AAAA,IAC1B;AACA,WAAO;AAAA,EACR;AAAA,EACA,UAAU,MAAM;AACf,WAAO,KAAK,SAAS,KAAK,YAAY,CAAC;AAAA,EACxC;AAAA,EACA,aAAa;AACZ,WAAO,KAAK;AAAA,EACb;AAAA,EACA,iBAAiB;AAChB,WAAO,OAAO,KAAK,KAAK,QAAQ;AAAA,EACjC;AAAA,EACA,UAAU,MAAM;AACf,WAAO,KAAK,YAAY,KAAK,KAAK;AAAA,EACnC;AAAA,EACA,aAAa,MAAM;AAClB,WAAO,KAAK,SAAS,KAAK,YAAY,CAAC;AAAA,EACxC;AAAA,EACA,YAAY,UAAU;AAAA,EAAC;AAAA,EACvB,eAAe;AAAA,EAAC;AAAA,EAChB,gBAAgB,UAAU,IAAI;AAC7B,QAAI,OAAO,OAAO,YAAY;AAC7B,SAAG;AAAA,IACJ;AAAA,EACD;AACD;;;AC/FO,IAAM,UAAU;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AACO,IAAM,eAAe;AAAA,EAC3B,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACN;AACO,IAAM,gBAAgB;;;AC7FtB,IAAM,eAA+B,eAAe,mBAAmB;AACvE,IAAM,UAA0B,eAAe,cAAc;AAC7D,IAAM,MAAsB,eAAe,UAAU;AACrD,IAAM,SAAyB,oBAAoB,aAAa;AAChE,IAAM,kBAAkC,oBAAoB,sBAAsB;AAClF,IAAM,gBAAgC,oBAAoB,oBAAoB;AAC9E,IAAM,cAAc,IAAI,MAAM;AAC9B,IAAM,qBAAqC,eAAe,yBAAyB;AACnF,IAAM,sBAAsC,eAAe,0BAA0B;AACrF,IAAM,wBAAwC,eAAe,4BAA4B;AACzF,IAAM,sBAAsC,eAAe,0BAA0B;AACrF,IAAM,YAAY,WAAW,aAA6B,oBAAoB,WAAW;AACzF,IAAM,aAAa,WAAW,cAA8B,oBAAoB,YAAY;AAC5F,IAAM,eAAe,WAAW,gBAAgC,oBAAoB,cAAc;AACzG,IAAO,eAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}