{"version": 3, "names": ["React", "NativeModules", "RNMBXSkyLayerNativeComponent", "AbstractLayer", "jsx", "_jsx", "Mapbox", "RNMBXModule", "SkyLayer", "defaultProps", "sourceID", "StyleSource", "DefaultSourceID", "render", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseProps"], "sourceRoot": "../../../src", "sources": ["components/SkyLayer.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,cAAc;AAI5C,OAAOC,4BAA4B,MAAM,uCAAuC;AAEhF,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE5C,MAAMC,MAAM,GAAGL,aAAa,CAACM,WAAW;AAgDxC;AACA;AACA;AACA,MAAMC,QAAQ,SAASL,aAAa,CAAyB;EAC3D,OAAOM,YAAY,GAAG;IACpBC,QAAQ,EAAEJ,MAAM,CAACK,WAAW,CAACC;EAC/B,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,oBACER,IAAA,CAACH;IACC;IAAA;MACAY,GAAG,EAAE,IAAI,CAACC,cAAe;MAAA,GACrB,IAAI,CAACC;IAAS,CACnB,CAAC;EAEN;AACF;AAEA,eAAeR,QAAQ", "ignoreList": []}