import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
  Dimensions,
  Animated,
  StatusBar,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import { useLocation } from '../../hooks/useLocation';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
  accuracy?: number;
}

interface RecentLocation {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
}

const LocationScreen: React.FC = () => {
  const navigation = useNavigation();
  const { location, loading, error, hasPermission, requestPermission, getCurrentLocation } = useLocation();

  // État local
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [mapRegion, setMapRegion] = useState({
    latitude: 5.348, // Côte d'Ivoire par défaut
    longitude: -4.007,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  const [isMapReady, setIsMapReady] = useState(false);
  const [showRecentLocations, setShowRecentLocations] = useState(true);

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;

  // Références
  const mapRef = useRef<MapView>(null);
  const searchInputRef = useRef<TextInput>(null);

  // Lieux récents simulés
  const [recentLocations] = useState<RecentLocation[]>([
    {
      id: '1',
      name: 'Marché Dantokpa',
      address: 'Cotonou, Bénin',
      latitude: 6.3703,
      longitude: 2.3912,
    },
    {
      id: '2',
      name: 'Restaurant Le Bénin',
      address: 'Porto-Novo, Bénin',
      latitude: 6.4969,
      longitude: 2.6283,
    },
  ]);

  useEffect(() => {
    initializeScreen();
  }, []);

  useEffect(() => {
    if (location) {
      const newRegion = {
        latitude: location.latitude,
        longitude: location.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
      setMapRegion(newRegion);
      setSelectedLocation({
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy || undefined,
      });

      // Obtenir l'adresse de la position
      getAddressFromCoordinates(location.latitude, location.longitude);
    }
  }, [location]);

  const initializeScreen = async () => {
    // Animations d'entrée
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Demander les permissions si nécessaire
    if (!hasPermission) {
      await requestPermission();
    }
  };

  const getAddressFromCoordinates = async (latitude: number, longitude: number) => {
    try {
      const result = await Location.reverseGeocodeAsync({ latitude, longitude });
      if (result.length > 0) {
        const address = result[0];
        const formattedAddress = `${address.street || ''} ${address.name || ''}, ${address.city || ''}, ${address.country || ''}`.trim();
        setSelectedLocation(prev => prev ? { ...prev, address: formattedAddress } : null);
      }
    } catch (error) {
      console.log('Erreur géocodage inverse:', error);
    }
  };

  const handleMapPress = (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    setSelectedLocation({ latitude, longitude });
    getAddressFromCoordinates(latitude, longitude);
    setShowRecentLocations(false);
  };

  const handleCurrentLocationPress = async () => {
    if (!hasPermission) {
      const granted = await requestPermission();
      if (!granted) {
        Alert.alert(
          'Permission requise',
          'L\'accès à la localisation est nécessaire pour cette fonctionnalité.',
          [{ text: 'OK' }]
        );
        return;
      }
    }

    try {
      await getCurrentLocation();
      setShowRecentLocations(false);
    } catch (error) {
      Alert.alert(
        'Erreur de localisation',
        'Impossible d\'obtenir votre position actuelle. Vérifiez que le GPS est activé.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleConfirmLocation = () => {
    if (!selectedLocation) {
      Alert.alert('Aucune position sélectionnée', 'Veuillez sélectionner une position sur la carte.');
      return;
    }

    // Animation du bouton
    Animated.sequence([
      Animated.timing(buttonScaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonScaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // Sauvegarder la position et naviguer
    console.log('Position confirmée:', selectedLocation);
    
    // Navigation vers l'écran suivant
    navigation.goBack(); // Retourner à l'écran précédent (HomeTabs)
  };

  const handleManualSelection = () => {
    setShowRecentLocations(false);
    Alert.alert(
      'Sélection manuelle',
      'Touchez la carte pour sélectionner votre position exacte.',
      [{ text: 'Compris' }]
    );
  };

  const handleRecentLocationPress = (recentLocation: RecentLocation) => {
    const newRegion = {
      latitude: recentLocation.latitude,
      longitude: recentLocation.longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };
    setMapRegion(newRegion);
    setSelectedLocation({
      latitude: recentLocation.latitude,
      longitude: recentLocation.longitude,
      address: recentLocation.address,
    });
    setShowRecentLocations(false);
    
    // Animer vers la nouvelle position
    if (mapRef.current) {
      mapRef.current.animateToRegion(newRegion, 1000);
    }
  };

  const formatAddress = (address: string) => {
    if (address.length > 30) {
      return address.substring(0, 30) + '...';
    }
    return address;
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      {/* Header */}
      <Animated.View style={[
        styles.header,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Votre localisation</Text>
      </Animated.View>

      {/* Search Bar */}
      <Animated.View style={[
        styles.searchContainer,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        <View style={styles.searchInputWrapper}>
          <Ionicons name="search" size={20} color="#9CA3AF" style={styles.searchIcon} />
          <TextInput
            ref={searchInputRef}
            style={styles.searchInput}
            placeholder="Rechercher une adresse"
            placeholderTextColor="#9CA3AF"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </Animated.View>

      {/* Map Container */}
      <Animated.View style={[
        styles.mapContainer,
        {
          opacity: fadeAnim,
        }
      ]}>
        <MapView
          ref={mapRef}
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          region={mapRegion}
          onPress={handleMapPress}
          onMapReady={() => setIsMapReady(true)}
          showsUserLocation={false}
          showsMyLocationButton={false}
          scrollEnabled={true}
          zoomEnabled={true}
          rotateEnabled={true}
          pitchEnabled={true}
          loadingEnabled={true}
          loadingIndicatorColor="#0DCAA8"
          mapType="standard"
        >
          {selectedLocation && (
            <Marker
              coordinate={{
                latitude: selectedLocation.latitude,
                longitude: selectedLocation.longitude,
              }}
              title="Position sélectionnée"
              description={selectedLocation.address || "Votre position"}
              pinColor="#0DCAA8"
            />
          )}
        </MapView>

        {/* Current Location Button */}
        <TouchableOpacity
          style={styles.currentLocationButton}
          onPress={handleCurrentLocationPress}
          disabled={loading}
        >
          <Ionicons
            name="locate"
            size={24}
            color={loading ? "#9CA3AF" : "#0DCAA8"}
          />
        </TouchableOpacity>
      </Animated.View>

      {/* Bottom Sheet */}
      <Animated.View style={[
        styles.bottomSheet,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        {/* Selected Location Info */}
        {selectedLocation && (
          <View style={styles.locationInfo}>
            <View style={styles.locationHeader}>
              <Ionicons name="location" size={24} color="#0DCAA8" />
              <View style={styles.locationDetails}>
                <Text style={styles.locationTitle}>
                  {selectedLocation.address ? formatAddress(selectedLocation.address) : 'QPPH+6J5, Yamoussoukro, Côte d\'Ivoire'}
                </Text>
                <Text style={styles.locationCoordinates}>
                  Lat: {selectedLocation.latitude.toFixed(4)}, Long: {selectedLocation.longitude.toFixed(4)}
                </Text>
                {selectedLocation.accuracy && (
                  <Text style={styles.locationAccuracy}>
                    Précision: ±{Math.round(selectedLocation.accuracy)}m
                  </Text>
                )}
              </View>
            </View>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Animated.View style={{ transform: [{ scale: buttonScaleAnim }] }}>
            <TouchableOpacity
              style={[
                styles.confirmButton,
                !selectedLocation && styles.confirmButtonDisabled
              ]}
              onPress={handleConfirmLocation}
              disabled={!selectedLocation}
            >
              <Text style={styles.confirmButtonText}>Confirmer ma position</Text>
            </TouchableOpacity>
          </Animated.View>

          <TouchableOpacity
            style={styles.manualButton}
            onPress={handleManualSelection}
          >
            <Text style={styles.manualButtonText}>Sélectionner manuellement</Text>
          </TouchableOpacity>
        </View>

        {/* Recent Locations */}
        {showRecentLocations && recentLocations.length > 0 && (
          <View style={styles.recentLocationsContainer}>
            <Text style={styles.recentLocationsTitle}>Lieux récents</Text>
            <ScrollView
              style={styles.recentLocationsList}
              showsVerticalScrollIndicator={false}
            >
              {recentLocations.map((location) => (
                <TouchableOpacity
                  key={location.id}
                  style={styles.recentLocationItem}
                  onPress={() => handleRecentLocationPress(location)}
                >
                  <View style={styles.recentLocationIcon}>
                    <Ionicons name="time-outline" size={20} color="#6B7280" />
                  </View>
                  <View style={styles.recentLocationInfo}>
                    <Text style={styles.recentLocationName}>{location.name}</Text>
                    <Text style={styles.recentLocationAddress}>{location.address}</Text>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}
      </Animated.View>

      {/* Bottom Indicator */}
      <View style={styles.bottomIndicator}>
        <View style={styles.indicator} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  searchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: 12,
    minHeight: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    paddingVertical: 12,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  currentLocationButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  bottomSheet: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 8,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    maxHeight: screenHeight * 0.5,
  },
  locationInfo: {
    marginBottom: 20,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  locationDetails: {
    flex: 1,
    marginLeft: 12,
  },
  locationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  locationCoordinates: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  locationAccuracy: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  actionButtons: {
    marginBottom: 24,
  },
  confirmButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#0DCAA8',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  confirmButtonDisabled: {
    backgroundColor: '#9CA3AF',
    shadowOpacity: 0.1,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  manualButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  manualButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0DCAA8',
  },
  recentLocationsContainer: {
    flex: 1,
  },
  recentLocationsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  recentLocationsList: {
    flex: 1,
  },
  recentLocationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  recentLocationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F9FAFB',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  recentLocationInfo: {
    flex: 1,
  },
  recentLocationName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 2,
  },
  recentLocationAddress: {
    fontSize: 14,
    color: '#6B7280',
  },
  bottomIndicator: {
    alignItems: 'center',
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    paddingTop: 8,
    backgroundColor: '#FFFFFF',
  },
  indicator: {
    width: 134,
    height: 5,
    backgroundColor: '#000000',
    borderRadius: 3,
  },
});

export default LocationScreen;
