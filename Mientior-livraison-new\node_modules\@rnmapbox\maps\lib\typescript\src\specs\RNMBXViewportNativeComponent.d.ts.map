{"version": 3, "file": "RNMBXViewportNativeComponent.d.ts", "sourceRoot": "", "sources": ["../../../../src/specs/RNMBXViewportNativeComponent.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAE7D,OAAO,EAAE,kBAAkB,EAAE,MAAM,2CAA2C,CAAC;AAE/E,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAGlD,KAAK,YAAY,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AAEtC,MAAM,MAAM,uBAAuB,GAAG;IACpC,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACvB,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,EAAE,QAAQ,GAAG,SAAS,GAAG,MAAM,GAAG,MAAM,CAAC;IACjD,OAAO,CAAC,EACJ;QACE,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,KAAK,CAAC,EAAE,MAAM,CAAC;KAChB,GACD,MAAM,CAAC;CACZ,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAC3B;IACE,IAAI,EAAE,YAAY,CAAC;IACnB,OAAO,CAAC,EAAE,uBAAuB,CAAC;CACnC,GACD;IACE,IAAI,EAAE,UAAU,CAAC;CAClB,CAAC;AAEN,KAAK,cAAc,GACf;IACE,IAAI,EAAE,MAAM,CAAC;CACd,GACD;IACE,IAAI,EAAE,YAAY,CAAC;IACnB,OAAO,EAAE,mBAAmB,CAAC;IAC7B,UAAU,EAAE,kBAAkB,CAAC;CAChC,CAAC;AAEN,KAAK,kBAAkB,GACnB;IACE,IAAI,EAAE,WAAW,CAAC;CACnB,GACD;IACE,IAAI,EAAE,SAAS,CAAC;IAChB,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB,CAAC;AAEN,KAAK,0BAA0B,GAC3B,mBAAmB,GACnB,qBAAqB,GACrB,eAAe,GACf,iBAAiB,CAAC;AA6BtB,MAAM,MAAM,2BAA2B,GAAG;IACxC,IAAI,EAAE,cAAc,CAAC;IACrB,EAAE,EAAE,cAAc,CAAC;IACnB,MAAM,EAAE,0BAA0B,CAAC;CACpC,CAAC;AAEF,KAAK,wBAAwB,GAAG;IAC9B,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;CAGjB,CAAC;AAEF,MAAM,MAAM,4BAA4B,GAAG;IACzC,IAAI,EAAE,eAAe,CAAC;IACtB,OAAO,EAAE,WAAW,CAAC,2BAA2B,CAAC,CAAC;CACnD,CAAC;AAEF,MAAM,WAAW,WAAY,SAAQ,SAAS;IAC5C,oCAAoC,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IAC7D,gBAAgB,EAAE,OAAO,CAAC;IAC1B,eAAe,CAAC,EAAE,kBAAkB,CAAC,wBAAwB,CAAC,CAAC;CAChE;;AAED,wBAEgC;AAEhC,MAAM,MAAM,kBAAkB,GAAG,aAAa,CAC5C,IAAI,CAAC,WAAW,EAAE,iBAAiB,CAAC,GAAG;IACrC,eAAe,CAAC,EAAE,kBAAkB,CAAC,4BAA4B,CAAC,CAAC;CACpE,CACF,CAAC"}