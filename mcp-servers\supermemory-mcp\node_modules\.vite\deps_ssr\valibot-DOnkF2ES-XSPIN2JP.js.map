{"version": 3, "sources": ["../../valibot/dist/index.js", "../../@valibot/to-json-schema/dist/index.js", "../../@standard-community/standard-json/dist/valibot-DOnkF2ES.js"], "sourcesContent": ["// src/storages/globalConfig/globalConfig.ts\nvar store;\nfunction setGlobalConfig(config2) {\n  store = { ...store, ...config2 };\n}\n// @__NO_SIDE_EFFECTS__\nfunction getGlobalConfig(config2) {\n  return {\n    lang: config2?.lang ?? store?.lang,\n    message: config2?.message,\n    abortEarly: config2?.abortEarly ?? store?.abortEarly,\n    abortPipeEarly: config2?.abortPipeEarly ?? store?.abortPipeEarly\n  };\n}\nfunction deleteGlobalConfig() {\n  store = void 0;\n}\n\n// src/storages/globalMessage/globalMessage.ts\nvar store2;\nfunction setGlobalMessage(message2, lang) {\n  if (!store2) store2 = /* @__PURE__ */ new Map();\n  store2.set(lang, message2);\n}\n// @__NO_SIDE_EFFECTS__\nfunction getGlobalMessage(lang) {\n  return store2?.get(lang);\n}\nfunction deleteGlobalMessage(lang) {\n  store2?.delete(lang);\n}\n\n// src/storages/schemaMessage/schemaMessage.ts\nvar store3;\nfunction setSchemaMessage(message2, lang) {\n  if (!store3) store3 = /* @__PURE__ */ new Map();\n  store3.set(lang, message2);\n}\n// @__NO_SIDE_EFFECTS__\nfunction getSchemaMessage(lang) {\n  return store3?.get(lang);\n}\nfunction deleteSchemaMessage(lang) {\n  store3?.delete(lang);\n}\n\n// src/storages/specificMessage/specificMessage.ts\nvar store4;\nfunction setSpecificMessage(reference, message2, lang) {\n  if (!store4) store4 = /* @__PURE__ */ new Map();\n  if (!store4.get(reference)) store4.set(reference, /* @__PURE__ */ new Map());\n  store4.get(reference).set(lang, message2);\n}\n// @__NO_SIDE_EFFECTS__\nfunction getSpecificMessage(reference, lang) {\n  return store4?.get(reference)?.get(lang);\n}\nfunction deleteSpecificMessage(reference, lang) {\n  store4?.get(reference)?.delete(lang);\n}\n\n// src/utils/_stringify/_stringify.ts\n// @__NO_SIDE_EFFECTS__\nfunction _stringify(input) {\n  const type = typeof input;\n  if (type === \"string\") {\n    return `\"${input}\"`;\n  }\n  if (type === \"number\" || type === \"bigint\" || type === \"boolean\") {\n    return `${input}`;\n  }\n  if (type === \"object\" || type === \"function\") {\n    return (input && Object.getPrototypeOf(input)?.constructor?.name) ?? \"null\";\n  }\n  return type;\n}\n\n// src/utils/_addIssue/_addIssue.ts\nfunction _addIssue(context, label, dataset, config2, other) {\n  const input = other && \"input\" in other ? other.input : dataset.value;\n  const expected = other?.expected ?? context.expects ?? null;\n  const received = other?.received ?? _stringify(input);\n  const issue = {\n    kind: context.kind,\n    type: context.type,\n    input,\n    expected,\n    received,\n    message: `Invalid ${label}: ${expected ? `Expected ${expected} but r` : \"R\"}eceived ${received}`,\n    requirement: context.requirement,\n    path: other?.path,\n    issues: other?.issues,\n    lang: config2.lang,\n    abortEarly: config2.abortEarly,\n    abortPipeEarly: config2.abortPipeEarly\n  };\n  const isSchema = context.kind === \"schema\";\n  const message2 = other?.message ?? context.message ?? getSpecificMessage(context.reference, issue.lang) ?? (isSchema ? getSchemaMessage(issue.lang) : null) ?? config2.message ?? getGlobalMessage(issue.lang);\n  if (message2 !== void 0) {\n    issue.message = typeof message2 === \"function\" ? (\n      // @ts-expect-error\n      message2(issue)\n    ) : message2;\n  }\n  if (isSchema) {\n    dataset.typed = false;\n  }\n  if (dataset.issues) {\n    dataset.issues.push(issue);\n  } else {\n    dataset.issues = [issue];\n  }\n}\n\n// src/utils/_getByteCount/_getByteCount.ts\nvar textEncoder;\n// @__NO_SIDE_EFFECTS__\nfunction _getByteCount(input) {\n  if (!textEncoder) {\n    textEncoder = new TextEncoder();\n  }\n  return textEncoder.encode(input).length;\n}\n\n// src/utils/_getGraphemeCount/_getGraphemeCount.ts\nvar segmenter;\n// @__NO_SIDE_EFFECTS__\nfunction _getGraphemeCount(input) {\n  if (!segmenter) {\n    segmenter = new Intl.Segmenter();\n  }\n  const segments = segmenter.segment(input);\n  let count = 0;\n  for (const _ of segments) {\n    count++;\n  }\n  return count;\n}\n\n// src/utils/_getLastMetadata/_getLastMetadata.ts\n// @__NO_SIDE_EFFECTS__\nfunction _getLastMetadata(schema, type) {\n  if (\"pipe\" in schema) {\n    const nestedSchemas = [];\n    for (let index = schema.pipe.length - 1; index >= 0; index--) {\n      const item = schema.pipe[index];\n      if (item.kind === \"schema\" && \"pipe\" in item) {\n        nestedSchemas.push(item);\n      } else if (item.kind === \"metadata\" && item.type === type) {\n        return item[type];\n      }\n    }\n    for (const nestedSchema of nestedSchemas) {\n      const result = /* @__PURE__ */ _getLastMetadata(nestedSchema, type);\n      if (result !== void 0) {\n        return result;\n      }\n    }\n  }\n}\n\n// src/utils/_getStandardProps/_getStandardProps.ts\n// @__NO_SIDE_EFFECTS__\nfunction _getStandardProps(context) {\n  return {\n    version: 1,\n    vendor: \"valibot\",\n    validate(value2) {\n      return context[\"~run\"]({ value: value2 }, getGlobalConfig());\n    }\n  };\n}\n\n// src/utils/_getWordCount/_getWordCount.ts\nvar store5;\n// @__NO_SIDE_EFFECTS__\nfunction _getWordCount(locales, input) {\n  if (!store5) {\n    store5 = /* @__PURE__ */ new Map();\n  }\n  if (!store5.get(locales)) {\n    store5.set(locales, new Intl.Segmenter(locales, { granularity: \"word\" }));\n  }\n  const segments = store5.get(locales).segment(input);\n  let count = 0;\n  for (const segment of segments) {\n    if (segment.isWordLike) {\n      count++;\n    }\n  }\n  return count;\n}\n\n// src/utils/_isLuhnAlgo/_isLuhnAlgo.ts\nvar NON_DIGIT_REGEX = /\\D/gu;\n// @__NO_SIDE_EFFECTS__\nfunction _isLuhnAlgo(input) {\n  const number2 = input.replace(NON_DIGIT_REGEX, \"\");\n  let length2 = number2.length;\n  let bit = 1;\n  let sum = 0;\n  while (length2) {\n    const value2 = +number2[--length2];\n    bit ^= 1;\n    sum += bit ? [0, 2, 4, 6, 8, 1, 3, 5, 7, 9][value2] : value2;\n  }\n  return sum % 10 === 0;\n}\n\n// src/utils/_isValidObjectKey/_isValidObjectKey.ts\n// @__NO_SIDE_EFFECTS__\nfunction _isValidObjectKey(object2, key) {\n  return Object.hasOwn(object2, key) && key !== \"__proto__\" && key !== \"prototype\" && key !== \"constructor\";\n}\n\n// src/utils/_joinExpects/_joinExpects.ts\n// @__NO_SIDE_EFFECTS__\nfunction _joinExpects(values2, separator) {\n  const list = [...new Set(values2)];\n  if (list.length > 1) {\n    return `(${list.join(` ${separator} `)})`;\n  }\n  return list[0] ?? \"never\";\n}\n\n// src/utils/entriesFromList/entriesFromList.ts\n// @__NO_SIDE_EFFECTS__\nfunction entriesFromList(list, schema) {\n  const entries2 = {};\n  for (const key of list) {\n    entries2[key] = schema;\n  }\n  return entries2;\n}\n\n// src/utils/entriesFromObjects/entriesFromObjects.ts\n// @__NO_SIDE_EFFECTS__\nfunction entriesFromObjects(schemas) {\n  const entries2 = {};\n  for (const schema of schemas) {\n    Object.assign(entries2, schema.entries);\n  }\n  return entries2;\n}\n\n// src/utils/getDotPath/getDotPath.ts\n// @__NO_SIDE_EFFECTS__\nfunction getDotPath(issue) {\n  if (issue.path) {\n    let key = \"\";\n    for (const item of issue.path) {\n      if (typeof item.key === \"string\" || typeof item.key === \"number\") {\n        if (key) {\n          key += `.${item.key}`;\n        } else {\n          key += item.key;\n        }\n      } else {\n        return null;\n      }\n    }\n    return key;\n  }\n  return null;\n}\n\n// src/utils/isOfKind/isOfKind.ts\n// @__NO_SIDE_EFFECTS__\nfunction isOfKind(kind, object2) {\n  return object2.kind === kind;\n}\n\n// src/utils/isOfType/isOfType.ts\n// @__NO_SIDE_EFFECTS__\nfunction isOfType(type, object2) {\n  return object2.type === type;\n}\n\n// src/utils/isValiError/isValiError.ts\n// @__NO_SIDE_EFFECTS__\nfunction isValiError(error) {\n  return error instanceof ValiError;\n}\n\n// src/utils/ValiError/ValiError.ts\nvar ValiError = class extends Error {\n  /**\n   * Creates a Valibot error with useful information.\n   *\n   * @param issues The error issues.\n   */\n  constructor(issues) {\n    super(issues[0].message);\n    this.name = \"ValiError\";\n    this.issues = issues;\n  }\n};\n\n// src/actions/args/args.ts\n// @__NO_SIDE_EFFECTS__\nfunction args(schema) {\n  return {\n    kind: \"transformation\",\n    type: \"args\",\n    reference: args,\n    async: false,\n    schema,\n    \"~run\"(dataset, config2) {\n      const func = dataset.value;\n      dataset.value = (...args_) => {\n        const argsDataset = this.schema[\"~run\"]({ value: args_ }, config2);\n        if (argsDataset.issues) {\n          throw new ValiError(argsDataset.issues);\n        }\n        return func(...argsDataset.value);\n      };\n      return dataset;\n    }\n  };\n}\n\n// src/actions/args/argsAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction argsAsync(schema) {\n  return {\n    kind: \"transformation\",\n    type: \"args\",\n    reference: argsAsync,\n    async: false,\n    schema,\n    \"~run\"(dataset, config2) {\n      const func = dataset.value;\n      dataset.value = async (...args2) => {\n        const argsDataset = await schema[\"~run\"]({ value: args2 }, config2);\n        if (argsDataset.issues) {\n          throw new ValiError(argsDataset.issues);\n        }\n        return func(...argsDataset.value);\n      };\n      return dataset;\n    }\n  };\n}\n\n// src/actions/await/awaitAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction awaitAsync() {\n  return {\n    kind: \"transformation\",\n    type: \"await\",\n    reference: awaitAsync,\n    async: true,\n    async \"~run\"(dataset) {\n      dataset.value = await dataset.value;\n      return dataset;\n    }\n  };\n}\n\n// src/regex.ts\nvar BASE64_REGEX = /^(?:[\\da-z+/]{4})*(?:[\\da-z+/]{2}==|[\\da-z+/]{3}=)?$/iu;\nvar BIC_REGEX = /^[A-Z]{6}(?!00)[\\dA-Z]{2}(?:[\\dA-Z]{3})?$/u;\nvar CUID2_REGEX = /^[a-z][\\da-z]*$/u;\nvar DECIMAL_REGEX = /^[+-]?(?:\\d*\\.)?\\d+$/u;\nvar DIGITS_REGEX = /^\\d+$/u;\nvar EMAIL_REGEX = /^[\\w+-]+(?:\\.[\\w+-]+)*@[\\da-z]+(?:[.-][\\da-z]+)*\\.[a-z]{2,}$/iu;\nvar EMOJI_REGEX = (\n  // eslint-disable-next-line redos-detector/no-unsafe-regex, regexp/no-dupe-disjunctions -- false positives\n  /^(?:[\\u{1F1E6}-\\u{1F1FF}]{2}|\\u{1F3F4}[\\u{E0061}-\\u{E007A}]{2}[\\u{E0030}-\\u{E0039}\\u{E0061}-\\u{E007A}]{1,3}\\u{E007F}|(?:\\p{Emoji}\\uFE0F\\u20E3?|\\p{Emoji_Modifier_Base}\\p{Emoji_Modifier}?|\\p{Emoji_Presentation})(?:\\u200D(?:\\p{Emoji}\\uFE0F\\u20E3?|\\p{Emoji_Modifier_Base}\\p{Emoji_Modifier}?|\\p{Emoji_Presentation}))*)+$/u\n);\nvar HEXADECIMAL_REGEX = /^(?:0[hx])?[\\da-fA-F]+$/u;\nvar HEX_COLOR_REGEX = /^#(?:[\\da-fA-F]{3,4}|[\\da-fA-F]{6}|[\\da-fA-F]{8})$/u;\nvar IMEI_REGEX = /^\\d{15}$|^\\d{2}-\\d{6}-\\d{6}-\\d$/u;\nvar IPV4_REGEX = (\n  // eslint-disable-next-line redos-detector/no-unsafe-regex -- false positive\n  /^(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])(?:\\.(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])){3}$/u\n);\nvar IPV6_REGEX = /^(?:(?:[\\da-f]{1,4}:){7}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,7}:|(?:[\\da-f]{1,4}:){1,6}:[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,5}(?::[\\da-f]{1,4}){1,2}|(?:[\\da-f]{1,4}:){1,4}(?::[\\da-f]{1,4}){1,3}|(?:[\\da-f]{1,4}:){1,3}(?::[\\da-f]{1,4}){1,4}|(?:[\\da-f]{1,4}:){1,2}(?::[\\da-f]{1,4}){1,5}|[\\da-f]{1,4}:(?::[\\da-f]{1,4}){1,6}|:(?:(?::[\\da-f]{1,4}){1,7}|:)|fe80:(?::[\\da-f]{0,4}){0,4}%[\\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)|(?:[\\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d))$/iu;\nvar IP_REGEX = /^(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])(?:\\.(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])){3}$|^(?:(?:[\\da-f]{1,4}:){7}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,7}:|(?:[\\da-f]{1,4}:){1,6}:[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,5}(?::[\\da-f]{1,4}){1,2}|(?:[\\da-f]{1,4}:){1,4}(?::[\\da-f]{1,4}){1,3}|(?:[\\da-f]{1,4}:){1,3}(?::[\\da-f]{1,4}){1,4}|(?:[\\da-f]{1,4}:){1,2}(?::[\\da-f]{1,4}){1,5}|[\\da-f]{1,4}:(?::[\\da-f]{1,4}){1,6}|:(?:(?::[\\da-f]{1,4}){1,7}|:)|fe80:(?::[\\da-f]{0,4}){0,4}%[\\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)|(?:[\\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d))$/iu;\nvar ISO_DATE_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])$/u;\nvar ISO_DATE_TIME_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])[T ](?:0\\d|1\\d|2[0-3]):[0-5]\\d$/u;\nvar ISO_TIME_REGEX = /^(?:0\\d|1\\d|2[0-3]):[0-5]\\d$/u;\nvar ISO_TIME_SECOND_REGEX = /^(?:0\\d|1\\d|2[0-3])(?::[0-5]\\d){2}$/u;\nvar ISO_TIMESTAMP_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])[T ](?:0\\d|1\\d|2[0-3])(?::[0-5]\\d){2}(?:\\.\\d{1,9})?(?:Z|[+-](?:0\\d|1\\d|2[0-3])(?::?[0-5]\\d)?)$/u;\nvar ISO_WEEK_REGEX = /^\\d{4}-W(?:0[1-9]|[1-4]\\d|5[0-3])$/u;\nvar MAC48_REGEX = /^(?:[\\da-f]{2}:){5}[\\da-f]{2}$|^(?:[\\da-f]{2}-){5}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){2}[\\da-f]{4}$/iu;\nvar MAC64_REGEX = /^(?:[\\da-f]{2}:){7}[\\da-f]{2}$|^(?:[\\da-f]{2}-){7}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){3}[\\da-f]{4}$|^(?:[\\da-f]{4}:){3}[\\da-f]{4}$/iu;\nvar MAC_REGEX = /^(?:[\\da-f]{2}:){5}[\\da-f]{2}$|^(?:[\\da-f]{2}-){5}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){2}[\\da-f]{4}$|^(?:[\\da-f]{2}:){7}[\\da-f]{2}$|^(?:[\\da-f]{2}-){7}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){3}[\\da-f]{4}$|^(?:[\\da-f]{4}:){3}[\\da-f]{4}$/iu;\nvar NANO_ID_REGEX = /^[\\w-]+$/u;\nvar OCTAL_REGEX = /^(?:0o)?[0-7]+$/u;\nvar RFC_EMAIL_REGEX = (\n  // eslint-disable-next-line regexp/prefer-w, no-useless-escape, regexp/no-useless-escape, regexp/require-unicode-regexp\n  /^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/\n);\nvar SLUG_REGEX = /^[\\da-z]+(?:[-_][\\da-z]+)*$/u;\nvar ULID_REGEX = /^[\\da-hjkmnp-tv-zA-HJKMNP-TV-Z]{26}$/u;\nvar UUID_REGEX = /^[\\da-f]{8}(?:-[\\da-f]{4}){3}-[\\da-f]{12}$/iu;\n\n// src/actions/base64/base64.ts\n// @__NO_SIDE_EFFECTS__\nfunction base64(message2) {\n  return {\n    kind: \"validation\",\n    type: \"base64\",\n    reference: base64,\n    async: false,\n    expects: null,\n    requirement: BASE64_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"Base64\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/bic/bic.ts\n// @__NO_SIDE_EFFECTS__\nfunction bic(message2) {\n  return {\n    kind: \"validation\",\n    type: \"bic\",\n    reference: bic,\n    async: false,\n    expects: null,\n    requirement: BIC_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"BIC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/brand/brand.ts\n// @__NO_SIDE_EFFECTS__\nfunction brand(name) {\n  return {\n    kind: \"transformation\",\n    type: \"brand\",\n    reference: brand,\n    async: false,\n    name,\n    \"~run\"(dataset) {\n      return dataset;\n    }\n  };\n}\n\n// src/actions/bytes/bytes.ts\n// @__NO_SIDE_EFFECTS__\nfunction bytes(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"bytes\",\n    reference: bytes,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = _getByteCount(dataset.value);\n        if (length2 !== this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/check/check.ts\n// @__NO_SIDE_EFFECTS__\nfunction check(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"check\",\n    reference: check,\n    async: false,\n    expects: null,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/check/checkAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction checkAsync(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"check\",\n    reference: checkAsync,\n    async: true,\n    expects: null,\n    requirement,\n    message: message2,\n    async \"~run\"(dataset, config2) {\n      if (dataset.typed && !await this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/checkItems/checkItems.ts\n// @__NO_SIDE_EFFECTS__\nfunction checkItems(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"check_items\",\n    reference: checkItems,\n    async: false,\n    expects: null,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        for (let index = 0; index < dataset.value.length; index++) {\n          const item = dataset.value[index];\n          if (!this.requirement(item, index, dataset.value)) {\n            _addIssue(this, \"item\", dataset, config2, {\n              input: item,\n              path: [\n                {\n                  type: \"array\",\n                  origin: \"value\",\n                  input: dataset.value,\n                  key: index,\n                  value: item\n                }\n              ]\n            });\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/checkItems/checkItemsAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction checkItemsAsync(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"check_items\",\n    reference: checkItemsAsync,\n    async: true,\n    expects: null,\n    requirement,\n    message: message2,\n    async \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const requirementResults = await Promise.all(\n          dataset.value.map(this.requirement)\n        );\n        for (let index = 0; index < dataset.value.length; index++) {\n          if (!requirementResults[index]) {\n            const item = dataset.value[index];\n            _addIssue(this, \"item\", dataset, config2, {\n              input: item,\n              path: [\n                {\n                  type: \"array\",\n                  origin: \"value\",\n                  input: dataset.value,\n                  key: index,\n                  value: item\n                }\n              ]\n            });\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/creditCard/creditCard.ts\nvar CREDIT_CARD_REGEX = /^(?:\\d{14,19}|\\d{4}(?: \\d{3,6}){2,4}|\\d{4}(?:-\\d{3,6}){2,4})$/u;\nvar SANITIZE_REGEX = /[- ]/gu;\nvar PROVIDER_REGEX_LIST = [\n  // American Express\n  /^3[47]\\d{13}$/u,\n  // Diners Club\n  /^3(?:0[0-5]|[68]\\d)\\d{11,13}$/u,\n  // Discover\n  /^6(?:011|5\\d{2})\\d{12,15}$/u,\n  // JCB\n  /^(?:2131|1800|35\\d{3})\\d{11}$/u,\n  // Mastercard\n  // eslint-disable-next-line redos-detector/no-unsafe-regex\n  /^5[1-5]\\d{2}|(?:222\\d|22[3-9]\\d|2[3-6]\\d{2}|27[01]\\d|2720)\\d{12}$/u,\n  // UnionPay\n  /^(?:6[27]\\d{14,17}|81\\d{14,17})$/u,\n  // Visa\n  /^4\\d{12}(?:\\d{3,6})?$/u\n];\n// @__NO_SIDE_EFFECTS__\nfunction creditCard(message2) {\n  return {\n    kind: \"validation\",\n    type: \"credit_card\",\n    reference: creditCard,\n    async: false,\n    expects: null,\n    requirement(input) {\n      let sanitized;\n      return CREDIT_CARD_REGEX.test(input) && // Remove any hyphens and blanks\n      (sanitized = input.replace(SANITIZE_REGEX, \"\")) && // Check if it matches a provider\n      PROVIDER_REGEX_LIST.some((regex2) => regex2.test(sanitized)) && // Check if passes luhn algorithm\n      _isLuhnAlgo(sanitized);\n    },\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"credit card\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/cuid2/cuid2.ts\n// @__NO_SIDE_EFFECTS__\nfunction cuid2(message2) {\n  return {\n    kind: \"validation\",\n    type: \"cuid2\",\n    reference: cuid2,\n    async: false,\n    expects: null,\n    requirement: CUID2_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"Cuid2\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/decimal/decimal.ts\n// @__NO_SIDE_EFFECTS__\nfunction decimal(message2) {\n  return {\n    kind: \"validation\",\n    type: \"decimal\",\n    reference: decimal,\n    async: false,\n    expects: null,\n    requirement: DECIMAL_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"decimal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/description/description.ts\n// @__NO_SIDE_EFFECTS__\nfunction description(description_) {\n  return {\n    kind: \"metadata\",\n    type: \"description\",\n    reference: description,\n    description: description_\n  };\n}\n\n// src/actions/digits/digits.ts\n// @__NO_SIDE_EFFECTS__\nfunction digits(message2) {\n  return {\n    kind: \"validation\",\n    type: \"digits\",\n    reference: digits,\n    async: false,\n    expects: null,\n    requirement: DIGITS_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"digits\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/email/email.ts\n// @__NO_SIDE_EFFECTS__\nfunction email(message2) {\n  return {\n    kind: \"validation\",\n    type: \"email\",\n    reference: email,\n    expects: null,\n    async: false,\n    requirement: EMAIL_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"email\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/emoji/emoji.ts\n// @__NO_SIDE_EFFECTS__\nfunction emoji(message2) {\n  return {\n    kind: \"validation\",\n    type: \"emoji\",\n    reference: emoji,\n    async: false,\n    expects: null,\n    requirement: EMOJI_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"emoji\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/empty/empty.ts\n// @__NO_SIDE_EFFECTS__\nfunction empty(message2) {\n  return {\n    kind: \"validation\",\n    type: \"empty\",\n    reference: empty,\n    async: false,\n    expects: \"0\",\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value.length > 0) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/endsWith/endsWith.ts\n// @__NO_SIDE_EFFECTS__\nfunction endsWith(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"ends_with\",\n    reference: endsWith,\n    async: false,\n    expects: `\"${requirement}\"`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !dataset.value.endsWith(this.requirement)) {\n        _addIssue(this, \"end\", dataset, config2, {\n          received: `\"${dataset.value.slice(-this.requirement.length)}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/entries/entries.ts\n// @__NO_SIDE_EFFECTS__\nfunction entries(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"entries\",\n    reference: entries,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (!dataset.typed) return dataset;\n      const count = Object.keys(dataset.value).length;\n      if (dataset.typed && count !== this.requirement) {\n        _addIssue(this, \"entries\", dataset, config2, {\n          received: `${count}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/everyItem/everyItem.ts\n// @__NO_SIDE_EFFECTS__\nfunction everyItem(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"every_item\",\n    reference: everyItem,\n    async: false,\n    expects: null,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !dataset.value.every(this.requirement)) {\n        _addIssue(this, \"item\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/excludes/excludes.ts\n// @__NO_SIDE_EFFECTS__\nfunction excludes(requirement, message2) {\n  const received = _stringify(requirement);\n  return {\n    kind: \"validation\",\n    type: \"excludes\",\n    reference: excludes,\n    async: false,\n    expects: `!${received}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value.includes(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2, { received });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/filterItems/filterItems.ts\n// @__NO_SIDE_EFFECTS__\nfunction filterItems(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"filter_items\",\n    reference: filterItems,\n    async: false,\n    operation,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value.filter(this.operation);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/findItem/findItem.ts\n// @__NO_SIDE_EFFECTS__\nfunction findItem(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"find_item\",\n    reference: findItem,\n    async: false,\n    operation,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value.find(this.operation);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/finite/finite.ts\n// @__NO_SIDE_EFFECTS__\nfunction finite(message2) {\n  return {\n    kind: \"validation\",\n    type: \"finite\",\n    reference: finite,\n    async: false,\n    expects: null,\n    requirement: Number.isFinite,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"finite\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/flavor/flavor.ts\n// @__NO_SIDE_EFFECTS__\nfunction flavor(name) {\n  return {\n    kind: \"transformation\",\n    type: \"flavor\",\n    reference: flavor,\n    async: false,\n    name,\n    \"~run\"(dataset) {\n      return dataset;\n    }\n  };\n}\n\n// src/actions/graphemes/graphemes.ts\n// @__NO_SIDE_EFFECTS__\nfunction graphemes(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"graphemes\",\n    reference: graphemes,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const count = _getGraphemeCount(dataset.value);\n        if (count !== this.requirement) {\n          _addIssue(this, \"graphemes\", dataset, config2, {\n            received: `${count}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/gtValue/gtValue.ts\n// @__NO_SIDE_EFFECTS__\nfunction gtValue(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"gt_value\",\n    reference: gtValue,\n    async: false,\n    expects: `>${requirement instanceof Date ? requirement.toJSON() : _stringify(requirement)}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !(dataset.value > this.requirement)) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hash/hash.ts\nvar HASH_LENGTHS = {\n  md4: 32,\n  md5: 32,\n  sha1: 40,\n  sha256: 64,\n  sha384: 96,\n  sha512: 128,\n  ripemd128: 32,\n  ripemd160: 40,\n  tiger128: 32,\n  tiger160: 40,\n  tiger192: 48,\n  crc32: 8,\n  crc32b: 8,\n  adler32: 8\n};\n// @__NO_SIDE_EFFECTS__\nfunction hash(types, message2) {\n  return {\n    kind: \"validation\",\n    type: \"hash\",\n    reference: hash,\n    expects: null,\n    async: false,\n    requirement: RegExp(\n      types.map((type) => `^[a-f0-9]{${HASH_LENGTHS[type]}}$`).join(\"|\"),\n      \"iu\"\n    ),\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hash\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hexadecimal/hexadecimal.ts\n// @__NO_SIDE_EFFECTS__\nfunction hexadecimal(message2) {\n  return {\n    kind: \"validation\",\n    type: \"hexadecimal\",\n    reference: hexadecimal,\n    async: false,\n    expects: null,\n    requirement: HEXADECIMAL_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hexadecimal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hexColor/hexColor.ts\n// @__NO_SIDE_EFFECTS__\nfunction hexColor(message2) {\n  return {\n    kind: \"validation\",\n    type: \"hex_color\",\n    reference: hexColor,\n    async: false,\n    expects: null,\n    requirement: HEX_COLOR_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hex color\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/imei/imei.ts\n// @__NO_SIDE_EFFECTS__\nfunction imei(message2) {\n  return {\n    kind: \"validation\",\n    type: \"imei\",\n    reference: imei,\n    async: false,\n    expects: null,\n    requirement(input) {\n      return IMEI_REGEX.test(input) && _isLuhnAlgo(input);\n    },\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"IMEI\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/includes/includes.ts\n// @__NO_SIDE_EFFECTS__\nfunction includes(requirement, message2) {\n  const expects = _stringify(requirement);\n  return {\n    kind: \"validation\",\n    type: \"includes\",\n    reference: includes,\n    async: false,\n    expects,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !dataset.value.includes(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2, {\n          received: `!${expects}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/integer/integer.ts\n// @__NO_SIDE_EFFECTS__\nfunction integer(message2) {\n  return {\n    kind: \"validation\",\n    type: \"integer\",\n    reference: integer,\n    async: false,\n    expects: null,\n    requirement: Number.isInteger,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"integer\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ip/ip.ts\n// @__NO_SIDE_EFFECTS__\nfunction ip(message2) {\n  return {\n    kind: \"validation\",\n    type: \"ip\",\n    reference: ip,\n    async: false,\n    expects: null,\n    requirement: IP_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IP\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ipv4/ipv4.ts\n// @__NO_SIDE_EFFECTS__\nfunction ipv4(message2) {\n  return {\n    kind: \"validation\",\n    type: \"ipv4\",\n    reference: ipv4,\n    async: false,\n    expects: null,\n    requirement: IPV4_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IPv4\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ipv6/ipv6.ts\n// @__NO_SIDE_EFFECTS__\nfunction ipv6(message2) {\n  return {\n    kind: \"validation\",\n    type: \"ipv6\",\n    reference: ipv6,\n    async: false,\n    expects: null,\n    requirement: IPV6_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IPv6\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoDate/isoDate.ts\n// @__NO_SIDE_EFFECTS__\nfunction isoDate(message2) {\n  return {\n    kind: \"validation\",\n    type: \"iso_date\",\n    reference: isoDate,\n    async: false,\n    expects: null,\n    requirement: ISO_DATE_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"date\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoDateTime/isoDateTime.ts\n// @__NO_SIDE_EFFECTS__\nfunction isoDateTime(message2) {\n  return {\n    kind: \"validation\",\n    type: \"iso_date_time\",\n    reference: isoDateTime,\n    async: false,\n    expects: null,\n    requirement: ISO_DATE_TIME_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"date-time\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTime/isoTime.ts\n// @__NO_SIDE_EFFECTS__\nfunction isoTime(message2) {\n  return {\n    kind: \"validation\",\n    type: \"iso_time\",\n    reference: isoTime,\n    async: false,\n    expects: null,\n    requirement: ISO_TIME_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"time\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTimeSecond/isoTimeSecond.ts\n// @__NO_SIDE_EFFECTS__\nfunction isoTimeSecond(message2) {\n  return {\n    kind: \"validation\",\n    type: \"iso_time_second\",\n    reference: isoTimeSecond,\n    async: false,\n    expects: null,\n    requirement: ISO_TIME_SECOND_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"time-second\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTimestamp/isoTimestamp.ts\n// @__NO_SIDE_EFFECTS__\nfunction isoTimestamp(message2) {\n  return {\n    kind: \"validation\",\n    type: \"iso_timestamp\",\n    reference: isoTimestamp,\n    async: false,\n    expects: null,\n    requirement: ISO_TIMESTAMP_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"timestamp\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoWeek/isoWeek.ts\n// @__NO_SIDE_EFFECTS__\nfunction isoWeek(message2) {\n  return {\n    kind: \"validation\",\n    type: \"iso_week\",\n    reference: isoWeek,\n    async: false,\n    expects: null,\n    requirement: ISO_WEEK_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"week\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/length/length.ts\n// @__NO_SIDE_EFFECTS__\nfunction length(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"length\",\n    reference: length,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value.length !== this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ltValue/ltValue.ts\n// @__NO_SIDE_EFFECTS__\nfunction ltValue(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"lt_value\",\n    reference: ltValue,\n    async: false,\n    expects: `<${requirement instanceof Date ? requirement.toJSON() : _stringify(requirement)}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !(dataset.value < this.requirement)) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac/mac.ts\n// @__NO_SIDE_EFFECTS__\nfunction mac(message2) {\n  return {\n    kind: \"validation\",\n    type: \"mac\",\n    reference: mac,\n    async: false,\n    expects: null,\n    requirement: MAC_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac48/mac48.ts\n// @__NO_SIDE_EFFECTS__\nfunction mac48(message2) {\n  return {\n    kind: \"validation\",\n    type: \"mac48\",\n    reference: mac48,\n    async: false,\n    expects: null,\n    requirement: MAC48_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"48-bit MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac64/mac64.ts\n// @__NO_SIDE_EFFECTS__\nfunction mac64(message2) {\n  return {\n    kind: \"validation\",\n    type: \"mac64\",\n    reference: mac64,\n    async: false,\n    expects: null,\n    requirement: MAC64_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"64-bit MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mapItems/mapItems.ts\n// @__NO_SIDE_EFFECTS__\nfunction mapItems(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"map_items\",\n    reference: mapItems,\n    async: false,\n    operation,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value.map(this.operation);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxBytes/maxBytes.ts\n// @__NO_SIDE_EFFECTS__\nfunction maxBytes(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"max_bytes\",\n    reference: maxBytes,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = _getByteCount(dataset.value);\n        if (length2 > this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxEntries/maxEntries.ts\n// @__NO_SIDE_EFFECTS__\nfunction maxEntries(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"max_entries\",\n    reference: maxEntries,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (!dataset.typed) return dataset;\n      const count = Object.keys(dataset.value).length;\n      if (dataset.typed && count > this.requirement) {\n        _addIssue(this, \"entries\", dataset, config2, {\n          received: `${count}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxGraphemes/maxGraphemes.ts\n// @__NO_SIDE_EFFECTS__\nfunction maxGraphemes(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"max_graphemes\",\n    reference: maxGraphemes,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const count = _getGraphemeCount(dataset.value);\n        if (count > this.requirement) {\n          _addIssue(this, \"graphemes\", dataset, config2, {\n            received: `${count}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxLength/maxLength.ts\n// @__NO_SIDE_EFFECTS__\nfunction maxLength(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"max_length\",\n    reference: maxLength,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value.length > this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxSize/maxSize.ts\n// @__NO_SIDE_EFFECTS__\nfunction maxSize(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"max_size\",\n    reference: maxSize,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value.size > this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxValue/maxValue.ts\n// @__NO_SIDE_EFFECTS__\nfunction maxValue(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"max_value\",\n    reference: maxValue,\n    async: false,\n    expects: `<=${requirement instanceof Date ? requirement.toJSON() : _stringify(requirement)}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !(dataset.value <= this.requirement)) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxWords/maxWords.ts\n// @__NO_SIDE_EFFECTS__\nfunction maxWords(locales, requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"max_words\",\n    reference: maxWords,\n    async: false,\n    expects: `<=${requirement}`,\n    locales,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const count = _getWordCount(this.locales, dataset.value);\n        if (count > this.requirement) {\n          _addIssue(this, \"words\", dataset, config2, {\n            received: `${count}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/metadata/metadata.ts\n// @__NO_SIDE_EFFECTS__\nfunction metadata(metadata_) {\n  return {\n    kind: \"metadata\",\n    type: \"metadata\",\n    reference: metadata,\n    metadata: metadata_\n  };\n}\n\n// src/actions/mimeType/mimeType.ts\n// @__NO_SIDE_EFFECTS__\nfunction mimeType(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"mime_type\",\n    reference: mimeType,\n    async: false,\n    expects: _joinExpects(\n      requirement.map((option) => `\"${option}\"`),\n      \"|\"\n    ),\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.includes(dataset.value.type)) {\n        _addIssue(this, \"MIME type\", dataset, config2, {\n          received: `\"${dataset.value.type}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minBytes/minBytes.ts\n// @__NO_SIDE_EFFECTS__\nfunction minBytes(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"min_bytes\",\n    reference: minBytes,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = _getByteCount(dataset.value);\n        if (length2 < this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minEntries/minEntries.ts\n// @__NO_SIDE_EFFECTS__\nfunction minEntries(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"min_entries\",\n    reference: minEntries,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (!dataset.typed) return dataset;\n      const count = Object.keys(dataset.value).length;\n      if (dataset.typed && count < this.requirement) {\n        _addIssue(this, \"entries\", dataset, config2, {\n          received: `${count}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minGraphemes/minGraphemes.ts\n// @__NO_SIDE_EFFECTS__\nfunction minGraphemes(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"min_graphemes\",\n    reference: minGraphemes,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const count = _getGraphemeCount(dataset.value);\n        if (count < this.requirement) {\n          _addIssue(this, \"graphemes\", dataset, config2, {\n            received: `${count}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minLength/minLength.ts\n// @__NO_SIDE_EFFECTS__\nfunction minLength(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"min_length\",\n    reference: minLength,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value.length < this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minSize/minSize.ts\n// @__NO_SIDE_EFFECTS__\nfunction minSize(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"min_size\",\n    reference: minSize,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value.size < this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minValue/minValue.ts\n// @__NO_SIDE_EFFECTS__\nfunction minValue(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"min_value\",\n    reference: minValue,\n    async: false,\n    expects: `>=${requirement instanceof Date ? requirement.toJSON() : _stringify(requirement)}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !(dataset.value >= this.requirement)) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minWords/minWords.ts\n// @__NO_SIDE_EFFECTS__\nfunction minWords(locales, requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"min_words\",\n    reference: minWords,\n    async: false,\n    expects: `>=${requirement}`,\n    locales,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const count = _getWordCount(this.locales, dataset.value);\n        if (count < this.requirement) {\n          _addIssue(this, \"words\", dataset, config2, {\n            received: `${count}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/multipleOf/multipleOf.ts\n// @__NO_SIDE_EFFECTS__\nfunction multipleOf(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"multiple_of\",\n    reference: multipleOf,\n    async: false,\n    expects: `%${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value % this.requirement != 0) {\n        _addIssue(this, \"multiple\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/nanoid/nanoid.ts\n// @__NO_SIDE_EFFECTS__\nfunction nanoid(message2) {\n  return {\n    kind: \"validation\",\n    type: \"nanoid\",\n    reference: nanoid,\n    async: false,\n    expects: null,\n    requirement: NANO_ID_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"Nano ID\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/nonEmpty/nonEmpty.ts\n// @__NO_SIDE_EFFECTS__\nfunction nonEmpty(message2) {\n  return {\n    kind: \"validation\",\n    type: \"non_empty\",\n    reference: nonEmpty,\n    async: false,\n    expects: \"!0\",\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value.length === 0) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: \"0\"\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/normalize/normalize.ts\n// @__NO_SIDE_EFFECTS__\nfunction normalize(form) {\n  return {\n    kind: \"transformation\",\n    type: \"normalize\",\n    reference: normalize,\n    async: false,\n    form,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value.normalize(this.form);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notBytes/notBytes.ts\n// @__NO_SIDE_EFFECTS__\nfunction notBytes(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"not_bytes\",\n    reference: notBytes,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = _getByteCount(dataset.value);\n        if (length2 === this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notEntries/notEntries.ts\n// @__NO_SIDE_EFFECTS__\nfunction notEntries(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"not_entries\",\n    reference: notEntries,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (!dataset.typed) return dataset;\n      const count = Object.keys(dataset.value).length;\n      if (dataset.typed && count === this.requirement) {\n        _addIssue(this, \"entries\", dataset, config2, {\n          received: `${count}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notGraphemes/notGraphemes.ts\n// @__NO_SIDE_EFFECTS__\nfunction notGraphemes(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"not_graphemes\",\n    reference: notGraphemes,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const count = _getGraphemeCount(dataset.value);\n        if (count === this.requirement) {\n          _addIssue(this, \"graphemes\", dataset, config2, {\n            received: `${count}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notLength/notLength.ts\n// @__NO_SIDE_EFFECTS__\nfunction notLength(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"not_length\",\n    reference: notLength,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value.length === this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notSize/notSize.ts\n// @__NO_SIDE_EFFECTS__\nfunction notSize(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"not_size\",\n    reference: notSize,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value.size === this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notValue/notValue.ts\n// @__NO_SIDE_EFFECTS__\nfunction notValue(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"not_value\",\n    reference: notValue,\n    async: false,\n    expects: requirement instanceof Date ? `!${requirement.toJSON()}` : `!${_stringify(requirement)}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && this.requirement <= dataset.value && this.requirement >= dataset.value) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notValues/notValues.ts\n// @__NO_SIDE_EFFECTS__\nfunction notValues(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"not_values\",\n    reference: notValues,\n    async: false,\n    expects: `!${_joinExpects(\n      requirement.map(\n        (value2) => value2 instanceof Date ? value2.toJSON() : _stringify(value2)\n      ),\n      \"|\"\n    )}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && this.requirement.some(\n        (value2) => value2 <= dataset.value && value2 >= dataset.value\n      )) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notWords/notWords.ts\n// @__NO_SIDE_EFFECTS__\nfunction notWords(locales, requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"not_words\",\n    reference: notWords,\n    async: false,\n    expects: `!${requirement}`,\n    locales,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const count = _getWordCount(this.locales, dataset.value);\n        if (count === this.requirement) {\n          _addIssue(this, \"words\", dataset, config2, {\n            received: `${count}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/octal/octal.ts\n// @__NO_SIDE_EFFECTS__\nfunction octal(message2) {\n  return {\n    kind: \"validation\",\n    type: \"octal\",\n    reference: octal,\n    async: false,\n    expects: null,\n    requirement: OCTAL_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"octal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/parseJson/parseJson.ts\n// @__NO_SIDE_EFFECTS__\nfunction parseJson(config2, message2) {\n  return {\n    kind: \"transformation\",\n    type: \"parse_json\",\n    reference: parseJson,\n    config: config2,\n    message: message2,\n    async: false,\n    \"~run\"(dataset, config3) {\n      try {\n        dataset.value = JSON.parse(dataset.value, this.config?.reviver);\n      } catch (error) {\n        if (error instanceof Error) {\n          _addIssue(this, \"JSON\", dataset, config3, {\n            received: `\"${error.message}\"`\n          });\n          dataset.typed = false;\n        } else {\n          throw error;\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/partialCheck/utils/_isPartiallyTyped/_isPartiallyTyped.ts\n// @__NO_SIDE_EFFECTS__\nfunction _isPartiallyTyped(dataset, paths) {\n  if (dataset.issues) {\n    for (const path of paths) {\n      for (const issue of dataset.issues) {\n        let typed = false;\n        const bound = Math.min(path.length, issue.path?.length ?? 0);\n        for (let index = 0; index < bound; index++) {\n          if (\n            // @ts-expect-error\n            path[index] !== issue.path[index].key && // @ts-expect-error\n            (path[index] !== \"$\" || issue.path[index].type !== \"array\")\n          ) {\n            typed = true;\n            break;\n          }\n        }\n        if (!typed) {\n          return false;\n        }\n      }\n    }\n  }\n  return true;\n}\n\n// src/actions/partialCheck/partialCheck.ts\n// @__NO_SIDE_EFFECTS__\nfunction partialCheck(paths, requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"partial_check\",\n    reference: partialCheck,\n    async: false,\n    expects: null,\n    paths,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if ((dataset.typed || _isPartiallyTyped(dataset, paths)) && // @ts-expect-error\n      !this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/partialCheck/partialCheckAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction partialCheckAsync(paths, requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"partial_check\",\n    reference: partialCheckAsync,\n    async: true,\n    expects: null,\n    paths,\n    requirement,\n    message: message2,\n    async \"~run\"(dataset, config2) {\n      if ((dataset.typed || _isPartiallyTyped(dataset, paths)) && // @ts-expect-error\n      !await this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/rawCheck/rawCheck.ts\n// @__NO_SIDE_EFFECTS__\nfunction rawCheck(action) {\n  return {\n    kind: \"validation\",\n    type: \"raw_check\",\n    reference: rawCheck,\n    async: false,\n    expects: null,\n    \"~run\"(dataset, config2) {\n      action({\n        dataset,\n        config: config2,\n        addIssue: (info) => _addIssue(this, info?.label ?? \"input\", dataset, config2, info)\n      });\n      return dataset;\n    }\n  };\n}\n\n// src/actions/rawCheck/rawCheckAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction rawCheckAsync(action) {\n  return {\n    kind: \"validation\",\n    type: \"raw_check\",\n    reference: rawCheckAsync,\n    async: true,\n    expects: null,\n    async \"~run\"(dataset, config2) {\n      await action({\n        dataset,\n        config: config2,\n        addIssue: (info) => _addIssue(this, info?.label ?? \"input\", dataset, config2, info)\n      });\n      return dataset;\n    }\n  };\n}\n\n// src/actions/rawTransform/rawTransform.ts\n// @__NO_SIDE_EFFECTS__\nfunction rawTransform(action) {\n  return {\n    kind: \"transformation\",\n    type: \"raw_transform\",\n    reference: rawTransform,\n    async: false,\n    \"~run\"(dataset, config2) {\n      const output = action({\n        dataset,\n        config: config2,\n        addIssue: (info) => _addIssue(this, info?.label ?? \"input\", dataset, config2, info),\n        NEVER: null\n      });\n      if (dataset.issues) {\n        dataset.typed = false;\n      } else {\n        dataset.value = output;\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/rawTransform/rawTransformAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction rawTransformAsync(action) {\n  return {\n    kind: \"transformation\",\n    type: \"raw_transform\",\n    reference: rawTransformAsync,\n    async: true,\n    async \"~run\"(dataset, config2) {\n      const output = await action({\n        dataset,\n        config: config2,\n        addIssue: (info) => _addIssue(this, info?.label ?? \"input\", dataset, config2, info),\n        NEVER: null\n      });\n      if (dataset.issues) {\n        dataset.typed = false;\n      } else {\n        dataset.value = output;\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/readonly/readonly.ts\n// @__NO_SIDE_EFFECTS__\nfunction readonly() {\n  return {\n    kind: \"transformation\",\n    type: \"readonly\",\n    reference: readonly,\n    async: false,\n    \"~run\"(dataset) {\n      return dataset;\n    }\n  };\n}\n\n// src/actions/reduceItems/reduceItems.ts\n// @__NO_SIDE_EFFECTS__\nfunction reduceItems(operation, initial) {\n  return {\n    kind: \"transformation\",\n    type: \"reduce_items\",\n    reference: reduceItems,\n    async: false,\n    operation,\n    initial,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value.reduce(this.operation, this.initial);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/regex/regex.ts\n// @__NO_SIDE_EFFECTS__\nfunction regex(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"regex\",\n    reference: regex,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"format\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/returns/returns.ts\n// @__NO_SIDE_EFFECTS__\nfunction returns(schema) {\n  return {\n    kind: \"transformation\",\n    type: \"returns\",\n    reference: returns,\n    async: false,\n    schema,\n    \"~run\"(dataset, config2) {\n      const func = dataset.value;\n      dataset.value = (...args_) => {\n        const returnsDataset = this.schema[\"~run\"](\n          { value: func(...args_) },\n          config2\n        );\n        if (returnsDataset.issues) {\n          throw new ValiError(returnsDataset.issues);\n        }\n        return returnsDataset.value;\n      };\n      return dataset;\n    }\n  };\n}\n\n// src/actions/returns/returnsAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction returnsAsync(schema) {\n  return {\n    kind: \"transformation\",\n    type: \"returns\",\n    reference: returnsAsync,\n    async: false,\n    schema,\n    \"~run\"(dataset, config2) {\n      const func = dataset.value;\n      dataset.value = async (...args_) => {\n        const returnsDataset = await this.schema[\"~run\"](\n          { value: await func(...args_) },\n          config2\n        );\n        if (returnsDataset.issues) {\n          throw new ValiError(returnsDataset.issues);\n        }\n        return returnsDataset.value;\n      };\n      return dataset;\n    }\n  };\n}\n\n// src/actions/rfcEmail/rfcEmail.ts\n// @__NO_SIDE_EFFECTS__\nfunction rfcEmail(message2) {\n  return {\n    kind: \"validation\",\n    type: \"rfc_email\",\n    reference: rfcEmail,\n    expects: null,\n    async: false,\n    requirement: RFC_EMAIL_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"email\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/safeInteger/safeInteger.ts\n// @__NO_SIDE_EFFECTS__\nfunction safeInteger(message2) {\n  return {\n    kind: \"validation\",\n    type: \"safe_integer\",\n    reference: safeInteger,\n    async: false,\n    expects: null,\n    requirement: Number.isSafeInteger,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"safe integer\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/size/size.ts\n// @__NO_SIDE_EFFECTS__\nfunction size(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"size\",\n    reference: size,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && dataset.value.size !== this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/slug/slug.ts\n// @__NO_SIDE_EFFECTS__\nfunction slug(message2) {\n  return {\n    kind: \"validation\",\n    type: \"slug\",\n    reference: slug,\n    async: false,\n    expects: null,\n    requirement: SLUG_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"slug\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/someItem/someItem.ts\n// @__NO_SIDE_EFFECTS__\nfunction someItem(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"some_item\",\n    reference: someItem,\n    async: false,\n    expects: null,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !dataset.value.some(this.requirement)) {\n        _addIssue(this, \"item\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/sortItems/sortItems.ts\n// @__NO_SIDE_EFFECTS__\nfunction sortItems(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"sort_items\",\n    reference: sortItems,\n    async: false,\n    operation,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value.sort(this.operation);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/startsWith/startsWith.ts\n// @__NO_SIDE_EFFECTS__\nfunction startsWith(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"starts_with\",\n    reference: startsWith,\n    async: false,\n    expects: `\"${requirement}\"`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !dataset.value.startsWith(this.requirement)) {\n        _addIssue(this, \"start\", dataset, config2, {\n          received: `\"${dataset.value.slice(0, this.requirement.length)}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/stringifyJson/stringifyJson.ts\n// @__NO_SIDE_EFFECTS__\nfunction stringifyJson(config2, message2) {\n  return {\n    kind: \"transformation\",\n    type: \"stringify_json\",\n    reference: stringifyJson,\n    message: message2,\n    config: config2,\n    async: false,\n    \"~run\"(dataset, config3) {\n      try {\n        const output = JSON.stringify(\n          dataset.value,\n          // @ts-expect-error\n          this.config?.replacer,\n          this.config?.space\n        );\n        if (output === void 0) {\n          _addIssue(this, \"JSON\", dataset, config3);\n          dataset.typed = false;\n        }\n        dataset.value = output;\n      } catch (error) {\n        if (error instanceof Error) {\n          _addIssue(this, \"JSON\", dataset, config3, {\n            received: `\"${error.message}\"`\n          });\n          dataset.typed = false;\n        } else {\n          throw error;\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/title/title.ts\n// @__NO_SIDE_EFFECTS__\nfunction title(title_) {\n  return {\n    kind: \"metadata\",\n    type: \"title\",\n    reference: title,\n    title: title_\n  };\n}\n\n// src/actions/toLowerCase/toLowerCase.ts\n// @__NO_SIDE_EFFECTS__\nfunction toLowerCase() {\n  return {\n    kind: \"transformation\",\n    type: \"to_lower_case\",\n    reference: toLowerCase,\n    async: false,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value.toLowerCase();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toMaxValue/toMaxValue.ts\n// @__NO_SIDE_EFFECTS__\nfunction toMaxValue(requirement) {\n  return {\n    kind: \"transformation\",\n    type: \"to_max_value\",\n    reference: toMaxValue,\n    async: false,\n    requirement,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value > this.requirement ? this.requirement : dataset.value;\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toMinValue/toMinValue.ts\n// @__NO_SIDE_EFFECTS__\nfunction toMinValue(requirement) {\n  return {\n    kind: \"transformation\",\n    type: \"to_min_value\",\n    reference: toMinValue,\n    async: false,\n    requirement,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value < this.requirement ? this.requirement : dataset.value;\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toUpperCase/toUpperCase.ts\n// @__NO_SIDE_EFFECTS__\nfunction toUpperCase() {\n  return {\n    kind: \"transformation\",\n    type: \"to_upper_case\",\n    reference: toUpperCase,\n    async: false,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value.toUpperCase();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/transform/transform.ts\n// @__NO_SIDE_EFFECTS__\nfunction transform(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"transform\",\n    reference: transform,\n    async: false,\n    operation,\n    \"~run\"(dataset) {\n      dataset.value = this.operation(dataset.value);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/transform/transformAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction transformAsync(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"transform\",\n    reference: transformAsync,\n    async: true,\n    operation,\n    async \"~run\"(dataset) {\n      dataset.value = await this.operation(dataset.value);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trim/trim.ts\n// @__NO_SIDE_EFFECTS__\nfunction trim() {\n  return {\n    kind: \"transformation\",\n    type: \"trim\",\n    reference: trim,\n    async: false,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value.trim();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trimEnd/trimEnd.ts\n// @__NO_SIDE_EFFECTS__\nfunction trimEnd() {\n  return {\n    kind: \"transformation\",\n    type: \"trim_end\",\n    reference: trimEnd,\n    async: false,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value.trimEnd();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trimStart/trimStart.ts\n// @__NO_SIDE_EFFECTS__\nfunction trimStart() {\n  return {\n    kind: \"transformation\",\n    type: \"trim_start\",\n    reference: trimStart,\n    async: false,\n    \"~run\"(dataset) {\n      dataset.value = dataset.value.trimStart();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ulid/ulid.ts\n// @__NO_SIDE_EFFECTS__\nfunction ulid(message2) {\n  return {\n    kind: \"validation\",\n    type: \"ulid\",\n    reference: ulid,\n    async: false,\n    expects: null,\n    requirement: ULID_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"ULID\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/url/url.ts\n// @__NO_SIDE_EFFECTS__\nfunction url(message2) {\n  return {\n    kind: \"validation\",\n    type: \"url\",\n    reference: url,\n    async: false,\n    expects: null,\n    requirement(input) {\n      try {\n        new URL(input);\n        return true;\n      } catch {\n        return false;\n      }\n    },\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"URL\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/uuid/uuid.ts\n// @__NO_SIDE_EFFECTS__\nfunction uuid(message2) {\n  return {\n    kind: \"validation\",\n    type: \"uuid\",\n    reference: uuid,\n    async: false,\n    expects: null,\n    requirement: UUID_REGEX,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"UUID\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/value/value.ts\n// @__NO_SIDE_EFFECTS__\nfunction value(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"value\",\n    reference: value,\n    async: false,\n    expects: requirement instanceof Date ? requirement.toJSON() : _stringify(requirement),\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !(this.requirement <= dataset.value && this.requirement >= dataset.value)) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/values/values.ts\n// @__NO_SIDE_EFFECTS__\nfunction values(requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"values\",\n    reference: values,\n    async: false,\n    expects: `${_joinExpects(\n      requirement.map(\n        (value2) => value2 instanceof Date ? value2.toJSON() : _stringify(value2)\n      ),\n      \"|\"\n    )}`,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed && !this.requirement.some(\n        (value2) => value2 <= dataset.value && value2 >= dataset.value\n      )) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/words/words.ts\n// @__NO_SIDE_EFFECTS__\nfunction words(locales, requirement, message2) {\n  return {\n    kind: \"validation\",\n    type: \"words\",\n    reference: words,\n    async: false,\n    expects: `${requirement}`,\n    locales,\n    requirement,\n    message: message2,\n    \"~run\"(dataset, config2) {\n      if (dataset.typed) {\n        const count = _getWordCount(this.locales, dataset.value);\n        if (count !== this.requirement) {\n          _addIssue(this, \"words\", dataset, config2, {\n            received: `${count}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/assert/assert.ts\nfunction assert(schema, input) {\n  const issues = schema[\"~run\"]({ value: input }, { abortEarly: true }).issues;\n  if (issues) {\n    throw new ValiError(issues);\n  }\n}\n\n// src/methods/config/config.ts\n// @__NO_SIDE_EFFECTS__\nfunction config(schema, config2) {\n  return {\n    ...schema,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config_) {\n      return schema[\"~run\"](dataset, { ...config_, ...config2 });\n    }\n  };\n}\n\n// src/methods/getFallback/getFallback.ts\n// @__NO_SIDE_EFFECTS__\nfunction getFallback(schema, dataset, config2) {\n  return typeof schema.fallback === \"function\" ? (\n    // @ts-expect-error\n    schema.fallback(dataset, config2)\n  ) : (\n    // @ts-expect-error\n    schema.fallback\n  );\n}\n\n// src/methods/fallback/fallback.ts\n// @__NO_SIDE_EFFECTS__\nfunction fallback(schema, fallback2) {\n  return {\n    ...schema,\n    fallback: fallback2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const outputDataset = schema[\"~run\"](dataset, config2);\n      return outputDataset.issues ? { typed: true, value: getFallback(this, outputDataset, config2) } : outputDataset;\n    }\n  };\n}\n\n// src/methods/fallback/fallbackAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction fallbackAsync(schema, fallback2) {\n  return {\n    ...schema,\n    fallback: fallback2,\n    async: true,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const outputDataset = await schema[\"~run\"](dataset, config2);\n      return outputDataset.issues ? {\n        typed: true,\n        value: await getFallback(this, outputDataset, config2)\n      } : outputDataset;\n    }\n  };\n}\n\n// src/methods/flatten/flatten.ts\n// @__NO_SIDE_EFFECTS__\nfunction flatten(issues) {\n  const flatErrors = {};\n  for (const issue of issues) {\n    if (issue.path) {\n      const dotPath = getDotPath(issue);\n      if (dotPath) {\n        if (!flatErrors.nested) {\n          flatErrors.nested = {};\n        }\n        if (flatErrors.nested[dotPath]) {\n          flatErrors.nested[dotPath].push(issue.message);\n        } else {\n          flatErrors.nested[dotPath] = [issue.message];\n        }\n      } else {\n        if (flatErrors.other) {\n          flatErrors.other.push(issue.message);\n        } else {\n          flatErrors.other = [issue.message];\n        }\n      }\n    } else {\n      if (flatErrors.root) {\n        flatErrors.root.push(issue.message);\n      } else {\n        flatErrors.root = [issue.message];\n      }\n    }\n  }\n  return flatErrors;\n}\n\n// src/methods/forward/forward.ts\n// @__NO_SIDE_EFFECTS__\nfunction forward(action, path) {\n  return {\n    ...action,\n    \"~run\"(dataset, config2) {\n      const prevIssues = dataset.issues && [...dataset.issues];\n      dataset = action[\"~run\"](dataset, config2);\n      if (dataset.issues) {\n        for (const issue of dataset.issues) {\n          if (!prevIssues?.includes(issue)) {\n            let pathInput = dataset.value;\n            for (const key of path) {\n              const pathValue = pathInput[key];\n              const pathItem = {\n                type: \"unknown\",\n                origin: \"value\",\n                input: pathInput,\n                key,\n                value: pathValue\n              };\n              if (issue.path) {\n                issue.path.push(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              if (!pathValue) {\n                break;\n              }\n              pathInput = pathValue;\n            }\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/forward/forwardAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction forwardAsync(action, path) {\n  return {\n    ...action,\n    async: true,\n    async \"~run\"(dataset, config2) {\n      const prevIssues = dataset.issues && [...dataset.issues];\n      dataset = await action[\"~run\"](dataset, config2);\n      if (dataset.issues) {\n        for (const issue of dataset.issues) {\n          if (!prevIssues?.includes(issue)) {\n            let pathInput = dataset.value;\n            for (const key of path) {\n              const pathValue = pathInput[key];\n              const pathItem = {\n                type: \"unknown\",\n                origin: \"value\",\n                input: pathInput,\n                key,\n                value: pathValue\n              };\n              if (issue.path) {\n                issue.path.push(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              if (!pathValue) {\n                break;\n              }\n              pathInput = pathValue;\n            }\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/getDefault/getDefault.ts\n// @__NO_SIDE_EFFECTS__\nfunction getDefault(schema, dataset, config2) {\n  return typeof schema.default === \"function\" ? (\n    // @ts-expect-error\n    schema.default(dataset, config2)\n  ) : (\n    // @ts-expect-error\n    schema.default\n  );\n}\n\n// src/methods/getDefaults/getDefaults.ts\n// @__NO_SIDE_EFFECTS__\nfunction getDefaults(schema) {\n  if (\"entries\" in schema) {\n    const object2 = {};\n    for (const key in schema.entries) {\n      object2[key] = /* @__PURE__ */ getDefaults(schema.entries[key]);\n    }\n    return object2;\n  }\n  if (\"items\" in schema) {\n    return schema.items.map(getDefaults);\n  }\n  return getDefault(schema);\n}\n\n// src/methods/getDefaults/getDefaultsAsync.ts\n// @__NO_SIDE_EFFECTS__\nasync function getDefaultsAsync(schema) {\n  if (\"entries\" in schema) {\n    return Object.fromEntries(\n      await Promise.all(\n        Object.entries(schema.entries).map(async ([key, value2]) => [\n          key,\n          await /* @__PURE__ */ getDefaultsAsync(value2)\n        ])\n      )\n    );\n  }\n  if (\"items\" in schema) {\n    return Promise.all(schema.items.map(getDefaultsAsync));\n  }\n  return getDefault(schema);\n}\n\n// src/methods/getDescription/getDescription.ts\n// @__NO_SIDE_EFFECTS__\nfunction getDescription(schema) {\n  return _getLastMetadata(schema, \"description\");\n}\n\n// src/methods/getFallbacks/getFallbacks.ts\n// @__NO_SIDE_EFFECTS__\nfunction getFallbacks(schema) {\n  if (\"entries\" in schema) {\n    const object2 = {};\n    for (const key in schema.entries) {\n      object2[key] = /* @__PURE__ */ getFallbacks(schema.entries[key]);\n    }\n    return object2;\n  }\n  if (\"items\" in schema) {\n    return schema.items.map(getFallbacks);\n  }\n  return getFallback(schema);\n}\n\n// src/methods/getFallbacks/getFallbacksAsync.ts\n// @__NO_SIDE_EFFECTS__\nasync function getFallbacksAsync(schema) {\n  if (\"entries\" in schema) {\n    return Object.fromEntries(\n      await Promise.all(\n        Object.entries(schema.entries).map(async ([key, value2]) => [\n          key,\n          await /* @__PURE__ */ getFallbacksAsync(value2)\n        ])\n      )\n    );\n  }\n  if (\"items\" in schema) {\n    return Promise.all(schema.items.map(getFallbacksAsync));\n  }\n  return getFallback(schema);\n}\n\n// src/methods/getMetadata/getMetadata.ts\n// @__NO_SIDE_EFFECTS__\nfunction getMetadata(schema) {\n  const result = {};\n  function depthFirstMerge(schema2) {\n    if (\"pipe\" in schema2) {\n      for (const item of schema2.pipe) {\n        if (item.kind === \"schema\" && \"pipe\" in item) {\n          depthFirstMerge(item);\n        } else if (item.kind === \"metadata\" && item.type === \"metadata\") {\n          Object.assign(result, item.metadata);\n        }\n      }\n    }\n  }\n  depthFirstMerge(schema);\n  return result;\n}\n\n// src/methods/getTitle/getTitle.ts\n// @__NO_SIDE_EFFECTS__\nfunction getTitle(schema) {\n  return _getLastMetadata(schema, \"title\");\n}\n\n// src/methods/is/is.ts\n// @__NO_SIDE_EFFECTS__\nfunction is(schema, input) {\n  return !schema[\"~run\"]({ value: input }, { abortEarly: true }).issues;\n}\n\n// src/schemas/any/any.ts\n// @__NO_SIDE_EFFECTS__\nfunction any() {\n  return {\n    kind: \"schema\",\n    type: \"any\",\n    reference: any,\n    expects: \"any\",\n    async: false,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset) {\n      dataset.typed = true;\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/array/array.ts\n// @__NO_SIDE_EFFECTS__\nfunction array(item, message2) {\n  return {\n    kind: \"schema\",\n    type: \"array\",\n    reference: array,\n    expects: \"Array\",\n    async: false,\n    item,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < input.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.item[\"~run\"]({ value: value2 }, config2);\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/array/arrayAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction arrayAsync(item, message2) {\n  return {\n    kind: \"schema\",\n    type: \"array\",\n    reference: arrayAsync,\n    expects: \"Array\",\n    async: true,\n    item,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          input.map((value2) => this.item[\"~run\"]({ value: value2 }, config2))\n        );\n        for (let key = 0; key < itemDatasets.length; key++) {\n          const itemDataset = itemDatasets[key];\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: input[key]\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/bigint/bigint.ts\n// @__NO_SIDE_EFFECTS__\nfunction bigint(message2) {\n  return {\n    kind: \"schema\",\n    type: \"bigint\",\n    reference: bigint,\n    expects: \"bigint\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (typeof dataset.value === \"bigint\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/blob/blob.ts\n// @__NO_SIDE_EFFECTS__\nfunction blob(message2) {\n  return {\n    kind: \"schema\",\n    type: \"blob\",\n    reference: blob,\n    expects: \"Blob\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value instanceof Blob) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/boolean/boolean.ts\n// @__NO_SIDE_EFFECTS__\nfunction boolean(message2) {\n  return {\n    kind: \"schema\",\n    type: \"boolean\",\n    reference: boolean,\n    expects: \"boolean\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (typeof dataset.value === \"boolean\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/custom/custom.ts\n// @__NO_SIDE_EFFECTS__\nfunction custom(check2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"custom\",\n    reference: custom,\n    expects: \"unknown\",\n    async: false,\n    check: check2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (this.check(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/custom/customAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction customAsync(check2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"custom\",\n    reference: customAsync,\n    expects: \"unknown\",\n    async: true,\n    check: check2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      if (await this.check(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/date/date.ts\n// @__NO_SIDE_EFFECTS__\nfunction date(message2) {\n  return {\n    kind: \"schema\",\n    type: \"date\",\n    reference: date,\n    expects: \"Date\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value instanceof Date) {\n        if (!isNaN(dataset.value)) {\n          dataset.typed = true;\n        } else {\n          _addIssue(this, \"type\", dataset, config2, {\n            received: '\"Invalid Date\"'\n          });\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/enum/enum.ts\n// @__NO_SIDE_EFFECTS__\nfunction enum_(enum__, message2) {\n  const options = [];\n  for (const key in enum__) {\n    if (`${+key}` !== key || typeof enum__[key] !== \"string\" || !Object.is(enum__[enum__[key]], +key)) {\n      options.push(enum__[key]);\n    }\n  }\n  return {\n    kind: \"schema\",\n    type: \"enum\",\n    reference: enum_,\n    expects: _joinExpects(options.map(_stringify), \"|\"),\n    async: false,\n    enum: enum__,\n    options,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (this.options.includes(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/exactOptional/exactOptional.ts\n// @__NO_SIDE_EFFECTS__\nfunction exactOptional(wrapped, default_) {\n  return {\n    kind: \"schema\",\n    type: \"exact_optional\",\n    reference: exactOptional,\n    expects: wrapped.expects,\n    async: false,\n    wrapped,\n    default: default_,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      return this.wrapped[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/exactOptional/exactOptionalAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction exactOptionalAsync(wrapped, default_) {\n  return {\n    kind: \"schema\",\n    type: \"exact_optional\",\n    reference: exactOptionalAsync,\n    expects: wrapped.expects,\n    async: true,\n    wrapped,\n    default: default_,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      return this.wrapped[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/file/file.ts\n// @__NO_SIDE_EFFECTS__\nfunction file(message2) {\n  return {\n    kind: \"schema\",\n    type: \"file\",\n    reference: file,\n    expects: \"File\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value instanceof File) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/function/function.ts\n// @__NO_SIDE_EFFECTS__\nfunction function_(message2) {\n  return {\n    kind: \"schema\",\n    type: \"function\",\n    reference: function_,\n    expects: \"Function\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (typeof dataset.value === \"function\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/instance/instance.ts\n// @__NO_SIDE_EFFECTS__\nfunction instance(class_, message2) {\n  return {\n    kind: \"schema\",\n    type: \"instance\",\n    reference: instance,\n    expects: class_.name,\n    async: false,\n    class: class_,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value instanceof this.class) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/intersect/utils/_merge/_merge.ts\n// @__NO_SIDE_EFFECTS__\nfunction _merge(value1, value2) {\n  if (typeof value1 === typeof value2) {\n    if (value1 === value2 || value1 instanceof Date && value2 instanceof Date && +value1 === +value2) {\n      return { value: value1 };\n    }\n    if (value1 && value2 && value1.constructor === Object && value2.constructor === Object) {\n      for (const key in value2) {\n        if (key in value1) {\n          const dataset = /* @__PURE__ */ _merge(value1[key], value2[key]);\n          if (dataset.issue) {\n            return dataset;\n          }\n          value1[key] = dataset.value;\n        } else {\n          value1[key] = value2[key];\n        }\n      }\n      return { value: value1 };\n    }\n    if (Array.isArray(value1) && Array.isArray(value2)) {\n      if (value1.length === value2.length) {\n        for (let index = 0; index < value1.length; index++) {\n          const dataset = /* @__PURE__ */ _merge(value1[index], value2[index]);\n          if (dataset.issue) {\n            return dataset;\n          }\n          value1[index] = dataset.value;\n        }\n        return { value: value1 };\n      }\n    }\n  }\n  return { issue: true };\n}\n\n// src/schemas/intersect/intersect.ts\n// @__NO_SIDE_EFFECTS__\nfunction intersect(options, message2) {\n  return {\n    kind: \"schema\",\n    type: \"intersect\",\n    reference: intersect,\n    expects: _joinExpects(\n      options.map((option) => option.expects),\n      \"&\"\n    ),\n    async: false,\n    options,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (this.options.length) {\n        const input = dataset.value;\n        let outputs;\n        dataset.typed = true;\n        for (const schema of this.options) {\n          const optionDataset = schema[\"~run\"]({ value: input }, config2);\n          if (optionDataset.issues) {\n            if (dataset.issues) {\n              dataset.issues.push(...optionDataset.issues);\n            } else {\n              dataset.issues = optionDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!optionDataset.typed) {\n            dataset.typed = false;\n          }\n          if (dataset.typed) {\n            if (outputs) {\n              outputs.push(optionDataset.value);\n            } else {\n              outputs = [optionDataset.value];\n            }\n          }\n        }\n        if (dataset.typed) {\n          dataset.value = outputs[0];\n          for (let index = 1; index < outputs.length; index++) {\n            const mergeDataset = _merge(dataset.value, outputs[index]);\n            if (mergeDataset.issue) {\n              _addIssue(this, \"type\", dataset, config2, {\n                received: \"unknown\"\n              });\n              break;\n            }\n            dataset.value = mergeDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/intersect/intersectAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction intersectAsync(options, message2) {\n  return {\n    kind: \"schema\",\n    type: \"intersect\",\n    reference: intersectAsync,\n    expects: _joinExpects(\n      options.map((option) => option.expects),\n      \"&\"\n    ),\n    async: true,\n    options,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      if (this.options.length) {\n        const input = dataset.value;\n        let outputs;\n        dataset.typed = true;\n        const optionDatasets = await Promise.all(\n          this.options.map((schema) => schema[\"~run\"]({ value: input }, config2))\n        );\n        for (const optionDataset of optionDatasets) {\n          if (optionDataset.issues) {\n            if (dataset.issues) {\n              dataset.issues.push(...optionDataset.issues);\n            } else {\n              dataset.issues = optionDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!optionDataset.typed) {\n            dataset.typed = false;\n          }\n          if (dataset.typed) {\n            if (outputs) {\n              outputs.push(optionDataset.value);\n            } else {\n              outputs = [optionDataset.value];\n            }\n          }\n        }\n        if (dataset.typed) {\n          dataset.value = outputs[0];\n          for (let index = 1; index < outputs.length; index++) {\n            const mergeDataset = _merge(dataset.value, outputs[index]);\n            if (mergeDataset.issue) {\n              _addIssue(this, \"type\", dataset, config2, {\n                received: \"unknown\"\n              });\n              break;\n            }\n            dataset.value = mergeDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/lazy/lazy.ts\n// @__NO_SIDE_EFFECTS__\nfunction lazy(getter) {\n  return {\n    kind: \"schema\",\n    type: \"lazy\",\n    reference: lazy,\n    expects: \"unknown\",\n    async: false,\n    getter,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      return this.getter(dataset.value)[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/lazy/lazyAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction lazyAsync(getter) {\n  return {\n    kind: \"schema\",\n    type: \"lazy\",\n    reference: lazyAsync,\n    expects: \"unknown\",\n    async: true,\n    getter,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      return (await this.getter(dataset.value))[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/literal/literal.ts\n// @__NO_SIDE_EFFECTS__\nfunction literal(literal_, message2) {\n  return {\n    kind: \"schema\",\n    type: \"literal\",\n    reference: literal,\n    expects: _stringify(literal_),\n    async: false,\n    literal: literal_,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value === this.literal) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseObject/looseObject.ts\n// @__NO_SIDE_EFFECTS__\nfunction looseObject(entries2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"loose_object\",\n    reference: looseObject,\n    expects: \"Object\",\n    async: false,\n    entries: entries2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const valueSchema = this.entries[key];\n          if (key in input || (valueSchema.type === \"exact_optional\" || valueSchema.type === \"optional\" || valueSchema.type === \"nullish\") && // @ts-expect-error\n          valueSchema.default !== void 0) {\n            const value2 = key in input ? (\n              // @ts-expect-error\n              input[key]\n            ) : getDefault(valueSchema);\n            const valueDataset = valueSchema[\"~run\"]({ value: value2 }, config2);\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          } else if (valueSchema.fallback !== void 0) {\n            dataset.value[key] = getFallback(valueSchema);\n          } else if (valueSchema.type !== \"exact_optional\" && valueSchema.type !== \"optional\" && valueSchema.type !== \"nullish\") {\n            _addIssue(this, \"key\", dataset, config2, {\n              input: void 0,\n              expected: `\"${key}\"`,\n              path: [\n                {\n                  type: \"object\",\n                  origin: \"key\",\n                  input,\n                  key,\n                  // @ts-expect-error\n                  value: input[key]\n                }\n              ]\n            });\n            if (config2.abortEarly) {\n              break;\n            }\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isValidObjectKey(input, key) && !(key in this.entries)) {\n              dataset.value[key] = input[key];\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseObject/looseObjectAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction looseObjectAsync(entries2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"loose_object\",\n    reference: looseObjectAsync,\n    expects: \"Object\",\n    async: true,\n    entries: entries2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, valueSchema]) => {\n            if (key in input || (valueSchema.type === \"exact_optional\" || valueSchema.type === \"optional\" || valueSchema.type === \"nullish\") && // @ts-expect-error\n            valueSchema.default !== void 0) {\n              const value2 = key in input ? (\n                // @ts-expect-error\n                input[key]\n              ) : await getDefault(valueSchema);\n              return [\n                key,\n                value2,\n                valueSchema,\n                await valueSchema[\"~run\"]({ value: value2 }, config2)\n              ];\n            }\n            return [\n              key,\n              // @ts-expect-error\n              input[key],\n              valueSchema,\n              null\n            ];\n          })\n        );\n        for (const [key, value2, valueSchema, valueDataset] of valueDatasets) {\n          if (valueDataset) {\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          } else if (valueSchema.fallback !== void 0) {\n            dataset.value[key] = await getFallback(valueSchema);\n          } else if (valueSchema.type !== \"exact_optional\" && valueSchema.type !== \"optional\" && valueSchema.type !== \"nullish\") {\n            _addIssue(this, \"key\", dataset, config2, {\n              input: void 0,\n              expected: `\"${key}\"`,\n              path: [\n                {\n                  type: \"object\",\n                  origin: \"key\",\n                  input,\n                  key,\n                  value: value2\n                }\n              ]\n            });\n            if (config2.abortEarly) {\n              break;\n            }\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isValidObjectKey(input, key) && !(key in this.entries)) {\n              dataset.value[key] = input[key];\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseTuple/looseTuple.ts\n// @__NO_SIDE_EFFECTS__\nfunction looseTuple(items, message2) {\n  return {\n    kind: \"schema\",\n    type: \"loose_tuple\",\n    reference: looseTuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < this.items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key][\"~run\"]({ value: value2 }, config2);\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = this.items.length; key < input.length; key++) {\n            dataset.value.push(input[key]);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseTuple/looseTupleAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction looseTupleAsync(items, message2) {\n  return {\n    kind: \"schema\",\n    type: \"loose_tuple\",\n    reference: looseTupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          this.items.map(async (item, key) => {\n            const value2 = input[key];\n            return [key, value2, await item[\"~run\"]({ value: value2 }, config2)];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = this.items.length; key < input.length; key++) {\n            dataset.value.push(input[key]);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/map/map.ts\n// @__NO_SIDE_EFFECTS__\nfunction map(key, value2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"map\",\n    reference: map,\n    expects: \"Map\",\n    async: false,\n    key,\n    value: value2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Map) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Map();\n        for (const [inputKey, inputValue] of input) {\n          const keyDataset = this.key[\"~run\"]({ value: inputKey }, config2);\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"key\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of keyDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          const valueDataset = this.value[\"~run\"](\n            { value: inputValue },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"value\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.set(keyDataset.value, valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/map/mapAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction mapAsync(key, value2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"map\",\n    reference: mapAsync,\n    expects: \"Map\",\n    async: true,\n    key,\n    value: value2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Map) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Map();\n        const datasets = await Promise.all(\n          [...input].map(\n            ([inputKey, inputValue]) => Promise.all([\n              inputKey,\n              inputValue,\n              this.key[\"~run\"]({ value: inputKey }, config2),\n              this.value[\"~run\"]({ value: inputValue }, config2)\n            ])\n          )\n        );\n        for (const [\n          inputKey,\n          inputValue,\n          keyDataset,\n          valueDataset\n        ] of datasets) {\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"key\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of keyDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"value\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.set(keyDataset.value, valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nan/nan.ts\n// @__NO_SIDE_EFFECTS__\nfunction nan(message2) {\n  return {\n    kind: \"schema\",\n    type: \"nan\",\n    reference: nan,\n    expects: \"NaN\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (Number.isNaN(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/never/never.ts\n// @__NO_SIDE_EFFECTS__\nfunction never(message2) {\n  return {\n    kind: \"schema\",\n    type: \"never\",\n    reference: never,\n    expects: \"never\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      _addIssue(this, \"type\", dataset, config2);\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nonNullable/nonNullable.ts\n// @__NO_SIDE_EFFECTS__\nfunction nonNullable(wrapped, message2) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullable\",\n    reference: nonNullable,\n    expects: \"!null\",\n    async: false,\n    wrapped,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value !== null) {\n        dataset = this.wrapped[\"~run\"](dataset, config2);\n      }\n      if (dataset.value === null) {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nonNullable/nonNullableAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction nonNullableAsync(wrapped, message2) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullable\",\n    reference: nonNullableAsync,\n    expects: \"!null\",\n    async: true,\n    wrapped,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      if (dataset.value !== null) {\n        dataset = await this.wrapped[\"~run\"](dataset, config2);\n      }\n      if (dataset.value === null) {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nonNullish/nonNullish.ts\n// @__NO_SIDE_EFFECTS__\nfunction nonNullish(wrapped, message2) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullish\",\n    reference: nonNullish,\n    expects: \"(!null & !undefined)\",\n    async: false,\n    wrapped,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (!(dataset.value === null || dataset.value === void 0)) {\n        dataset = this.wrapped[\"~run\"](dataset, config2);\n      }\n      if (dataset.value === null || dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nonNullish/nonNullishAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction nonNullishAsync(wrapped, message2) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullish\",\n    reference: nonNullishAsync,\n    expects: \"(!null & !undefined)\",\n    async: true,\n    wrapped,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      if (!(dataset.value === null || dataset.value === void 0)) {\n        dataset = await this.wrapped[\"~run\"](dataset, config2);\n      }\n      if (dataset.value === null || dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nonOptional/nonOptional.ts\n// @__NO_SIDE_EFFECTS__\nfunction nonOptional(wrapped, message2) {\n  return {\n    kind: \"schema\",\n    type: \"non_optional\",\n    reference: nonOptional,\n    expects: \"!undefined\",\n    async: false,\n    wrapped,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value !== void 0) {\n        dataset = this.wrapped[\"~run\"](dataset, config2);\n      }\n      if (dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nonOptional/nonOptionalAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction nonOptionalAsync(wrapped, message2) {\n  return {\n    kind: \"schema\",\n    type: \"non_optional\",\n    reference: nonOptionalAsync,\n    expects: \"!undefined\",\n    async: true,\n    wrapped,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      if (dataset.value !== void 0) {\n        dataset = await this.wrapped[\"~run\"](dataset, config2);\n      }\n      if (dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/null/null.ts\n// @__NO_SIDE_EFFECTS__\nfunction null_(message2) {\n  return {\n    kind: \"schema\",\n    type: \"null\",\n    reference: null_,\n    expects: \"null\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value === null) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nullable/nullable.ts\n// @__NO_SIDE_EFFECTS__\nfunction nullable(wrapped, default_) {\n  return {\n    kind: \"schema\",\n    type: \"nullable\",\n    reference: nullable,\n    expects: `(${wrapped.expects} | null)`,\n    async: false,\n    wrapped,\n    default: default_,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value === null) {\n        if (this.default !== void 0) {\n          dataset.value = getDefault(this, dataset, config2);\n        }\n        if (dataset.value === null) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nullable/nullableAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction nullableAsync(wrapped, default_) {\n  return {\n    kind: \"schema\",\n    type: \"nullable\",\n    reference: nullableAsync,\n    expects: `(${wrapped.expects} | null)`,\n    async: true,\n    wrapped,\n    default: default_,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      if (dataset.value === null) {\n        if (this.default !== void 0) {\n          dataset.value = await getDefault(this, dataset, config2);\n        }\n        if (dataset.value === null) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nullish/nullish.ts\n// @__NO_SIDE_EFFECTS__\nfunction nullish(wrapped, default_) {\n  return {\n    kind: \"schema\",\n    type: \"nullish\",\n    reference: nullish,\n    expects: `(${wrapped.expects} | null | undefined)`,\n    async: false,\n    wrapped,\n    default: default_,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        if (this.default !== void 0) {\n          dataset.value = getDefault(this, dataset, config2);\n        }\n        if (dataset.value === null || dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nullish/nullishAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction nullishAsync(wrapped, default_) {\n  return {\n    kind: \"schema\",\n    type: \"nullish\",\n    reference: nullishAsync,\n    expects: `(${wrapped.expects} | null | undefined)`,\n    async: true,\n    wrapped,\n    default: default_,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        if (this.default !== void 0) {\n          dataset.value = await getDefault(this, dataset, config2);\n        }\n        if (dataset.value === null || dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/number/number.ts\n// @__NO_SIDE_EFFECTS__\nfunction number(message2) {\n  return {\n    kind: \"schema\",\n    type: \"number\",\n    reference: number,\n    expects: \"number\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (typeof dataset.value === \"number\" && !isNaN(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/object/object.ts\n// @__NO_SIDE_EFFECTS__\nfunction object(entries2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"object\",\n    reference: object,\n    expects: \"Object\",\n    async: false,\n    entries: entries2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const valueSchema = this.entries[key];\n          if (key in input || (valueSchema.type === \"exact_optional\" || valueSchema.type === \"optional\" || valueSchema.type === \"nullish\") && // @ts-expect-error\n          valueSchema.default !== void 0) {\n            const value2 = key in input ? (\n              // @ts-expect-error\n              input[key]\n            ) : getDefault(valueSchema);\n            const valueDataset = valueSchema[\"~run\"]({ value: value2 }, config2);\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          } else if (valueSchema.fallback !== void 0) {\n            dataset.value[key] = getFallback(valueSchema);\n          } else if (valueSchema.type !== \"exact_optional\" && valueSchema.type !== \"optional\" && valueSchema.type !== \"nullish\") {\n            _addIssue(this, \"key\", dataset, config2, {\n              input: void 0,\n              expected: `\"${key}\"`,\n              path: [\n                {\n                  type: \"object\",\n                  origin: \"key\",\n                  input,\n                  key,\n                  // @ts-expect-error\n                  value: input[key]\n                }\n              ]\n            });\n            if (config2.abortEarly) {\n              break;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/object/objectAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction objectAsync(entries2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"object\",\n    reference: objectAsync,\n    expects: \"Object\",\n    async: true,\n    entries: entries2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, valueSchema]) => {\n            if (key in input || (valueSchema.type === \"exact_optional\" || valueSchema.type === \"optional\" || valueSchema.type === \"nullish\") && // @ts-expect-error\n            valueSchema.default !== void 0) {\n              const value2 = key in input ? (\n                // @ts-expect-error\n                input[key]\n              ) : await getDefault(valueSchema);\n              return [\n                key,\n                value2,\n                valueSchema,\n                await valueSchema[\"~run\"]({ value: value2 }, config2)\n              ];\n            }\n            return [\n              key,\n              // @ts-expect-error\n              input[key],\n              valueSchema,\n              null\n            ];\n          })\n        );\n        for (const [key, value2, valueSchema, valueDataset] of valueDatasets) {\n          if (valueDataset) {\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          } else if (valueSchema.fallback !== void 0) {\n            dataset.value[key] = await getFallback(valueSchema);\n          } else if (valueSchema.type !== \"exact_optional\" && valueSchema.type !== \"optional\" && valueSchema.type !== \"nullish\") {\n            _addIssue(this, \"key\", dataset, config2, {\n              input: void 0,\n              expected: `\"${key}\"`,\n              path: [\n                {\n                  type: \"object\",\n                  origin: \"key\",\n                  input,\n                  key,\n                  value: value2\n                }\n              ]\n            });\n            if (config2.abortEarly) {\n              break;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/objectWithRest/objectWithRest.ts\n// @__NO_SIDE_EFFECTS__\nfunction objectWithRest(entries2, rest, message2) {\n  return {\n    kind: \"schema\",\n    type: \"object_with_rest\",\n    reference: objectWithRest,\n    expects: \"Object\",\n    async: false,\n    entries: entries2,\n    rest,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const valueSchema = this.entries[key];\n          if (key in input || (valueSchema.type === \"exact_optional\" || valueSchema.type === \"optional\" || valueSchema.type === \"nullish\") && // @ts-expect-error\n          valueSchema.default !== void 0) {\n            const value2 = key in input ? (\n              // @ts-expect-error\n              input[key]\n            ) : getDefault(valueSchema);\n            const valueDataset = valueSchema[\"~run\"]({ value: value2 }, config2);\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          } else if (valueSchema.fallback !== void 0) {\n            dataset.value[key] = getFallback(valueSchema);\n          } else if (valueSchema.type !== \"exact_optional\" && valueSchema.type !== \"optional\" && valueSchema.type !== \"nullish\") {\n            _addIssue(this, \"key\", dataset, config2, {\n              input: void 0,\n              expected: `\"${key}\"`,\n              path: [\n                {\n                  type: \"object\",\n                  origin: \"key\",\n                  input,\n                  key,\n                  // @ts-expect-error\n                  value: input[key]\n                }\n              ]\n            });\n            if (config2.abortEarly) {\n              break;\n            }\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isValidObjectKey(input, key) && !(key in this.entries)) {\n              const valueDataset = this.rest[\"~run\"](\n                // @ts-expect-error\n                { value: input[key] },\n                config2\n              );\n              if (valueDataset.issues) {\n                const pathItem = {\n                  type: \"object\",\n                  origin: \"value\",\n                  input,\n                  key,\n                  // @ts-expect-error\n                  value: input[key]\n                };\n                for (const issue of valueDataset.issues) {\n                  if (issue.path) {\n                    issue.path.unshift(pathItem);\n                  } else {\n                    issue.path = [pathItem];\n                  }\n                  dataset.issues?.push(issue);\n                }\n                if (!dataset.issues) {\n                  dataset.issues = valueDataset.issues;\n                }\n                if (config2.abortEarly) {\n                  dataset.typed = false;\n                  break;\n                }\n              }\n              if (!valueDataset.typed) {\n                dataset.typed = false;\n              }\n              dataset.value[key] = valueDataset.value;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/objectWithRest/objectWithRestAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction objectWithRestAsync(entries2, rest, message2) {\n  return {\n    kind: \"schema\",\n    type: \"object_with_rest\",\n    reference: objectWithRestAsync,\n    expects: \"Object\",\n    async: true,\n    entries: entries2,\n    rest,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const [normalDatasets, restDatasets] = await Promise.all([\n          // If key is present or its an optional schema with a default value,\n          // parse input of key or default value asynchronously\n          Promise.all(\n            Object.entries(this.entries).map(async ([key, valueSchema]) => {\n              if (key in input || (valueSchema.type === \"exact_optional\" || valueSchema.type === \"optional\" || valueSchema.type === \"nullish\") && // @ts-expect-error\n              valueSchema.default !== void 0) {\n                const value2 = key in input ? (\n                  // @ts-expect-error\n                  input[key]\n                ) : await getDefault(valueSchema);\n                return [\n                  key,\n                  value2,\n                  valueSchema,\n                  await valueSchema[\"~run\"]({ value: value2 }, config2)\n                ];\n              }\n              return [\n                key,\n                // @ts-expect-error\n                input[key],\n                valueSchema,\n                null\n              ];\n            })\n          ),\n          // Parse other entries with rest schema asynchronously\n          // Hint: We exclude specific keys for security reasons\n          Promise.all(\n            Object.entries(input).filter(\n              ([key]) => _isValidObjectKey(input, key) && !(key in this.entries)\n            ).map(\n              async ([key, value2]) => [\n                key,\n                value2,\n                await this.rest[\"~run\"]({ value: value2 }, config2)\n              ]\n            )\n          )\n        ]);\n        for (const [key, value2, valueSchema, valueDataset] of normalDatasets) {\n          if (valueDataset) {\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          } else if (valueSchema.fallback !== void 0) {\n            dataset.value[key] = await getFallback(valueSchema);\n          } else if (valueSchema.type !== \"exact_optional\" && valueSchema.type !== \"optional\" && valueSchema.type !== \"nullish\") {\n            _addIssue(this, \"key\", dataset, config2, {\n              input: void 0,\n              expected: `\"${key}\"`,\n              path: [\n                {\n                  type: \"object\",\n                  origin: \"key\",\n                  input,\n                  key,\n                  value: value2\n                }\n              ]\n            });\n            if (config2.abortEarly) {\n              break;\n            }\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const [key, value2, valueDataset] of restDatasets) {\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/optional/optional.ts\n// @__NO_SIDE_EFFECTS__\nfunction optional(wrapped, default_) {\n  return {\n    kind: \"schema\",\n    type: \"optional\",\n    reference: optional,\n    expects: `(${wrapped.expects} | undefined)`,\n    async: false,\n    wrapped,\n    default: default_,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value === void 0) {\n        if (this.default !== void 0) {\n          dataset.value = getDefault(this, dataset, config2);\n        }\n        if (dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/optional/optionalAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction optionalAsync(wrapped, default_) {\n  return {\n    kind: \"schema\",\n    type: \"optional\",\n    reference: optionalAsync,\n    expects: `(${wrapped.expects} | undefined)`,\n    async: true,\n    wrapped,\n    default: default_,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      if (dataset.value === void 0) {\n        if (this.default !== void 0) {\n          dataset.value = await getDefault(this, dataset, config2);\n        }\n        if (dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/picklist/picklist.ts\n// @__NO_SIDE_EFFECTS__\nfunction picklist(options, message2) {\n  return {\n    kind: \"schema\",\n    type: \"picklist\",\n    reference: picklist,\n    expects: _joinExpects(options.map(_stringify), \"|\"),\n    async: false,\n    options,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (this.options.includes(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/promise/promise.ts\n// @__NO_SIDE_EFFECTS__\nfunction promise(message2) {\n  return {\n    kind: \"schema\",\n    type: \"promise\",\n    reference: promise,\n    expects: \"Promise\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value instanceof Promise) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/record/record.ts\n// @__NO_SIDE_EFFECTS__\nfunction record(key, value2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"record\",\n    reference: record,\n    expects: \"Object\",\n    async: false,\n    key,\n    value: value2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const entryKey in input) {\n          if (_isValidObjectKey(input, entryKey)) {\n            const entryValue = input[entryKey];\n            const keyDataset = this.key[\"~run\"]({ value: entryKey }, config2);\n            if (keyDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"key\",\n                input,\n                key: entryKey,\n                value: entryValue\n              };\n              for (const issue of keyDataset.issues) {\n                issue.path = [pathItem];\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = keyDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            const valueDataset = this.value[\"~run\"](\n              { value: entryValue },\n              config2\n            );\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key: entryKey,\n                value: entryValue\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!keyDataset.typed || !valueDataset.typed) {\n              dataset.typed = false;\n            }\n            if (keyDataset.typed) {\n              dataset.value[keyDataset.value] = valueDataset.value;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/record/recordAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction recordAsync(key, value2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"record\",\n    reference: recordAsync,\n    expects: \"Object\",\n    async: true,\n    key,\n    value: value2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const datasets = await Promise.all(\n          Object.entries(input).filter(([key2]) => _isValidObjectKey(input, key2)).map(\n            ([entryKey, entryValue]) => Promise.all([\n              entryKey,\n              entryValue,\n              this.key[\"~run\"]({ value: entryKey }, config2),\n              this.value[\"~run\"]({ value: entryValue }, config2)\n            ])\n          )\n        );\n        for (const [\n          entryKey,\n          entryValue,\n          keyDataset,\n          valueDataset\n        ] of datasets) {\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"key\",\n              input,\n              key: entryKey,\n              value: entryValue\n            };\n            for (const issue of keyDataset.issues) {\n              issue.path = [pathItem];\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key: entryKey,\n              value: entryValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (keyDataset.typed) {\n            dataset.value[keyDataset.value] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/set/set.ts\n// @__NO_SIDE_EFFECTS__\nfunction set(value2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"set\",\n    reference: set,\n    expects: \"Set\",\n    async: false,\n    value: value2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Set) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Set();\n        for (const inputValue of input) {\n          const valueDataset = this.value[\"~run\"](\n            { value: inputValue },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"set\",\n              origin: \"value\",\n              input,\n              key: null,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.add(valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/set/setAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction setAsync(value2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"set\",\n    reference: setAsync,\n    expects: \"Set\",\n    async: true,\n    value: value2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Set) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Set();\n        const valueDatasets = await Promise.all(\n          [...input].map(\n            async (inputValue) => [\n              inputValue,\n              await this.value[\"~run\"]({ value: inputValue }, config2)\n            ]\n          )\n        );\n        for (const [inputValue, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"set\",\n              origin: \"value\",\n              input,\n              key: null,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.add(valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictObject/strictObject.ts\n// @__NO_SIDE_EFFECTS__\nfunction strictObject(entries2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"strict_object\",\n    reference: strictObject,\n    expects: \"Object\",\n    async: false,\n    entries: entries2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const valueSchema = this.entries[key];\n          if (key in input || (valueSchema.type === \"exact_optional\" || valueSchema.type === \"optional\" || valueSchema.type === \"nullish\") && // @ts-expect-error\n          valueSchema.default !== void 0) {\n            const value2 = key in input ? (\n              // @ts-expect-error\n              input[key]\n            ) : getDefault(valueSchema);\n            const valueDataset = valueSchema[\"~run\"]({ value: value2 }, config2);\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          } else if (valueSchema.fallback !== void 0) {\n            dataset.value[key] = getFallback(valueSchema);\n          } else if (valueSchema.type !== \"exact_optional\" && valueSchema.type !== \"optional\" && valueSchema.type !== \"nullish\") {\n            _addIssue(this, \"key\", dataset, config2, {\n              input: void 0,\n              expected: `\"${key}\"`,\n              path: [\n                {\n                  type: \"object\",\n                  origin: \"key\",\n                  input,\n                  key,\n                  // @ts-expect-error\n                  value: input[key]\n                }\n              ]\n            });\n            if (config2.abortEarly) {\n              break;\n            }\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (!(key in this.entries)) {\n              _addIssue(this, \"key\", dataset, config2, {\n                input: key,\n                expected: \"never\",\n                path: [\n                  {\n                    type: \"object\",\n                    origin: \"key\",\n                    input,\n                    key,\n                    // @ts-expect-error\n                    value: input[key]\n                  }\n                ]\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictObject/strictObjectAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction strictObjectAsync(entries2, message2) {\n  return {\n    kind: \"schema\",\n    type: \"strict_object\",\n    reference: strictObjectAsync,\n    expects: \"Object\",\n    async: true,\n    entries: entries2,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, valueSchema]) => {\n            if (key in input || (valueSchema.type === \"exact_optional\" || valueSchema.type === \"optional\" || valueSchema.type === \"nullish\") && // @ts-expect-error\n            valueSchema.default !== void 0) {\n              const value2 = key in input ? (\n                // @ts-expect-error\n                input[key]\n              ) : await getDefault(valueSchema);\n              return [\n                key,\n                value2,\n                valueSchema,\n                await valueSchema[\"~run\"]({ value: value2 }, config2)\n              ];\n            }\n            return [\n              key,\n              // @ts-expect-error\n              input[key],\n              valueSchema,\n              null\n            ];\n          })\n        );\n        for (const [key, value2, valueSchema, valueDataset] of valueDatasets) {\n          if (valueDataset) {\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          } else if (valueSchema.fallback !== void 0) {\n            dataset.value[key] = await getFallback(valueSchema);\n          } else if (valueSchema.type !== \"exact_optional\" && valueSchema.type !== \"optional\" && valueSchema.type !== \"nullish\") {\n            _addIssue(this, \"key\", dataset, config2, {\n              input: void 0,\n              expected: `\"${key}\"`,\n              path: [\n                {\n                  type: \"object\",\n                  origin: \"key\",\n                  input,\n                  key,\n                  value: value2\n                }\n              ]\n            });\n            if (config2.abortEarly) {\n              break;\n            }\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (!(key in this.entries)) {\n              _addIssue(this, \"key\", dataset, config2, {\n                input: key,\n                expected: \"never\",\n                path: [\n                  {\n                    type: \"object\",\n                    origin: \"key\",\n                    input,\n                    key,\n                    // @ts-expect-error\n                    value: input[key]\n                  }\n                ]\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictTuple/strictTuple.ts\n// @__NO_SIDE_EFFECTS__\nfunction strictTuple(items, message2) {\n  return {\n    kind: \"schema\",\n    type: \"strict_tuple\",\n    reference: strictTuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < this.items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key][\"~run\"]({ value: value2 }, config2);\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!(dataset.issues && config2.abortEarly) && this.items.length < input.length) {\n          _addIssue(this, \"type\", dataset, config2, {\n            input: input[this.items.length],\n            expected: \"never\",\n            path: [\n              {\n                type: \"array\",\n                origin: \"value\",\n                input,\n                key: this.items.length,\n                value: input[this.items.length]\n              }\n            ]\n          });\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictTuple/strictTupleAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction strictTupleAsync(items, message2) {\n  return {\n    kind: \"schema\",\n    type: \"strict_tuple\",\n    reference: strictTupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          this.items.map(async (item, key) => {\n            const value2 = input[key];\n            return [key, value2, await item[\"~run\"]({ value: value2 }, config2)];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!(dataset.issues && config2.abortEarly) && this.items.length < input.length) {\n          _addIssue(this, \"type\", dataset, config2, {\n            input: input[this.items.length],\n            expected: \"never\",\n            path: [\n              {\n                type: \"array\",\n                origin: \"value\",\n                input,\n                key: this.items.length,\n                value: input[this.items.length]\n              }\n            ]\n          });\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/string/string.ts\n// @__NO_SIDE_EFFECTS__\nfunction string(message2) {\n  return {\n    kind: \"schema\",\n    type: \"string\",\n    reference: string,\n    expects: \"string\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (typeof dataset.value === \"string\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/symbol/symbol.ts\n// @__NO_SIDE_EFFECTS__\nfunction symbol(message2) {\n  return {\n    kind: \"schema\",\n    type: \"symbol\",\n    reference: symbol,\n    expects: \"symbol\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (typeof dataset.value === \"symbol\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tuple/tuple.ts\n// @__NO_SIDE_EFFECTS__\nfunction tuple(items, message2) {\n  return {\n    kind: \"schema\",\n    type: \"tuple\",\n    reference: tuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < this.items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key][\"~run\"]({ value: value2 }, config2);\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tuple/tupleAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction tupleAsync(items, message2) {\n  return {\n    kind: \"schema\",\n    type: \"tuple\",\n    reference: tupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          this.items.map(async (item, key) => {\n            const value2 = input[key];\n            return [key, value2, await item[\"~run\"]({ value: value2 }, config2)];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tupleWithRest/tupleWithRest.ts\n// @__NO_SIDE_EFFECTS__\nfunction tupleWithRest(items, rest, message2) {\n  return {\n    kind: \"schema\",\n    type: \"tuple_with_rest\",\n    reference: tupleWithRest,\n    expects: \"Array\",\n    async: false,\n    items,\n    rest,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < this.items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key][\"~run\"]({ value: value2 }, config2);\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = this.items.length; key < input.length; key++) {\n            const value2 = input[key];\n            const itemDataset = this.rest[\"~run\"]({ value: value2 }, config2);\n            if (itemDataset.issues) {\n              const pathItem = {\n                type: \"array\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of itemDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = itemDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!itemDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value.push(itemDataset.value);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tupleWithRest/tupleWithRestAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction tupleWithRestAsync(items, rest, message2) {\n  return {\n    kind: \"schema\",\n    type: \"tuple_with_rest\",\n    reference: tupleWithRestAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    rest,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const [normalDatasets, restDatasets] = await Promise.all([\n          // Parse schema of each normal item\n          Promise.all(\n            this.items.map(async (item, key) => {\n              const value2 = input[key];\n              return [\n                key,\n                value2,\n                await item[\"~run\"]({ value: value2 }, config2)\n              ];\n            })\n          ),\n          // Parse other items with rest schema\n          Promise.all(\n            input.slice(this.items.length).map(async (value2, key) => {\n              return [\n                key + this.items.length,\n                value2,\n                await this.rest[\"~run\"]({ value: value2 }, config2)\n              ];\n            })\n          )\n        ]);\n        for (const [key, value2, itemDataset] of normalDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const [key, value2, itemDataset] of restDatasets) {\n            if (itemDataset.issues) {\n              const pathItem = {\n                type: \"array\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of itemDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = itemDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!itemDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value.push(itemDataset.value);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/undefined/undefined.ts\n// @__NO_SIDE_EFFECTS__\nfunction undefined_(message2) {\n  return {\n    kind: \"schema\",\n    type: \"undefined\",\n    reference: undefined_,\n    expects: \"undefined\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value === void 0) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/undefinedable/undefinedable.ts\n// @__NO_SIDE_EFFECTS__\nfunction undefinedable(wrapped, default_) {\n  return {\n    kind: \"schema\",\n    type: \"undefinedable\",\n    reference: undefinedable,\n    expects: `(${wrapped.expects} | undefined)`,\n    async: false,\n    wrapped,\n    default: default_,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value === void 0) {\n        if (this.default !== void 0) {\n          dataset.value = getDefault(this, dataset, config2);\n        }\n        if (dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/undefinedable/undefinedableAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction undefinedableAsync(wrapped, default_) {\n  return {\n    kind: \"schema\",\n    type: \"undefinedable\",\n    reference: undefinedableAsync,\n    expects: `(${wrapped.expects} | undefined)`,\n    async: true,\n    wrapped,\n    default: default_,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      if (dataset.value === void 0) {\n        if (this.default !== void 0) {\n          dataset.value = await getDefault(this, dataset, config2);\n        }\n        if (dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped[\"~run\"](dataset, config2);\n    }\n  };\n}\n\n// src/schemas/union/utils/_subIssues/_subIssues.ts\n// @__NO_SIDE_EFFECTS__\nfunction _subIssues(datasets) {\n  let issues;\n  if (datasets) {\n    for (const dataset of datasets) {\n      if (issues) {\n        issues.push(...dataset.issues);\n      } else {\n        issues = dataset.issues;\n      }\n    }\n  }\n  return issues;\n}\n\n// src/schemas/union/union.ts\n// @__NO_SIDE_EFFECTS__\nfunction union(options, message2) {\n  return {\n    kind: \"schema\",\n    type: \"union\",\n    reference: union,\n    expects: _joinExpects(\n      options.map((option) => option.expects),\n      \"|\"\n    ),\n    async: false,\n    options,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      let validDataset;\n      let typedDatasets;\n      let untypedDatasets;\n      for (const schema of this.options) {\n        const optionDataset = schema[\"~run\"]({ value: dataset.value }, config2);\n        if (optionDataset.typed) {\n          if (optionDataset.issues) {\n            if (typedDatasets) {\n              typedDatasets.push(optionDataset);\n            } else {\n              typedDatasets = [optionDataset];\n            }\n          } else {\n            validDataset = optionDataset;\n            break;\n          }\n        } else {\n          if (untypedDatasets) {\n            untypedDatasets.push(optionDataset);\n          } else {\n            untypedDatasets = [optionDataset];\n          }\n        }\n      }\n      if (validDataset) {\n        return validDataset;\n      }\n      if (typedDatasets) {\n        if (typedDatasets.length === 1) {\n          return typedDatasets[0];\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(typedDatasets)\n        });\n        dataset.typed = true;\n      } else if (untypedDatasets?.length === 1) {\n        return untypedDatasets[0];\n      } else {\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(untypedDatasets)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/union/unionAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction unionAsync(options, message2) {\n  return {\n    kind: \"schema\",\n    type: \"union\",\n    reference: unionAsync,\n    expects: _joinExpects(\n      options.map((option) => option.expects),\n      \"|\"\n    ),\n    async: true,\n    options,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      let validDataset;\n      let typedDatasets;\n      let untypedDatasets;\n      for (const schema of this.options) {\n        const optionDataset = await schema[\"~run\"](\n          { value: dataset.value },\n          config2\n        );\n        if (optionDataset.typed) {\n          if (optionDataset.issues) {\n            if (typedDatasets) {\n              typedDatasets.push(optionDataset);\n            } else {\n              typedDatasets = [optionDataset];\n            }\n          } else {\n            validDataset = optionDataset;\n            break;\n          }\n        } else {\n          if (untypedDatasets) {\n            untypedDatasets.push(optionDataset);\n          } else {\n            untypedDatasets = [optionDataset];\n          }\n        }\n      }\n      if (validDataset) {\n        return validDataset;\n      }\n      if (typedDatasets) {\n        if (typedDatasets.length === 1) {\n          return typedDatasets[0];\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(typedDatasets)\n        });\n        dataset.typed = true;\n      } else if (untypedDatasets?.length === 1) {\n        return untypedDatasets[0];\n      } else {\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(untypedDatasets)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/unknown/unknown.ts\n// @__NO_SIDE_EFFECTS__\nfunction unknown() {\n  return {\n    kind: \"schema\",\n    type: \"unknown\",\n    reference: unknown,\n    expects: \"unknown\",\n    async: false,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset) {\n      dataset.typed = true;\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/variant/variant.ts\n// @__NO_SIDE_EFFECTS__\nfunction variant(key, options, message2) {\n  return {\n    kind: \"schema\",\n    type: \"variant\",\n    reference: variant,\n    expects: \"Object\",\n    async: false,\n    key,\n    options,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        let outputDataset;\n        let maxDiscriminatorPriority = 0;\n        let invalidDiscriminatorKey = this.key;\n        let expectedDiscriminators = [];\n        const parseOptions = (variant2, allKeys) => {\n          for (const schema of variant2.options) {\n            if (schema.type === \"variant\") {\n              parseOptions(schema, new Set(allKeys).add(schema.key));\n            } else {\n              let keysAreValid = true;\n              let currentPriority = 0;\n              for (const currentKey of allKeys) {\n                const discriminatorSchema = schema.entries[currentKey];\n                if (currentKey in input ? discriminatorSchema[\"~run\"](\n                  // @ts-expect-error\n                  { typed: false, value: input[currentKey] },\n                  { abortEarly: true }\n                ).issues : discriminatorSchema.type !== \"exact_optional\" && discriminatorSchema.type !== \"optional\" && discriminatorSchema.type !== \"nullish\") {\n                  keysAreValid = false;\n                  if (invalidDiscriminatorKey !== currentKey && (maxDiscriminatorPriority < currentPriority || maxDiscriminatorPriority === currentPriority && currentKey in input && !(invalidDiscriminatorKey in input))) {\n                    maxDiscriminatorPriority = currentPriority;\n                    invalidDiscriminatorKey = currentKey;\n                    expectedDiscriminators = [];\n                  }\n                  if (invalidDiscriminatorKey === currentKey) {\n                    expectedDiscriminators.push(\n                      schema.entries[currentKey].expects\n                    );\n                  }\n                  break;\n                }\n                currentPriority++;\n              }\n              if (keysAreValid) {\n                const optionDataset = schema[\"~run\"]({ value: input }, config2);\n                if (!outputDataset || !outputDataset.typed && optionDataset.typed) {\n                  outputDataset = optionDataset;\n                }\n              }\n            }\n            if (outputDataset && !outputDataset.issues) {\n              break;\n            }\n          }\n        };\n        parseOptions(this, /* @__PURE__ */ new Set([this.key]));\n        if (outputDataset) {\n          return outputDataset;\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          // @ts-expect-error\n          input: input[invalidDiscriminatorKey],\n          expected: _joinExpects(expectedDiscriminators, \"|\"),\n          path: [\n            {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key: invalidDiscriminatorKey,\n              // @ts-expect-error\n              value: input[invalidDiscriminatorKey]\n            }\n          ]\n        });\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/variant/variantAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction variantAsync(key, options, message2) {\n  return {\n    kind: \"schema\",\n    type: \"variant\",\n    reference: variantAsync,\n    expects: \"Object\",\n    async: true,\n    key,\n    options,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        let outputDataset;\n        let maxDiscriminatorPriority = 0;\n        let invalidDiscriminatorKey = this.key;\n        let expectedDiscriminators = [];\n        const parseOptions = async (variant2, allKeys) => {\n          for (const schema of variant2.options) {\n            if (schema.type === \"variant\") {\n              await parseOptions(schema, new Set(allKeys).add(schema.key));\n            } else {\n              let keysAreValid = true;\n              let currentPriority = 0;\n              for (const currentKey of allKeys) {\n                const discriminatorSchema = schema.entries[currentKey];\n                if (currentKey in input ? (await discriminatorSchema[\"~run\"](\n                  // @ts-expect-error\n                  { typed: false, value: input[currentKey] },\n                  { abortEarly: true }\n                )).issues : discriminatorSchema.type !== \"exact_optional\" && discriminatorSchema.type !== \"optional\" && discriminatorSchema.type !== \"nullish\") {\n                  keysAreValid = false;\n                  if (invalidDiscriminatorKey !== currentKey && (maxDiscriminatorPriority < currentPriority || maxDiscriminatorPriority === currentPriority && currentKey in input && !(invalidDiscriminatorKey in input))) {\n                    maxDiscriminatorPriority = currentPriority;\n                    invalidDiscriminatorKey = currentKey;\n                    expectedDiscriminators = [];\n                  }\n                  if (invalidDiscriminatorKey === currentKey) {\n                    expectedDiscriminators.push(\n                      schema.entries[currentKey].expects\n                    );\n                  }\n                  break;\n                }\n                currentPriority++;\n              }\n              if (keysAreValid) {\n                const optionDataset = await schema[\"~run\"](\n                  { value: input },\n                  config2\n                );\n                if (!outputDataset || !outputDataset.typed && optionDataset.typed) {\n                  outputDataset = optionDataset;\n                }\n              }\n            }\n            if (outputDataset && !outputDataset.issues) {\n              break;\n            }\n          }\n        };\n        await parseOptions(this, /* @__PURE__ */ new Set([this.key]));\n        if (outputDataset) {\n          return outputDataset;\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          // @ts-expect-error\n          input: input[invalidDiscriminatorKey],\n          expected: _joinExpects(expectedDiscriminators, \"|\"),\n          path: [\n            {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key: invalidDiscriminatorKey,\n              // @ts-expect-error\n              value: input[invalidDiscriminatorKey]\n            }\n          ]\n        });\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/void/void.ts\n// @__NO_SIDE_EFFECTS__\nfunction void_(message2) {\n  return {\n    kind: \"schema\",\n    type: \"void\",\n    reference: void_,\n    expects: \"void\",\n    async: false,\n    message: message2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      if (dataset.value === void 0) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/keyof/keyof.ts\n// @__NO_SIDE_EFFECTS__\nfunction keyof(schema, message2) {\n  return picklist(Object.keys(schema.entries), message2);\n}\n\n// src/methods/message/message.ts\n// @__NO_SIDE_EFFECTS__\nfunction message(schema, message_) {\n  return {\n    ...schema,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      return schema[\"~run\"](dataset, { ...config2, message: message_ });\n    }\n  };\n}\n\n// src/methods/omit/omit.ts\n// @__NO_SIDE_EFFECTS__\nfunction omit(schema, keys) {\n  const entries2 = {\n    ...schema.entries\n  };\n  for (const key of keys) {\n    delete entries2[key];\n  }\n  return {\n    ...schema,\n    entries: entries2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    }\n  };\n}\n\n// src/methods/parse/parse.ts\nfunction parse(schema, input, config2) {\n  const dataset = schema[\"~run\"]({ value: input }, getGlobalConfig(config2));\n  if (dataset.issues) {\n    throw new ValiError(dataset.issues);\n  }\n  return dataset.value;\n}\n\n// src/methods/parse/parseAsync.ts\nasync function parseAsync(schema, input, config2) {\n  const dataset = await schema[\"~run\"](\n    { value: input },\n    getGlobalConfig(config2)\n  );\n  if (dataset.issues) {\n    throw new ValiError(dataset.issues);\n  }\n  return dataset.value;\n}\n\n// src/methods/parser/parser.ts\n// @__NO_SIDE_EFFECTS__\nfunction parser(schema, config2) {\n  const func = (input) => parse(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/parser/parserAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction parserAsync(schema, config2) {\n  const func = (input) => parseAsync(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/partial/partial.ts\n// @__NO_SIDE_EFFECTS__\nfunction partial(schema, keys) {\n  const entries2 = {};\n  for (const key in schema.entries) {\n    entries2[key] = !keys || keys.includes(key) ? optional(schema.entries[key]) : schema.entries[key];\n  }\n  return {\n    ...schema,\n    entries: entries2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    }\n  };\n}\n\n// src/methods/partial/partialAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction partialAsync(schema, keys) {\n  const entries2 = {};\n  for (const key in schema.entries) {\n    entries2[key] = !keys || keys.includes(key) ? optionalAsync(schema.entries[key]) : schema.entries[key];\n  }\n  return {\n    ...schema,\n    entries: entries2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    }\n  };\n}\n\n// src/methods/pick/pick.ts\n// @__NO_SIDE_EFFECTS__\nfunction pick(schema, keys) {\n  const entries2 = {};\n  for (const key of keys) {\n    entries2[key] = schema.entries[key];\n  }\n  return {\n    ...schema,\n    entries: entries2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    }\n  };\n}\n\n// src/methods/pipe/pipe.ts\n// @__NO_SIDE_EFFECTS__\nfunction pipe(...pipe2) {\n  return {\n    ...pipe2[0],\n    pipe: pipe2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    \"~run\"(dataset, config2) {\n      for (const item of pipe2) {\n        if (item.kind !== \"metadata\") {\n          if (dataset.issues && (item.kind === \"schema\" || item.kind === \"transformation\")) {\n            dataset.typed = false;\n            break;\n          }\n          if (!dataset.issues || !config2.abortEarly && !config2.abortPipeEarly) {\n            dataset = item[\"~run\"](dataset, config2);\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/pipe/pipeAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction pipeAsync(...pipe2) {\n  return {\n    ...pipe2[0],\n    pipe: pipe2,\n    async: true,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    },\n    async \"~run\"(dataset, config2) {\n      for (const item of pipe2) {\n        if (item.kind !== \"metadata\") {\n          if (dataset.issues && (item.kind === \"schema\" || item.kind === \"transformation\")) {\n            dataset.typed = false;\n            break;\n          }\n          if (!dataset.issues || !config2.abortEarly && !config2.abortPipeEarly) {\n            dataset = await item[\"~run\"](dataset, config2);\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/required/required.ts\n// @__NO_SIDE_EFFECTS__\nfunction required(schema, arg2, arg3) {\n  const keys = Array.isArray(arg2) ? arg2 : void 0;\n  const message2 = Array.isArray(arg2) ? arg3 : arg2;\n  const entries2 = {};\n  for (const key in schema.entries) {\n    entries2[key] = !keys || keys.includes(key) ? nonOptional(schema.entries[key], message2) : schema.entries[key];\n  }\n  return {\n    ...schema,\n    entries: entries2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    }\n  };\n}\n\n// src/methods/required/requiredAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction requiredAsync(schema, arg2, arg3) {\n  const keys = Array.isArray(arg2) ? arg2 : void 0;\n  const message2 = Array.isArray(arg2) ? arg3 : arg2;\n  const entries2 = {};\n  for (const key in schema.entries) {\n    entries2[key] = !keys || keys.includes(key) ? nonOptionalAsync(schema.entries[key], message2) : schema.entries[key];\n  }\n  return {\n    ...schema,\n    entries: entries2,\n    get \"~standard\"() {\n      return _getStandardProps(this);\n    }\n  };\n}\n\n// src/methods/safeParse/safeParse.ts\n// @__NO_SIDE_EFFECTS__\nfunction safeParse(schema, input, config2) {\n  const dataset = schema[\"~run\"]({ value: input }, getGlobalConfig(config2));\n  return {\n    typed: dataset.typed,\n    success: !dataset.issues,\n    output: dataset.value,\n    issues: dataset.issues\n  };\n}\n\n// src/methods/safeParse/safeParseAsync.ts\n// @__NO_SIDE_EFFECTS__\nasync function safeParseAsync(schema, input, config2) {\n  const dataset = await schema[\"~run\"](\n    { value: input },\n    getGlobalConfig(config2)\n  );\n  return {\n    typed: dataset.typed,\n    success: !dataset.issues,\n    output: dataset.value,\n    issues: dataset.issues\n  };\n}\n\n// src/methods/safeParser/safeParser.ts\n// @__NO_SIDE_EFFECTS__\nfunction safeParser(schema, config2) {\n  const func = (input) => safeParse(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/safeParser/safeParserAsync.ts\n// @__NO_SIDE_EFFECTS__\nfunction safeParserAsync(schema, config2) {\n  const func = (input) => safeParseAsync(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/summarize/summarize.ts\n// @__NO_SIDE_EFFECTS__\nfunction summarize(issues) {\n  let summary = \"\";\n  for (const issue of issues) {\n    if (summary) {\n      summary += \"\\n\";\n    }\n    summary += `\\xD7 ${issue.message}`;\n    const dotPath = getDotPath(issue);\n    if (dotPath) {\n      summary += `\n  \\u2192 at ${dotPath}`;\n    }\n  }\n  return summary;\n}\n\n// src/methods/unwrap/unwrap.ts\n// @__NO_SIDE_EFFECTS__\nfunction unwrap(schema) {\n  return schema.wrapped;\n}\nexport {\n  BASE64_REGEX,\n  BIC_REGEX,\n  CUID2_REGEX,\n  DECIMAL_REGEX,\n  DIGITS_REGEX,\n  EMAIL_REGEX,\n  EMOJI_REGEX,\n  HEXADECIMAL_REGEX,\n  HEX_COLOR_REGEX,\n  IMEI_REGEX,\n  IPV4_REGEX,\n  IPV6_REGEX,\n  IP_REGEX,\n  ISO_DATE_REGEX,\n  ISO_DATE_TIME_REGEX,\n  ISO_TIMESTAMP_REGEX,\n  ISO_TIME_REGEX,\n  ISO_TIME_SECOND_REGEX,\n  ISO_WEEK_REGEX,\n  MAC48_REGEX,\n  MAC64_REGEX,\n  MAC_REGEX,\n  NANO_ID_REGEX,\n  OCTAL_REGEX,\n  RFC_EMAIL_REGEX,\n  SLUG_REGEX,\n  ULID_REGEX,\n  UUID_REGEX,\n  ValiError,\n  _addIssue,\n  _getByteCount,\n  _getGraphemeCount,\n  _getLastMetadata,\n  _getStandardProps,\n  _getWordCount,\n  _isLuhnAlgo,\n  _isValidObjectKey,\n  _joinExpects,\n  _stringify,\n  any,\n  args,\n  argsAsync,\n  array,\n  arrayAsync,\n  assert,\n  awaitAsync,\n  base64,\n  bic,\n  bigint,\n  blob,\n  boolean,\n  brand,\n  bytes,\n  check,\n  checkAsync,\n  checkItems,\n  checkItemsAsync,\n  config,\n  creditCard,\n  cuid2,\n  custom,\n  customAsync,\n  date,\n  decimal,\n  deleteGlobalConfig,\n  deleteGlobalMessage,\n  deleteSchemaMessage,\n  deleteSpecificMessage,\n  description,\n  digits,\n  email,\n  emoji,\n  empty,\n  endsWith,\n  entries,\n  entriesFromList,\n  entriesFromObjects,\n  enum_ as enum,\n  enum_,\n  everyItem,\n  exactOptional,\n  exactOptionalAsync,\n  excludes,\n  fallback,\n  fallbackAsync,\n  file,\n  filterItems,\n  findItem,\n  finite,\n  flatten,\n  flavor,\n  forward,\n  forwardAsync,\n  function_ as function,\n  function_,\n  getDefault,\n  getDefaults,\n  getDefaultsAsync,\n  getDescription,\n  getDotPath,\n  getFallback,\n  getFallbacks,\n  getFallbacksAsync,\n  getGlobalConfig,\n  getGlobalMessage,\n  getMetadata,\n  getSchemaMessage,\n  getSpecificMessage,\n  getTitle,\n  graphemes,\n  gtValue,\n  hash,\n  hexColor,\n  hexadecimal,\n  imei,\n  includes,\n  instance,\n  integer,\n  intersect,\n  intersectAsync,\n  ip,\n  ipv4,\n  ipv6,\n  is,\n  isOfKind,\n  isOfType,\n  isValiError,\n  isoDate,\n  isoDateTime,\n  isoTime,\n  isoTimeSecond,\n  isoTimestamp,\n  isoWeek,\n  keyof,\n  lazy,\n  lazyAsync,\n  length,\n  literal,\n  looseObject,\n  looseObjectAsync,\n  looseTuple,\n  looseTupleAsync,\n  ltValue,\n  mac,\n  mac48,\n  mac64,\n  map,\n  mapAsync,\n  mapItems,\n  maxBytes,\n  maxEntries,\n  maxGraphemes,\n  maxLength,\n  maxSize,\n  maxValue,\n  maxWords,\n  message,\n  metadata,\n  mimeType,\n  minBytes,\n  minEntries,\n  minGraphemes,\n  minLength,\n  minSize,\n  minValue,\n  minWords,\n  multipleOf,\n  nan,\n  nanoid,\n  never,\n  nonEmpty,\n  nonNullable,\n  nonNullableAsync,\n  nonNullish,\n  nonNullishAsync,\n  nonOptional,\n  nonOptionalAsync,\n  normalize,\n  notBytes,\n  notEntries,\n  notGraphemes,\n  notLength,\n  notSize,\n  notValue,\n  notValues,\n  notWords,\n  null_ as null,\n  null_,\n  nullable,\n  nullableAsync,\n  nullish,\n  nullishAsync,\n  number,\n  object,\n  objectAsync,\n  objectWithRest,\n  objectWithRestAsync,\n  octal,\n  omit,\n  optional,\n  optionalAsync,\n  parse,\n  parseAsync,\n  parseJson,\n  parser,\n  parserAsync,\n  partial,\n  partialAsync,\n  partialCheck,\n  partialCheckAsync,\n  pick,\n  picklist,\n  pipe,\n  pipeAsync,\n  promise,\n  rawCheck,\n  rawCheckAsync,\n  rawTransform,\n  rawTransformAsync,\n  readonly,\n  record,\n  recordAsync,\n  reduceItems,\n  regex,\n  required,\n  requiredAsync,\n  returns,\n  returnsAsync,\n  rfcEmail,\n  safeInteger,\n  safeParse,\n  safeParseAsync,\n  safeParser,\n  safeParserAsync,\n  set,\n  setAsync,\n  setGlobalConfig,\n  setGlobalMessage,\n  setSchemaMessage,\n  setSpecificMessage,\n  size,\n  slug,\n  someItem,\n  sortItems,\n  startsWith,\n  strictObject,\n  strictObjectAsync,\n  strictTuple,\n  strictTupleAsync,\n  string,\n  stringifyJson,\n  summarize,\n  symbol,\n  title,\n  toLowerCase,\n  toMaxValue,\n  toMinValue,\n  toUpperCase,\n  transform,\n  transformAsync,\n  trim,\n  trimEnd,\n  trimStart,\n  tuple,\n  tupleAsync,\n  tupleWithRest,\n  tupleWithRestAsync,\n  ulid,\n  undefined_ as undefined,\n  undefined_,\n  undefinedable,\n  undefinedableAsync,\n  union,\n  unionAsync,\n  unknown,\n  unwrap,\n  url,\n  uuid,\n  value,\n  values,\n  variant,\n  variantAsync,\n  void_ as void,\n  void_,\n  words\n};\n", "import * as v from \"valibot\";\n\n//#region src/utils/addError.ts\n/**\n* Adds an error message to the errors array.\n*\n* @param errors The array of error messages.\n* @param message The error message to add.\n*\n* @returns The new errors.\n*/\nfunction addError(errors, message) {\n\tif (errors) {\n\t\terrors.push(message);\n\t\treturn errors;\n\t}\n\treturn [message];\n}\n\n//#endregion\n//#region src/utils/handleError.ts\n/**\n* Throws an error or logs a warning based on the configuration.\n*\n* @param message The message to throw or log.\n* @param config The conversion configuration.\n*/\nfunction handleError(message, config) {\n\tswitch (config?.errorMode) {\n\t\tcase \"ignore\": break;\n\t\tcase \"warn\": {\n\t\t\tconsole.warn(message);\n\t\t\tbreak;\n\t\t}\n\t\tdefault: throw new Error(message);\n\t}\n}\n\n//#endregion\n//#region src/converters/convertAction/convertAction.ts\n/**\n* Converts any supported Valibot action to the JSON Schema format.\n*\n* @param jsonSchema The JSON Schema object.\n* @param valibotAction The Valibot action object.\n* @param config The conversion configuration.\n*\n* @returns The converted JSON Schema.\n*/\nfunction convertAction(jsonSchema, valibotAction, config) {\n\tif (config?.ignoreActions?.includes(valibotAction.type)) return jsonSchema;\n\tlet errors;\n\tswitch (valibotAction.type) {\n\t\tcase \"base64\": {\n\t\t\tjsonSchema.contentEncoding = \"base64\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"bic\":\n\t\tcase \"cuid2\":\n\t\tcase \"decimal\":\n\t\tcase \"digits\":\n\t\tcase \"emoji\":\n\t\tcase \"hexadecimal\":\n\t\tcase \"hex_color\":\n\t\tcase \"nanoid\":\n\t\tcase \"octal\":\n\t\tcase \"ulid\": {\n\t\t\tjsonSchema.pattern = valibotAction.requirement.source;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"description\": {\n\t\t\tjsonSchema.description = valibotAction.description;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"email\": {\n\t\t\tjsonSchema.format = \"email\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"empty\": {\n\t\t\tif (jsonSchema.type === \"array\") jsonSchema.maxItems = 0;\n\t\t\telse {\n\t\t\t\tif (jsonSchema.type !== \"string\") errors = addError(errors, `The \"${valibotAction.type}\" action is not supported on type \"${jsonSchema.type}\".`);\n\t\t\t\tjsonSchema.maxLength = 0;\n\t\t\t}\n\t\t\tbreak;\n\t\t}\n\t\tcase \"entries\": {\n\t\t\tjsonSchema.minProperties = valibotAction.requirement;\n\t\t\tjsonSchema.maxProperties = valibotAction.requirement;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"integer\": {\n\t\t\tjsonSchema.type = \"integer\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"ipv4\": {\n\t\t\tjsonSchema.format = \"ipv4\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"ipv6\": {\n\t\t\tjsonSchema.format = \"ipv6\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"iso_date\": {\n\t\t\tjsonSchema.format = \"date\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"iso_date_time\":\n\t\tcase \"iso_timestamp\": {\n\t\t\tjsonSchema.format = \"date-time\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"iso_time\": {\n\t\t\tjsonSchema.format = \"time\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"length\": {\n\t\t\tif (jsonSchema.type === \"array\") {\n\t\t\t\tjsonSchema.minItems = valibotAction.requirement;\n\t\t\t\tjsonSchema.maxItems = valibotAction.requirement;\n\t\t\t} else {\n\t\t\t\tif (jsonSchema.type !== \"string\") errors = addError(errors, `The \"${valibotAction.type}\" action is not supported on type \"${jsonSchema.type}\".`);\n\t\t\t\tjsonSchema.minLength = valibotAction.requirement;\n\t\t\t\tjsonSchema.maxLength = valibotAction.requirement;\n\t\t\t}\n\t\t\tbreak;\n\t\t}\n\t\tcase \"max_entries\": {\n\t\t\tjsonSchema.maxProperties = valibotAction.requirement;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"max_length\": {\n\t\t\tif (jsonSchema.type === \"array\") jsonSchema.maxItems = valibotAction.requirement;\n\t\t\telse {\n\t\t\t\tif (jsonSchema.type !== \"string\") errors = addError(errors, `The \"${valibotAction.type}\" action is not supported on type \"${jsonSchema.type}\".`);\n\t\t\t\tjsonSchema.maxLength = valibotAction.requirement;\n\t\t\t}\n\t\t\tbreak;\n\t\t}\n\t\tcase \"max_value\": {\n\t\t\tif (jsonSchema.type !== \"number\") errors = addError(errors, `The \"max_value\" action is not supported on type \"${jsonSchema.type}\".`);\n\t\t\tjsonSchema.maximum = valibotAction.requirement;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"metadata\": {\n\t\t\tif (typeof valibotAction.metadata.title === \"string\") jsonSchema.title = valibotAction.metadata.title;\n\t\t\tif (typeof valibotAction.metadata.description === \"string\") jsonSchema.description = valibotAction.metadata.description;\n\t\t\tif (Array.isArray(valibotAction.metadata.examples)) jsonSchema.examples = valibotAction.metadata.examples;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"min_entries\": {\n\t\t\tjsonSchema.minProperties = valibotAction.requirement;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"min_length\": {\n\t\t\tif (jsonSchema.type === \"array\") jsonSchema.minItems = valibotAction.requirement;\n\t\t\telse {\n\t\t\t\tif (jsonSchema.type !== \"string\") errors = addError(errors, `The \"${valibotAction.type}\" action is not supported on type \"${jsonSchema.type}\".`);\n\t\t\t\tjsonSchema.minLength = valibotAction.requirement;\n\t\t\t}\n\t\t\tbreak;\n\t\t}\n\t\tcase \"min_value\": {\n\t\t\tif (jsonSchema.type !== \"number\") errors = addError(errors, `The \"min_value\" action is not supported on type \"${jsonSchema.type}\".`);\n\t\t\tjsonSchema.minimum = valibotAction.requirement;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"multiple_of\": {\n\t\t\tjsonSchema.multipleOf = valibotAction.requirement;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"non_empty\": {\n\t\t\tif (jsonSchema.type === \"array\") jsonSchema.minItems = 1;\n\t\t\telse {\n\t\t\t\tif (jsonSchema.type !== \"string\") errors = addError(errors, `The \"${valibotAction.type}\" action is not supported on type \"${jsonSchema.type}\".`);\n\t\t\t\tjsonSchema.minLength = 1;\n\t\t\t}\n\t\t\tbreak;\n\t\t}\n\t\tcase \"regex\": {\n\t\t\tif (valibotAction.requirement.flags) errors = addError(errors, \"RegExp flags are not supported by JSON Schema.\");\n\t\t\tjsonSchema.pattern = valibotAction.requirement.source;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"title\": {\n\t\t\tjsonSchema.title = valibotAction.title;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"url\": {\n\t\t\tjsonSchema.format = \"uri\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"uuid\": {\n\t\t\tjsonSchema.format = \"uuid\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"value\": {\n\t\t\tjsonSchema.const = valibotAction.requirement;\n\t\t\tbreak;\n\t\t}\n\t\tdefault: errors = addError(errors, `The \"${valibotAction.type}\" action cannot be converted to JSON Schema.`);\n\t}\n\tif (config?.overrideAction) {\n\t\tconst actionOverride = config.overrideAction({\n\t\t\tvalibotAction,\n\t\t\tjsonSchema,\n\t\t\terrors\n\t\t});\n\t\tif (actionOverride) return { ...actionOverride };\n\t}\n\tif (errors) for (const message of errors) handleError(message, config);\n\treturn jsonSchema;\n}\n\n//#endregion\n//#region src/converters/convertSchema/convertSchema.ts\n/**\n* Flattens a Valibot pipe by recursively expanding nested pipes.\n*\n* @param pipe The pipeline to flatten.\n*\n* @returns A flat pipeline.\n*/\nfunction flattenPipe(pipe) {\n\treturn pipe.flatMap((item) => \"pipe\" in item ? flattenPipe(item.pipe) : item);\n}\nlet refCount = 0;\n/**\n* Converts any supported Valibot schema to the JSON Schema format.\n*\n* @param jsonSchema The JSON Schema object.\n* @param valibotSchema The Valibot schema object.\n* @param config The conversion configuration.\n* @param context The conversion context.\n* @param skipRef Whether to skip using a reference.\n*\n* @returns The converted JSON Schema.\n*/\nfunction convertSchema(jsonSchema, valibotSchema, config, context, skipRef = false) {\n\tif (!skipRef) {\n\t\tconst referenceId = context.referenceMap.get(valibotSchema);\n\t\tif (referenceId) {\n\t\t\tjsonSchema.$ref = `#/$defs/${referenceId}`;\n\t\t\tif (config?.overrideRef) {\n\t\t\t\tconst refOverride = config.overrideRef({\n\t\t\t\t\t...context,\n\t\t\t\t\treferenceId,\n\t\t\t\t\tvalibotSchema,\n\t\t\t\t\tjsonSchema\n\t\t\t\t});\n\t\t\t\tif (refOverride) jsonSchema.$ref = refOverride;\n\t\t\t}\n\t\t\treturn jsonSchema;\n\t\t}\n\t}\n\tif (\"pipe\" in valibotSchema) {\n\t\tconst flatPipe = flattenPipe(valibotSchema.pipe);\n\t\tlet startIndex = 0;\n\t\tlet stopIndex = flatPipe.length - 1;\n\t\tif (config?.typeMode === \"input\") {\n\t\t\tconst inputStopIndex = flatPipe.slice(1).findIndex((item) => item.kind === \"schema\" || item.kind === \"transformation\" && (item.type === \"find_item\" || item.type === \"parse_json\" || item.type === \"raw_transform\" || item.type === \"reduce_items\" || item.type === \"stringify_json\" || item.type === \"transform\"));\n\t\t\tif (inputStopIndex !== -1) stopIndex = inputStopIndex;\n\t\t} else if (config?.typeMode === \"output\") {\n\t\t\tconst outputStartIndex = flatPipe.findLastIndex((item) => item.kind === \"schema\");\n\t\t\tif (outputStartIndex !== -1) startIndex = outputStartIndex;\n\t\t}\n\t\tfor (let index = startIndex; index <= stopIndex; index++) {\n\t\t\tconst valibotPipeItem = flatPipe[index];\n\t\t\tif (valibotPipeItem.kind === \"schema\") {\n\t\t\t\tif (index > startIndex) handleError(\"Set the \\\"typeMode\\\" config to \\\"input\\\" or \\\"output\\\" to convert pipelines with multiple schemas.\", config);\n\t\t\t\tjsonSchema = convertSchema(jsonSchema, valibotPipeItem, config, context, true);\n\t\t\t} else jsonSchema = convertAction(jsonSchema, valibotPipeItem, config);\n\t\t}\n\t\treturn jsonSchema;\n\t}\n\tlet errors;\n\tswitch (valibotSchema.type) {\n\t\tcase \"boolean\": {\n\t\t\tjsonSchema.type = \"boolean\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"null\": {\n\t\t\tjsonSchema.type = \"null\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"number\": {\n\t\t\tjsonSchema.type = \"number\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"string\": {\n\t\t\tjsonSchema.type = \"string\";\n\t\t\tbreak;\n\t\t}\n\t\tcase \"array\": {\n\t\t\tjsonSchema.type = \"array\";\n\t\t\tjsonSchema.items = convertSchema({}, valibotSchema.item, config, context);\n\t\t\tbreak;\n\t\t}\n\t\tcase \"tuple\":\n\t\tcase \"tuple_with_rest\":\n\t\tcase \"loose_tuple\":\n\t\tcase \"strict_tuple\": {\n\t\t\tjsonSchema.type = \"array\";\n\t\t\tjsonSchema.items = [];\n\t\t\tjsonSchema.minItems = valibotSchema.items.length;\n\t\t\tfor (const item of valibotSchema.items) jsonSchema.items.push(convertSchema({}, item, config, context));\n\t\t\tif (valibotSchema.type === \"tuple_with_rest\") jsonSchema.additionalItems = convertSchema({}, valibotSchema.rest, config, context);\n\t\t\telse if (valibotSchema.type === \"strict_tuple\") jsonSchema.additionalItems = false;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"object\":\n\t\tcase \"object_with_rest\":\n\t\tcase \"loose_object\":\n\t\tcase \"strict_object\": {\n\t\t\tjsonSchema.type = \"object\";\n\t\t\tjsonSchema.properties = {};\n\t\t\tjsonSchema.required = [];\n\t\t\tfor (const key in valibotSchema.entries) {\n\t\t\t\tconst entry = valibotSchema.entries[key];\n\t\t\t\tjsonSchema.properties[key] = convertSchema({}, entry, config, context);\n\t\t\t\tif (entry.type !== \"nullish\" && entry.type !== \"optional\") jsonSchema.required.push(key);\n\t\t\t}\n\t\t\tif (valibotSchema.type === \"object_with_rest\") jsonSchema.additionalProperties = convertSchema({}, valibotSchema.rest, config, context);\n\t\t\telse if (valibotSchema.type === \"strict_object\") jsonSchema.additionalProperties = false;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"record\": {\n\t\t\tif (\"pipe\" in valibotSchema.key) errors = addError(errors, \"The \\\"record\\\" schema with a schema for the key that contains a \\\"pipe\\\" cannot be converted to JSON Schema.\");\n\t\t\tif (valibotSchema.key.type !== \"string\") errors = addError(errors, `The \"record\" schema with the \"${valibotSchema.key.type}\" schema for the key cannot be converted to JSON Schema.`);\n\t\t\tjsonSchema.type = \"object\";\n\t\t\tjsonSchema.additionalProperties = convertSchema({}, valibotSchema.value, config, context);\n\t\t\tbreak;\n\t\t}\n\t\tcase \"any\":\n\t\tcase \"unknown\": break;\n\t\tcase \"nullable\":\n\t\tcase \"nullish\": {\n\t\t\tjsonSchema.anyOf = [convertSchema({}, valibotSchema.wrapped, config, context), { type: \"null\" }];\n\t\t\tif (valibotSchema.default !== void 0) jsonSchema.default = v.getDefault(valibotSchema);\n\t\t\tbreak;\n\t\t}\n\t\tcase \"exact_optional\":\n\t\tcase \"optional\":\n\t\tcase \"undefinedable\": {\n\t\t\tjsonSchema = convertSchema(jsonSchema, valibotSchema.wrapped, config, context);\n\t\t\tif (valibotSchema.default !== void 0) jsonSchema.default = v.getDefault(valibotSchema);\n\t\t\tbreak;\n\t\t}\n\t\tcase \"literal\": {\n\t\t\tif (typeof valibotSchema.literal !== \"boolean\" && typeof valibotSchema.literal !== \"number\" && typeof valibotSchema.literal !== \"string\") errors = addError(errors, \"The value of the \\\"literal\\\" schema is not JSON compatible.\");\n\t\t\tjsonSchema.const = valibotSchema.literal;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"enum\": {\n\t\t\tjsonSchema.enum = valibotSchema.options;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"picklist\": {\n\t\t\tif (valibotSchema.options.some((option) => typeof option !== \"number\" && typeof option !== \"string\")) errors = addError(errors, \"An option of the \\\"picklist\\\" schema is not JSON compatible.\");\n\t\t\tjsonSchema.enum = valibotSchema.options;\n\t\t\tbreak;\n\t\t}\n\t\tcase \"union\":\n\t\tcase \"variant\": {\n\t\t\tjsonSchema.anyOf = valibotSchema.options.map((option) => convertSchema({}, option, config, context));\n\t\t\tbreak;\n\t\t}\n\t\tcase \"intersect\": {\n\t\t\tjsonSchema.allOf = valibotSchema.options.map((option) => convertSchema({}, option, config, context));\n\t\t\tbreak;\n\t\t}\n\t\tcase \"lazy\": {\n\t\t\tlet wrappedValibotSchema = context.getterMap.get(valibotSchema.getter);\n\t\t\tif (!wrappedValibotSchema) {\n\t\t\t\twrappedValibotSchema = valibotSchema.getter(void 0);\n\t\t\t\tcontext.getterMap.set(valibotSchema.getter, wrappedValibotSchema);\n\t\t\t}\n\t\t\tlet referenceId = context.referenceMap.get(wrappedValibotSchema);\n\t\t\tif (!referenceId) {\n\t\t\t\treferenceId = `${refCount++}`;\n\t\t\t\tcontext.referenceMap.set(wrappedValibotSchema, referenceId);\n\t\t\t\tcontext.definitions[referenceId] = convertSchema({}, wrappedValibotSchema, config, context, true);\n\t\t\t}\n\t\t\tjsonSchema.$ref = `#/$defs/${referenceId}`;\n\t\t\tif (config?.overrideRef) {\n\t\t\t\tconst refOverride = config.overrideRef({\n\t\t\t\t\t...context,\n\t\t\t\t\treferenceId,\n\t\t\t\t\tvalibotSchema: wrappedValibotSchema,\n\t\t\t\t\tjsonSchema\n\t\t\t\t});\n\t\t\t\tif (refOverride) jsonSchema.$ref = refOverride;\n\t\t\t}\n\t\t\tbreak;\n\t\t}\n\t\tdefault: errors = addError(errors, `The \"${valibotSchema.type}\" schema cannot be converted to JSON Schema.`);\n\t}\n\tif (config?.overrideSchema) {\n\t\tconst schemaOverride = config.overrideSchema({\n\t\t\t...context,\n\t\t\treferenceId: context.referenceMap.get(valibotSchema),\n\t\t\tvalibotSchema,\n\t\t\tjsonSchema,\n\t\t\terrors\n\t\t});\n\t\tif (schemaOverride) return { ...schemaOverride };\n\t}\n\tif (errors) for (const message of errors) handleError(message, config);\n\treturn jsonSchema;\n}\n\n//#endregion\n//#region src/storages/globalDefs/globalDefs.ts\nlet store;\n/**\n* Adds new definitions to the global schema definitions.\n*\n* @param definitions The schema definitions.\n*\n* @beta\n*/\nfunction addGlobalDefs(definitions) {\n\tstore = {\n\t\t...store ?? {},\n\t\t...definitions\n\t};\n}\n/**\n* Returns the current global schema definitions.\n*\n* @returns The schema definitions.\n*\n* @beta\n*/\nfunction getGlobalDefs() {\n\treturn store;\n}\n\n//#endregion\n//#region src/functions/toJsonSchema/toJsonSchema.ts\n/**\n* Converts a Valibot schema to the JSON Schema format.\n*\n* @param schema The Valibot schema object.\n* @param config The JSON Schema configuration.\n*\n* @returns The converted JSON Schema.\n*/\nfunction toJsonSchema(schema, config) {\n\tconst context = {\n\t\tdefinitions: {},\n\t\treferenceMap: new Map(),\n\t\tgetterMap: new Map()\n\t};\n\tconst definitions = config?.definitions ?? getGlobalDefs();\n\tif (definitions) {\n\t\tfor (const key in definitions) context.referenceMap.set(definitions[key], key);\n\t\tfor (const key in definitions) context.definitions[key] = convertSchema({}, definitions[key], config, context, true);\n\t}\n\tconst jsonSchema = convertSchema({ $schema: \"http://json-schema.org/draft-07/schema#\" }, schema, config, context);\n\tif (context.referenceMap.size) jsonSchema.$defs = context.definitions;\n\treturn jsonSchema;\n}\n\n//#endregion\n//#region src/functions/toJsonSchemaDefs/toJsonSchemaDefs.ts\n/**\n* Converts Valibot schema definitions to JSON Schema definitions.\n*\n* @param definitions The Valibot schema definitions.\n* @param config The JSON Schema configuration.\n*\n* @returns The converted JSON Schema definitions.\n*/\nfunction toJsonSchemaDefs(definitions, config) {\n\tconst context = {\n\t\tdefinitions: {},\n\t\treferenceMap: new Map(),\n\t\tgetterMap: new Map()\n\t};\n\tfor (const key in definitions) context.referenceMap.set(definitions[key], key);\n\tfor (const key in definitions) context.definitions[key] = convertSchema({}, definitions[key], config, context, true);\n\treturn context.definitions;\n}\n\n//#endregion\nexport { addGlobalDefs, getGlobalDefs, toJsonSchema, toJsonSchemaDefs };", "var s=Object.defineProperty;var m=(o,t)=>s(o,\"name\",{value:t,configurable:!0});import{toJsonSchema as a}from\"@valibot/to-json-schema\";const c=m((o,t)=>a(o,t),\"toJsonSchema\");export{c as toJsonSchema};\n"], "mappings": ";;;AAg9FA,SAAS,WAAW,QAAQ,SAAS,SAAS;AAC5C,SAAO,OAAO,OAAO,YAAY;AAAA;AAAA,IAE/B,OAAO,QAAQ,SAAS,OAAO;AAAA;AAAA;AAAA,IAG/B,OAAO;AAAA;AAEX;;;AC78FA,SAAS,SAAS,QAAQ,SAAS;AAClC,MAAI,QAAQ;AACX,WAAO,KAAK,OAAO;AACnB,WAAO;AAAA,EACR;AACA,SAAO,CAAC,OAAO;AAChB;AAUA,SAAS,YAAY,SAAS,QAAQ;AACrC,UAAQ,QAAQ,WAAW;AAAA,IAC1B,KAAK;AAAU;AAAA,IACf,KAAK,QAAQ;AACZ,cAAQ,KAAK,OAAO;AACpB;AAAA,IACD;AAAA,IACA;AAAS,YAAM,IAAI,MAAM,OAAO;AAAA,EACjC;AACD;AAaA,SAAS,cAAc,YAAY,eAAe,QAAQ;AACzD,MAAI,QAAQ,eAAe,SAAS,cAAc,IAAI,EAAG,QAAO;AAChE,MAAI;AACJ,UAAQ,cAAc,MAAM;AAAA,IAC3B,KAAK,UAAU;AACd,iBAAW,kBAAkB;AAC7B;AAAA,IACD;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,QAAQ;AACZ,iBAAW,UAAU,cAAc,YAAY;AAC/C;AAAA,IACD;AAAA,IACA,KAAK,eAAe;AACnB,iBAAW,cAAc,cAAc;AACvC;AAAA,IACD;AAAA,IACA,KAAK,SAAS;AACb,iBAAW,SAAS;AACpB;AAAA,IACD;AAAA,IACA,KAAK,SAAS;AACb,UAAI,WAAW,SAAS,QAAS,YAAW,WAAW;AAAA,WAClD;AACJ,YAAI,WAAW,SAAS,SAAU,UAAS,SAAS,QAAQ,QAAQ,cAAc,IAAI,sCAAsC,WAAW,IAAI,IAAI;AAC/I,mBAAW,YAAY;AAAA,MACxB;AACA;AAAA,IACD;AAAA,IACA,KAAK,WAAW;AACf,iBAAW,gBAAgB,cAAc;AACzC,iBAAW,gBAAgB,cAAc;AACzC;AAAA,IACD;AAAA,IACA,KAAK,WAAW;AACf,iBAAW,OAAO;AAClB;AAAA,IACD;AAAA,IACA,KAAK,QAAQ;AACZ,iBAAW,SAAS;AACpB;AAAA,IACD;AAAA,IACA,KAAK,QAAQ;AACZ,iBAAW,SAAS;AACpB;AAAA,IACD;AAAA,IACA,KAAK,YAAY;AAChB,iBAAW,SAAS;AACpB;AAAA,IACD;AAAA,IACA,KAAK;AAAA,IACL,KAAK,iBAAiB;AACrB,iBAAW,SAAS;AACpB;AAAA,IACD;AAAA,IACA,KAAK,YAAY;AAChB,iBAAW,SAAS;AACpB;AAAA,IACD;AAAA,IACA,KAAK,UAAU;AACd,UAAI,WAAW,SAAS,SAAS;AAChC,mBAAW,WAAW,cAAc;AACpC,mBAAW,WAAW,cAAc;AAAA,MACrC,OAAO;AACN,YAAI,WAAW,SAAS,SAAU,UAAS,SAAS,QAAQ,QAAQ,cAAc,IAAI,sCAAsC,WAAW,IAAI,IAAI;AAC/I,mBAAW,YAAY,cAAc;AACrC,mBAAW,YAAY,cAAc;AAAA,MACtC;AACA;AAAA,IACD;AAAA,IACA,KAAK,eAAe;AACnB,iBAAW,gBAAgB,cAAc;AACzC;AAAA,IACD;AAAA,IACA,KAAK,cAAc;AAClB,UAAI,WAAW,SAAS,QAAS,YAAW,WAAW,cAAc;AAAA,WAChE;AACJ,YAAI,WAAW,SAAS,SAAU,UAAS,SAAS,QAAQ,QAAQ,cAAc,IAAI,sCAAsC,WAAW,IAAI,IAAI;AAC/I,mBAAW,YAAY,cAAc;AAAA,MACtC;AACA;AAAA,IACD;AAAA,IACA,KAAK,aAAa;AACjB,UAAI,WAAW,SAAS,SAAU,UAAS,SAAS,QAAQ,oDAAoD,WAAW,IAAI,IAAI;AACnI,iBAAW,UAAU,cAAc;AACnC;AAAA,IACD;AAAA,IACA,KAAK,YAAY;AAChB,UAAI,OAAO,cAAc,SAAS,UAAU,SAAU,YAAW,QAAQ,cAAc,SAAS;AAChG,UAAI,OAAO,cAAc,SAAS,gBAAgB,SAAU,YAAW,cAAc,cAAc,SAAS;AAC5G,UAAI,MAAM,QAAQ,cAAc,SAAS,QAAQ,EAAG,YAAW,WAAW,cAAc,SAAS;AACjG;AAAA,IACD;AAAA,IACA,KAAK,eAAe;AACnB,iBAAW,gBAAgB,cAAc;AACzC;AAAA,IACD;AAAA,IACA,KAAK,cAAc;AAClB,UAAI,WAAW,SAAS,QAAS,YAAW,WAAW,cAAc;AAAA,WAChE;AACJ,YAAI,WAAW,SAAS,SAAU,UAAS,SAAS,QAAQ,QAAQ,cAAc,IAAI,sCAAsC,WAAW,IAAI,IAAI;AAC/I,mBAAW,YAAY,cAAc;AAAA,MACtC;AACA;AAAA,IACD;AAAA,IACA,KAAK,aAAa;AACjB,UAAI,WAAW,SAAS,SAAU,UAAS,SAAS,QAAQ,oDAAoD,WAAW,IAAI,IAAI;AACnI,iBAAW,UAAU,cAAc;AACnC;AAAA,IACD;AAAA,IACA,KAAK,eAAe;AACnB,iBAAW,aAAa,cAAc;AACtC;AAAA,IACD;AAAA,IACA,KAAK,aAAa;AACjB,UAAI,WAAW,SAAS,QAAS,YAAW,WAAW;AAAA,WAClD;AACJ,YAAI,WAAW,SAAS,SAAU,UAAS,SAAS,QAAQ,QAAQ,cAAc,IAAI,sCAAsC,WAAW,IAAI,IAAI;AAC/I,mBAAW,YAAY;AAAA,MACxB;AACA;AAAA,IACD;AAAA,IACA,KAAK,SAAS;AACb,UAAI,cAAc,YAAY,MAAO,UAAS,SAAS,QAAQ,gDAAgD;AAC/G,iBAAW,UAAU,cAAc,YAAY;AAC/C;AAAA,IACD;AAAA,IACA,KAAK,SAAS;AACb,iBAAW,QAAQ,cAAc;AACjC;AAAA,IACD;AAAA,IACA,KAAK,OAAO;AACX,iBAAW,SAAS;AACpB;AAAA,IACD;AAAA,IACA,KAAK,QAAQ;AACZ,iBAAW,SAAS;AACpB;AAAA,IACD;AAAA,IACA,KAAK,SAAS;AACb,iBAAW,QAAQ,cAAc;AACjC;AAAA,IACD;AAAA,IACA;AAAS,eAAS,SAAS,QAAQ,QAAQ,cAAc,IAAI,8CAA8C;AAAA,EAC5G;AACA,MAAI,QAAQ,gBAAgB;AAC3B,UAAM,iBAAiB,OAAO,eAAe;AAAA,MAC5C;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AACD,QAAI,eAAgB,QAAO,EAAE,GAAG,eAAe;AAAA,EAChD;AACA,MAAI,OAAQ,YAAW,WAAW,OAAQ,aAAY,SAAS,MAAM;AACrE,SAAO;AACR;AAWA,SAAS,YAAY,MAAM;AAC1B,SAAO,KAAK,QAAQ,CAAC,SAAS,UAAU,OAAO,YAAY,KAAK,IAAI,IAAI,IAAI;AAC7E;AACA,IAAI,WAAW;AAYf,SAAS,cAAc,YAAY,eAAe,QAAQ,SAAS,UAAU,OAAO;AACnF,MAAI,CAAC,SAAS;AACb,UAAM,cAAc,QAAQ,aAAa,IAAI,aAAa;AAC1D,QAAI,aAAa;AAChB,iBAAW,OAAO,WAAW,WAAW;AACxC,UAAI,QAAQ,aAAa;AACxB,cAAM,cAAc,OAAO,YAAY;AAAA,UACtC,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,QACD,CAAC;AACD,YAAI,YAAa,YAAW,OAAO;AAAA,MACpC;AACA,aAAO;AAAA,IACR;AAAA,EACD;AACA,MAAI,UAAU,eAAe;AAC5B,UAAM,WAAW,YAAY,cAAc,IAAI;AAC/C,QAAI,aAAa;AACjB,QAAI,YAAY,SAAS,SAAS;AAClC,QAAI,QAAQ,aAAa,SAAS;AACjC,YAAM,iBAAiB,SAAS,MAAM,CAAC,EAAE,UAAU,CAAC,SAAS,KAAK,SAAS,YAAY,KAAK,SAAS,qBAAqB,KAAK,SAAS,eAAe,KAAK,SAAS,gBAAgB,KAAK,SAAS,mBAAmB,KAAK,SAAS,kBAAkB,KAAK,SAAS,oBAAoB,KAAK,SAAS,YAAY;AAClT,UAAI,mBAAmB,GAAI,aAAY;AAAA,IACxC,WAAW,QAAQ,aAAa,UAAU;AACzC,YAAM,mBAAmB,SAAS,cAAc,CAAC,SAAS,KAAK,SAAS,QAAQ;AAChF,UAAI,qBAAqB,GAAI,cAAa;AAAA,IAC3C;AACA,aAAS,QAAQ,YAAY,SAAS,WAAW,SAAS;AACzD,YAAM,kBAAkB,SAAS,KAAK;AACtC,UAAI,gBAAgB,SAAS,UAAU;AACtC,YAAI,QAAQ,WAAY,aAAY,gGAAsG,MAAM;AAChJ,qBAAa,cAAc,YAAY,iBAAiB,QAAQ,SAAS,IAAI;AAAA,MAC9E,MAAO,cAAa,cAAc,YAAY,iBAAiB,MAAM;AAAA,IACtE;AACA,WAAO;AAAA,EACR;AACA,MAAI;AACJ,UAAQ,cAAc,MAAM;AAAA,IAC3B,KAAK,WAAW;AACf,iBAAW,OAAO;AAClB;AAAA,IACD;AAAA,IACA,KAAK,QAAQ;AACZ,iBAAW,OAAO;AAClB;AAAA,IACD;AAAA,IACA,KAAK,UAAU;AACd,iBAAW,OAAO;AAClB;AAAA,IACD;AAAA,IACA,KAAK,UAAU;AACd,iBAAW,OAAO;AAClB;AAAA,IACD;AAAA,IACA,KAAK,SAAS;AACb,iBAAW,OAAO;AAClB,iBAAW,QAAQ,cAAc,CAAC,GAAG,cAAc,MAAM,QAAQ,OAAO;AACxE;AAAA,IACD;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,gBAAgB;AACpB,iBAAW,OAAO;AAClB,iBAAW,QAAQ,CAAC;AACpB,iBAAW,WAAW,cAAc,MAAM;AAC1C,iBAAW,QAAQ,cAAc,MAAO,YAAW,MAAM,KAAK,cAAc,CAAC,GAAG,MAAM,QAAQ,OAAO,CAAC;AACtG,UAAI,cAAc,SAAS,kBAAmB,YAAW,kBAAkB,cAAc,CAAC,GAAG,cAAc,MAAM,QAAQ,OAAO;AAAA,eACvH,cAAc,SAAS,eAAgB,YAAW,kBAAkB;AAC7E;AAAA,IACD;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,iBAAiB;AACrB,iBAAW,OAAO;AAClB,iBAAW,aAAa,CAAC;AACzB,iBAAW,WAAW,CAAC;AACvB,iBAAW,OAAO,cAAc,SAAS;AACxC,cAAM,QAAQ,cAAc,QAAQ,GAAG;AACvC,mBAAW,WAAW,GAAG,IAAI,cAAc,CAAC,GAAG,OAAO,QAAQ,OAAO;AACrE,YAAI,MAAM,SAAS,aAAa,MAAM,SAAS,WAAY,YAAW,SAAS,KAAK,GAAG;AAAA,MACxF;AACA,UAAI,cAAc,SAAS,mBAAoB,YAAW,uBAAuB,cAAc,CAAC,GAAG,cAAc,MAAM,QAAQ,OAAO;AAAA,eAC7H,cAAc,SAAS,gBAAiB,YAAW,uBAAuB;AACnF;AAAA,IACD;AAAA,IACA,KAAK,UAAU;AACd,UAAI,UAAU,cAAc,IAAK,UAAS,SAAS,QAAQ,0GAA8G;AACzK,UAAI,cAAc,IAAI,SAAS,SAAU,UAAS,SAAS,QAAQ,iCAAiC,cAAc,IAAI,IAAI,0DAA0D;AACpL,iBAAW,OAAO;AAClB,iBAAW,uBAAuB,cAAc,CAAC,GAAG,cAAc,OAAO,QAAQ,OAAO;AACxF;AAAA,IACD;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAW;AAAA,IAChB,KAAK;AAAA,IACL,KAAK,WAAW;AACf,iBAAW,QAAQ,CAAC,cAAc,CAAC,GAAG,cAAc,SAAS,QAAQ,OAAO,GAAG,EAAE,MAAM,OAAO,CAAC;AAC/F,UAAI,cAAc,YAAY,OAAQ,YAAW,UAAY,WAAW,aAAa;AACrF;AAAA,IACD;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,iBAAiB;AACrB,mBAAa,cAAc,YAAY,cAAc,SAAS,QAAQ,OAAO;AAC7E,UAAI,cAAc,YAAY,OAAQ,YAAW,UAAY,WAAW,aAAa;AACrF;AAAA,IACD;AAAA,IACA,KAAK,WAAW;AACf,UAAI,OAAO,cAAc,YAAY,aAAa,OAAO,cAAc,YAAY,YAAY,OAAO,cAAc,YAAY,SAAU,UAAS,SAAS,QAAQ,2DAA6D;AACjO,iBAAW,QAAQ,cAAc;AACjC;AAAA,IACD;AAAA,IACA,KAAK,QAAQ;AACZ,iBAAW,OAAO,cAAc;AAChC;AAAA,IACD;AAAA,IACA,KAAK,YAAY;AAChB,UAAI,cAAc,QAAQ,KAAK,CAAC,WAAW,OAAO,WAAW,YAAY,OAAO,WAAW,QAAQ,EAAG,UAAS,SAAS,QAAQ,4DAA8D;AAC9L,iBAAW,OAAO,cAAc;AAChC;AAAA,IACD;AAAA,IACA,KAAK;AAAA,IACL,KAAK,WAAW;AACf,iBAAW,QAAQ,cAAc,QAAQ,IAAI,CAAC,WAAW,cAAc,CAAC,GAAG,QAAQ,QAAQ,OAAO,CAAC;AACnG;AAAA,IACD;AAAA,IACA,KAAK,aAAa;AACjB,iBAAW,QAAQ,cAAc,QAAQ,IAAI,CAAC,WAAW,cAAc,CAAC,GAAG,QAAQ,QAAQ,OAAO,CAAC;AACnG;AAAA,IACD;AAAA,IACA,KAAK,QAAQ;AACZ,UAAI,uBAAuB,QAAQ,UAAU,IAAI,cAAc,MAAM;AACrE,UAAI,CAAC,sBAAsB;AAC1B,+BAAuB,cAAc,OAAO,MAAM;AAClD,gBAAQ,UAAU,IAAI,cAAc,QAAQ,oBAAoB;AAAA,MACjE;AACA,UAAI,cAAc,QAAQ,aAAa,IAAI,oBAAoB;AAC/D,UAAI,CAAC,aAAa;AACjB,sBAAc,GAAG,UAAU;AAC3B,gBAAQ,aAAa,IAAI,sBAAsB,WAAW;AAC1D,gBAAQ,YAAY,WAAW,IAAI,cAAc,CAAC,GAAG,sBAAsB,QAAQ,SAAS,IAAI;AAAA,MACjG;AACA,iBAAW,OAAO,WAAW,WAAW;AACxC,UAAI,QAAQ,aAAa;AACxB,cAAM,cAAc,OAAO,YAAY;AAAA,UACtC,GAAG;AAAA,UACH;AAAA,UACA,eAAe;AAAA,UACf;AAAA,QACD,CAAC;AACD,YAAI,YAAa,YAAW,OAAO;AAAA,MACpC;AACA;AAAA,IACD;AAAA,IACA;AAAS,eAAS,SAAS,QAAQ,QAAQ,cAAc,IAAI,8CAA8C;AAAA,EAC5G;AACA,MAAI,QAAQ,gBAAgB;AAC3B,UAAM,iBAAiB,OAAO,eAAe;AAAA,MAC5C,GAAG;AAAA,MACH,aAAa,QAAQ,aAAa,IAAI,aAAa;AAAA,MACnD;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AACD,QAAI,eAAgB,QAAO,EAAE,GAAG,eAAe;AAAA,EAChD;AACA,MAAI,OAAQ,YAAW,WAAW,OAAQ,aAAY,SAAS,MAAM;AACrE,SAAO;AACR;AAIA,IAAI;AAqBJ,SAAS,gBAAgB;AACxB,SAAO;AACR;AAYA,SAAS,aAAa,QAAQ,QAAQ;AACrC,QAAM,UAAU;AAAA,IACf,aAAa,CAAC;AAAA,IACd,cAAc,oBAAI,IAAI;AAAA,IACtB,WAAW,oBAAI,IAAI;AAAA,EACpB;AACA,QAAM,cAAc,QAAQ,eAAe,cAAc;AACzD,MAAI,aAAa;AAChB,eAAW,OAAO,YAAa,SAAQ,aAAa,IAAI,YAAY,GAAG,GAAG,GAAG;AAC7E,eAAW,OAAO,YAAa,SAAQ,YAAY,GAAG,IAAI,cAAc,CAAC,GAAG,YAAY,GAAG,GAAG,QAAQ,SAAS,IAAI;AAAA,EACpH;AACA,QAAM,aAAa,cAAc,EAAE,SAAS,0CAA0C,GAAG,QAAQ,QAAQ,OAAO;AAChH,MAAI,QAAQ,aAAa,KAAM,YAAW,QAAQ,QAAQ;AAC1D,SAAO;AACR;;;AC9cA,IAAI,IAAE,OAAO;AAAe,IAAI,IAAE,CAAC,GAAE,MAAI,EAAE,GAAE,QAAO,EAAC,OAAM,GAAE,cAAa,KAAE,CAAC;AAAyD,IAAM,IAAE,EAAE,CAAC,GAAE,MAAI,aAAE,GAAE,CAAC,GAAE,cAAc;", "names": []}