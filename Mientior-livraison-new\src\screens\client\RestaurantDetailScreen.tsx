import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  FlatList,
  Alert,
  StatusBar,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { theme } from '../../constants/theme';
import { RestaurantImage } from '../../components/common/OptimizedImage';
import { useRestaurants } from '../../hooks/useRestaurants';
import { useProducts } from '../../hooks/useProducts';
import { useCart } from '../../hooks/useCart';
import { useLocation } from '../../hooks/useLocation';
import { Produit, merchant } from '../../types';

interface RouteParams {
  restaurantId: string;
}

export const RestaurantDetailScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { restaurantId } = route.params as RouteParams;
  
  const { location } = useLocation();
  const { getRestaurantById } = useRestaurants(location || undefined);
  const { products, loading, error, getCategories, getProductsByCategory } = useProducts(restaurantId);
  const { addItem, getItemQuantity, totalItems } = useCart();
  
  const [restaurant, setRestaurant] = useState<merchant | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [loadingRestaurant, setLoadingRestaurant] = useState(true);

  useEffect(() => {
    loadRestaurant();
  }, [restaurantId]);

  useEffect(() => {
    if (products.length > 0 && !selectedCategory) {
      const categories = getCategories();
      if (categories.length > 0) {
        setSelectedCategory(categories[0]);
      }
    }
  }, [products]);

  const loadRestaurant = async () => {
    try {
      setLoadingRestaurant(true);
      const restaurantData = await getRestaurantById(restaurantId);
      setRestaurant(restaurantData);
    } catch (error) {
      console.error('Erreur lors du chargement du restaurant:', error);
      Alert.alert('Erreur', 'Impossible de charger les informations du restaurant');
    } finally {
      setLoadingRestaurant(false);
    }
  };

  const handleAddToCart = (product: Produit) => {
    const success = addItem(product, 1);
    if (!success) {
      Alert.alert(
        'Impossible d\'ajouter',
        'Vous ne pouvez commander que dans un seul restaurant à la fois. Videz votre panier pour commander ici.'
      );
    }
  };

  const renderProductCard = ({ item }: { item: Produit }) => {
    const quantity = getItemQuantity(item.id);
    const price = item.prix_reduit || item.prix;
    const hasDiscount = item.prix_reduit && item.prix_reduit < item.prix;

    return (
      <View style={styles.productCard}>
        <View style={styles.productImageContainer}>
          {item.image_url ? (
            <Image source={{ uri: item.image_url }} style={styles.productImage as any} />
          ) : (
            <View style={[styles.productImage, styles.productImagePlaceholder]}>
              <Text style={styles.productImagePlaceholderText}>🍽️</Text>
            </View>
          )}
          {!item.is_disponible && (
            <View style={styles.unavailableBadge}>
              <Text style={styles.unavailableBadgeText}>Indisponible</Text>
            </View>
          )}
          {hasDiscount && (
            <View style={styles.discountBadge}>
              <Text style={styles.discountBadgeText}>
                -{Math.round(((item.prix - item.prix_reduit!) / item.prix) * 100)}%
              </Text>
            </View>
          )}
        </View>

        <View style={styles.productInfo}>
          <Text style={styles.productName} numberOfLines={2}>
            {item.nom}
          </Text>
          <Text style={styles.productDescription} numberOfLines={3}>
            {item.description || 'Délicieux produit de notre sélection'}
          </Text>

          <View style={styles.productFooter}>
            <View style={styles.priceContainer}>
              <Text style={styles.currentPrice}>{price} FCFA</Text>
              {hasDiscount && (
                <Text style={styles.originalPrice}>{item.prix} FCFA</Text>
              )}
            </View>

            <View style={styles.addToCartContainer}>
              {quantity > 0 ? (
                <View style={styles.quantityControls}>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => {/* Diminuer quantité */}}
                  >
                    <Text style={styles.quantityButtonText}>-</Text>
                  </TouchableOpacity>
                  <Text style={styles.quantityText}>{quantity}</Text>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => handleAddToCart(item)}
                  >
                    <Text style={styles.quantityButtonText}>+</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <TouchableOpacity
                  style={[
                    styles.addButton,
                    !item.is_disponible && styles.addButtonDisabled
                  ]}
                  onPress={() => handleAddToCart(item)}
                  disabled={!item.is_disponible}
                >
                  <Text style={styles.addButtonText}>Ajouter</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      </View>
    );
  };

  const renderCategoryTab = (category: string) => (
    <TouchableOpacity
      key={category}
      style={[
        styles.categoryTab,
        selectedCategory === category && styles.categoryTabActive
      ]}
      onPress={() => setSelectedCategory(category)}
    >
      <Text style={[
        styles.categoryTabText,
        selectedCategory === category && styles.categoryTabTextActive
      ]}>
        {category}
      </Text>
    </TouchableOpacity>
  );

  if (loadingRestaurant) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Chargement du restaurant...</Text>
      </View>
    );
  }

  if (!restaurant) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Restaurant non trouvé</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Retour</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const categories = getCategories();
  const filteredProducts = selectedCategory ? 
    getProductsByCategory(selectedCategory) : 
    products;

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={theme.colors.white} barStyle="dark-content" />
      
      {/* Header avec image du restaurant */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>

        <RestaurantImage
          source={restaurant.image_url ? { uri: restaurant.image_url } : undefined}
          style={styles.restaurantImage}
          accessibilityLabel={`Image du restaurant ${restaurant.nom}`}
        />

        {restaurant.is_active && (
          <View style={styles.openBadge}>
            <Text style={styles.openBadgeText}>Ouvert</Text>
          </View>
        )}
      </View>

      {/* Informations du restaurant */}
      <View style={styles.restaurantInfo}>
        <Text style={styles.restaurantName}>{restaurant.nom}</Text>
        <Text style={styles.restaurantDescription}>
          {restaurant.description || 'Restaurant de qualité'}
        </Text>

        <View style={styles.restaurantMeta}>
          <View style={styles.metaItem}>
            <Text style={styles.metaIcon}>⭐</Text>
            <Text style={styles.metaText}>
              {restaurant.note_moyenne ? restaurant.note_moyenne.toFixed(1) : '4.5'}
            </Text>
          </View>

          <View style={styles.metaItem}>
            <Text style={styles.metaIcon}>🕒</Text>
            <Text style={styles.metaText}>
              {restaurant.temps_preparation_moyen || 30}-{(restaurant.temps_preparation_moyen || 30) + 15} min
            </Text>
          </View>

          <View style={styles.metaItem}>
            <Text style={styles.metaIcon}>🚚</Text>
            <Text style={styles.metaText}>
              {restaurant.frais_livraison_base ? `${restaurant.frais_livraison_base} FCFA` : 'Gratuit'}
            </Text>
          </View>
        </View>
      </View>

      {/* Catégories */}
      {categories.length > 0 && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesContainer}
          contentContainerStyle={styles.categoriesContent}
        >
          {categories.map(renderCategoryTab)}
        </ScrollView>
      )}

      {/* Liste des produits */}
      <FlatList
        data={filteredProducts}
        renderItem={renderProductCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.productsContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          loading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Chargement des produits...</Text>
            </View>
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                {error ? error : 'Aucun produit disponible dans cette catégorie'}
              </Text>
            </View>
          )
        }
      />

      {/* Bouton panier flottant */}
      {totalItems > 0 && (
        <TouchableOpacity
          style={styles.cartButton}
          onPress={() => navigation.navigate('CartScreen' as never)}
        >
          <View style={styles.cartButtonContent}>
            <View style={styles.cartBadge}>
              <Text style={styles.cartBadgeText}>{totalItems}</Text>
            </View>
            <Text style={styles.cartButtonText}>Voir le panier</Text>
            <Text style={styles.cartButtonIcon}>🛒</Text>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  header: {
    position: 'relative',
    height: 200,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  backButtonText: {
    fontSize: 20,
    color: theme.colors.text.primary,
  },
  restaurantImage: {
    width: '100%',
    height: '100%',
  },
  restaurantImagePlaceholder: {
    backgroundColor: theme.colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  restaurantImagePlaceholderText: {
    fontSize: 60,
  },
  openBadge: {
    position: 'absolute',
    top: 50,
    right: 20,
    backgroundColor: theme.colors.success,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  openBadgeText: {
    color: theme.colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
  restaurantInfo: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border.light,
  },
  restaurantName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text.primary,
    marginBottom: 8,
  },
  restaurantDescription: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 16,
    lineHeight: 22,
  },
  restaurantMeta: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  metaText: {
    fontSize: 14,
    color: theme.colors.text.primary,
    fontWeight: '500',
  },
  categoriesContainer: {
    maxHeight: 50,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border.light,
  },
  categoriesContent: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: theme.colors.background.secondary,
  },
  categoryTabActive: {
    backgroundColor: theme.colors.primary,
  },
  categoryTabText: {
    fontSize: 14,
    color: theme.colors.text.primary,
    fontWeight: '500',
  },
  categoryTabTextActive: {
    color: theme.colors.white,
  },
  productsContainer: {
    padding: 20,
    paddingBottom: 100,
  },
  productCard: {
    flexDirection: 'row',
    backgroundColor: theme.colors.white,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  productImageContainer: {
    position: 'relative',
  },
  productImage: {
    width: 100,
    height: 100,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
  },
  productImagePlaceholder: {
    backgroundColor: theme.colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productImagePlaceholderText: {
    fontSize: 30,
  },
  unavailableBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: theme.colors.error,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  unavailableBadgeText: {
    color: theme.colors.white,
    fontSize: 10,
    fontWeight: '600',
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: theme.colors.success,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  discountBadgeText: {
    color: theme.colors.white,
    fontSize: 10,
    fontWeight: '600',
  },
  productInfo: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 4,
  },
  productDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 18,
    marginBottom: 8,
  },
  productFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  originalPrice: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textDecorationLine: 'line-through',
    marginLeft: 8,
  },
  addToCartContainer: {
    alignItems: 'flex-end',
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addButtonDisabled: {
    backgroundColor: theme.colors.textSecondary,
  },
  addButtonText: {
    color: theme.colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  quantityText: {
    marginHorizontal: 12,
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  cartButton: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    shadowColor: theme.colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  cartButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  cartBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: theme.colors.primary,
    fontSize: 12,
    fontWeight: 'bold',
  },
  cartButtonText: {
    color: theme.colors.white,
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  cartButtonIcon: {
    fontSize: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.error,
    textAlign: 'center',
    marginBottom: 20,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
});
