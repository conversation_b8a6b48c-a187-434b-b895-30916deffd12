{"version": 3, "names": ["lineString", "point", "convertDistance", "convertDistanceFn", "convertLength", "convertLengthFn", "distance", "nearestPointOnLine", "length", "AnimatedCoordinatesArray", "AnimatedRouteCoordinatesArray", "onInitialState", "coordinatesArray", "options", "end", "from", "fullRoute", "map", "coord", "onGetValue", "state", "actRoute", "onCalculate", "progress", "currentEnd", "to", "prevsum", "actsum", "i", "distconf", "slice", "minLineStringElements", "push", "current", "r", "or", "onStart", "toValue", "toDist", "console", "error", "along", "units", "ls", "warn", "nearest", "properties", "location", "result", "originalRoute"], "sourceRoot": "../../../src", "sources": ["classes/AnimatedRouteCoordinatesArray.js"], "mappings": ";;AAAA,SACEA,UAAU,EACVC,KAAK,EACLC,eAAe,IAAIC,iBAAiB;AAAE;AACtCC,aAAa,IAAIC,eAAe,QAC3B,eAAe;AACtB,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,kBAAkB,MAAM,6BAA6B;AAC5D,OAAOC,MAAM,MAAM,cAAc;AAEjC,OAAOC,wBAAwB,MAAM,4BAA4B;AAEjE,MAAML,aAAa,GAAGC,eAAe,IAAIF,iBAAiB;;AAE1D;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,6BAA6B,SAASD,wBAAwB,CAAC;EAC1E;AACF;AACA;AACA;AACA;AACA;AACA;EACEE,cAAcA,CAACC,gBAAgB,EAAEC,OAAO,GAAG,IAAI,EAAE;IAC/C,IAAIC,GAAG,GAAG;MAAEC,IAAI,EAAE;IAAE,CAAC;IACrB,IAAIF,OAAO,IAAIA,OAAO,CAACC,GAAG,EAAE;MAC1BA,GAAG,GAAGD,OAAO,CAACC,GAAG;IACnB;IACA,OAAO;MACLE,SAAS,EAAEJ,gBAAgB,CAACK,GAAG,CAAEC,KAAK,IAAK,CAACA,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAChEJ;IACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEK,UAAUA,CAACC,KAAK,EAAE;IAChB,OAAOA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACJ,SAAS;EAC1C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEM,WAAWA,CAACF,KAAK,EAAEG,QAAQ,EAAE;IAC3B,MAAM;MAAEP,SAAS;MAAEF;IAAI,CAAC,GAAGM,KAAK;IAChC,MAAMI,UAAU,GAAGV,GAAG,CAACC,IAAI,IAAI,GAAG,GAAGQ,QAAQ,CAAC,GAAGA,QAAQ,GAAGT,GAAG,CAACW,EAAE;IAElE,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,CAAC,GAAGZ,SAAS,CAACR,MAAM,GAAG,CAAC;IAC5B,OAAOmB,MAAM,GAAGH,UAAU,IAAII,CAAC,GAAG,CAAC,EAAE;MACnCF,OAAO,GAAGC,MAAM;MAChBA,MAAM,IAAIrB,QAAQ,CAChBL,KAAK,CAACe,SAAS,CAACY,CAAC,CAAC,CAAC,EACnB3B,KAAK,CAACe,SAAS,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC,EACvB,IAAI,CAACC,QACP,CAAC;MACDD,CAAC,IAAI,CAAC;IACR;IACA,IAAID,MAAM,IAAIH,UAAU,EAAE;MACxB,MAAMH,QAAQ,GAAG,CAAC,GAAGL,SAAS,CAACc,KAAK,CAAC,CAAC,EAAEF,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/C,MAAMG,qBAAqB,GAAG,CAAC;MAC/B,IAAIV,QAAQ,CAACb,MAAM,GAAGuB,qBAAqB,EAAE;QAC3CV,QAAQ,CAACW,IAAI,CAACX,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC5B;MACA,OAAO;QAAEL,SAAS;QAAEF,GAAG,EAAE;UAAE,GAAGA,GAAG;UAAEmB,OAAO,EAAET;QAAW,CAAC;QAAEH;MAAS,CAAC;IACtE;IACA,MAAMa,CAAC,GAAG,CAACV,UAAU,GAAGE,OAAO,KAAKC,MAAM,GAAGD,OAAO,CAAC;IACrD,MAAMS,EAAE,GAAG,GAAG,GAAGD,CAAC;;IAElB;IACA,MAAMb,QAAQ,GAAG,CACf,GAAGL,SAAS,CAACc,KAAK,CAAC,CAAC,EAAEF,CAAC,GAAG,CAAC,CAAC,EAC5B,CACEZ,SAAS,CAACY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGM,CAAC,GAAGlB,SAAS,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGO,EAAE,EAC9CnB,SAAS,CAACY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGM,CAAC,GAAGlB,SAAS,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGO,EAAE,CAC/C,CACF;IACD,OAAO;MAAEnB,SAAS;MAAEF,GAAG,EAAE;QAAE,GAAGA,GAAG;QAAEmB,OAAO,EAAET;MAAW,CAAC;MAAEH;IAAS,CAAC;EACtE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEe,OAAOA,CAAChB,KAAK,EAAEiB,OAAO,EAAE;IACtB,MAAM;MAAErB,SAAS;MAAEF;IAAI,CAAC,GAAGM,KAAK;IAChC,IAAIkB,MAAM;IACV,IAAI,CAACD,OAAO,CAACvB,GAAG,EAAE;MAChByB,OAAO,CAACC,KAAK,CACX,2EACF,CAAC;IACH;IACA,IAAIH,OAAO,CAACvB,GAAG,CAAC2B,KAAK,IAAI,IAAI,EAAE;MAC7B,MAAM;QAAEC;MAAM,CAAC,GAAGL,OAAO;MACzB,MAAMM,EAAE,GAAG3C,UAAU,CAACgB,SAAS,CAAC;MAChCsB,MAAM,GAAGlC,aAAa,CAACiC,OAAO,CAACvB,GAAG,CAAC2B,KAAK,EAAEC,KAAK,CAAC;MAChDJ,MAAM,GAAG9B,MAAM,CAACmC,EAAE,CAAC,GAAGL,MAAM;IAC9B;IACA,IAAIA,MAAM,IAAI,IAAI,EAAE;MAClB,IAAID,OAAO,CAACvB,GAAG,CAACb,KAAK,EAAE;QACrBsC,OAAO,CAACK,IAAI,CACV,gFACF,CAAC;MACH;IACF,CAAC,MAAM,IAAIP,OAAO,CAACvB,GAAG,CAACb,KAAK,EAAE;MAC5B,MAAM0C,EAAE,GAAG3C,UAAU,CAACgB,SAAS,CAAC;MAEhC,MAAM6B,OAAO,GAAGtC,kBAAkB,CAACoC,EAAE,EAAEN,OAAO,CAACvB,GAAG,CAACb,KAAK,CAAC;MACzDqC,MAAM,GAAG9B,MAAM,CAACmC,EAAE,CAAC,GAAGE,OAAO,CAACC,UAAU,CAACC,QAAQ;IACnD,CAAC,MAAM;MACLR,OAAO,CAACK,IAAI,CACV,uEACF,CAAC;IACH;IAEA,MAAMI,MAAM,GAAG;MACbhC,SAAS;MACTF,GAAG,EAAE;QACH,GAAGA,GAAG;QACNC,IAAI,EAAED,GAAG,CAACmB,OAAO,IAAI,IAAI,GAAGnB,GAAG,CAACmB,OAAO,GAAGnB,GAAG,CAACC,IAAI;QAClDU,EAAE,EAAEa;MACN;IACF,CAAC;IACD,OAAOU,MAAM;EACf;EAEA,IAAIC,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC7B,KAAK,CAACJ,SAAS;EAC7B;AACF;AAEA,eAAeN,6BAA6B", "ignoreList": []}