{"version": 3, "file": "Location.types.js", "sourceRoot": "", "sources": ["../src/Location.types.ts"], "names": [], "mappings": "AAEA,cAAc;AACd;;GAEG;AACH,MAAM,CAAN,IAAY,gBAyBX;AAzBD,WAAY,gBAAgB;IAC1B;;OAEG;IACH,2DAAU,CAAA;IACV;;OAEG;IACH,qDAAO,CAAA;IACP;;OAEG;IACH,+DAAY,CAAA;IACZ;;OAEG;IACH,uDAAQ,CAAA;IACR;;OAEG;IACH,6DAAW,CAAA;IACX;;OAEG;IACH,iFAAqB,CAAA;AACvB,CAAC,EAzBW,gBAAgB,KAAhB,gBAAgB,QAyB3B;AAED,cAAc;AACd;;GAEG;AACH,MAAM,CAAN,IAAY,oBA0BX;AA1BD,WAAY,oBAAoB;IAC9B;;OAEG;IACH,iEAAS,CAAA;IACT;;;OAGG;IACH,+FAAwB,CAAA;IACxB;;;OAGG;IACH,qEAAW,CAAA;IACX;;;OAGG;IACH,qFAAmB,CAAA;IACnB;;;;OAIG;IACH,uEAAY,CAAA;AACd,CAAC,EA1BW,oBAAoB,KAApB,oBAAoB,QA0B/B;AAED,cAAc;AACd;;GAEG;AACH,MAAM,CAAN,IAAY,2BASX;AATD,WAAY,2BAA2B;IACrC;;OAEG;IACH,+EAAS,CAAA;IACT;;OAEG;IACH,6EAAQ,CAAA;AACV,CAAC,EATW,2BAA2B,KAA3B,2BAA2B,QAStC;AAED,cAAc;AACd;;GAEG;AACH,MAAM,CAAN,IAAY,6BAaX;AAbD,WAAY,6BAA6B;IACvC;;OAEG;IACH,uFAAW,CAAA;IACX;;OAEG;IACH,qFAAU,CAAA;IACV;;OAEG;IACH,uFAAW,CAAA;AACb,CAAC,EAbW,6BAA6B,KAA7B,6BAA6B,QAaxC", "sourcesContent": ["import { PermissionResponse } from 'expo-modules-core';\n\n// @needsAudit\n/**\n * Enum with available location accuracies.\n */\nexport enum LocationAccuracy {\n  /**\n   * Accurate to the nearest three kilometers.\n   */\n  Lowest = 1,\n  /**\n   * Accurate to the nearest kilometer.\n   */\n  Low = 2,\n  /**\n   * Accurate to within one hundred meters.\n   */\n  Balanced = 3,\n  /**\n   * Accurate to within ten meters of the desired target.\n   */\n  High = 4,\n  /**\n   * The best level of accuracy available.\n   */\n  Highest = 5,\n  /**\n   * The highest possible accuracy that uses additional sensor data to facilitate navigation apps.\n   */\n  BestForNavigation = 6,\n}\n\n// @needsAudit\n/**\n * Enum with available activity types of background location tracking.\n */\nexport enum LocationActivityType {\n  /**\n   * Default activity type. Use it if there is no other type that matches the activity you track.\n   */\n  Other = 1,\n  /**\n   * Location updates are being used specifically during vehicular navigation to track location\n   * changes to the automobile.\n   */\n  AutomotiveNavigation = 2,\n  /**\n   * Use this activity type if you track fitness activities such as walking, running, cycling,\n   * and so on.\n   */\n  Fitness = 3,\n  /**\n   * Activity type for movements for other types of vehicular navigation that are not automobile\n   * related.\n   */\n  OtherNavigation = 4,\n  /**\n   * Intended for airborne activities. Fall backs to `ActivityType.Other` if\n   * unsupported.\n   * @platform ios\n   */\n  Airborne = 5,\n}\n\n// @needsAudit\n/**\n * A type of the event that geofencing task can receive.\n */\nexport enum LocationGeofencingEventType {\n  /**\n   * Emitted when the device entered observed region.\n   */\n  Enter = 1,\n  /**\n   * Occurs as soon as the device left observed region\n   */\n  Exit = 2,\n}\n\n// @needsAudit\n/**\n * State of the geofencing region that you receive through the geofencing task.\n */\nexport enum LocationGeofencingRegionState {\n  /**\n   * Indicates that the device position related to the region is unknown.\n   */\n  Unknown = 0,\n  /**\n   * Indicates that the device is inside the region.\n   */\n  Inside = 1,\n  /**\n   * Inverse of inside state.\n   */\n  Outside = 2,\n}\n\n// @needsAudit\n/**\n * Type representing options argument in `getCurrentPositionAsync`.\n */\nexport type LocationOptions = {\n  /**\n   * Location manager accuracy. Pass one of `Accuracy` enum values.\n   * For low-accuracies the implementation can avoid geolocation providers\n   * that consume a significant amount of power (such as GPS).\n   *\n   * @default LocationAccuracy.Balanced\n   */\n  accuracy?: LocationAccuracy;\n  /**\n   * Specifies whether to ask the user to turn on improved accuracy location mode\n   * which uses Wi-Fi, cell networks and GPS sensor.\n   * @default true\n   * @platform android\n   */\n  mayShowUserSettingsDialog?: boolean;\n  /**\n   * Minimum time to wait between each update in milliseconds.\n   * Default value may depend on `accuracy` option.\n   * @platform android\n   */\n  timeInterval?: number;\n  /**\n   * Receive updates only when the location has changed by at least this distance in meters.\n   * Default value may depend on `accuracy` option.\n   */\n  distanceInterval?: number;\n};\n\n// @needsAudit\n/**\n * Type representing options object that can be passed to `getLastKnownPositionAsync`.\n */\nexport type LocationLastKnownOptions = {\n  /**\n   * A number of milliseconds after which the last known location starts to be invalid and thus\n   * `null` is returned.\n   */\n  maxAge?: number;\n  /**\n   * The maximum radius of uncertainty for the location, measured in meters. If the last known\n   * location's accuracy radius is bigger (less accurate) then `null` is returned.\n   */\n  requiredAccuracy?: number;\n};\n\n// @needsAudit\n/**\n * Type representing background location task options.\n */\nexport type LocationTaskOptions = LocationOptions & {\n  /**\n   * A boolean indicating whether the status bar changes its appearance when\n   * location services are used in the background.\n   * @default false\n   * @platform ios\n   */\n  showsBackgroundLocationIndicator?: boolean;\n  /**\n   * The distance in meters that must occur between last reported location and the current location\n   * before deferred locations are reported.\n   * @default 0\n   */\n  deferredUpdatesDistance?: number;\n  // @docsMissing\n  deferredUpdatesTimeout?: number;\n  /**\n   * Minimum time interval in milliseconds that must pass since last reported location before all\n   * later locations are reported in a batched update\n   * @default 0\n   */\n  deferredUpdatesInterval?: number;\n  /**\n   * The type of user activity associated with the location updates.\n   * @see See [Apple docs](https://developer.apple.com/documentation/corelocation/cllocationmanager/1620567-activitytype) for more details.\n   * @default ActivityType.Other\n   * @platform ios\n   */\n  activityType?: LocationActivityType;\n  /**\n   * A boolean value indicating whether the location manager can pause location\n   * updates to improve battery life without sacrificing location data. When this option is set to\n   * `true`, the location manager pauses updates (and powers down the appropriate hardware) at times\n   * when the location data is unlikely to change. You can help the determination of when to pause\n   * location updates by assigning a value to the `activityType` property.\n   * @default false\n   * @platform ios\n   */\n  pausesUpdatesAutomatically?: boolean;\n  foregroundService?: LocationTaskServiceOptions;\n};\n\n// @needsAudit\nexport type LocationTaskServiceOptions = {\n  /**\n   * Title of the foreground service notification.\n   */\n  notificationTitle: string;\n  /**\n   * Subtitle of the foreground service notification.\n   */\n  notificationBody: string;\n  /**\n   * Color of the foreground service notification. Accepts `#RRGGBB` and `#AARRGGBB` hex formats.\n   */\n  notificationColor?: string;\n  /**\n   * Boolean value whether to destroy the foreground service if the app is killed.\n   */\n  killServiceOnDestroy?: boolean;\n};\n\n// @needsAudit\n/**\n * Type representing geofencing region object.\n */\nexport type LocationRegion = {\n  /**\n   * The identifier of the region object. Defaults to auto-generated UUID hash.\n   */\n  identifier?: string;\n  /**\n   * The latitude in degrees of region's center point.\n   */\n  latitude: number;\n  /**\n   * The longitude in degrees of region's center point.\n   */\n  longitude: number;\n  /**\n   * The radius measured in meters that defines the region's outer boundary.\n   */\n  radius: number;\n  /**\n   * Boolean value whether to call the task if the device enters the region.\n   * @default true\n   */\n  notifyOnEnter?: boolean;\n  /**\n   * Boolean value whether to call the task if the device exits the region.\n   * @default true\n   */\n  notifyOnExit?: boolean;\n  /**\n   * One of [GeofencingRegionState](#geofencingregionstate) region state. Determines whether the\n   * device is inside or outside a region.\n   */\n  state?: LocationGeofencingRegionState;\n};\n\n// @needsAudit\n/**\n * Type representing the location object.\n */\nexport type LocationObject = {\n  /**\n   * The coordinates of the position.\n   */\n  coords: LocationObjectCoords;\n  /**\n   * The time at which this position information was obtained, in milliseconds since epoch.\n   */\n  timestamp: number;\n  /**\n   * Whether the location coordinates is mocked or not.\n   * @platform android\n   */\n  mocked?: boolean;\n};\n\n// @needsAudit\n/**\n * Type representing the location GPS related data.\n */\nexport type LocationObjectCoords = {\n  /**\n   * The latitude in degrees.\n   */\n  latitude: number;\n  /**\n   * The longitude in degrees.\n   */\n  longitude: number;\n  /**\n   * The altitude in meters above the WGS 84 reference ellipsoid. Can be `null` on Web if it's not available.\n   */\n  altitude: number | null;\n  /**\n   * The radius of uncertainty for the location, measured in meters. Can be `null` on Web if it's not available.\n   */\n  accuracy: number | null;\n  /**\n   * The accuracy of the altitude value, in meters. Can be `null` on Web if it's not available.\n   */\n  altitudeAccuracy: number | null;\n  /**\n   * Horizontal direction of travel of this device, measured in degrees starting at due north and\n   * continuing clockwise around the compass. Thus, north is 0 degrees, east is 90 degrees, south is\n   * 180 degrees, and so on. Can be `null` on Web if it's not available.\n   */\n  heading: number | null;\n  /**\n   * The instantaneous speed of the device in meters per second. Can be `null` on Web if it's not available.\n   */\n  speed: number | null;\n};\n\n// @needsAudit\n/**\n * Represents `watchPositionAsync` callback.\n */\nexport type LocationCallback = (location: LocationObject) => any;\n\n/**\n * Error callback for location methods.\n */\nexport type LocationErrorCallback = (reason: string) => void;\n\n// @needsAudit\n/**\n * Represents the object containing details about location provider.\n */\nexport type LocationProviderStatus = {\n  /**\n   * Whether location services are enabled. See [Location.hasServicesEnabledAsync](#locationhasservicesenabledasync)\n   * for a more convenient solution to get this value.\n   */\n  locationServicesEnabled: boolean;\n  // @docsMissing\n  backgroundModeEnabled: boolean;\n  /**\n   * Whether the GPS provider is available. If `true` the location data will come\n   * from GPS, especially for requests with high accuracy.\n   * @platform android\n   */\n  gpsAvailable?: boolean;\n  /**\n   * Whether the network provider is available. If `true` the location data will\n   * come from cellular network, especially for requests with low accuracy.\n   * @platform android\n   */\n  networkAvailable?: boolean;\n  /**\n   * Whether the passive provider is available. If `true` the location data will\n   * be determined passively.\n   * @platform android\n   */\n  passiveAvailable?: boolean;\n};\n\n// @needsAudit\n/**\n * Type of the object containing heading details and provided by `watchHeadingAsync` callback.\n */\nexport type LocationHeadingObject = {\n  /**\n   * Measure of true north in degrees (needs location permissions, will return `-1` if not given).\n   */\n  trueHeading: number;\n  /**\n   * Measure of magnetic north in degrees.\n   */\n  magHeading: number;\n  /**\n   * Level of calibration of compass:\n   * - `3`: high accuracy\n   * - `2`: medium accuracy\n   * - `1`: low accuracy\n   * - `0`: none\n   *\n   * Reference for iOS:\n   * - `3`: < 20 degrees uncertainty\n   * - `2`: < 35 degrees\n   * - `1`: < 50 degrees\n   * - `0`: > 50 degrees\n   */\n  accuracy: number;\n};\n\n// @needsAudit\n/**\n * Represents `watchHeadingAsync` callback.\n */\nexport type LocationHeadingCallback = (location: LocationHeadingObject) => any;\n\n// @needsAudit\n/**\n * Type representing a result of `geocodeAsync`.\n */\nexport type LocationGeocodedLocation = {\n  /**\n   * The latitude in degrees.\n   */\n  latitude: number;\n  /**\n   * The longitude in degrees.\n   */\n  longitude: number;\n  /**\n   * The altitude in meters above the WGS 84 reference ellipsoid.\n   */\n  altitude?: number;\n  /**\n   * The radius of uncertainty for the location, measured in meters.\n   */\n  accuracy?: number;\n};\n\n// @needsAudit\n/**\n * Type representing a result of `reverseGeocodeAsync`.\n */\nexport type LocationGeocodedAddress = {\n  /**\n   * City name of the address.\n   */\n  city: string | null;\n  /**\n   * Additional city-level information like district name.\n   */\n  district: string | null;\n  /**\n   * Street number of the address.\n   */\n  streetNumber: string | null;\n  /**\n   * Street name of the address.\n   */\n  street: string | null;\n  /**\n   * The state or province associated with the address.\n   */\n  region: string | null;\n  /**\n   * Additional information about administrative area.\n   */\n  subregion: string | null;\n  /**\n   * Localized country name of the address.\n   */\n  country: string | null;\n  /**\n   * Postal code of the address.\n   */\n  postalCode: string | null;\n  /**\n   * The name of the placemark, for example, \"Tower Bridge\".\n   */\n  name: string | null;\n  /**\n   * Localized (ISO) country code of the address, if available.\n   */\n  isoCountryCode: string | null;\n  /**\n   * The timezone identifier associated with the address.\n   * @platform ios\n   */\n  timezone: string | null;\n  /**\n   * Composed string of the address components, for example, \"111 8th Avenue, New York, NY\".\n   * @platform android\n   */\n  formattedAddress: string | null;\n};\n\n// @needsAudit\n/**\n * Represents subscription object returned by methods watching for new locations or headings.\n */\nexport type LocationSubscription = {\n  /**\n   * Call this function with no arguments to remove this subscription. The callback will no longer\n   * be called for location updates.\n   */\n  remove: () => void;\n};\n\n// @needsAudit\nexport type PermissionDetailsLocationIOS = {\n  /**\n   * The scope of granted permission. Indicates when it's possible to use location.\n   */\n  scope: 'whenInUse' | 'always' | 'none';\n};\n\n// @needsAudit\nexport type PermissionDetailsLocationAndroid = {\n  /**\n   * Indicates the type of location provider.\n   */\n  accuracy: 'fine' | 'coarse' | 'none';\n};\n\n// @needsAudit\n/**\n * `LocationPermissionResponse` extends [`PermissionResponse`](#permissionresponse)\n * type exported by `expo-modules-core` and contains additional platform-specific fields.\n */\nexport type LocationPermissionResponse = PermissionResponse & {\n  ios?: PermissionDetailsLocationIOS;\n  android?: PermissionDetailsLocationAndroid;\n};\n\nexport type { PermissionResponse };\n"]}