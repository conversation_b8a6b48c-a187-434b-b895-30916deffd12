import { useState, useEffect, useRef } from 'react';
import { Location as LocationType } from '../types';

interface LocationState {
  location: LocationType | null;
  loading: boolean;
  error: string | null;
  hasPermission: boolean;
}

export const useLocation = (): LocationState & {
  requestPermission: () => Promise<boolean>;
  getCurrentLocation: () => Promise<void>;
  watchLocation: () => Promise<{ remove: () => void } | null>;
  clearWatch: () => void;
  requestLocation: () => Promise<void>;
} => {
  const [state, setState] = useState<LocationState>({
    location: null,
    loading: false,
    error: null,
    hasPermission: true, // Simuler permission accordée
  });

  const watchSubscription = useRef<{ remove: () => void } | null>(null);

  const checkPermissions = async () => {
    // Simulation - toujours accordée
    setState(prev => ({ ...prev, hasPermission: true }));
  };

  const requestPermission = async (): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // Simulation d'une demande de permission
      await new Promise(resolve => setTimeout(resolve, 500));

      setState(prev => ({ ...prev, hasPermission: true, loading: false }));
      await getCurrentLocation();

      return true;
    } catch (error) {
      console.error('Erreur demande permission:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Erreur lors de la demande de permission',
        hasPermission: false
      }));
      return false;
    }
  };

  const getCurrentLocation = async (): Promise<void> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // Simulation d'obtention de position (Cotonou, Bénin)
      await new Promise(resolve => setTimeout(resolve, 1000));

      const locationData: LocationType = {
        latitude: 6.3702 + (Math.random() - 0.5) * 0.01,
        longitude: 2.3912 + (Math.random() - 0.5) * 0.01,
        accuracy: 10 + Math.random() * 20,
        timestamp: Date.now(),
      };

      setState(prev => ({
        ...prev,
        location: locationData,
        loading: false,
        error: null
      }));
    } catch (error) {
      console.error('Erreur obtention position:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Impossible d\'obtenir votre position'
      }));
    }
  };

  const watchLocation = async (): Promise<{ remove: () => void } | null> => {
    try {
      if (!state.hasPermission) {
        console.warn('Permission de localisation non accordée');
        return null;
      }

      // Simulation de surveillance de position
      const interval = setInterval(() => {
        const locationData: LocationType = {
          latitude: 6.3702 + (Math.random() - 0.5) * 0.005,
          longitude: 2.3912 + (Math.random() - 0.5) * 0.005,
          accuracy: 10 + Math.random() * 15,
          timestamp: Date.now(),
        };

        setState(prev => ({
          ...prev,
          location: locationData,
          error: null
        }));
      }, 5000);

      return {
        remove: () => clearInterval(interval)
      };
    } catch (error) {
      console.error('Erreur surveillance position:', error);
      setState(prev => ({
        ...prev,
        error: 'Erreur lors de la surveillance de position'
      }));
      return null;
    }
  };

  const clearWatch = () => {
    if (watchSubscription.current) {
      watchSubscription.current.remove();
      watchSubscription.current = null;
    }
  };

  useEffect(() => {
    checkPermissions();

    return () => {
      clearWatch();
    };
  }, []);

  // Alias pour compatibilité
  const requestLocation = getCurrentLocation;

  return {
    ...state,
    requestPermission,
    getCurrentLocation,
    watchLocation,
    clearWatch,
    requestLocation,
  };
};
