import {
  Agent
} from "./chunk-M5C2KFXU.js";
import {
  notImplemented,
  notImplementedClass
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/https.mjs
var Server = notImplementedClass("https.Server");
var Agent2 = Agent;
var globalAgent = new Agent2();
var get = notImplemented("https.get");
var createServer = notImplemented("https.createServer");
var request = notImplemented("https.request");
var https_default = {
  Server,
  Agent: Agent2,
  globalAgent,
  get,
  createServer,
  request
};
export {
  Agent2 as Agent,
  Server,
  createServer,
  https_default as default,
  get,
  globalAgent,
  request
};
//# sourceMappingURL=unenv_node_https.js.map
