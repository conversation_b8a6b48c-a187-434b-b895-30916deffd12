{"version": 3, "names": ["React", "forwardRef", "memo", "useImperativeHandle", "useRef", "RNMBXLightNativeComponent", "transformStyle", "nativeRef", "jsx", "_jsx", "Light", "props", "ref", "style", "propWithoutStyle", "nativeLightRef", "setNativeProps", "_props", "propsToPass", "reactStyle", "current", "testID"], "sourceRoot": "../../../src", "sources": ["components/Light.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,IAAI,EAAEC,mBAAmB,EAAEC,MAAM,QAAQ,OAAO;AAE5E,OAAOC,yBAAyB,MAAM,oCAAoC;AAG1E,SAASC,cAAc,QAAQ,qBAAqB;AACpD,OAAOC,SAAS,MAAM,oBAAoB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAa3C;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAY,EAAEC,GAAqC,EAAE;EAClE,MAAM;IAAEC,KAAK;IAAE,GAAGC;EAAiB,CAAC,GAAGH,KAAK;EAE5C,MAAMI,cAAc,GAAGR,SAAS,CAACH,MAAM,CAAC,IAAI,CAAC,CAAC;EAE9CD,mBAAmB,CAACS,GAAG,EAAE,OAAO;IAC9BI,cAAcA,CAACC,MAAkC,EAAE;MACjD,IAAIC,WAAW,GAAGD,MAAM;MACxB,IAAIA,MAAM,CAACJ,KAAK,EAAE;QAChBK,WAAW,GAAG;UACZ,GAAGD,MAAM;UACTE,UAAU,EAAEb,cAAc,CAACW,MAAM,CAACJ,KAAK;QACzC,CAAC;MACH;MACAE,cAAc,CAACK,OAAO,EAAEJ,cAAc,CAACE,WAAW,CAAC;IACrD;EACF,CAAC,CAAC,CAAC;EAEH,oBACET,IAAA,CAACJ;EACC;EAAA;IACAO,GAAG,EAAEG,cAAe;IACpBM,MAAM,EAAC,YAAY;IAAA,GACfP,gBAAgB;IACpBK,UAAU,EAAEb,cAAc,CAACO,KAAK;EAAE,CACnC,CAAC;AAEN;AAEA,4BAAeX,IAAI,cAACD,UAAU,CAACS,KAAK,CAAC,CAAC", "ignoreList": []}