{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/http/agent.mjs"], "sourcesContent": ["import { EventEmitter } from \"node:events\";\nexport class Agent extends EventEmitter {\n\t__unenv__ = {};\n\tmaxFreeSockets = 256;\n\tmaxSockets = Infinity;\n\tmaxTotalSockets = Infinity;\n\tfreeSockets = {};\n\tsockets = {};\n\trequests = {};\n\toptions;\n\tconstructor(opts = {}) {\n\t\tsuper();\n\t\tthis.options = opts;\n\t}\n\tdestroy() {}\n}\n"], "mappings": ";AAAA,SAAS,oBAAoB;AACtB,IAAM,QAAN,cAAoB,aAAa;AAAA,EACvC,YAAY,CAAC;AAAA,EACb,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,cAAc,CAAC;AAAA,EACf,UAAU,CAAC;AAAA,EACX,WAAW,CAAC;AAAA,EACZ;AAAA,EACA,YAAY,OAAO,CAAC,GAAG;AACtB,UAAM;AACN,SAAK,UAAU;AAAA,EAChB;AAAA,EACA,UAAU;AAAA,EAAC;AACZ;", "names": []}