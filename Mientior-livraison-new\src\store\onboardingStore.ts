import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface OnboardingState {
  // Workflow state
  hasSeenOnboarding: boolean;
  hasCompletedLanguageSelection: boolean;
  hasCompletedLocationPermission: boolean;
  selectedLanguage: string;
  locationPermissionGranted: boolean;
  
  // Actions
  completeOnboarding: () => void;
  completeLanguageSelection: (language: string) => void;
  completeLocationPermission: (granted: boolean) => void;
  resetOnboarding: () => void;
  
  // Getters
  shouldShowOnboarding: () => boolean;
  getNextScreen: () => string;
}

export const useOnboardingStore = create<OnboardingState>()(
  persist(
    (set, get) => ({
      // Initial state
      hasSeenOnboarding: false,
      hasCompletedLanguageSelection: false,
      hasCompletedLocationPermission: false,
      selectedLanguage: 'fr',
      locationPermissionGranted: false,

      // Actions
      completeOnboarding: () => {
        set({ hasSeenOnboarding: true });
        console.log('✅ Onboarding marqué comme terminé');
      },

      completeLanguageSelection: (language: string) => {
        set({ 
          hasCompletedLanguageSelection: true,
          selectedLanguage: language 
        });
        console.log(`✅ Langue sélectionnée: ${language}`);
      },

      completeLocationPermission: (granted: boolean) => {
        set({ 
          hasCompletedLocationPermission: true,
          locationPermissionGranted: granted 
        });
        console.log(`✅ Permission localisation: ${granted ? 'accordée' : 'refusée'}`);
      },

      resetOnboarding: () => {
        set({
          hasSeenOnboarding: false,
          hasCompletedLanguageSelection: false,
          hasCompletedLocationPermission: false,
          selectedLanguage: 'fr',
          locationPermissionGranted: false,
        });
        console.log('🔄 Onboarding réinitialisé');
      },

      // Getters
      shouldShowOnboarding: () => {
        const state = get();
        return !state.hasSeenOnboarding;
      },

      getNextScreen: () => {
        const state = get();
        
        if (!state.hasSeenOnboarding) {
          return 'Onboarding';
        }
        
        if (!state.hasCompletedLanguageSelection) {
          return 'LanguageSelection';
        }
        
        if (!state.hasCompletedLocationPermission) {
          return 'LocationPermission';
        }
        
        return 'AuthChoiceScreen';
      },
    }),
    {
      name: 'onboarding-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        hasSeenOnboarding: state.hasSeenOnboarding,
        hasCompletedLanguageSelection: state.hasCompletedLanguageSelection,
        hasCompletedLocationPermission: state.hasCompletedLocationPermission,
        selectedLanguage: state.selectedLanguage,
        locationPermissionGranted: state.locationPermissionGranted,
      }),
    }
  )
);

// Hook pour faciliter l'utilisation
export const useOnboardingFlow = () => {
  const store = useOnboardingStore();
  
  return {
    ...store,
    // Méthodes utilitaires
    isOnboardingComplete: () => {
      return store.hasSeenOnboarding && 
             store.hasCompletedLanguageSelection && 
             store.hasCompletedLocationPermission;
    },
    
    getWorkflowProgress: () => {
      let completed = 0;
      let total = 3;
      
      if (store.hasSeenOnboarding) completed++;
      if (store.hasCompletedLanguageSelection) completed++;
      if (store.hasCompletedLocationPermission) completed++;
      
      return {
        completed,
        total,
        percentage: Math.round((completed / total) * 100)
      };
    }
  };
};
