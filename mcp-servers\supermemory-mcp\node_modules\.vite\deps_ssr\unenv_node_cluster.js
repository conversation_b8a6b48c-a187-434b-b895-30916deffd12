import {
  notImplemented
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/cluster.mjs
import { EventEmitter } from "node:events";
var SCHED_NONE = 1;
var SCHED_RR = 2;
var isMaster = true;
var isPrimary = true;
var isWorker = false;
var schedulingPolicy = SCHED_RR;
var settings = {};
var workers = {};
var fork = notImplemented("cluster.fork");
var disconnect = notImplemented("cluster.disconnect");
var setupPrimary = notImplemented("cluster.setupPrimary");
var setupMaster = notImplemented("cluster.setupMaster");
var _events = [];
var _eventsCount = 0;
var _maxListeners = 0;
var Worker = class extends EventEmitter {
  _connected = false;
  id = 0;
  get process() {
    return globalThis.process;
  }
  get exitedAfterDisconnect() {
    return this._connected;
  }
  isConnected() {
    return this._connected;
  }
  isDead() {
    return true;
  }
  send(message, sendHandle, options, callback) {
    return false;
  }
  kill(signal) {
    this._connected = false;
  }
  destroy(signal) {
    this._connected = false;
  }
  disconnect() {
    this._connected = false;
  }
};
var _Cluster = class extends EventEmitter {
  Worker = Worker;
  isMaster = isMaster;
  isPrimary = isPrimary;
  isWorker = isWorker;
  SCHED_NONE = SCHED_NONE;
  SCHED_RR = SCHED_RR;
  schedulingPolicy = SCHED_RR;
  settings = settings;
  workers = workers;
  setupPrimary() {
    return setupPrimary();
  }
  setupMaster() {
    return setupPrimary();
  }
  disconnect() {
    return disconnect();
  }
  fork() {
    return fork();
  }
};
var cluster_default = new _Cluster();
export {
  SCHED_NONE,
  SCHED_RR,
  Worker,
  _events,
  _eventsCount,
  _maxListeners,
  cluster_default as default,
  disconnect,
  fork,
  isMaster,
  isPrimary,
  isWorker,
  schedulingPolicy,
  settings,
  setupMaster,
  setupPrimary,
  workers
};
//# sourceMappingURL=unenv_node_cluster.js.map
