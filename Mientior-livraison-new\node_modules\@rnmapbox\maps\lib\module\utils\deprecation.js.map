{"version": 3, "names": ["deprecatedClass", "origClass", "deprecationMessage", "result", "constructor", "args", "console", "log", "copyPropertiesAsDeprecated", "origObject", "newObject", "onDeprecatedCalled", "accessors", "key", "value", "Object", "entries", "defineProperty", "get"], "sourceRoot": "../../../src", "sources": ["utils/deprecation.ts"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAO,SAASA,eAAeA,CAC7BC,SAAY,EACZC,kBAA0B,EACvB;EACH,MAAMC,MAAM,GAAG,cAAcF,SAAS,CAAC;IACrCG,WAAWA,CAAC,GAAGC,IAAW,EAAE;MAC1BC,OAAO,CAACC,GAAG,CAAC,eAAeL,kBAAkB,EAAE,CAAC;MAChD,KAAK,CAAC,GAAGG,IAAI,CAAC;IAChB;EACF,CAAC;EACD,OAAOF,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASK,0BAA0BA,CAIxCC,UAA0B,EAC1BC,SAA6B,EAC7BC,kBAAyC,EACzCC,SAAyD,GAAG,CAAC,CAAC,EAC1C;EACpB,MAAMT,MAAM,GAAGO,SAAS;EACxB,KAAK,MAAM,CAACG,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACP,UAAU,CAAC,EAAE;IACrD,IAAI,CAACC,SAAS,CAACG,GAAG,CAAC,EAAE;MACnBE,MAAM,CAACE,cAAc,CAACd,MAAM,EAAEU,GAAG,EAAE;QACjCK,GAAGA,CAAA,EAAG;UACJP,kBAAkB,CAACE,GAAG,CAAC;UACvB,OAAOD,SAAS,CAACC,GAAG,CAAC,GAAGD,SAAS,CAACC,GAAG,CAAC,CAACC,KAAK,CAAC,GAAGA,KAAK;QACvD;MACF,CAAC,CAAC;IACJ;EACF;EACA,OAAOX,MAAM;AACf", "ignoreList": []}