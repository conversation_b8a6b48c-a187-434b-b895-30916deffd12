{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/http2/constants.mjs", "../../unenv/dist/runtime/node/http2.mjs"], "sourcesContent": ["export const NGHTTP2_ERR_FRAME_SIZE_ERROR = -522;\nexport const NGHTTP2_SESSION_SERVER = 0;\nexport const NGHTTP2_SESSION_CLIENT = 1;\nexport const NGHTTP2_STREAM_STATE_IDLE = 1;\nexport const NGHTTP2_STREAM_STATE_OPEN = 2;\nexport const NGHTTP2_STREAM_STATE_RESERVED_LOCAL = 3;\nexport const NGHTTP2_STREAM_STATE_RESERVED_REMOTE = 4;\nexport const NGHTTP2_STREAM_STATE_HALF_CLOSED_LOCAL = 5;\nexport const NGHTTP2_STREAM_STATE_HALF_CLOSED_REMOTE = 6;\nexport const NGHTTP2_STREAM_STATE_CLOSED = 7;\nexport const NGHTTP2_FLAG_NONE = 0;\nexport const NGHTTP2_FLAG_END_STREAM = 1;\nexport const NGHTTP2_FLAG_END_HEADERS = 4;\nexport const NGHTTP2_FLAG_ACK = 1;\nexport const NGHTTP2_FLAG_PADDED = 8;\nexport const NGHTTP2_FLAG_PRIORITY = 32;\nexport const DEFAULT_SETTINGS_HEADER_TABLE_SIZE = 4096;\nexport const DEFAULT_SETTINGS_ENABLE_PUSH = 1;\nexport const DEFAULT_SETTINGS_MAX_CONCURRENT_STREAMS = 4294967295;\nexport const DEFAULT_SETTINGS_INITIAL_WINDOW_SIZE = 65535;\nexport const DEFAULT_SETTINGS_MAX_FRAME_SIZE = 16384;\nexport const DEFAULT_SETTINGS_MAX_HEADER_LIST_SIZE = 65535;\nexport const DEFAULT_SETTINGS_ENABLE_CONNECT_PROTOCOL = 0;\nexport const MAX_MAX_FRAME_SIZE = 16777215;\nexport const MIN_MAX_FRAME_SIZE = 16384;\nexport const MAX_INITIAL_WINDOW_SIZE = 2147483647;\nexport const NGHTTP2_SETTINGS_HEADER_TABLE_SIZE = 1;\nexport const NGHTTP2_SETTINGS_ENABLE_PUSH = 2;\nexport const NGHTTP2_SETTINGS_MAX_CONCURRENT_STREAMS = 3;\nexport const NGHTTP2_SETTINGS_INITIAL_WINDOW_SIZE = 4;\nexport const NGHTTP2_SETTINGS_MAX_FRAME_SIZE = 5;\nexport const NGHTTP2_SETTINGS_MAX_HEADER_LIST_SIZE = 6;\nexport const NGHTTP2_SETTINGS_ENABLE_CONNECT_PROTOCOL = 8;\nexport const PADDING_STRATEGY_NONE = 0;\nexport const PADDING_STRATEGY_ALIGNED = 1;\nexport const PADDING_STRATEGY_MAX = 2;\nexport const PADDING_STRATEGY_CALLBACK = 1;\nexport const NGHTTP2_NO_ERROR = 0;\nexport const NGHTTP2_PROTOCOL_ERROR = 1;\nexport const NGHTTP2_INTERNAL_ERROR = 2;\nexport const NGHTTP2_FLOW_CONTROL_ERROR = 3;\nexport const NGHTTP2_SETTINGS_TIMEOUT = 4;\nexport const NGHTTP2_STREAM_CLOSED = 5;\nexport const NGHTTP2_FRAME_SIZE_ERROR = 6;\nexport const NGHTTP2_REFUSED_STREAM = 7;\nexport const NGHTTP2_CANCEL = 8;\nexport const NGHTTP2_COMPRESSION_ERROR = 9;\nexport const NGHTTP2_CONNECT_ERROR = 10;\nexport const NGHTTP2_ENHANCE_YOUR_CALM = 11;\nexport const NGHTTP2_INADEQUATE_SECURITY = 12;\nexport const NGHTTP2_HTTP_1_1_REQUIRED = 13;\nexport const NGHTTP2_DEFAULT_WEIGHT = 16;\nexport const HTTP2_HEADER_STATUS = \":status\";\nexport const HTTP2_HEADER_METHOD = \":method\";\nexport const HTTP2_HEADER_AUTHORITY = \":authority\";\nexport const HTTP2_HEADER_SCHEME = \":scheme\";\nexport const HTTP2_HEADER_PATH = \":path\";\nexport const HTTP2_HEADER_PROTOCOL = \":protocol\";\nexport const HTTP2_HEADER_ACCEPT_ENCODING = \"accept-encoding\";\nexport const HTTP2_HEADER_ACCEPT_LANGUAGE = \"accept-language\";\nexport const HTTP2_HEADER_ACCEPT_RANGES = \"accept-ranges\";\nexport const HTTP2_HEADER_ACCEPT = \"accept\";\nexport const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_CREDENTIALS = \"access-control-allow-credentials\";\nexport const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_HEADERS = \"access-control-allow-headers\";\nexport const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_METHODS = \"access-control-allow-methods\";\nexport const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN = \"access-control-allow-origin\";\nexport const HTTP2_HEADER_ACCESS_CONTROL_EXPOSE_HEADERS = \"access-control-expose-headers\";\nexport const HTTP2_HEADER_ACCESS_CONTROL_REQUEST_HEADERS = \"access-control-request-headers\";\nexport const HTTP2_HEADER_ACCESS_CONTROL_REQUEST_METHOD = \"access-control-request-method\";\nexport const HTTP2_HEADER_AGE = \"age\";\nexport const HTTP2_HEADER_AUTHORIZATION = \"authorization\";\nexport const HTTP2_HEADER_CACHE_CONTROL = \"cache-control\";\nexport const HTTP2_HEADER_CONNECTION = \"connection\";\nexport const HTTP2_HEADER_CONTENT_DISPOSITION = \"content-disposition\";\nexport const HTTP2_HEADER_CONTENT_ENCODING = \"content-encoding\";\nexport const HTTP2_HEADER_CONTENT_LENGTH = \"content-length\";\nexport const HTTP2_HEADER_CONTENT_TYPE = \"content-type\";\nexport const HTTP2_HEADER_COOKIE = \"cookie\";\nexport const HTTP2_HEADER_DATE = \"date\";\nexport const HTTP2_HEADER_ETAG = \"etag\";\nexport const HTTP2_HEADER_FORWARDED = \"forwarded\";\nexport const HTTP2_HEADER_HOST = \"host\";\nexport const HTTP2_HEADER_IF_MODIFIED_SINCE = \"if-modified-since\";\nexport const HTTP2_HEADER_IF_NONE_MATCH = \"if-none-match\";\nexport const HTTP2_HEADER_IF_RANGE = \"if-range\";\nexport const HTTP2_HEADER_LAST_MODIFIED = \"last-modified\";\nexport const HTTP2_HEADER_LINK = \"link\";\nexport const HTTP2_HEADER_LOCATION = \"location\";\nexport const HTTP2_HEADER_RANGE = \"range\";\nexport const HTTP2_HEADER_REFERER = \"referer\";\nexport const HTTP2_HEADER_SERVER = \"server\";\nexport const HTTP2_HEADER_SET_COOKIE = \"set-cookie\";\nexport const HTTP2_HEADER_STRICT_TRANSPORT_SECURITY = \"strict-transport-security\";\nexport const HTTP2_HEADER_TRANSFER_ENCODING = \"transfer-encoding\";\nexport const HTTP2_HEADER_TE = \"te\";\nexport const HTTP2_HEADER_UPGRADE_INSECURE_REQUESTS = \"upgrade-insecure-requests\";\nexport const HTTP2_HEADER_UPGRADE = \"upgrade\";\nexport const HTTP2_HEADER_USER_AGENT = \"user-agent\";\nexport const HTTP2_HEADER_VARY = \"vary\";\nexport const HTTP2_HEADER_X_CONTENT_TYPE_OPTIONS = \"x-content-type-options\";\nexport const HTTP2_HEADER_X_FRAME_OPTIONS = \"x-frame-options\";\nexport const HTTP2_HEADER_KEEP_ALIVE = \"keep-alive\";\nexport const HTTP2_HEADER_PROXY_CONNECTION = \"proxy-connection\";\nexport const HTTP2_HEADER_X_XSS_PROTECTION = \"x-xss-protection\";\nexport const HTTP2_HEADER_ALT_SVC = \"alt-svc\";\nexport const HTTP2_HEADER_CONTENT_SECURITY_POLICY = \"content-security-policy\";\nexport const HTTP2_HEADER_EARLY_DATA = \"early-data\";\nexport const HTTP2_HEADER_EXPECT_CT = \"expect-ct\";\nexport const HTTP2_HEADER_ORIGIN = \"origin\";\nexport const HTTP2_HEADER_PURPOSE = \"purpose\";\nexport const HTTP2_HEADER_TIMING_ALLOW_ORIGIN = \"timing-allow-origin\";\nexport const HTTP2_HEADER_X_FORWARDED_FOR = \"x-forwarded-for\";\nexport const HTTP2_HEADER_PRIORITY = \"priority\";\nexport const HTTP2_HEADER_ACCEPT_CHARSET = \"accept-charset\";\nexport const HTTP2_HEADER_ACCESS_CONTROL_MAX_AGE = \"access-control-max-age\";\nexport const HTTP2_HEADER_ALLOW = \"allow\";\nexport const HTTP2_HEADER_CONTENT_LANGUAGE = \"content-language\";\nexport const HTTP2_HEADER_CONTENT_LOCATION = \"content-location\";\nexport const HTTP2_HEADER_CONTENT_MD5 = \"content-md5\";\nexport const HTTP2_HEADER_CONTENT_RANGE = \"content-range\";\nexport const HTTP2_HEADER_DNT = \"dnt\";\nexport const HTTP2_HEADER_EXPECT = \"expect\";\nexport const HTTP2_HEADER_EXPIRES = \"expires\";\nexport const HTTP2_HEADER_FROM = \"from\";\nexport const HTTP2_HEADER_IF_MATCH = \"if-match\";\nexport const HTTP2_HEADER_IF_UNMODIFIED_SINCE = \"if-unmodified-since\";\nexport const HTTP2_HEADER_MAX_FORWARDS = \"max-forwards\";\nexport const HTTP2_HEADER_PREFER = \"prefer\";\nexport const HTTP2_HEADER_PROXY_AUTHENTICATE = \"proxy-authenticate\";\nexport const HTTP2_HEADER_PROXY_AUTHORIZATION = \"proxy-authorization\";\nexport const HTTP2_HEADER_REFRESH = \"refresh\";\nexport const HTTP2_HEADER_RETRY_AFTER = \"retry-after\";\nexport const HTTP2_HEADER_TRAILER = \"trailer\";\nexport const HTTP2_HEADER_TK = \"tk\";\nexport const HTTP2_HEADER_VIA = \"via\";\nexport const HTTP2_HEADER_WARNING = \"warning\";\nexport const HTTP2_HEADER_WWW_AUTHENTICATE = \"www-authenticate\";\nexport const HTTP2_HEADER_HTTP2_SETTINGS = \"http2-settings\";\nexport const HTTP2_METHOD_ACL = \"ACL\";\nexport const HTTP2_METHOD_BASELINE_CONTROL = \"BASELINE-CONTROL\";\nexport const HTTP2_METHOD_BIND = \"BIND\";\nexport const HTTP2_METHOD_CHECKIN = \"CHECKIN\";\nexport const HTTP2_METHOD_CHECKOUT = \"CHECKOUT\";\nexport const HTTP2_METHOD_CONNECT = \"CONNECT\";\nexport const HTTP2_METHOD_COPY = \"COPY\";\nexport const HTTP2_METHOD_DELETE = \"DELETE\";\nexport const HTTP2_METHOD_GET = \"GET\";\nexport const HTTP2_METHOD_HEAD = \"HEAD\";\nexport const HTTP2_METHOD_LABEL = \"LABEL\";\nexport const HTTP2_METHOD_LINK = \"LINK\";\nexport const HTTP2_METHOD_LOCK = \"LOCK\";\nexport const HTTP2_METHOD_MERGE = \"MERGE\";\nexport const HTTP2_METHOD_MKACTIVITY = \"MKACTIVITY\";\nexport const HTTP2_METHOD_MKCALENDAR = \"MKCALENDAR\";\nexport const HTTP2_METHOD_MKCOL = \"MKCOL\";\nexport const HTTP2_METHOD_MKREDIRECTREF = \"MKREDIRECTREF\";\nexport const HTTP2_METHOD_MKWORKSPACE = \"MKWORKSPACE\";\nexport const HTTP2_METHOD_MOVE = \"MOVE\";\nexport const HTTP2_METHOD_OPTIONS = \"OPTIONS\";\nexport const HTTP2_METHOD_ORDERPATCH = \"ORDERPATCH\";\nexport const HTTP2_METHOD_PATCH = \"PATCH\";\nexport const HTTP2_METHOD_POST = \"POST\";\nexport const HTTP2_METHOD_PRI = \"PRI\";\nexport const HTTP2_METHOD_PROPFIND = \"PROPFIND\";\nexport const HTTP2_METHOD_PROPPATCH = \"PROPPATCH\";\nexport const HTTP2_METHOD_PUT = \"PUT\";\nexport const HTTP2_METHOD_REBIND = \"REBIND\";\nexport const HTTP2_METHOD_REPORT = \"REPORT\";\nexport const HTTP2_METHOD_SEARCH = \"SEARCH\";\nexport const HTTP2_METHOD_TRACE = \"TRACE\";\nexport const HTTP2_METHOD_UNBIND = \"UNBIND\";\nexport const HTTP2_METHOD_UNCHECKOUT = \"UNCHECKOUT\";\nexport const HTTP2_METHOD_UNLINK = \"UNLINK\";\nexport const HTTP2_METHOD_UNLOCK = \"UNLOCK\";\nexport const HTTP2_METHOD_UPDATE = \"UPDATE\";\nexport const HTTP2_METHOD_UPDATEREDIRECTREF = \"UPDATEREDIRECTREF\";\nexport const HTTP2_METHOD_VERSION_CONTROL = \"VERSION-CONTROL\";\nexport const HTTP_STATUS_CONTINUE = 100;\nexport const HTTP_STATUS_SWITCHING_PROTOCOLS = 101;\nexport const HTTP_STATUS_PROCESSING = 102;\nexport const HTTP_STATUS_EARLY_HINTS = 103;\nexport const HTTP_STATUS_OK = 200;\nexport const HTTP_STATUS_CREATED = 201;\nexport const HTTP_STATUS_ACCEPTED = 202;\nexport const HTTP_STATUS_NON_AUTHORITATIVE_INFORMATION = 203;\nexport const HTTP_STATUS_NO_CONTENT = 204;\nexport const HTTP_STATUS_RESET_CONTENT = 205;\nexport const HTTP_STATUS_PARTIAL_CONTENT = 206;\nexport const HTTP_STATUS_MULTI_STATUS = 207;\nexport const HTTP_STATUS_ALREADY_REPORTED = 208;\nexport const HTTP_STATUS_IM_USED = 226;\nexport const HTTP_STATUS_MULTIPLE_CHOICES = 300;\nexport const HTTP_STATUS_MOVED_PERMANENTLY = 301;\nexport const HTTP_STATUS_FOUND = 302;\nexport const HTTP_STATUS_SEE_OTHER = 303;\nexport const HTTP_STATUS_NOT_MODIFIED = 304;\nexport const HTTP_STATUS_USE_PROXY = 305;\nexport const HTTP_STATUS_TEMPORARY_REDIRECT = 307;\nexport const HTTP_STATUS_PERMANENT_REDIRECT = 308;\nexport const HTTP_STATUS_BAD_REQUEST = 400;\nexport const HTTP_STATUS_UNAUTHORIZED = 401;\nexport const HTTP_STATUS_PAYMENT_REQUIRED = 402;\nexport const HTTP_STATUS_FORBIDDEN = 403;\nexport const HTTP_STATUS_NOT_FOUND = 404;\nexport const HTTP_STATUS_METHOD_NOT_ALLOWED = 405;\nexport const HTTP_STATUS_NOT_ACCEPTABLE = 406;\nexport const HTTP_STATUS_PROXY_AUTHENTICATION_REQUIRED = 407;\nexport const HTTP_STATUS_REQUEST_TIMEOUT = 408;\nexport const HTTP_STATUS_CONFLICT = 409;\nexport const HTTP_STATUS_GONE = 410;\nexport const HTTP_STATUS_LENGTH_REQUIRED = 411;\nexport const HTTP_STATUS_PRECONDITION_FAILED = 412;\nexport const HTTP_STATUS_PAYLOAD_TOO_LARGE = 413;\nexport const HTTP_STATUS_URI_TOO_LONG = 414;\nexport const HTTP_STATUS_UNSUPPORTED_MEDIA_TYPE = 415;\nexport const HTTP_STATUS_RANGE_NOT_SATISFIABLE = 416;\nexport const HTTP_STATUS_EXPECTATION_FAILED = 417;\nexport const HTTP_STATUS_TEAPOT = 418;\nexport const HTTP_STATUS_MISDIRECTED_REQUEST = 421;\nexport const HTTP_STATUS_UNPROCESSABLE_ENTITY = 422;\nexport const HTTP_STATUS_LOCKED = 423;\nexport const HTTP_STATUS_FAILED_DEPENDENCY = 424;\nexport const HTTP_STATUS_TOO_EARLY = 425;\nexport const HTTP_STATUS_UPGRADE_REQUIRED = 426;\nexport const HTTP_STATUS_PRECONDITION_REQUIRED = 428;\nexport const HTTP_STATUS_TOO_MANY_REQUESTS = 429;\nexport const HTTP_STATUS_REQUEST_HEADER_FIELDS_TOO_LARGE = 431;\nexport const HTTP_STATUS_UNAVAILABLE_FOR_LEGAL_REASONS = 451;\nexport const HTTP_STATUS_INTERNAL_SERVER_ERROR = 500;\nexport const HTTP_STATUS_NOT_IMPLEMENTED = 501;\nexport const HTTP_STATUS_BAD_GATEWAY = 502;\nexport const HTTP_STATUS_SERVICE_UNAVAILABLE = 503;\nexport const HTTP_STATUS_GATEWAY_TIMEOUT = 504;\nexport const HTTP_STATUS_HTTP_VERSION_NOT_SUPPORTED = 505;\nexport const HTTP_STATUS_VARIANT_ALSO_NEGOTIATES = 506;\nexport const HTTP_STATUS_INSUFFICIENT_STORAGE = 507;\nexport const HTTP_STATUS_LOOP_DETECTED = 508;\nexport const HTTP_STATUS_BANDWIDTH_LIMIT_EXCEEDED = 509;\nexport const HTTP_STATUS_NOT_EXTENDED = 510;\nexport const HTTP_STATUS_NETWORK_AUTHENTICATION_REQUIRED = 511;\n", "import { notImplemented, notImplementedClass } from \"../_internal/utils.mjs\";\nimport { NGHTTP2_ERR_FRAME_SIZE_ERROR, NGHTTP2_SESSION_SERVER, NGHTTP2_SESSION_CLIENT, NGHTTP2_STREAM_STATE_IDLE, NGHTTP2_STREAM_STATE_OPEN, NGHTTP2_STREAM_STATE_RESERVED_LOCAL, NGHTTP2_STREAM_STATE_RESERVED_REMOTE, NGHTTP2_STREAM_STATE_HALF_CLOSED_LOCAL, NGHTTP2_STREAM_STATE_HALF_CLOSED_REMOTE, NGHTTP2_STREAM_STATE_CLOSED, NGHTTP2_FLAG_NONE, NGHTTP2_FLAG_END_STREAM, NGHTTP2_FLAG_END_HEADERS, NGHTTP2_FLAG_ACK, NGHTTP2_FLAG_PADDED, NGHTTP2_FLAG_PRIORITY, DEFAULT_SETTINGS_HEADER_TABLE_SIZE, DEFAULT_SETTINGS_ENABLE_PUSH, DEFAULT_SETTINGS_MAX_CONCURRENT_STREAMS, DEFAULT_SETTINGS_INITIAL_WINDOW_SIZE, DEFAULT_SETTINGS_MAX_FRAME_SIZE, DEFAULT_SETTINGS_MAX_HEADER_LIST_SIZE, DEFAULT_SETTINGS_ENABLE_CONNECT_PROTOCOL, MAX_MAX_FRAME_SIZE, MIN_MAX_FRAME_SIZE, MAX_INITIAL_WINDOW_SIZE, NGHTTP2_SETTINGS_HEADER_TABLE_SIZE, NGHTTP2_SETTINGS_ENABLE_PUSH, NGHTTP2_SETTINGS_MAX_CONCURRENT_STREAMS, NGHTTP2_SETTINGS_INITIAL_WINDOW_SIZE, NGHTTP2_SETTINGS_MAX_FRAME_SIZE, NGHTTP2_SETTINGS_MAX_HEADER_LIST_SIZE, NGHTTP2_SETTINGS_ENABLE_CONNECT_PROTOCOL, PADDING_STRATEGY_NONE, PADDING_STRATEGY_ALIGNED, PADDING_STRATEGY_MAX, PADDING_STRATEGY_CALLBACK, NGHTTP2_NO_ERROR, NGHTTP2_PROTOCOL_ERROR, NGHTTP2_INTERNAL_ERROR, NGHTTP2_FLOW_CONTROL_ERROR, NGHTTP2_SETTINGS_TIMEOUT, NGHTTP2_STREAM_CLOSED, NGHTTP2_FRAME_SIZE_ERROR, NGHTTP2_REFUSED_STREAM, NGHTTP2_CANCEL, NGHTTP2_COMPRESSION_ERROR, NGHTTP2_CONNECT_ERROR, NGHTTP2_ENHANCE_YOUR_CALM, NGHTTP2_INADEQUATE_SECURITY, NGHTTP2_HTTP_1_1_REQUIRED, NGHTTP2_DEFAULT_WEIGHT, HTTP2_HEADER_STATUS, HTTP2_HEADER_METHOD, HTTP2_HEADER_AUTHORITY, HTTP2_HEADER_SCHEME, HTTP2_HEADER_PATH, HTTP2_HEADER_PROTOCOL, HTTP2_HEADER_ACCEPT_ENCODING, HTTP2_HEADER_ACCEPT_LANGUAGE, HTTP2_HEADER_ACCEPT_RANGES, HTTP2_HEADER_ACCEPT, HTTP2_HEADER_ACCESS_CONTROL_ALLOW_CREDENTIALS, HTTP2_HEADER_ACCESS_CONTROL_ALLOW_HEADERS, HTTP2_HEADER_ACCESS_CONTROL_ALLOW_METHODS, HTTP2_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN, HTTP2_HEADER_ACCESS_CONTROL_EXPOSE_HEADERS, HTTP2_HEADER_ACCESS_CONTROL_REQUEST_HEADERS, HTTP2_HEADER_ACCESS_CONTROL_REQUEST_METHOD, HTTP2_HEADER_AGE, HTTP2_HEADER_AUTHORIZATION, HTTP2_HEADER_CACHE_CONTROL, HTTP2_HEADER_CONNECTION, HTTP2_HEADER_CONTENT_DISPOSITION, HTTP2_HEADER_CONTENT_ENCODING, HTTP2_HEADER_CONTENT_LENGTH, HTTP2_HEADER_CONTENT_TYPE, HTTP2_HEADER_COOKIE, HTTP2_HEADER_DATE, HTTP2_HEADER_ETAG, HTTP2_HEADER_FORWARDED, HTTP2_HEADER_HOST, HTTP2_HEADER_IF_MODIFIED_SINCE, HTTP2_HEADER_IF_NONE_MATCH, HTTP2_HEADER_IF_RANGE, HTTP2_HEADER_LAST_MODIFIED, HTTP2_HEADER_LINK, HTTP2_HEADER_LOCATION, HTTP2_HEADER_RANGE, HTTP2_HEADER_REFERER, HTTP2_HEADER_SERVER, HTTP2_HEADER_SET_COOKIE, HTTP2_HEADER_STRICT_TRANSPORT_SECURITY, HTTP2_HEADER_TRANSFER_ENCODING, HTTP2_HEADER_TE, HTTP2_HEADER_UPGRADE_INSECURE_REQUESTS, HTTP2_HEADER_UPGRADE, HTTP2_HEADER_USER_AGENT, HTTP2_HEADER_VARY, HTTP2_HEADER_X_CONTENT_TYPE_OPTIONS, HTTP2_HEADER_X_FRAME_OPTIONS, HTTP2_HEADER_KEEP_ALIVE, HTTP2_HEADER_PROXY_CONNECTION, HTTP2_HEADER_X_XSS_PROTECTION, HTTP2_HEADER_ALT_SVC, HTTP2_HEADER_CONTENT_SECURITY_POLICY, HTTP2_HEADER_EARLY_DATA, HTTP2_HEADER_EXPECT_CT, HTTP2_HEADER_ORIGIN, HTTP2_HEADER_PURPOSE, HTTP2_HEADER_TIMING_ALLOW_ORIGIN, HTTP2_HEADER_X_FORWARDED_FOR, HTTP2_HEADER_PRIORITY, HTTP2_HEADER_ACCEPT_CHARSET, HTTP2_HEADER_ACCESS_CONTROL_MAX_AGE, HTTP2_HEADER_ALLOW, HTTP2_HEADER_CONTENT_LANGUAGE, HTTP2_HEADER_CONTENT_LOCATION, HTTP2_HEADER_CONTENT_MD5, HTTP2_HEADER_CONTENT_RANGE, HTTP2_HEADER_DNT, HTTP2_HEADER_EXPECT, HTTP2_HEADER_EXPIRES, HTTP2_HEADER_FROM, HTTP2_HEADER_IF_MATCH, HTTP2_HEADER_IF_UNMODIFIED_SINCE, HTTP2_HEADER_MAX_FORWARDS, HTTP2_HEADER_PREFER, HTTP2_HEADER_PROXY_AUTHENTICATE, HTTP2_HEADER_PROXY_AUTHORIZATION, HTTP2_HEADER_REFRESH, HTTP2_HEADER_RETRY_AFTER, HTTP2_HEADER_TRAILER, HTTP2_HEADER_TK, HTTP2_HEADER_VIA, HTTP2_HEADER_WARNING, HTTP2_HEADER_WWW_AUTHENTICATE, HTTP2_HEADER_HTTP2_SETTINGS, HTTP2_METHOD_ACL, HTTP2_METHOD_BASELINE_CONTROL, HTTP2_METHOD_BIND, HTTP2_METHOD_CHECKIN, HTTP2_METHOD_CHECKOUT, HTTP2_METHOD_CONNECT, HTTP2_METHOD_COPY, HTTP2_METHOD_DELETE, HTTP2_METHOD_GET, HTTP2_METHOD_HEAD, HTTP2_METHOD_LABEL, HTTP2_METHOD_LINK, HTTP2_METHOD_LOCK, HTTP2_METHOD_MERGE, HTTP2_METHOD_MKACTIVITY, HTTP2_METHOD_MKCALENDAR, HTTP2_METHOD_MKCOL, HTTP2_METHOD_MKREDIRECTREF, HTTP2_METHOD_MKWORKSPACE, HTTP2_METHOD_MOVE, HTTP2_METHOD_OPTIONS, HTTP2_METHOD_ORDERPATCH, HTTP2_METHOD_PATCH, HTTP2_METHOD_POST, HTTP2_METHOD_PRI, HTTP2_METHOD_PROPFIND, HTTP2_METHOD_PROPPATCH, HTTP2_METHOD_PUT, HTTP2_METHOD_REBIND, HTTP2_METHOD_REPORT, HTTP2_METHOD_SEARCH, HTTP2_METHOD_TRACE, HTTP2_METHOD_UNBIND, HTTP2_METHOD_UNCHECKOUT, HTTP2_METHOD_UNLINK, HTTP2_METHOD_UNLOCK, HTTP2_METHOD_UPDATE, HTTP2_METHOD_UPDATEREDIRECTREF, HTTP2_METHOD_VERSION_CONTROL, HTTP_STATUS_CONTINUE, HTTP_STATUS_SWITCHING_PROTOCOLS, HTTP_STATUS_PROCESSING, HTTP_STATUS_EARLY_HINTS, HTTP_STATUS_OK, HTTP_STATUS_CREATED, HTTP_STATUS_ACCEPTED, HTTP_STATUS_NON_AUTHORITATIVE_INFORMATION, HTTP_STATUS_NO_CONTENT, HTTP_STATUS_RESET_CONTENT, HTTP_STATUS_PARTIAL_CONTENT, HTTP_STATUS_MULTI_STATUS, HTTP_STATUS_ALREADY_REPORTED, HTTP_STATUS_IM_USED, HTTP_STATUS_MULTIPLE_CHOICES, HTTP_STATUS_MOVED_PERMANENTLY, HTTP_STATUS_FOUND, HTTP_STATUS_SEE_OTHER, HTTP_STATUS_NOT_MODIFIED, HTTP_STATUS_USE_PROXY, HTTP_STATUS_TEMPORARY_REDIRECT, HTTP_STATUS_PERMANENT_REDIRECT, HTTP_STATUS_BAD_REQUEST, HTTP_STATUS_UNAUTHORIZED, HTTP_STATUS_PAYMENT_REQUIRED, HTTP_STATUS_FORBIDDEN, HTTP_STATUS_NOT_FOUND, HTTP_STATUS_METHOD_NOT_ALLOWED, HTTP_STATUS_NOT_ACCEPTABLE, HTTP_STATUS_PROXY_AUTHENTICATION_REQUIRED, HTTP_STATUS_REQUEST_TIMEOUT, HTTP_STATUS_CONFLICT, HTTP_STATUS_GONE, HTTP_STATUS_LENGTH_REQUIRED, HTTP_STATUS_PRECONDITION_FAILED, HTTP_STATUS_PAYLOAD_TOO_LARGE, HTTP_STATUS_URI_TOO_LONG, HTTP_STATUS_UNSUPPORTED_MEDIA_TYPE, HTTP_STATUS_RANGE_NOT_SATISFIABLE, HTTP_STATUS_EXPECTATION_FAILED, HTTP_STATUS_TEAPOT, HTTP_STATUS_MISDIRECTED_REQUEST, HTTP_STATUS_UNPROCESSABLE_ENTITY, HTTP_STATUS_LOCKED, HTTP_STATUS_FAILED_DEPENDENCY, HTTP_STATUS_TOO_EARLY, HTTP_STATUS_UPGRADE_REQUIRED, HTTP_STATUS_PRECONDITION_REQUIRED, HTTP_STATUS_TOO_MANY_REQUESTS, HTTP_STATUS_REQUEST_HEADER_FIELDS_TOO_LARGE, HTTP_STATUS_UNAVAILABLE_FOR_LEGAL_REASONS, HTTP_STATUS_INTERNAL_SERVER_ERROR, HTTP_STATUS_NOT_IMPLEMENTED, HTTP_STATUS_BAD_GATEWAY, HTTP_STATUS_SERVICE_UNAVAILABLE, HTTP_STATUS_GATEWAY_TIMEOUT, HTTP_STATUS_HTTP_VERSION_NOT_SUPPORTED, HTTP_STATUS_VARIANT_ALSO_NEGOTIATES, HTTP_STATUS_INSUFFICIENT_STORAGE, HTTP_STATUS_LOOP_DETECTED, HTTP_STATUS_BANDWIDTH_LIMIT_EXCEEDED, HTTP_STATUS_NOT_EXTENDED, HTTP_STATUS_NETWORK_AUTHENTICATION_REQUIRED } from \"./internal/http2/constants.mjs\";\nexport const constants = {\n\tNGHTTP2_ERR_FRAME_SIZE_ERROR,\n\tNGHTTP2_SESSION_SERVER,\n\tNGHTTP2_SESSION_CLIENT,\n\tNGHTTP2_STREAM_STATE_IDLE,\n\tNGHTTP2_STREAM_STATE_OPEN,\n\tNGHTTP2_STREAM_STATE_RESERVED_LOCAL,\n\tNGHTTP2_STREAM_STATE_RESERVED_REMOTE,\n\tNGHTTP2_STREAM_STATE_HALF_CLOSED_LOCAL,\n\tNGHTTP2_STREAM_STATE_HALF_CLOSED_REMOTE,\n\tNGHTTP2_STREAM_STATE_CLOSED,\n\tNGHTTP2_FLAG_NONE,\n\tNGHTTP2_FLAG_END_STREAM,\n\tNGHTTP2_FLAG_END_HEADERS,\n\tNGHTTP2_FLAG_ACK,\n\tNGHTTP2_FLAG_PADDED,\n\tNGHTTP2_FLAG_PRIORITY,\n\tDEFAULT_SETTINGS_HEADER_TABLE_SIZE,\n\tDEFAULT_SETTINGS_ENABLE_PUSH,\n\tDEFAULT_SETTINGS_MAX_CONCURRENT_STREAMS,\n\tDEFAULT_SETTINGS_INITIAL_WINDOW_SIZE,\n\tDEFAULT_SETTINGS_MAX_FRAME_SIZE,\n\tDEFAULT_SETTINGS_MAX_HEADER_LIST_SIZE,\n\tDEFAULT_SETTINGS_ENABLE_CONNECT_PROTOCOL,\n\tMAX_MAX_FRAME_SIZE,\n\tMIN_MAX_FRAME_SIZE,\n\tMAX_INITIAL_WINDOW_SIZE,\n\tNGHTTP2_SETTINGS_HEADER_TABLE_SIZE,\n\tNGHTTP2_SETTINGS_ENABLE_PUSH,\n\tNGHTTP2_SETTINGS_MAX_CONCURRENT_STREAMS,\n\tNGHTTP2_SETTINGS_INITIAL_WINDOW_SIZE,\n\tNGHTTP2_SETTINGS_MAX_FRAME_SIZE,\n\tNGHTTP2_SETTINGS_MAX_HEADER_LIST_SIZE,\n\tNGHTTP2_SETTINGS_ENABLE_CONNECT_PROTOCOL,\n\tPADDING_STRATEGY_NONE,\n\tPADDING_STRATEGY_ALIGNED,\n\tPADDING_STRATEGY_MAX,\n\tPADDING_STRATEGY_CALLBACK,\n\tNGHTTP2_NO_ERROR,\n\tNGHTTP2_PROTOCOL_ERROR,\n\tNGHTTP2_INTERNAL_ERROR,\n\tNGHTTP2_FLOW_CONTROL_ERROR,\n\tNGHTTP2_SETTINGS_TIMEOUT,\n\tNGHTTP2_STREAM_CLOSED,\n\tNGHTTP2_FRAME_SIZE_ERROR,\n\tNGHTTP2_REFUSED_STREAM,\n\tNGHTTP2_CANCEL,\n\tNGHTTP2_COMPRESSION_ERROR,\n\tNGHTTP2_CONNECT_ERROR,\n\tNGHTTP2_ENHANCE_YOUR_CALM,\n\tNGHTTP2_INADEQUATE_SECURITY,\n\tNGHTTP2_HTTP_1_1_REQUIRED,\n\tNGHTTP2_DEFAULT_WEIGHT,\n\tHTTP2_HEADER_STATUS,\n\tHTTP2_HEADER_METHOD,\n\tHTTP2_HEADER_AUTHORITY,\n\tHTTP2_HEADER_SCHEME,\n\tHTTP2_HEADER_PATH,\n\tHTTP2_HEADER_PROTOCOL,\n\tHTTP2_HEADER_ACCEPT_ENCODING,\n\tHTTP2_HEADER_ACCEPT_LANGUAGE,\n\tHTTP2_HEADER_ACCEPT_RANGES,\n\tHTTP2_HEADER_ACCEPT,\n\tHTTP2_HEADER_ACCESS_CONTROL_ALLOW_CREDENTIALS,\n\tHTTP2_HEADER_ACCESS_CONTROL_ALLOW_HEADERS,\n\tHTTP2_HEADER_ACCESS_CONTROL_ALLOW_METHODS,\n\tHTTP2_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN,\n\tHTTP2_HEADER_ACCESS_CONTROL_EXPOSE_HEADERS,\n\tHTTP2_HEADER_ACCESS_CONTROL_REQUEST_HEADERS,\n\tHTTP2_HEADER_ACCESS_CONTROL_REQUEST_METHOD,\n\tHTTP2_HEADER_AGE,\n\tHTTP2_HEADER_AUTHORIZATION,\n\tHTTP2_HEADER_CACHE_CONTROL,\n\tHTTP2_HEADER_CONNECTION,\n\tHTTP2_HEADER_CONTENT_DISPOSITION,\n\tHTTP2_HEADER_CONTENT_ENCODING,\n\tHTTP2_HEADER_CONTENT_LENGTH,\n\tHTTP2_HEADER_CONTENT_TYPE,\n\tHTTP2_HEADER_COOKIE,\n\tHTTP2_HEADER_DATE,\n\tHTTP2_HEADER_ETAG,\n\tHTTP2_HEADER_FORWARDED,\n\tHTTP2_HEADER_HOST,\n\tHTTP2_HEADER_IF_MODIFIED_SINCE,\n\tHTTP2_HEADER_IF_NONE_MATCH,\n\tHTTP2_HEADER_IF_RANGE,\n\tHTTP2_HEADER_LAST_MODIFIED,\n\tHTTP2_HEADER_LINK,\n\tHTTP2_HEADER_LOCATION,\n\tHTTP2_HEADER_RANGE,\n\tHTTP2_HEADER_REFERER,\n\tHTTP2_HEADER_SERVER,\n\tHTTP2_HEADER_SET_COOKIE,\n\tHTTP2_HEADER_STRICT_TRANSPORT_SECURITY,\n\tHTTP2_HEADER_TRANSFER_ENCODING,\n\tHTTP2_HEADER_TE,\n\tHTTP2_HEADER_UPGRADE_INSECURE_REQUESTS,\n\tHTTP2_HEADER_UPGRADE,\n\tHTTP2_HEADER_USER_AGENT,\n\tHTTP2_HEADER_VARY,\n\tHTTP2_HEADER_X_CONTENT_TYPE_OPTIONS,\n\tHTTP2_HEADER_X_FRAME_OPTIONS,\n\tHTTP2_HEADER_KEEP_ALIVE,\n\tHTTP2_HEADER_PROXY_CONNECTION,\n\tHTTP2_HEADER_X_XSS_PROTECTION,\n\tHTTP2_HEADER_ALT_SVC,\n\tHTTP2_HEADER_CONTENT_SECURITY_POLICY,\n\tHTTP2_HEADER_EARLY_DATA,\n\tHTTP2_HEADER_EXPECT_CT,\n\tHTTP2_HEADER_ORIGIN,\n\tHTTP2_HEADER_PURPOSE,\n\tHTTP2_HEADER_TIMING_ALLOW_ORIGIN,\n\tHTTP2_HEADER_X_FORWARDED_FOR,\n\tHTTP2_HEADER_PRIORITY,\n\tHTTP2_HEADER_ACCEPT_CHARSET,\n\tHTTP2_HEADER_ACCESS_CONTROL_MAX_AGE,\n\tHTTP2_HEADER_ALLOW,\n\tHTTP2_HEADER_CONTENT_LANGUAGE,\n\tHTTP2_HEADER_CONTENT_LOCATION,\n\tHTTP2_HEADER_CONTENT_MD5,\n\tHTTP2_HEADER_CONTENT_RANGE,\n\tHTTP2_HEADER_DNT,\n\tHTTP2_HEADER_EXPECT,\n\tHTTP2_HEADER_EXPIRES,\n\tHTTP2_HEADER_FROM,\n\tHTTP2_HEADER_IF_MATCH,\n\tHTTP2_HEADER_IF_UNMODIFIED_SINCE,\n\tHTTP2_HEADER_MAX_FORWARDS,\n\tHTTP2_HEADER_PREFER,\n\tHTTP2_HEADER_PROXY_AUTHENTICATE,\n\tHTTP2_HEADER_PROXY_AUTHORIZATION,\n\tHTTP2_HEADER_REFRESH,\n\tHTTP2_HEADER_RETRY_AFTER,\n\tHTTP2_HEADER_TRAILER,\n\tHTTP2_HEADER_TK,\n\tHTTP2_HEADER_VIA,\n\tHTTP2_HEADER_WARNING,\n\tHTTP2_HEADER_WWW_AUTHENTICATE,\n\tHTTP2_HEADER_HTTP2_SETTINGS,\n\tHTTP2_METHOD_ACL,\n\tHTTP2_METHOD_BASELINE_CONTROL,\n\tHTTP2_METHOD_BIND,\n\tHTTP2_METHOD_CHECKIN,\n\tHTTP2_METHOD_CHECKOUT,\n\tHTTP2_METHOD_CONNECT,\n\tHTTP2_METHOD_COPY,\n\tHTTP2_METHOD_DELETE,\n\tHTTP2_METHOD_GET,\n\tHTTP2_METHOD_HEAD,\n\tHTTP2_METHOD_LABEL,\n\tHTTP2_METHOD_LINK,\n\tHTTP2_METHOD_LOCK,\n\tHTTP2_METHOD_MERGE,\n\tHTTP2_METHOD_MKACTIVITY,\n\tHTTP2_METHOD_MKCALENDAR,\n\tHTTP2_METHOD_MKCOL,\n\tHTTP2_METHOD_MKREDIRECTREF,\n\tHTTP2_METHOD_MKWORKSPACE,\n\tHTTP2_METHOD_MOVE,\n\tHTTP2_METHOD_OPTIONS,\n\tHTTP2_METHOD_ORDERPATCH,\n\tHTTP2_METHOD_PATCH,\n\tHTTP2_METHOD_POST,\n\tHTTP2_METHOD_PRI,\n\tHTTP2_METHOD_PROPFIND,\n\tHTTP2_METHOD_PROPPATCH,\n\tHTTP2_METHOD_PUT,\n\tHTTP2_METHOD_REBIND,\n\tHTTP2_METHOD_REPORT,\n\tHTTP2_METHOD_SEARCH,\n\tHTTP2_METHOD_TRACE,\n\tHTTP2_METHOD_UNBIND,\n\tHTTP2_METHOD_UNCHECKOUT,\n\tHTTP2_METHOD_UNLINK,\n\tHTTP2_METHOD_UNLOCK,\n\tHTTP2_METHOD_UPDATE,\n\tHTTP2_METHOD_UPDATEREDIRECTREF,\n\tHTTP2_METHOD_VERSION_CONTROL,\n\tHTTP_STATUS_CONTINUE,\n\tHTTP_STATUS_SWITCHING_PROTOCOLS,\n\tHTTP_STATUS_PROCESSING,\n\tHTTP_STATUS_EARLY_HINTS,\n\tHTTP_STATUS_OK,\n\tHTTP_STATUS_CREATED,\n\tHTTP_STATUS_ACCEPTED,\n\tHTTP_STATUS_NON_AUTHORITATIVE_INFORMATION,\n\tHTTP_STATUS_NO_CONTENT,\n\tHTTP_STATUS_RESET_CONTENT,\n\tHTTP_STATUS_PARTIAL_CONTENT,\n\tHTTP_STATUS_MULTI_STATUS,\n\tHTTP_STATUS_ALREADY_REPORTED,\n\tHTTP_STATUS_IM_USED,\n\tHTTP_STATUS_MULTIPLE_CHOICES,\n\tHTTP_STATUS_MOVED_PERMANENTLY,\n\tHTTP_STATUS_FOUND,\n\tHTTP_STATUS_SEE_OTHER,\n\tHTTP_STATUS_NOT_MODIFIED,\n\tHTTP_STATUS_USE_PROXY,\n\tHTTP_STATUS_TEMPORARY_REDIRECT,\n\tHTTP_STATUS_PERMANENT_REDIRECT,\n\tHTTP_STATUS_BAD_REQUEST,\n\tHTTP_STATUS_UNAUTHORIZED,\n\tHTTP_STATUS_PAYMENT_REQUIRED,\n\tHTTP_STATUS_FORBIDDEN,\n\tHTTP_STATUS_NOT_FOUND,\n\tHTTP_STATUS_METHOD_NOT_ALLOWED,\n\tHTTP_STATUS_NOT_ACCEPTABLE,\n\tHTTP_STATUS_PROXY_AUTHENTICATION_REQUIRED,\n\tHTTP_STATUS_REQUEST_TIMEOUT,\n\tHTTP_STATUS_CONFLICT,\n\tHTTP_STATUS_GONE,\n\tHTTP_STATUS_LENGTH_REQUIRED,\n\tHTTP_STATUS_PRECONDITION_FAILED,\n\tHTTP_STATUS_PAYLOAD_TOO_LARGE,\n\tHTTP_STATUS_URI_TOO_LONG,\n\tHTTP_STATUS_UNSUPPORTED_MEDIA_TYPE,\n\tHTTP_STATUS_RANGE_NOT_SATISFIABLE,\n\tHTTP_STATUS_EXPECTATION_FAILED,\n\tHTTP_STATUS_TEAPOT,\n\tHTTP_STATUS_MISDIRECTED_REQUEST,\n\tHTTP_STATUS_UNPROCESSABLE_ENTITY,\n\tHTTP_STATUS_LOCKED,\n\tHTTP_STATUS_FAILED_DEPENDENCY,\n\tHTTP_STATUS_TOO_EARLY,\n\tHTTP_STATUS_UPGRADE_REQUIRED,\n\tHTTP_STATUS_PRECONDITION_REQUIRED,\n\tHTTP_STATUS_TOO_MANY_REQUESTS,\n\tHTTP_STATUS_REQUEST_HEADER_FIELDS_TOO_LARGE,\n\tHTTP_STATUS_UNAVAILABLE_FOR_LEGAL_REASONS,\n\tHTTP_STATUS_INTERNAL_SERVER_ERROR,\n\tHTTP_STATUS_NOT_IMPLEMENTED,\n\tHTTP_STATUS_BAD_GATEWAY,\n\tHTTP_STATUS_SERVICE_UNAVAILABLE,\n\tHTTP_STATUS_GATEWAY_TIMEOUT,\n\tHTTP_STATUS_HTTP_VERSION_NOT_SUPPORTED,\n\tHTTP_STATUS_VARIANT_ALSO_NEGOTIATES,\n\tHTTP_STATUS_INSUFFICIENT_STORAGE,\n\tHTTP_STATUS_LOOP_DETECTED,\n\tHTTP_STATUS_BANDWIDTH_LIMIT_EXCEEDED,\n\tHTTP_STATUS_NOT_EXTENDED,\n\tHTTP_STATUS_NETWORK_AUTHENTICATION_REQUIRED\n};\nexport const createSecureServer = /* @__PURE__ */ notImplemented(\"http2.createSecureServer\");\nexport const createServer = /* @__PURE__ */ notImplemented(\"http2.createServer\");\nexport const connect = /* @__PURE__ */ notImplemented(\"http2.connect\");\nexport const performServerHandshake = /* @__PURE__ */ notImplemented(\"http2.performServerHandshake \");\nexport const Http2ServerRequest = /* @__PURE__ */ notImplementedClass(\"http2.Http2ServerRequest\");\nexport const Http2ServerResponse = /* @__PURE__ */ notImplementedClass(\"http2.Http2ServerResponse\");\nexport const getDefaultSettings = function() {\n\treturn Object.create({\n\t\theaderTableSize: 4096,\n\t\tenablePush: true,\n\t\tinitialWindowSize: 65535,\n\t\tmaxFrameSize: 16384,\n\t\tmaxConcurrentStreams: 4294967295,\n\t\tmaxHeaderSize: 65535,\n\t\tmaxHeaderListSize: 65535,\n\t\tenableConnectProtocol: false\n\t});\n};\nexport const getPackedSettings = function() {\n\treturn Buffer.from(\"\");\n};\nexport const getUnpackedSettings = function() {\n\treturn Object.create({});\n};\nexport const sensitiveHeaders = /* @__PURE__ */ Symbol(\"nodejs.http2.sensitiveHeaders\");\nexport default {\n\tconstants,\n\tcreateSecureServer,\n\tcreateServer,\n\tHttp2ServerRequest,\n\tHttp2ServerResponse,\n\tconnect,\n\tgetDefaultSettings,\n\tgetPackedSettings,\n\tgetUnpackedSettings,\n\tperformServerHandshake,\n\tsensitiveHeaders\n};\n"], "mappings": ";;;;;;;AAAO,IAAM,+BAA+B;AACrC,IAAM,yBAAyB;AAC/B,IAAM,yBAAyB;AAC/B,IAAM,4BAA4B;AAClC,IAAM,4BAA4B;AAClC,IAAM,sCAAsC;AAC5C,IAAM,uCAAuC;AAC7C,IAAM,yCAAyC;AAC/C,IAAM,0CAA0C;AAChD,IAAM,8BAA8B;AACpC,IAAM,oBAAoB;AAC1B,IAAM,0BAA0B;AAChC,IAAM,2BAA2B;AACjC,IAAM,mBAAmB;AACzB,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAC9B,IAAM,qCAAqC;AAC3C,IAAM,+BAA+B;AACrC,IAAM,0CAA0C;AAChD,IAAM,uCAAuC;AAC7C,IAAM,kCAAkC;AACxC,IAAM,wCAAwC;AAC9C,IAAM,2CAA2C;AACjD,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB;AAC3B,IAAM,0BAA0B;AAChC,IAAM,qCAAqC;AAC3C,IAAM,+BAA+B;AACrC,IAAM,0CAA0C;AAChD,IAAM,uCAAuC;AAC7C,IAAM,kCAAkC;AACxC,IAAM,wCAAwC;AAC9C,IAAM,2CAA2C;AACjD,IAAM,wBAAwB;AAC9B,IAAM,2BAA2B;AACjC,IAAM,uBAAuB;AAC7B,IAAM,4BAA4B;AAClC,IAAM,mBAAmB;AACzB,IAAM,yBAAyB;AAC/B,IAAM,yBAAyB;AAC/B,IAAM,6BAA6B;AACnC,IAAM,2BAA2B;AACjC,IAAM,wBAAwB;AAC9B,IAAM,2BAA2B;AACjC,IAAM,yBAAyB;AAC/B,IAAM,iBAAiB;AACvB,IAAM,4BAA4B;AAClC,IAAM,wBAAwB;AAC9B,IAAM,4BAA4B;AAClC,IAAM,8BAA8B;AACpC,IAAM,4BAA4B;AAClC,IAAM,yBAAyB;AAC/B,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAC/B,IAAM,sBAAsB;AAC5B,IAAM,oBAAoB;AAC1B,IAAM,wBAAwB;AAC9B,IAAM,+BAA+B;AACrC,IAAM,+BAA+B;AACrC,IAAM,6BAA6B;AACnC,IAAM,sBAAsB;AAC5B,IAAM,gDAAgD;AACtD,IAAM,4CAA4C;AAClD,IAAM,4CAA4C;AAClD,IAAM,2CAA2C;AACjD,IAAM,6CAA6C;AACnD,IAAM,8CAA8C;AACpD,IAAM,6CAA6C;AACnD,IAAM,mBAAmB;AACzB,IAAM,6BAA6B;AACnC,IAAM,6BAA6B;AACnC,IAAM,0BAA0B;AAChC,IAAM,mCAAmC;AACzC,IAAM,gCAAgC;AACtC,IAAM,8BAA8B;AACpC,IAAM,4BAA4B;AAClC,IAAM,sBAAsB;AAC5B,IAAM,oBAAoB;AAC1B,IAAM,oBAAoB;AAC1B,IAAM,yBAAyB;AAC/B,IAAM,oBAAoB;AAC1B,IAAM,iCAAiC;AACvC,IAAM,6BAA6B;AACnC,IAAM,wBAAwB;AAC9B,IAAM,6BAA6B;AACnC,IAAM,oBAAoB;AAC1B,IAAM,wBAAwB;AAC9B,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB;AAC7B,IAAM,sBAAsB;AAC5B,IAAM,0BAA0B;AAChC,IAAM,yCAAyC;AAC/C,IAAM,iCAAiC;AACvC,IAAM,kBAAkB;AACxB,IAAM,yCAAyC;AAC/C,IAAM,uBAAuB;AAC7B,IAAM,0BAA0B;AAChC,IAAM,oBAAoB;AAC1B,IAAM,sCAAsC;AAC5C,IAAM,+BAA+B;AACrC,IAAM,0BAA0B;AAChC,IAAM,gCAAgC;AACtC,IAAM,gCAAgC;AACtC,IAAM,uBAAuB;AAC7B,IAAM,uCAAuC;AAC7C,IAAM,0BAA0B;AAChC,IAAM,yBAAyB;AAC/B,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAC7B,IAAM,mCAAmC;AACzC,IAAM,+BAA+B;AACrC,IAAM,wBAAwB;AAC9B,IAAM,8BAA8B;AACpC,IAAM,sCAAsC;AAC5C,IAAM,qBAAqB;AAC3B,IAAM,gCAAgC;AACtC,IAAM,gCAAgC;AACtC,IAAM,2BAA2B;AACjC,IAAM,6BAA6B;AACnC,IAAM,mBAAmB;AACzB,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAC7B,IAAM,oBAAoB;AAC1B,IAAM,wBAAwB;AAC9B,IAAM,mCAAmC;AACzC,IAAM,4BAA4B;AAClC,IAAM,sBAAsB;AAC5B,IAAM,kCAAkC;AACxC,IAAM,mCAAmC;AACzC,IAAM,uBAAuB;AAC7B,IAAM,2BAA2B;AACjC,IAAM,uBAAuB;AAC7B,IAAM,kBAAkB;AACxB,IAAM,mBAAmB;AACzB,IAAM,uBAAuB;AAC7B,IAAM,gCAAgC;AACtC,IAAM,8BAA8B;AACpC,IAAM,mBAAmB;AACzB,IAAM,gCAAgC;AACtC,IAAM,oBAAoB;AAC1B,IAAM,uBAAuB;AAC7B,IAAM,wBAAwB;AAC9B,IAAM,uBAAuB;AAC7B,IAAM,oBAAoB;AAC1B,IAAM,sBAAsB;AAC5B,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAC1B,IAAM,qBAAqB;AAC3B,IAAM,oBAAoB;AAC1B,IAAM,oBAAoB;AAC1B,IAAM,qBAAqB;AAC3B,IAAM,0BAA0B;AAChC,IAAM,0BAA0B;AAChC,IAAM,qBAAqB;AAC3B,IAAM,6BAA6B;AACnC,IAAM,2BAA2B;AACjC,IAAM,oBAAoB;AAC1B,IAAM,uBAAuB;AAC7B,IAAM,0BAA0B;AAChC,IAAM,qBAAqB;AAC3B,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,wBAAwB;AAC9B,IAAM,yBAAyB;AAC/B,IAAM,mBAAmB;AACzB,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;AAC5B,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAC5B,IAAM,0BAA0B;AAChC,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;AAC5B,IAAM,iCAAiC;AACvC,IAAM,+BAA+B;AACrC,IAAM,uBAAuB;AAC7B,IAAM,kCAAkC;AACxC,IAAM,yBAAyB;AAC/B,IAAM,0BAA0B;AAChC,IAAM,iBAAiB;AACvB,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAC7B,IAAM,4CAA4C;AAClD,IAAM,yBAAyB;AAC/B,IAAM,4BAA4B;AAClC,IAAM,8BAA8B;AACpC,IAAM,2BAA2B;AACjC,IAAM,+BAA+B;AACrC,IAAM,sBAAsB;AAC5B,IAAM,+BAA+B;AACrC,IAAM,gCAAgC;AACtC,IAAM,oBAAoB;AAC1B,IAAM,wBAAwB;AAC9B,IAAM,2BAA2B;AACjC,IAAM,wBAAwB;AAC9B,IAAM,iCAAiC;AACvC,IAAM,iCAAiC;AACvC,IAAM,0BAA0B;AAChC,IAAM,2BAA2B;AACjC,IAAM,+BAA+B;AACrC,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAC9B,IAAM,iCAAiC;AACvC,IAAM,6BAA6B;AACnC,IAAM,4CAA4C;AAClD,IAAM,8BAA8B;AACpC,IAAM,uBAAuB;AAC7B,IAAM,mBAAmB;AACzB,IAAM,8BAA8B;AACpC,IAAM,kCAAkC;AACxC,IAAM,gCAAgC;AACtC,IAAM,2BAA2B;AACjC,IAAM,qCAAqC;AAC3C,IAAM,oCAAoC;AAC1C,IAAM,iCAAiC;AACvC,IAAM,qBAAqB;AAC3B,IAAM,kCAAkC;AACxC,IAAM,mCAAmC;AACzC,IAAM,qBAAqB;AAC3B,IAAM,gCAAgC;AACtC,IAAM,wBAAwB;AAC9B,IAAM,+BAA+B;AACrC,IAAM,oCAAoC;AAC1C,IAAM,gCAAgC;AACtC,IAAM,8CAA8C;AACpD,IAAM,4CAA4C;AAClD,IAAM,oCAAoC;AAC1C,IAAM,8BAA8B;AACpC,IAAM,0BAA0B;AAChC,IAAM,kCAAkC;AACxC,IAAM,8BAA8B;AACpC,IAAM,yCAAyC;AAC/C,IAAM,sCAAsC;AAC5C,IAAM,mCAAmC;AACzC,IAAM,4BAA4B;AAClC,IAAM,uCAAuC;AAC7C,IAAM,2BAA2B;AACjC,IAAM,8CAA8C;;;AC7OpD,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AACO,IAAM,qBAAqC,eAAe,0BAA0B;AACpF,IAAM,eAA+B,eAAe,oBAAoB;AACxE,IAAM,UAA0B,eAAe,eAAe;AAC9D,IAAM,yBAAyC,eAAe,+BAA+B;AAC7F,IAAM,qBAAqC,oBAAoB,0BAA0B;AACzF,IAAM,sBAAsC,oBAAoB,2BAA2B;AAC3F,IAAM,qBAAqB,WAAW;AAC5C,SAAO,uBAAO,OAAO;AAAA,IACpB,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,cAAc;AAAA,IACd,sBAAsB;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,EACxB,CAAC;AACF;AACO,IAAM,oBAAoB,WAAW;AAC3C,SAAO,OAAO,KAAK,EAAE;AACtB;AACO,IAAM,sBAAsB,WAAW;AAC7C,SAAO,uBAAO,OAAO,CAAC,CAAC;AACxB;AACO,IAAM,mBAAmC,OAAO,+BAA+B;AACtF,IAAO,gBAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}