{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/fs/constants.mjs"], "sourcesContent": ["export const UV_FS_SYMLINK_DIR = 1;\nexport const UV_FS_SYMLINK_JUNCTION = 2;\nexport const O_RDONLY = 0;\nexport const O_WRONLY = 1;\nexport const O_RDWR = 2;\nexport const UV_DIRENT_UNKNOWN = 0;\nexport const UV_DIRENT_FILE = 1;\nexport const UV_DIRENT_DIR = 2;\nexport const UV_DIRENT_LINK = 3;\nexport const UV_DIRENT_FIFO = 4;\nexport const UV_DIRENT_SOCKET = 5;\nexport const UV_DIRENT_CHAR = 6;\nexport const UV_DIRENT_BLOCK = 7;\nexport const EXTENSIONLESS_FORMAT_JAVASCRIPT = 0;\nexport const EXTENSIONLESS_FORMAT_WASM = 1;\nexport const S_IFMT = 61440;\nexport const S_IFREG = 32768;\nexport const S_IFDIR = 16384;\nexport const S_IFCHR = 8192;\nexport const S_IFBLK = 24576;\nexport const S_IFIFO = 4096;\nexport const S_IFLNK = 40960;\nexport const S_IFSOCK = 49152;\nexport const O_CREAT = 64;\nexport const O_EXCL = 128;\nexport const UV_FS_O_FILEMAP = 0;\nexport const O_NOCTTY = 256;\nexport const O_TRUNC = 512;\nexport const O_APPEND = 1024;\nexport const O_DIRECTORY = 65536;\nexport const O_NOATIME = 262144;\nexport const O_NOFOLLOW = 131072;\nexport const O_SYNC = 1052672;\nexport const O_DSYNC = 4096;\nexport const O_DIRECT = 16384;\nexport const O_NONBLOCK = 2048;\nexport const S_IRWXU = 448;\nexport const S_IRUSR = 256;\nexport const S_IWUSR = 128;\nexport const S_IXUSR = 64;\nexport const S_IRWXG = 56;\nexport const S_IRGRP = 32;\nexport const S_IWGRP = 16;\nexport const S_IXGRP = 8;\nexport const S_IRWXO = 7;\nexport const S_IROTH = 4;\nexport const S_IWOTH = 2;\nexport const S_IXOTH = 1;\nexport const F_OK = 0;\nexport const R_OK = 4;\nexport const W_OK = 2;\nexport const X_OK = 1;\nexport const UV_FS_COPYFILE_EXCL = 1;\nexport const COPYFILE_EXCL = 1;\nexport const UV_FS_COPYFILE_FICLONE = 2;\nexport const COPYFILE_FICLONE = 2;\nexport const UV_FS_COPYFILE_FICLONE_FORCE = 4;\nexport const COPYFILE_FICLONE_FORCE = 4;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAM,oBAAoB;AAC1B,IAAM,yBAAyB;AAC/B,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,SAAS;AACf,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AACzB,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AACxB,IAAM,kCAAkC;AACxC,IAAM,4BAA4B;AAClC,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,kBAAkB;AACxB,IAAM,WAAW;AACjB,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,aAAa;AACnB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,sBAAsB;AAC5B,IAAM,gBAAgB;AACtB,IAAM,yBAAyB;AAC/B,IAAM,mBAAmB;AACzB,IAAM,+BAA+B;AACrC,IAAM,yBAAyB;", "names": []}