{"version": 3, "names": ["React", "NativeModules", "RNMBXShapeSourceNativeComponent", "NativeRNMBXShapeSourceModule", "toJSONString", "cloneReactChildrenWithProps", "isFunction", "isAndroid", "copyPropertiesAsDeprecated", "AbstractSource", "NativeBridgeComponent", "jsx", "_jsx", "MapboxGL", "RNMBXModule", "ShapeSource", "NATIVE_ASSETS_KEY", "defaultProps", "id", "StyleSource", "DefaultSourceID", "constructor", "props", "_setNativeRef", "nativeRef", "setNativeRef", "_runPendingNativeMethods", "getClusterExpansionZoom", "feature", "res", "_runNativeMethod", "_nativeRef", "JSON", "stringify", "data", "getClusterLeaves", "limit", "offset", "parse", "getClusterChildren", "setNativeProps", "shallowProps", "Object", "assign", "shape", "_getShape", "_decodePayload", "payload", "onPress", "event", "nativeEvent", "features", "coordinates", "point", "newEvent", "key", "console", "warn", "origNativeEvent", "render", "existing", "url", "hitbox", "hasPressListener", "onMapboxShapeSourcePress", "bind", "cluster", "clusterRadius", "clusterMaxZoomLevel", "clusterProperties", "maxZoomLevel", "buffer", "tolerance", "lineMetrics", "undefined", "ref", "children", "sourceID"], "sourceRoot": "../../../src", "sources": ["components/ShapeSource.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAEEC,aAAa,QAER,cAAc;AAErB,OAAOC,+BAA+B,MAAM,0CAA0C;AACtF,OAAOC,4BAA4B,MAAM,uCAAuC;AAChF,SACEC,YAAY,EACZC,2BAA2B,EAC3BC,UAAU,EACVC,SAAS,QACJ,UAAU;AACjB,SAASC,0BAA0B,QAAQ,sBAAsB;AAIjE,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,qBAAqB,MAAM,yBAAyB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE5D,MAAMC,QAAQ,GAAGZ,aAAa,CAACa,WAAW;AA4H1C;AACA;AACA;AACA;AACA,OAAO,MAAMC,WAAW,SAASL,qBAAqB,CACpDD,cAAc,EACdN,4BACF,CAAC,CAAC;EACA,OAAOa,iBAAiB,GAAG,QAAQ;EAEnC,OAAOC,YAAY,GAAG;IACpBC,EAAE,EAAEL,QAAQ,CAACM,WAAW,CAACC;EAC3B,CAAC;EAEDC,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;EACd;EAEAC,aAAaA,CACXC,SAAiE,EACjE;IACA,IAAI,CAACC,YAAY,CAACD,SAAS,CAAC;IAC5B,KAAK,CAACE,wBAAwB,CAACF,SAAS,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMG,uBAAuBA,CAC3BC,OAAiC,EAChB;IACjB,MAAMC,GAAqB,GAAG,MAAM,IAAI,CAACC,gBAAgB,CACvD,yBAAyB,EACzB,IAAI,CAACC,UAAU,EACf,CAACC,IAAI,CAACC,SAAS,CAACL,OAAO,CAAC,CAC1B,CAAC;IACD,OAAOC,GAAG,CAACK,IAAI;EACjB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,gBAAgBA,CACpBP,OAAiC,EACjCQ,KAAa,EACbC,MAAc,EACd;IACA,MAAMR,GAAqB,GAAG,MAAM,IAAI,CAACC,gBAAgB,CACvD,kBAAkB,EAClB,IAAI,CAACC,UAAU,EACf,CAACC,IAAI,CAACC,SAAS,CAACL,OAAO,CAAC,EAAEQ,KAAK,EAAEC,MAAM,CACzC,CAAC;IAED,IAAI9B,SAAS,CAAC,CAAC,EAAE;MACf,OAAOyB,IAAI,CAACM,KAAK,CAACT,GAAG,CAACK,IAAI,CAAC;IAC7B;IACA,OAAOL,GAAG,CAACK,IAAI;EACjB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMK,kBAAkBA,CAACX,OAAiC,EAAE;IAC1D,MAAMC,GAAqB,GAAG,MAAM,IAAI,CAACC,gBAAgB,CACvD,oBAAoB,EACpB,IAAI,CAACC,UAAU,EACf,CAACC,IAAI,CAACC,SAAS,CAACL,OAAO,CAAC,CAC1B,CAAC;IAED,IAAIrB,SAAS,CAAC,CAAC,EAAE;MACf,OAAOyB,IAAI,CAACM,KAAK,CAACT,GAAG,CAACK,IAAI,CAAC;IAC7B;IACA,OAAOL,GAAG,CAACK,IAAI;EACjB;EAEAM,cAAcA,CAAClB,KAAkB,EAAE;IACjC,MAAMmB,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErB,KAAK,CAAC;;IAE7C;IACA,IAAImB,YAAY,CAACG,KAAK,IAAI,OAAOH,YAAY,CAACG,KAAK,KAAK,QAAQ,EAAE;MAChEH,YAAY,CAACG,KAAK,GAAGZ,IAAI,CAACC,SAAS,CAACQ,YAAY,CAACG,KAAK,CAAC;IACzD;IAEA,KAAK,CAACJ,cAAc,CAACC,YAAY,CAAC;EACpC;EAEAI,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACvB,KAAK,CAACsB,KAAK,EAAE;MACrB;IACF;IACA,OAAOxC,YAAY,CAAC,IAAI,CAACkB,KAAK,CAACsB,KAAK,CAAC;EACvC;EAEAE,cAAcA,CAACC,OAA8B,EAAgB;IAC3D;IACA;IACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,OAAOf,IAAI,CAACM,KAAK,CAACS,OAAO,CAAC;IAC5B,CAAC,MAAM;MACL,OAAOA,OAAO;IAChB;EACF;EAEAC,OAAOA,CACLC,KAEE,EACF;IACA,MAAMF,OAAO,GAAG,IAAI,CAACD,cAAc,CAACG,KAAK,CAACC,WAAW,CAACH,OAAO,CAAC;IAC9D,MAAM;MAAEI,QAAQ;MAAEC,WAAW;MAAEC;IAAM,CAAC,GAAGN,OAAO;IAChD,IAAIO,QAAgC,GAAG;MACrCH,QAAQ;MACRC,WAAW;MACXC;IACF,CAAC;IAEDC,QAAQ,GAAG9C,0BAA0B,CACnCyC,KAAK,EACLK,QAAQ,EACPC,GAAG,IAAK;MACPC,OAAO,CAACC,IAAI,CACV,SAASF,GAAG,kEACd,CAAC;IACH,CAAC,EACD;MACEL,WAAW,EAAGQ,eAAwB,KAAM;QAC1C,GAAIA,eAAgC;QACpCX,OAAO,EAAEI,QAAQ,CAAC,CAAC;MACrB,CAAC;IACH,CACF,CAAC;IACD,IAAI,CAAC7B,KAAK,CAAC0B,OAAO,GAAGM,QAAQ,CAAC;EAChC;EAEAK,MAAMA,CAAA,EAAG;IACP,MAAMrC,KAAK,GAAG;MACZJ,EAAE,EAAE,IAAI,CAACI,KAAK,CAACJ,EAAE;MACjB0C,QAAQ,EAAE,IAAI,CAACtC,KAAK,CAACsC,QAAQ;MAC7BC,GAAG,EAAE,IAAI,CAACvC,KAAK,CAACuC,GAAG;MACnBjB,KAAK,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC;MACvBiB,MAAM,EAAE,IAAI,CAACxC,KAAK,CAACwC,MAAM;MACzBC,gBAAgB,EAAEzD,UAAU,CAAC,IAAI,CAACgB,KAAK,CAAC0B,OAAO,CAAC;MAChDgB,wBAAwB,EAAE,IAAI,CAAChB,OAAO,CAACiB,IAAI,CAAC,IAAI,CAAC;MACjDC,OAAO,EAAE,IAAI,CAAC5C,KAAK,CAAC4C,OAAO,GAAG,CAAC,GAAG,CAAC;MACnCC,aAAa,EAAE,IAAI,CAAC7C,KAAK,CAAC6C,aAAa;MACvCC,mBAAmB,EAAE,IAAI,CAAC9C,KAAK,CAAC8C,mBAAmB;MACnDC,iBAAiB,EAAE,IAAI,CAAC/C,KAAK,CAAC+C,iBAAiB;MAC/CC,YAAY,EAAE,IAAI,CAAChD,KAAK,CAACgD,YAAY;MACrCC,MAAM,EAAE,IAAI,CAACjD,KAAK,CAACiD,MAAM;MACzBC,SAAS,EAAE,IAAI,CAAClD,KAAK,CAACkD,SAAS;MAC/BC,WAAW,EAAE,IAAI,CAACnD,KAAK,CAACmD,WAAW;MACnCzB,OAAO,EAAE0B,SAAS;MAClBC,GAAG,EACDnD,SAAiE,IAC9D,IAAI,CAACD,aAAa,CAACC,SAAS;IACnC,CAAC;IAED;MAAA;MACE;MACAZ,IAAA,CAACV,+BAA+B;QAAA,GAAKoB,KAAK;QAAAsD,QAAA,EACvCvE,2BAA2B,CAAC,IAAI,CAACiB,KAAK,CAACsD,QAAQ,EAAE;UAChDC,QAAQ,EAAE,IAAI,CAACvD,KAAK,CAACJ;QACvB,CAAC;MAAC,CAC6B;IAAC;EAEtC;AACF", "ignoreList": []}