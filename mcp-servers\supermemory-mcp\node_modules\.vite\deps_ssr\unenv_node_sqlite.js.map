{"version": 3, "sources": ["../../unenv/dist/runtime/node/sqlite.mjs"], "sourcesContent": ["import { notImplementedClass } from \"../_internal/utils.mjs\";\nexport const DatabaseSync = /* @__PURE__ */ notImplementedClass(\"sqlite.DatabaseSync\");\nexport const StatementSync = /* @__PURE__ */ notImplementedClass(\"sqlite.StatementSync\");\nexport const constants = {};\nexport default {\n\tDatabaseSync,\n\tStatementSync,\n\tconstants\n};\n"], "mappings": ";;;;;;AACO,IAAM,eAA+B,oBAAoB,qBAAqB;AAC9E,IAAM,gBAAgC,oBAAoB,sBAAsB;AAChF,IAAM,YAAY,CAAC;AAC1B,IAAO,iBAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AACD;", "names": []}