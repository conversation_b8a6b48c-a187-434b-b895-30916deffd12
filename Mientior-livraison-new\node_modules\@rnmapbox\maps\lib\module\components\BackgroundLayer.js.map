{"version": 3, "names": ["React", "NativeModules", "RNMBXBackgroundLayerNativeComponent", "AbstractLayer", "jsx", "_jsx", "MapboxGL", "RNMBXModule", "<PERSON><PERSON><PERSON><PERSON>", "defaultProps", "sourceID", "StyleSource", "DefaultSourceID", "render", "props", "baseProps", "sourceLayerID", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../../src", "sources": ["components/BackgroundLayer.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,cAAc;AAO5C,OAAOC,mCAAmC,MAAM,8CAA8C;AAE9F,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE5C,MAAMC,QAAQ,GAAGL,aAAa,CAACM,WAAW;AAiE1C,MAAMC,eAAe,SAASL,aAAa,CAAyB;EAClE,OAAOM,YAAY,GAAG;IACpBC,QAAQ,EAAEJ,QAAQ,CAACK,WAAW,CAACC;EACjC,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAMC,KAAK,GAAG;MACZ,GAAG,IAAI,CAACC,SAAS;MACjBC,aAAa,EAAE,IAAI,CAACF,KAAK,CAACE;IAC5B,CAAC;IACD,oBACEX,IAAA,CAACH;IACC;IAAA;MACAe,GAAG,EAAE,IAAI,CAACC,cAAe;MAAA,GACrBJ;IAAK,CACV,CAAC;EAEN;AACF;AAEA,eAAeN,eAAe", "ignoreList": []}