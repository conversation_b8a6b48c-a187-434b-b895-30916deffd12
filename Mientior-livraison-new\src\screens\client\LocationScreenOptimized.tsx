import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
  Dimensions,
  StatusBar,
  Animated,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
  accuracy?: number;
}

interface RecentLocation {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
}

const LocationScreenOptimized: React.FC = () => {
  const navigation = useNavigation();

  // État local
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [showRecentLocations, setShowRecentLocations] = useState(true);

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;

  // Lieux récents simulés (en production, ces données viendraient du stockage local)
  const [recentLocations] = useState<RecentLocation[]>([
    {
      id: '1',
      name: 'Plateau, Abidjan',
      address: 'Plateau, Abidjan, Côte d\'Ivoire',
      latitude: 5.3197,
      longitude: -4.0267,
    },
    {
      id: '2',
      name: 'Cocody, Abidjan',
      address: 'Cocody, Abidjan, Côte d\'Ivoire',
      latitude: 5.3364,
      longitude: -3.9811,
    },
    {
      id: '3',
      name: 'Marcory, Abidjan',
      address: 'Marcory, Abidjan, Côte d\'Ivoire',
      latitude: 5.2669,
      longitude: -3.9969,
    },
  ]);

  useEffect(() => {
    initializeScreen();
  }, []);

  const initializeScreen = async () => {
    // Animations d'entrée
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Demander les permissions
    await requestLocationPermission();
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission requise',
          'L\'accès à la localisation est nécessaire pour cette fonctionnalité.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.log('Erreur permission:', error);
    }
  };

  const getAddressFromCoordinates = async (latitude: number, longitude: number) => {
    try {
      const result = await Location.reverseGeocodeAsync({ latitude, longitude });
      if (result.length > 0) {
        const address = result[0];
        const formattedAddress = `${address.street || ''} ${address.name || ''}, ${address.city || ''}, ${address.country || ''}`.trim();
        setSelectedLocation(prev => prev ? { ...prev, address: formattedAddress } : null);
      }
    } catch (error) {
      console.log('Erreur géocodage inverse:', error);
    }
  };

  const handleCurrentLocationPress = async () => {
    setLoading(true);
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission requise',
          'L\'accès à la localisation est nécessaire pour cette fonctionnalité.',
          [{ text: 'OK' }]
        );
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      setSelectedLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || undefined,
      });

      // Obtenir l'adresse
      await getAddressFromCoordinates(location.coords.latitude, location.coords.longitude);
      setShowRecentLocations(false);

    } catch (error) {
      Alert.alert(
        'Erreur de localisation',
        'Impossible d\'obtenir votre position actuelle. Vérifiez que le GPS est activé.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRecentLocationPress = (recentLocation: RecentLocation) => {
    setSelectedLocation({
      latitude: recentLocation.latitude,
      longitude: recentLocation.longitude,
      address: recentLocation.address,
    });
    setShowRecentLocations(false);
  };

  const handleManualEntry = () => {
    Alert.prompt(
      'Adresse manuelle',
      'Entrez votre adresse :',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Confirmer',
          onPress: (address) => {
            if (address && address.trim()) {
              setSelectedLocation({
                latitude: 5.348, // Coordonnées par défaut Côte d'Ivoire
                longitude: -4.007,
                address: address.trim(),
              });
              setShowRecentLocations(false);
            }
          },
        },
      ],
      'plain-text'
    );
  };

  const handleConfirmLocation = () => {
    if (!selectedLocation) {
      Alert.alert('Aucune position sélectionnée', 'Veuillez sélectionner une position.');
      return;
    }

    // Animation du bouton
    Animated.sequence([
      Animated.timing(buttonScaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonScaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    console.log('Position confirmée:', selectedLocation);
    navigation.goBack();
  };

  const formatAddress = (address: string) => {
    if (address.length > 50) {
      return address.substring(0, 50) + '...';
    }
    return address;
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      {/* Header */}
      <Animated.View style={[
        styles.header,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Votre localisation</Text>
      </Animated.View>

      {/* Search Bar */}
      <Animated.View style={[
        styles.searchContainer,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        <View style={styles.searchInputWrapper}>
          <Ionicons name="search" size={20} color="#9CA3AF" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher une adresse"
            placeholderTextColor="#9CA3AF"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </Animated.View>

      {/* Content */}
      <Animated.View style={[
        styles.content,
        {
          opacity: fadeAnim,
        }
      ]}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Map Placeholder - Carte visuelle simulée */}
          <View style={styles.mapPlaceholder}>
            <View style={styles.mapVisual}>
              {/* Grille de carte simulée */}
              <View style={styles.mapGrid}>
                {Array.from({ length: 6 }, (_, row) => (
                  <View key={row} style={styles.mapRow}>
                    {Array.from({ length: 4 }, (_, col) => (
                      <View key={col} style={styles.mapCell} />
                    ))}
                  </View>
                ))}
              </View>

              {/* Marqueur de position */}
              {selectedLocation && (
                <View style={styles.mapMarker}>
                  <Ionicons name="location" size={32} color="#FF0000" />
                </View>
              )}

              {/* Overlay avec informations */}
              <View style={styles.mapOverlay}>
                <View style={styles.mapInfo}>
                  <Ionicons name="map" size={24} color="#0DCAA8" />
                  <Text style={styles.mapInfoText}>🗺️ CARTE INTERACTIVE</Text>
                </View>

                <View style={styles.mapCenter}>
                  <View style={styles.mapCenterMarker}>
                    <Ionicons name="location" size={40} color="#FF0000" />
                    <Text style={styles.mapCenterText}>CÔTE D'IVOIRE</Text>
                  </View>
                </View>

                {!selectedLocation && (
                  <View style={styles.mapInstructions}>
                    <Text style={styles.mapHint}>
                      🎯 Utilisez les boutons ci-dessous pour définir votre position
                    </Text>
                  </View>
                )}

                {selectedLocation && (
                  <View style={styles.selectedLocationOverlay}>
                    <Text style={styles.selectedLocationText}>
                      ✅ Position confirmée !
                    </Text>
                    <Text style={styles.coordinatesText}>
                      📍 {selectedLocation.latitude.toFixed(4)}, {selectedLocation.longitude.toFixed(4)}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>

          {/* Quick Actions */}
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleCurrentLocationPress}
              disabled={loading}
            >
              <Ionicons name="locate" size={24} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>
                {loading ? 'Localisation...' : 'Ma position actuelle'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButtonSecondary}
              onPress={handleManualEntry}
            >
              <Ionicons name="create-outline" size={24} color="#0DCAA8" />
              <Text style={styles.actionButtonSecondaryText}>Saisir manuellement</Text>
            </TouchableOpacity>
          </View>

          {/* Recent Locations */}
          {showRecentLocations && (
            <View style={styles.recentLocations}>
              <Text style={styles.sectionTitle}>Lieux récents</Text>
              {recentLocations.map((location) => (
                <TouchableOpacity
                  key={location.id}
                  style={styles.recentLocationItem}
                  onPress={() => handleRecentLocationPress(location)}
                >
                  <Ionicons name="time-outline" size={20} color="#6B7280" />
                  <View style={styles.recentLocationDetails}>
                    <Text style={styles.recentLocationName}>{location.name}</Text>
                    <Text style={styles.recentLocationAddress}>{location.address}</Text>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Selected Location */}
          {selectedLocation && (
            <View style={styles.selectedLocation}>
              <Text style={styles.sectionTitle}>Position sélectionnée</Text>
              <View style={styles.locationCard}>
                <Ionicons name="location" size={24} color="#0DCAA8" />
                <View style={styles.locationDetails}>
                  <Text style={styles.locationTitle}>
                    {selectedLocation.address ? formatAddress(selectedLocation.address) : 'Position actuelle'}
                  </Text>
                  <Text style={styles.locationCoordinates}>
                    Lat: {selectedLocation.latitude.toFixed(4)}, Long: {selectedLocation.longitude.toFixed(4)}
                  </Text>
                  {selectedLocation.accuracy && (
                    <Text style={styles.locationAccuracy}>
                      Précision: ±{Math.round(selectedLocation.accuracy)}m
                    </Text>
                  )}
                </View>
              </View>
            </View>
          )}
        </ScrollView>
      </Animated.View>

      {/* Bottom Actions */}
      <Animated.View style={[
        styles.bottomActions,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        <Animated.View style={{ transform: [{ scale: buttonScaleAnim }] }}>
          <TouchableOpacity
            style={[
              styles.confirmButton,
              !selectedLocation && styles.confirmButtonDisabled
            ]}
            onPress={handleConfirmLocation}
            disabled={!selectedLocation}
          >
            <Text style={styles.confirmButtonText}>Confirmer ma position</Text>
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>

      {/* Bottom Indicator */}
      <View style={styles.bottomIndicator}>
        <View style={styles.indicator} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  searchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: 12,
    minHeight: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    paddingVertical: 12,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  mapPlaceholder: {
    height: 300,
    backgroundColor: '#E5F3F0',
    borderRadius: 16,
    marginVertical: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  mapVisual: {
    flex: 1,
    position: 'relative',
  },
  mapGrid: {
    flex: 1,
    backgroundColor: '#D1FAE5',
  },
  mapRow: {
    flex: 1,
    flexDirection: 'row',
  },
  mapCell: {
    flex: 1,
    borderWidth: 0.5,
    borderColor: '#A7F3D0',
    backgroundColor: '#ECFDF5',
  },
  mapMarker: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -16 }, { translateY: -16 }],
    zIndex: 10,
  },
  mapOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
    padding: 16,
  },
  mapInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    alignSelf: 'flex-start',
  },
  mapInfoText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#0DCAA8',
    marginLeft: 8,
  },
  mapCenter: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
    alignItems: 'center',
  },
  mapCenterMarker: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderWidth: 3,
    borderColor: '#0DCAA8',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  mapCenterText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#0DCAA8',
    marginTop: 8,
    textAlign: 'center',
  },
  mapInstructions: {
    alignSelf: 'center',
    backgroundColor: 'rgba(13, 202, 168, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 20,
  },
  mapHint: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  selectedLocationOverlay: {
    backgroundColor: 'rgba(13, 202, 168, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignSelf: 'center',
  },
  selectedLocationText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 4,
  },
  coordinatesText: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.9,
  },
  quickActions: {
    marginVertical: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0DCAA8',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginBottom: 12,
    shadowColor: '#0DCAA8',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  actionButtonSecondary: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#0DCAA8',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  actionButtonSecondaryText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0DCAA8',
    marginLeft: 8,
  },
  recentLocations: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  recentLocationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  recentLocationDetails: {
    flex: 1,
    marginLeft: 12,
  },
  recentLocationName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  recentLocationAddress: {
    fontSize: 14,
    color: '#6B7280',
  },
  selectedLocation: {
    marginVertical: 16,
  },
  locationCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F0FDF4',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#0DCAA8',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  locationDetails: {
    flex: 1,
    marginLeft: 12,
  },
  locationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  locationCoordinates: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  locationAccuracy: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  bottomActions: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  confirmButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    shadowColor: '#0DCAA8',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  confirmButtonDisabled: {
    backgroundColor: '#9CA3AF',
    shadowOpacity: 0.1,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  bottomIndicator: {
    alignItems: 'center',
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    paddingTop: 8,
    backgroundColor: '#FFFFFF',
  },
  indicator: {
    width: 134,
    height: 5,
    backgroundColor: '#000000',
    borderRadius: 3,
  },
});

export default LocationScreenOptimized;
