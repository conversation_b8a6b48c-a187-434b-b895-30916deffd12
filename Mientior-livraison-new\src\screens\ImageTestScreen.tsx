import React from 'react';
import { View, Text, StyleSheet, Image, ScrollView } from 'react-native';
import { PlaceholderImages } from '../assets/images/placeholders';

const ImageTestScreen: React.FC = () => {
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Test des Images</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Logo de l'application</Text>
        <Image 
          source={{ uri: PlaceholderImages.APP_LOGO_BASE64 }} 
          style={styles.logo}
          resizeMode="contain"
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Images d'onboarding</Text>
        <View style={styles.imageRow}>
          <Image 
            source={{ uri: PlaceholderImages.FOOD_DELIVERY_BASE64 }} 
            style={styles.onboardingImage}
            resizeMode="contain"
          />
          <Image 
            source={{ uri: PlaceholderImages.PACKAGE_DELIVERY_BASE64 }} 
            style={styles.onboardingImage}
            resizeMode="contain"
          />
          <Image 
            source={{ uri: PlaceholderImages.SHOPPING_DELIVERY_BASE64 }} 
            style={styles.onboardingImage}
            resizeMode="contain"
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Placeholders</Text>
        <View style={styles.imageRow}>
          <Image 
            source={{ uri: PlaceholderImages.DEFAULT_AVATAR_BASE64 }} 
            style={styles.avatar}
            resizeMode="contain"
          />
          <Image 
            source={{ uri: PlaceholderImages.RESTAURANT_PLACEHOLDER_BASE64 }} 
            style={styles.placeholder}
            resizeMode="cover"
          />
          <Image 
            source={{ uri: PlaceholderImages.PRODUCT_PLACEHOLDER_BASE64 }} 
            style={styles.placeholder}
            resizeMode="cover"
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 30,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 15,
  },
  logo: {
    width: 100,
    height: 100,
    alignSelf: 'center',
    borderRadius: 16,
  },
  imageRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  onboardingImage: {
    width: 80,
    height: 80,
    borderRadius: 12,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  placeholder: {
    width: 80,
    height: 60,
    borderRadius: 8,
  },
});

export default ImageTestScreen;
