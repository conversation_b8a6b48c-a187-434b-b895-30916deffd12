import {
  createNotImplementedError
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/internal/domain/domain.mjs
import { EventEmitter } from "node:events";
var Domain = class extends EventEmitter {
  __unenv__ = true;
  members = [];
  add() {
  }
  enter() {
  }
  exit() {
  }
  remove() {
  }
  bind(callback) {
    throw createNotImplementedError("Domain.bind");
  }
  intercept(callback) {
    throw createNotImplementedError("Domain.intercept");
  }
  run(fn, ...args) {
    throw createNotImplementedError("Domain.run");
  }
};

// node_modules/unenv/dist/runtime/node/domain.mjs
var create = function() {
  return new Domain();
};
var createDomain = create;
var _domain = create();
var active = () => _domain;
var _stack = [];
var domain_default = {
  Domain,
  _stack,
  active,
  create,
  createDomain
};
export {
  Domain,
  _stack,
  active,
  create,
  createDomain,
  domain_default as default
};
//# sourceMappingURL=unenv_node_domain.js.map
