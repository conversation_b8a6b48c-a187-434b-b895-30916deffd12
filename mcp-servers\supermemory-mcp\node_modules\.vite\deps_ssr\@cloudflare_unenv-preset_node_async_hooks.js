import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/internal/async_hooks/async-local-storage.mjs
var _AsyncLocalStorage = class {
  __unenv__ = true;
  _currentStore;
  _enterStore;
  _enabled = true;
  getStore() {
    return this._currentStore ?? this._enterStore;
  }
  disable() {
    this._enabled = false;
  }
  enable() {
    this._enabled = true;
  }
  enterWith(store) {
    this._enterStore = store;
  }
  run(store, callback, ...args) {
    this._currentStore = store;
    const res = callback(...args);
    this._currentStore = void 0;
    return res;
  }
  exit(callback, ...args) {
    const _previousStore = this._currentStore;
    this._currentStore = void 0;
    const res = callback(...args);
    this._currentStore = _previousStore;
    return res;
  }
  static snapshot() {
    throw new Error("[unenv] `AsyncLocalStorage.snapshot` is not implemented!");
  }
};
var AsyncLocalStorage = globalThis.AsyncLocalStorage || _AsyncLocalStorage;

// node_modules/unenv/dist/runtime/node/internal/async_hooks/async-hook.mjs
var kInit = Symbol("init");
var kBefore = Symbol("before");
var kAfter = Symbol("after");
var kDestroy = Symbol("destroy");
var kPromiseResolve = Symbol("promiseResolve");
var _AsyncHook = class {
  __unenv__ = true;
  _enabled = false;
  _callbacks = {};
  constructor(callbacks = {}) {
    this._callbacks = callbacks;
  }
  enable() {
    this._enabled = true;
    return this;
  }
  disable() {
    this._enabled = false;
    return this;
  }
  get [kInit]() {
    return this._callbacks.init;
  }
  get [kBefore]() {
    return this._callbacks.before;
  }
  get [kAfter]() {
    return this._callbacks.after;
  }
  get [kDestroy]() {
    return this._callbacks.destroy;
  }
  get [kPromiseResolve]() {
    return this._callbacks.promiseResolve;
  }
};
var createHook = function createHook2(callbacks) {
  const asyncHook = new _AsyncHook(callbacks);
  return asyncHook;
};
var executionAsyncId = function executionAsyncId2() {
  return 0;
};
var executionAsyncResource = function() {
  return /* @__PURE__ */ Object.create(null);
};
var triggerAsyncId = function() {
  return 0;
};
var asyncWrapProviders = Object.assign(/* @__PURE__ */ Object.create(null), {
  NONE: 0,
  DIRHANDLE: 1,
  DNSCHANNEL: 2,
  ELDHISTOGRAM: 3,
  FILEHANDLE: 4,
  FILEHANDLECLOSEREQ: 5,
  BLOBREADER: 6,
  FSEVENTWRAP: 7,
  FSREQCALLBACK: 8,
  FSREQPROMISE: 9,
  GETADDRINFOREQWRAP: 10,
  GETNAMEINFOREQWRAP: 11,
  HEAPSNAPSHOT: 12,
  HTTP2SESSION: 13,
  HTTP2STREAM: 14,
  HTTP2PING: 15,
  HTTP2SETTINGS: 16,
  HTTPINCOMINGMESSAGE: 17,
  HTTPCLIENTREQUEST: 18,
  JSSTREAM: 19,
  JSUDPWRAP: 20,
  MESSAGEPORT: 21,
  PIPECONNECTWRAP: 22,
  PIPESERVERWRAP: 23,
  PIPEWRAP: 24,
  PROCESSWRAP: 25,
  PROMISE: 26,
  QUERYWRAP: 27,
  QUIC_ENDPOINT: 28,
  QUIC_LOGSTREAM: 29,
  QUIC_PACKET: 30,
  QUIC_SESSION: 31,
  QUIC_STREAM: 32,
  QUIC_UDP: 33,
  SHUTDOWNWRAP: 34,
  SIGNALWRAP: 35,
  STATWATCHER: 36,
  STREAMPIPE: 37,
  TCPCONNECTWRAP: 38,
  TCPSERVERWRAP: 39,
  TCPWRAP: 40,
  TTYWRAP: 41,
  UDPSENDWRAP: 42,
  UDPWRAP: 43,
  SIGINTWATCHDOG: 44,
  WORKER: 45,
  WORKERHEAPSNAPSHOT: 46,
  WRITEWRAP: 47,
  ZLIB: 48,
  CHECKPRIMEREQUEST: 49,
  PBKDF2REQUEST: 50,
  KEYPAIRGENREQUEST: 51,
  KEYGENREQUEST: 52,
  KEYEXPORTREQUEST: 53,
  CIPHERREQUEST: 54,
  DERIVEBITSREQUEST: 55,
  HASHREQUEST: 56,
  RANDOMBYTESREQUEST: 57,
  RANDOMPRIMEREQUEST: 58,
  SCRYPTREQUEST: 59,
  SIGNREQUEST: 60,
  TLSWRAP: 61,
  VERIFYREQUEST: 62
});

// node_modules/unenv/dist/runtime/node/internal/async_hooks/async-resource.mjs
var _asyncIdCounter = 100;
var _AsyncResource = class {
  __unenv__ = true;
  type;
  _asyncId;
  _triggerAsyncId;
  constructor(type, triggerAsyncId2 = executionAsyncId()) {
    this.type = type;
    this._asyncId = -1 * _asyncIdCounter++;
    this._triggerAsyncId = typeof triggerAsyncId2 === "number" ? triggerAsyncId2 : triggerAsyncId2?.triggerAsyncId;
  }
  static bind(fn, type, thisArg) {
    const resource = new AsyncResource(type ?? "anonymous");
    return resource.bind(fn);
  }
  bind(fn, thisArg) {
    const binded = (...args) => this.runInAsyncScope(fn, thisArg, ...args);
    binded.asyncResource = this;
    return binded;
  }
  runInAsyncScope(fn, thisArg, ...args) {
    const result = fn.apply(thisArg, args);
    return result;
  }
  emitDestroy() {
    return this;
  }
  asyncId() {
    return this._asyncId;
  }
  triggerAsyncId() {
    return this._triggerAsyncId;
  }
};
var AsyncResource = globalThis.AsyncResource || _AsyncResource;

// node_modules/@cloudflare/unenv-preset/dist/runtime/node/async_hooks.mjs
var workerdAsyncHooks = process.getBuiltinModule("node:async_hooks");
var { AsyncLocalStorage: AsyncLocalStorage2, AsyncResource: AsyncResource2 } = workerdAsyncHooks;
var async_hooks_default = {
  /**
   * manually unroll unenv-polyfilled-symbols to make it tree-shakeable
   */
  asyncWrapProviders,
  createHook,
  executionAsyncId,
  executionAsyncResource,
  triggerAsyncId,
  /**
   * manually unroll workerd-polyfilled-symbols to make it tree-shakeable
   */
  AsyncLocalStorage: AsyncLocalStorage2,
  AsyncResource: AsyncResource2
};
export {
  AsyncLocalStorage2 as AsyncLocalStorage,
  AsyncResource2 as AsyncResource,
  asyncWrapProviders,
  createHook,
  async_hooks_default as default,
  executionAsyncId,
  executionAsyncResource,
  triggerAsyncId
};
//# sourceMappingURL=@cloudflare_unenv-preset_node_async_hooks.js.map
