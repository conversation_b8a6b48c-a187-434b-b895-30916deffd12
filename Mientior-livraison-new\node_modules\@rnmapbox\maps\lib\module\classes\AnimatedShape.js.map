{"version": 3, "names": ["Animated", "AnimatedWithChildren", "Object", "getPrototypeOf", "ValueXY", "__DEV__", "name", "console", "error", "AnimatedShape", "constructor", "shape", "_walkShapeAndGetValues", "value", "Array", "isArray", "map", "i", "Node", "__getValue", "result", "key", "_walkAndProcess", "cb", "for<PERSON>ach", "__attach", "v", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d"], "sourceRoot": "../../../src", "sources": ["classes/AnimatedShape.js"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,cAAc;;AAEvC;AACA;AACA,MAAMC,oBAAoB,GAAGC,MAAM,CAACC,cAAc,CAACH,QAAQ,CAACI,OAAO,CAAC;AACpE,IAAIC,OAAO,EAAE;EACX,IAAIJ,oBAAoB,CAACK,IAAI,KAAK,sBAAsB,EAAE;IACxDC,OAAO,CAACC,KAAK,CACX,gEACF,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,SAASR,oBAAoB,CAAC;EACtD;EACA;;EAEAS,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,KAAK,GAAGA,KAAK;EACpB;EAEAC,sBAAsBA,CAACC,KAAK,EAAE;IAC5B,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAACG,GAAG,CAAEC,CAAC,IAAK,IAAI,CAACL,sBAAsB,CAACK,CAAC,CAAC,CAAC;IACzD;IACA,IAAIJ,KAAK,YAAYb,QAAQ,CAACkB,IAAI,EAAE;MAClC,OAAOL,KAAK,CAACM,UAAU,CAAC,CAAC;IAC3B;IACA,IAAI,OAAON,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAMO,MAAM,GAAG,CAAC,CAAC;MACjB,KAAK,MAAMC,GAAG,IAAIR,KAAK,EAAE;QACvBO,MAAM,CAACC,GAAG,CAAC,GAAG,IAAI,CAACT,sBAAsB,CAACC,KAAK,CAACQ,GAAG,CAAC,CAAC;MACvD;MACA,OAAOD,MAAM;IACf;IACA,OAAOP,KAAK;EACd;EAEAM,UAAUA,CAAA,EAAG;IACX,MAAMC,MAAM,GAAG,IAAI,CAACR,sBAAsB,CAAC,IAAI,CAACD,KAAK,CAAC;IACtD,OAAOS,MAAM;EACf;EAEAE,eAAeA,CAACT,KAAK,EAAEU,EAAE,EAAE;IACzB,IAAIT,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MACxBA,KAAK,CAACW,OAAO,CAAEP,CAAC,IAAK,IAAI,CAACK,eAAe,CAACL,CAAC,EAAEM,EAAE,CAAC,CAAC;IACnD,CAAC,MAAM,IAAIV,KAAK,YAAYb,QAAQ,CAACkB,IAAI,EAAE;MACzCK,EAAE,CAACV,KAAK,CAAC;IACX,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACpC,KAAK,MAAMQ,GAAG,IAAIR,KAAK,EAAE;QACvB,IAAI,CAACS,eAAe,CAACT,KAAK,CAACQ,GAAG,CAAC,EAAEE,EAAE,CAAC;MACtC;IACF;EACF;EAEAE,QAAQA,CAAA,EAAG;IACT,IAAI,CAACH,eAAe,CAAC,IAAI,CAACX,KAAK,EAAGe,CAAC,IAAKA,CAAC,CAACC,UAAU,CAAC,IAAI,CAAC,CAAC;EAC7D;EAEAC,QAAQA,CAAA,EAAG;IACT,IAAI,CAACN,eAAe,CAAC,IAAI,CAACX,KAAK,EAAGe,CAAC,IAAKA,CAAC,CAACG,aAAa,CAAC,IAAI,CAAC,CAAC;IAC9D,KAAK,CAACD,QAAQ,CAAC,CAAC;EAClB;AACF;AAEA,eAAenB,aAAa", "ignoreList": []}