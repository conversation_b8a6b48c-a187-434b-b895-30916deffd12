# Mientior Livraison

Application mobile de livraison pour la Côte d'Ivoire développée avec React Native et Expo.

## 🚀 Fonctionnalités

### Authentification
- ✅ Inscription/Connexion avec email/mot de passe
- ✅ Authentification OTP par SMS
- ✅ Gestion des rôles (Client, Livreur, Commerçant)
- ✅ Persistance de session avec AsyncStorage

### Navigation
- ✅ Navigation par onglets pour les clients
- ✅ Navigation en pile pour les détails
- ✅ Navigation basée sur les rôles

### Cartes et Localisation
- ✅ Intégration Google Maps avec react-native-maps
- ✅ Géolocalisation avec expo-location
- ✅ Recherche d'adresses
- ✅ Sélection de position sur carte

### Interface Utilisateur
- ✅ Design system africain moderne
- ✅ Couleurs primaires : #0DCAA8
- ✅ Animations d'entrée et transitions
- ✅ Interface responsive

## 🛠 Technologies

- **Framework**: React Native avec Expo
- **Navigation**: React Navigation v6
- **État global**: Zustand
- **Base de données**: Supabase (PostgreSQL)
- **Authentification**: Supabase Auth
- **Cartes**: react-native-maps
- **Localisation**: expo-location
- **Stockage local**: AsyncStorage
- **Icônes**: Expo Vector Icons
- **Animations**: react-native-animatable

## 📱 Installation

### Prérequis
- Node.js 18+
- Expo CLI
- Android Studio (pour Android)
- Xcode (pour iOS)

### Installation des dépendances
```bash
cd Mientior-livraison-new
npm install
```

### Configuration
1. Créer un projet Supabase
2. Configurer les variables d'environnement
3. Configurer les clés API Google Maps

### Démarrage
```bash
# Démarrer le serveur de développement
npx expo start

# Pour Android
npx expo start --android

# Pour iOS
npx expo start --ios
```

## 🏗 Architecture

```
src/
├── components/          # Composants réutilisables
├── constants/          # Constantes (couleurs, dimensions, etc.)
├── hooks/              # Hooks personnalisés
├── navigation/         # Configuration de navigation
├── screens/            # Écrans de l'application
│   ├── auth/          # Écrans d'authentification
│   ├── client/        # Écrans clients
│   ├── livreur/       # Écrans livreurs
│   └── merchant/      # Écrans commerçants
├── services/          # Services (API, Supabase)
├── store/             # Gestion d'état (Zustand)
├── types/             # Types TypeScript
└── utils/             # Utilitaires
```

## 🗄 Base de données

La base de données utilise Supabase avec les tables principales :
- `users` - Utilisateurs
- `client_profiles` - Profils clients
- `merchant_profiles` - Profils commerçants
- `delivery_person_profiles` - Profils livreurs
- `products` - Produits
- `orders` - Commandes
- `addresses` - Adresses

## 🔧 Configuration

### Variables d'environnement
Créer un fichier `.env` avec :
```
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Google Maps
Configurer la clé API dans `app.json` :
```json
{
  "expo": {
    "android": {
      "config": {
        "googleMaps": {
          "apiKey": "your_google_maps_api_key"
        }
      }
    },
    "ios": {
      "config": {
        "googleMapsApiKey": "your_google_maps_api_key"
      }
    }
  }
}
```

## 📚 Documentation

- [Guide de correction Supabase](docs/SUPABASE_RELATIONS_FIX.md)
- [Guide de développement](GUIDE_DEVELOPPEMENT.md)

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT.

## 👥 Équipe

Développé pour Mientior Livraison - Service de livraison en Côte d'Ivoire.

---

**Status**: ✅ En développement actif
**Version**: 1.0.0-beta
**Dernière mise à jour**: Décembre 2024
