import {
  Interface
} from "./chunk-NVGP5Q3A.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/internal/readline/promises/interface.mjs
var Interface2 = class extends Interface {
  question(query, options) {
    return Promise.resolve("");
  }
};

// node_modules/unenv/dist/runtime/node/internal/readline/promises/readline.mjs
var Readline = class {
  clearLine(dir) {
    return this;
  }
  clearScreenDown() {
    return this;
  }
  commit() {
    return Promise.resolve();
  }
  cursorTo(x, y) {
    return this;
  }
  moveCursor(dx, dy) {
    return this;
  }
  rollback() {
    return this;
  }
};

// node_modules/unenv/dist/runtime/node/readline/promises.mjs
var createInterface = () => new Interface2();
var promises_default = {
  Interface: Interface2,
  Readline,
  createInterface
};
export {
  Interface2 as Interface,
  Readline,
  createInterface,
  promises_default as default
};
//# sourceMappingURL=unenv_node_readline_promises.js.map
