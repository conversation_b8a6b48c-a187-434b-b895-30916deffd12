{"version": 3, "names": ["Animated", "AnimatedCoordinatesArray", "AnimatedExtractCoordinateFromArray", "AnimatedPoint", "AnimatedRouteCoordinatesArray", "AnimatedShape"], "sourceRoot": "../../../src", "sources": ["classes/index.d.ts"], "mappings": ";;AACA,SAASA,QAAQ,QAAQ,cAAc;AAIvC,OAAO,MAAMC,wBAAwB,CAAC;AACtC,OAAO,MAAMC,kCAAkC,CAAC;AAChD,OAAO,MAAMC,aAAa,CAAsC;AAehE,OAAO,MAAMC,6BAA6B,CAAC;AAC3C,OAAO,MAAMC,aAAa,CAAC", "ignoreList": []}