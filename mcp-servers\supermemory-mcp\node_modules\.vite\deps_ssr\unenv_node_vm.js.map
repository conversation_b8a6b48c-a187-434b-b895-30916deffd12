{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/vm/script.mjs", "../../unenv/dist/runtime/node/internal/vm/constants.mjs", "../../unenv/dist/runtime/node/vm.mjs"], "sourcesContent": ["import { createNotImplementedError } from \"../../../_internal/utils.mjs\";\nexport class Script {\n\trunInContext(contextifiedObject, options) {\n\t\tthrow createNotImplementedError(\"Script.runInContext\");\n\t}\n\trunInNewContext(contextObject, options) {\n\t\tthrow createNotImplementedError(\"Script.runInNewContext\");\n\t}\n\trunInThisContext(options) {\n\t\tthrow createNotImplementedError(\"Script.runInThisContext\");\n\t}\n\tcreateCachedData() {\n\t\tthrow createNotImplementedError(\"Script.createCachedData\");\n\t}\n}\n", "export const USE_MAIN_CONTEXT_DEFAULT_LOADER = /* @__PURE__ */ Symbol(\"vm_dynamic_import_main_context_default\");\nexport const DONT_CONTEXTIFY = /* @__PURE__ */ Symbol(\"vm_context_no_contextify\");\n", "import { notImplemented } from \"../_internal/utils.mjs\";\nimport { Script } from \"./internal/vm/script.mjs\";\nimport * as constants from \"./internal/vm/constants.mjs\";\nexport { Script } from \"./internal/vm/script.mjs\";\nexport * as constants from \"./internal/vm/constants.mjs\";\nexport const compileFunction = /* @__PURE__ */ notImplemented(\"vm.compileFunction\");\nconst _contextSymbol = /* @__PURE__ */ Symbol(\"uenv.vm.context\");\nexport const createContext = function createContext() {\n\treturn Object.create(null, { [_contextSymbol]: { value: true } });\n};\nexport const createScript = function createScript() {\n\treturn new Script();\n};\nexport const isContext = (context) => {\n\treturn context && context[_contextSymbol] === true;\n};\nexport const measureMemory = () => Promise.resolve({\n\ttotal: {\n\t\tjsMemoryEstimate: 0,\n\t\tjsMemoryRange: [1, 2]\n\t},\n\tWebAssembly: {\n\t\tcode: 0,\n\t\tmetadata: 0\n\t}\n});\nexport const runInContext = /* @__PURE__ */ notImplemented(\"vm.runInContext\");\nexport const runInNewContext = /* @__PURE__ */ notImplemented(\"vm.runInNewContext\");\nexport const runInThisContext = /* @__PURE__ */ notImplemented(\"vm.runInThisContext\");\nexport default {\n\tScript,\n\tcompileFunction,\n\tconstants,\n\tcreateContext,\n\tisContext,\n\tmeasureMemory,\n\trunInContext,\n\trunInNewContext,\n\trunInThisContext,\n\tcreateScript\n};\n"], "mappings": ";;;;;;;;;AACO,IAAM,SAAN,MAAa;AAAA,EACnB,aAAa,oBAAoB,SAAS;AACzC,UAAM,0BAA0B,qBAAqB;AAAA,EACtD;AAAA,EACA,gBAAgB,eAAe,SAAS;AACvC,UAAM,0BAA0B,wBAAwB;AAAA,EACzD;AAAA,EACA,iBAAiB,SAAS;AACzB,UAAM,0BAA0B,yBAAyB;AAAA,EAC1D;AAAA,EACA,mBAAmB;AAClB,UAAM,0BAA0B,yBAAyB;AAAA,EAC1D;AACD;;;ACdA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAM,kCAAkD,OAAO,wCAAwC;AACvG,IAAM,kBAAkC,OAAO,0BAA0B;;;ACIzE,IAAM,kBAAkC,eAAe,oBAAoB;AAClF,IAAM,iBAAiC,OAAO,iBAAiB;AACxD,IAAM,gBAAgB,SAASA,iBAAgB;AACrD,SAAO,OAAO,OAAO,MAAM,EAAE,CAAC,cAAc,GAAG,EAAE,OAAO,KAAK,EAAE,CAAC;AACjE;AACO,IAAM,eAAe,SAASC,gBAAe;AACnD,SAAO,IAAI,OAAO;AACnB;AACO,IAAM,YAAY,CAAC,YAAY;AACrC,SAAO,WAAW,QAAQ,cAAc,MAAM;AAC/C;AACO,IAAM,gBAAgB,MAAM,QAAQ,QAAQ;AAAA,EAClD,OAAO;AAAA,IACN,kBAAkB;AAAA,IAClB,eAAe,CAAC,GAAG,CAAC;AAAA,EACrB;AAAA,EACA,aAAa;AAAA,IACZ,MAAM;AAAA,IACN,UAAU;AAAA,EACX;AACD,CAAC;AACM,IAAM,eAA+B,eAAe,iBAAiB;AACrE,IAAM,kBAAkC,eAAe,oBAAoB;AAC3E,IAAM,mBAAmC,eAAe,qBAAqB;AACpF,IAAO,aAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": ["createContext", "createScript"]}