import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configuration du client React Query avec persistance et optimisations pour mobile
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache pendant 5 minutes par défaut
      staleTime: 5 * 60 * 1000,
      // Garde en cache pendant 10 minutes
      gcTime: 10 * 60 * 1000,
      // Retry 3 fois en cas d'erreur
      retry: 3,
      // D<PERSON>lai entre les retries (exponentiel)
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Refetch en arrière-plan quand l'app devient active
      refetchOnWindowFocus: true,
      // Refetch quand la connexion est rétablie
      refetchOnReconnect: true,
      // Ne pas refetch automatiquement au mount si les données sont fraîches
      refetchOnMount: 'always',
      // Configuration pour les données en temps réel
      refetchInterval: false, // Désactivé par défaut, activé pour certaines queries
    },
    mutations: {
      // Retry les mutations en cas d'erreur réseau
      retry: (failureCount, error: any) => {
        // Ne pas retry les erreurs 4xx (erreurs client)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry jusqu'à 3 fois pour les erreurs réseau/serveur
        return failureCount < 3;
      },
      // Délai entre les retries pour les mutations
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
});

// Configuration de la persistance simplifiée avec AsyncStorage
// La persistance sera gérée manuellement dans les hooks individuels

// Clés de query standardisées pour l'application
export const queryKeys = {
  // Restaurants
  restaurants: {
    all: ['restaurants'] as const,
    lists: () => [...queryKeys.restaurants.all, 'list'] as const,
    list: (filters?: any) => [...queryKeys.restaurants.lists(), filters] as const,
    details: () => [...queryKeys.restaurants.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.restaurants.details(), id] as const,
    search: (query: string) => [...queryKeys.restaurants.all, 'search', query] as const,
    nearby: (location: any) => [...queryKeys.restaurants.all, 'nearby', location] as const,
  },
  
  // Produits
  products: {
    all: ['products'] as const,
    lists: () => [...queryKeys.products.all, 'list'] as const,
    list: (merchantId: string, filters?: any) => [...queryKeys.products.lists(), merchantId, filters] as const,
    details: () => [...queryKeys.products.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.products.details(), id] as const,
    categories: (merchantId: string) => [...queryKeys.products.all, 'categories', merchantId] as const,
    search: (query: string, merchantId?: string) => [...queryKeys.products.all, 'search', query, merchantId] as const,
  },
  
  // Commandes
  orders: {
    all: ['orders'] as const,
    lists: () => [...queryKeys.orders.all, 'list'] as const,
    list: (userId: string, role: string) => [...queryKeys.orders.lists(), userId, role] as const,
    details: () => [...queryKeys.orders.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.orders.details(), id] as const,
    tracking: (id: string) => [...queryKeys.orders.all, 'tracking', id] as const,
    history: (userId: string) => [...queryKeys.orders.all, 'history', userId] as const,
  },
  
  // Utilisateur
  user: {
    all: ['user'] as const,
    profile: () => [...queryKeys.user.all, 'profile'] as const,
    addresses: () => [...queryKeys.user.all, 'addresses'] as const,
    preferences: () => [...queryKeys.user.all, 'preferences'] as const,
    stats: () => [...queryKeys.user.all, 'stats'] as const,
  },
  
  // Livraisons
  deliveries: {
    all: ['deliveries'] as const,
    lists: () => [...queryKeys.deliveries.all, 'list'] as const,
    list: (deliveryPersonId: string) => [...queryKeys.deliveries.lists(), deliveryPersonId] as const,
    details: () => [...queryKeys.deliveries.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.deliveries.details(), id] as const,
    tracking: (id: string) => [...queryKeys.deliveries.all, 'tracking', id] as const,
    active: (deliveryPersonId: string) => [...queryKeys.deliveries.all, 'active', deliveryPersonId] as const,
  },
  
  // Catégories
  categories: {
    all: ['categories'] as const,
    list: () => [...queryKeys.categories.all, 'list'] as const,
    byType: (type: string) => [...queryKeys.categories.all, 'type', type] as const,
  },
  
  // Zones de livraison
  deliveryZones: {
    all: ['deliveryZones'] as const,
    list: () => [...queryKeys.deliveryZones.all, 'list'] as const,
    byLocation: (location: any) => [...queryKeys.deliveryZones.all, 'location', location] as const,
  },
} as const;

// Utilitaires pour invalider les caches
export const invalidateQueries = {
  restaurants: () => queryClient.invalidateQueries({ queryKey: queryKeys.restaurants.all }),
  products: (merchantId?: string) => {
    if (merchantId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.products.list(merchantId) });
    } else {
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
    }
  },
  orders: (userId?: string) => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.list(userId, 'client') });
    } else {
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.all });
    }
  },
  user: () => queryClient.invalidateQueries({ queryKey: queryKeys.user.all }),
  deliveries: () => queryClient.invalidateQueries({ queryKey: queryKeys.deliveries.all }),
};

// Configuration des intervalles de refetch pour les données en temps réel
export const realtimeIntervals = {
  // Tracking de livraison - toutes les 10 secondes
  deliveryTracking: 10 * 1000,
  // Statut des commandes - toutes les 30 secondes
  orderStatus: 30 * 1000,
  // Disponibilité des restaurants - toutes les 2 minutes
  restaurantAvailability: 2 * 60 * 1000,
  // Localisation du livreur - toutes les 15 secondes
  deliveryPersonLocation: 15 * 1000,
} as const;

interface QueryProviderProps {
  children: React.ReactNode;
}

export const QueryProvider: React.FC<QueryProviderProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

// Export du client pour utilisation directe si nécessaire
export { queryClient };

// Types pour TypeScript
export type QueryKey = typeof queryKeys;
export type InvalidateQueries = typeof invalidateQueries;
