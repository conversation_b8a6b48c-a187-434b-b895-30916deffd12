{"version": 3, "sources": ["../../unenv/dist/runtime/node/readline.mjs"], "sourcesContent": ["import noop from \"../mock/noop.mjs\";\nimport promises from \"node:readline/promises\";\nimport { Interface } from \"./internal/readline/interface.mjs\";\nexport { promises };\nexport { Interface } from \"./internal/readline/interface.mjs\";\nexport const clearLine = () => false;\nexport const clearScreenDown = () => false;\nexport const createInterface = () => new Interface();\nexport const cursorTo = () => false;\nexport const emitKeypressEvents = noop;\nexport const moveCursor = () => false;\nexport default {\n\tclearLine,\n\tclearScreenDown,\n\tcreateInterface,\n\tcursorTo,\n\temitKeypressEvents,\n\tmoveCursor,\n\tInterface,\n\tpromises\n};\n"], "mappings": ";;;;;;;;;AACA,OAAO,cAAc;AAId,IAAM,YAAY,MAAM;AACxB,IAAM,kBAAkB,MAAM;AAC9B,IAAM,kBAAkB,MAAM,IAAI,UAAU;AAC5C,IAAM,WAAW,MAAM;AACvB,IAAM,qBAAqB;AAC3B,IAAM,aAAa,MAAM;AAChC,IAAO,mBAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}