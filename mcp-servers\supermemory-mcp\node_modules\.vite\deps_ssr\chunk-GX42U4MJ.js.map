{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/os/constants.mjs"], "sourcesContent": ["export const UV_UDP_REUSEADDR = 4;\nexport const dlopen = {\n\tRTLD_LAZY: 1,\n\tRTLD_NOW: 2,\n\tRTLD_GLOBAL: 256,\n\tRTLD_LOCAL: 0,\n\tRTLD_DEEPBIND: 8\n};\nexport const errno = {\n\tE2BIG: 7,\n\tEACCES: 13,\n\tEADDRINUSE: 98,\n\tEADDRNOTAVAIL: 99,\n\tEAFNOSUPPORT: 97,\n\tEAGAIN: 11,\n\tEALREADY: 114,\n\tEBADF: 9,\n\tEBADMSG: 74,\n\tEBUSY: 16,\n\tECANCELED: 125,\n\tECHILD: 10,\n\tECONNABORTED: 103,\n\tECONNREFUSED: 111,\n\tECONNRESET: 104,\n\tEDEADLK: 35,\n\tEDESTADDRREQ: 89,\n\tEDOM: 33,\n\tEDQUOT: 122,\n\tEEXIST: 17,\n\tEFAULT: 14,\n\tEFBIG: 27,\n\tEHOSTUNREACH: 113,\n\tEIDRM: 43,\n\tEILSEQ: 84,\n\tEINPROGRESS: 115,\n\tEINTR: 4,\n\tEINVAL: 22,\n\t<PERSON><PERSON>: 5,\n\t<PERSON>ISCONN: 106,\n\tEISDIR: 21,\n\tELOOP: 40,\n\tEMFILE: 24,\n\tEMLINK: 31,\n\t<PERSON>MS<PERSON><PERSON><PERSON><PERSON>: 90,\n\tEMULTIHOP: 72,\n\tENAMETOOLONG: 36,\n\tENETDOWN: 100,\n\tENETRESET: 102,\n\tENETUNREACH: 101,\n\tENFILE: 23,\n\tENOBUFS: 105,\n\tENODATA: 61,\n\tENODEV: 19,\n\tENOENT: 2,\n\tENOEXEC: 8,\n\tENOLCK: 37,\n\tENOLINK: 67,\n\tENOMEM: 12,\n\tENOMSG: 42,\n\tENOPROTOOPT: 92,\n\tENOSPC: 28,\n\tENOSR: 63,\n\tENOSTR: 60,\n\tENOSYS: 38,\n\tENOTCONN: 107,\n\tENOTDIR: 20,\n\tENOTEMPTY: 39,\n\tENOTSOCK: 88,\n\tENOTSUP: 95,\n\tENOTTY: 25,\n\tENXIO: 6,\n\tEOPNOTSUPP: 95,\n\tEOVERFLOW: 75,\n\tEPERM: 1,\n\tEPIPE: 32,\n\tEPROTO: 71,\n\tEPROTONOSUPPORT: 93,\n\tEPROTOTYPE: 91,\n\tERANGE: 34,\n\tEROFS: 30,\n\tESPIPE: 29,\n\tESRCH: 3,\n\tESTALE: 116,\n\tETIME: 62,\n\tETIMEDOUT: 110,\n\tETXTBSY: 26,\n\tEWOULDBLOCK: 11,\n\tEXDEV: 18\n};\nexport const signals = {\n\tSIGHUP: 1,\n\tSIGINT: 2,\n\tSIGQUIT: 3,\n\tSIGILL: 4,\n\tSIGTRAP: 5,\n\tSIGABRT: 6,\n\tSIGIOT: 6,\n\tSIGBUS: 7,\n\tSIGFPE: 8,\n\tSIGKILL: 9,\n\tSIGUSR1: 10,\n\tSIGSEGV: 11,\n\tSIGUSR2: 12,\n\tSIGPIPE: 13,\n\tSIGALRM: 14,\n\tSIGTERM: 15,\n\tSIGCHLD: 17,\n\tSIGSTKFLT: 16,\n\tSIGCONT: 18,\n\tSIGSTOP: 19,\n\tSIGTSTP: 20,\n\tSIGTTIN: 21,\n\tSIGTTOU: 22,\n\tSIGURG: 23,\n\tSIGXCPU: 24,\n\tSIGXFSZ: 25,\n\tSIGVTALRM: 26,\n\tSIGPROF: 27,\n\tSIGWINCH: 28,\n\tSIGIO: 29,\n\tSIGPOLL: 29,\n\tSIGPWR: 30,\n\tSIGSYS: 31\n};\nexport const priority = {\n\tPRIORITY_LOW: 19,\n\tPRIORITY_BELOW_NORMAL: 10,\n\tPRIORITY_NORMAL: 0,\n\tPRIORITY_ABOVE_NORMAL: -7,\n\tPRIORITY_HIGH: -14,\n\tPRIORITY_HIGHEST: -20\n};\n"], "mappings": ";AAAO,IAAM,mBAAmB;AACzB,IAAM,SAAS;AAAA,EACrB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,eAAe;AAChB;AACO,IAAM,QAAQ;AAAA,EACpB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,cAAc;AAAA,EACd,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,cAAc;AAAA,EACd,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,aAAa;AAAA,EACb,OAAO;AACR;AACO,IAAM,UAAU;AAAA,EACtB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AACT;AACO,IAAM,WAAW;AAAA,EACvB,cAAc;AAAA,EACd,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,eAAe;AAAA,EACf,kBAAkB;AACnB;", "names": []}