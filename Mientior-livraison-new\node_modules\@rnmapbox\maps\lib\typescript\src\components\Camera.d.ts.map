{"version": 3, "file": "Camera.d.ts", "sourceRoot": "", "sources": ["../../../../src/components/Camera.tsx"], "names": [], "mappings": "AAAA,OAAO,KAQN,MAAM,OAAO,CAAC;AAGf,OAAO,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AACzC,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AASlD,oBAAY,gBAAgB;IAC1B,MAAM,WAAW;IACjB,iBAAiB,YAAY;IAC7B,gBAAgB,WAAW;CAC5B;AAED,MAAM,MAAM,8BAA8B,GAAG,CAC3C,KAAK,EAAE,aAAa,CAClB,wBAAwB,EACxB;IACE,kBAAkB,EAAE,OAAO,CAAC;IAC5B,cAAc,EAAE,gBAAgB,GAAG,IAAI,CAAC;CACzC,CACF,KACE,IAAI,CAAC;AAwDV,MAAM,WAAW,SAAS;IACxB,SAAS,EAAE,CAAC,MAAM,EAAE,UAAU,GAAG,WAAW,KAAK,IAAI,CAAC;IACtD,SAAS,EAAE,CACT,EAAE,EAAE,QAAQ,EACZ,EAAE,EAAE,QAAQ,EACZ,aAAa,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,EACjC,iBAAiB,CAAC,EAAE,MAAM,KACvB,IAAI,CAAC;IACV,KAAK,EAAE,CAAC,gBAAgB,EAAE,QAAQ,EAAE,iBAAiB,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC;IACxE,MAAM,EAAE,CAAC,gBAAgB,EAAE,QAAQ,EAAE,iBAAiB,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC;IACzE,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC;CACjE;AAED,MAAM,MAAM,UAAU,GAAG;IACvB,mEAAmE;IACnE,QAAQ,CAAC,IAAI,CAAC,EAAE,YAAY,CAAC;IAC7B,mDAAmD;IACnD,gBAAgB,CAAC,EAAE,QAAQ,CAAC;IAC5B;wEACoE;IACpE,MAAM,CAAC,EAAE,uBAAuB,CAAC;IACjC,4CAA4C;IAC5C,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,4BAA4B;IAC5B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,iCAAiC;IACjC,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,sCAAsC;IACtC,OAAO,CAAC,EAAE,aAAa,CAAC;IACxB,oEAAoE;IACpE,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,4EAA4E;IAC5E,aAAa,CAAC,EAAE,mBAAmB,CAAC;CACrC,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAC/B,6DAA6D;IAC7D,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B,2DAA2D;IAC3D,cAAc,CAAC,EAAE,gBAAgB,CAAC;IAClC,4DAA4D;IAC5D,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,uDAAuD;IACvD,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,yDAAyD;IACzD,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,qEAAqE;IACrE,aAAa,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;CACxC,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAC/B,qCAAqC;IACrC,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,sCAAsC;IACtC,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,oFAAoF;IACpF,SAAS,CAAC,EAAE;QACV,EAAE,EAAE,QAAQ,CAAC;QACb,EAAE,EAAE,QAAQ,CAAC;KACd,CAAC;CACH,CAAC;AAEF,MAAM,WAAW,WACf,SAAQ,UAAU,EAChB,kBAAkB,EAClB,kBAAkB;IACpB,yFAAyF;IACzF,eAAe,CAAC,EAAE,UAAU,CAAC;IAE7B;4FACwF;IACxF,YAAY,CAAC,EAAE,OAAO,CAAC;IAEvB;+CAC2C;IAC3C,UAAU,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IAE7B;;;OAGG;IACH,wBAAwB,CAAC,EAAE,8BAA8B,CAAC;CAC3D;AAED,MAAM,MAAM,YAAY,GAAG;IACzB,EAAE,EAAE,QAAQ,CAAC;IACb,EAAE,EAAE,QAAQ,CAAC;CACd,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG;IAC1B,6BAA6B;IAC7B,WAAW,EAAE,MAAM,CAAC;IACpB,8BAA8B;IAC9B,YAAY,EAAE,MAAM,CAAC;IACrB,4BAA4B;IAC5B,UAAU,EAAE,MAAM,CAAC;IACnB,+BAA+B;IAC/B,aAAa,EAAE,MAAM,CAAC;CACvB,CAAC;AAEF,MAAM,MAAM,uBAAuB,GAAG,OAAO,CAAC,aAAa,CAAC,GAAG,YAAY,CAAC;AAE5E,MAAM,MAAM,WAAW,GAAG;IACxB,mEAAmE;IACnE,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC;IAC7B,KAAK,EAAE,UAAU,EAAE,CAAC;CACrB,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAC3B,OAAO,GACP,QAAQ,GACR,UAAU,GACV,QAAQ,GACR,MAAM,CAAC;AAEX;;;;;;;;;;;;;;;;;;GAkBG;AACH,eAAO,MAAM,MAAM,0EA+XlB,CAAC;AAIF,MAAM,MAAM,MAAM,GAAG,SAAS,CAAC"}