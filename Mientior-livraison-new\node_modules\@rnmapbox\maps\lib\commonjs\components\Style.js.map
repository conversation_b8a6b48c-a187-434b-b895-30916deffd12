{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_<PERSON><PERSON><PERSON><PERSON>", "_interopRequireDefault", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_SymbolLayer", "_LineLayer", "_<PERSON>ll<PERSON><PERSON>er", "_FillExtrusionLayer", "_<PERSON><PERSON><PERSON>er", "_HeatmapLayer", "_VectorSource", "_RasterSource", "_ImageSource", "_ShapeSource", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "toCamelCase", "s", "replace", "$1", "toUpperCase", "toCamelCaseKeys", "oldObj", "newObj", "keys", "for<PERSON>ach", "key", "value", "includes", "getLayerComponentType", "layer", "type", "<PERSON><PERSON><PERSON><PERSON>", "SymbolLayer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LineLayer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FillExtrusionLayer", "<PERSON><PERSON><PERSON><PERSON>", "Heatmap<PERSON>ayer", "console", "warn", "asLayerComponent", "LayerComponent", "style", "paint", "layout", "layerProps", "source", "sourceID", "sourceLayerID", "minzoom", "minZoomLevel", "maxzoom", "maxZoomLevel", "filter", "length", "jsx", "id", "getTileSourceProps", "sourceProps", "url", "tiles", "tileUrlTemplates", "undefined", "attribution", "scheme", "tms", "getVectorSource", "getRasterSource", "tileSize", "getImageSource", "coordinates", "getShapeSource", "data", "shape", "cluster", "clusterRadius", "clusterMaxZoom", "clusterMaxZoomLevel", "clusterProperties", "buffer", "tolerance", "lineMetrics", "ShapeSource", "asSourceComponent", "Style", "props", "fetched<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useState", "json", "useEffect", "abortController", "AbortController", "fetchStyleJson", "response", "fetch", "signal", "responseJson", "error", "name", "cleanup", "abort", "layerComponents", "useMemo", "layers", "map", "x", "sources", "sourceComponents", "jsxs", "Fragment", "children", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/Style.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAIA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,UAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,mBAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,gBAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,aAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,aAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,aAAA,GAAAT,sBAAA,CAAAF,OAAA;AACA,IAAAY,YAAA,GAAAV,sBAAA,CAAAF,OAAA;AACA,IAAAa,YAAA,GAAAb,OAAA;AAA4C,IAAAc,WAAA,GAAAd,OAAA;AAAA,SAAAE,uBAAAa,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAhB,wBAAAgB,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAApB,uBAAA,YAAAA,CAAAgB,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAE5C,SAASgB,WAAWA,CAACC,CAAS,EAAU;EACtC,OAAOA,CAAC,CAACC,OAAO,CAAC,eAAe,EAAGC,EAAE,IAAK;IACxC,OAAOA,EAAE,CAACC,WAAW,CAAC,CAAC,CAACF,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;EAC3D,CAAC,CAAC;AACJ;;AAEA;AACA;AACA,SAASG,eAAeA,CAACC,MAAmC,EAE1D;EACA,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,CAAC,CAAC;EACX;EACA,MAAMC,MAAkC,GAAG,CAAC,CAAC;EAC7CV,MAAM,CAACW,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAAEC,GAAG,IAAK;IACnC,MAAMC,KAAK,GAAGL,MAAM,CAACI,GAAG,CAAC;IACzB,IAAIA,GAAG,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;MACrBL,MAAM,CAACP,WAAW,CAACU,GAAG,CAAC,CAAC,GAAGC,KAAK;IAClC,CAAC,MAAM;MACLJ,MAAM,CAACG,GAAG,CAAC,GAAGC,KAAK;IACrB;EACF,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf;AAEA,SAASM,qBAAqBA,CAACC,KAAuB,EAAE;EACtD,MAAM;IAAEC;EAAK,CAAC,GAAGD,KAAK;EAEtB,QAAQC,IAAI;IACV,KAAK,QAAQ;MACX,OAAOC,oBAAW;IACpB,KAAK,QAAQ;MACX,OAAOC,wBAAW;IACpB,KAAK,QAAQ;MACX,OAAOC,oBAAW;IACpB,KAAK,MAAM;MACT,OAAOC,kBAAS;IAClB,KAAK,MAAM;MACT,OAAOC,kBAAS;IAClB,KAAK,gBAAgB;MACnB,OAAOC,2BAAkB;IAC3B,KAAK,YAAY;MACf,OAAOC,wBAAe;IACxB,KAAK,SAAS;MACZ,OAAOC,qBAAY;EACvB;EAEAC,OAAO,CAACC,IAAI,CAAC,sBAAsBV,IAAI,qBAAqB,CAAC;EAE7D,OAAO,IAAI;AACb;AAEA,SAASW,gBAAgBA,CAACZ,KAAsB,EAAE;EAEhD,MAAMa,cAA+D,GACnEd,qBAAqB,CAACC,KAAK,CAA6C;EAC1E,IAAI,CAACa,cAAc,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,MAAMC,KAAK,GAAG;IACZ,GAAGvB,eAAe,CAACS,KAAK,CAACe,KAAK,CAAC;IAC/B,GAAGxB,eAAe,CAACS,KAAK,CAACgB,MAAM;EACjC,CAAC;EAED,MAAMC,UAOL,GAAG,CAAC,CAAC;EAEN,IAAIjB,KAAK,CAACkB,MAAM,EAAE;IAChBD,UAAU,CAACE,QAAQ,GAAGnB,KAAK,CAACkB,MAAM;EACpC;EACA,IAAIlB,KAAK,CAAC,cAAc,CAAC,EAAE;IACzBiB,UAAU,CAACG,aAAa,GAAGpB,KAAK,CAAC,cAAc,CAAC;EAClD;EACA,IAAIA,KAAK,CAACqB,OAAO,EAAE;IACjBJ,UAAU,CAACK,YAAY,GAAGtB,KAAK,CAACqB,OAAO;EACzC;EACA,IAAIrB,KAAK,CAACuB,OAAO,EAAE;IACjBN,UAAU,CAACO,YAAY,GAAGxB,KAAK,CAACuB,OAAO;EACzC;EACA,IAAIvB,KAAK,CAACyB,MAAM,EAAE;IAChBR,UAAU,CAACQ,MAAM,GAAGzB,KAAK,CAACyB,MAAM;EAClC;EACA,IAAI1C,MAAM,CAACW,IAAI,CAACoB,KAAK,CAAC,CAACY,MAAM,EAAE;IAC7BT,UAAU,CAACH,KAAK,GAAGA,KAAK;EAC1B;EAEA,oBAAO,IAAAhD,WAAA,CAAA6D,GAAA,EAACd,cAAc;IAAgBe,EAAE,EAAE5B,KAAK,CAAC4B,EAAG;IAAA,GAAKX;EAAU,GAAtCjB,KAAK,CAAC4B,EAAmC,CAAC;AACxE;AAWA,SAASC,kBAAkBA,CAACX,MAAwB,EAAe;EACjE,MAAMY,WAAwB,GAAG,CAAC,CAAC;EACnC,IAAIZ,MAAM,CAACa,GAAG,EAAE;IACdD,WAAW,CAACC,GAAG,GAAGb,MAAM,CAACa,GAAG;EAC9B;EACA,IAAIb,MAAM,CAACc,KAAK,EAAE;IAChBF,WAAW,CAACG,gBAAgB,GAAGf,MAAM,CAACc,KAAK;EAC7C;EACA,IAAId,MAAM,CAACG,OAAO,KAAKa,SAAS,EAAE;IAChCJ,WAAW,CAACR,YAAY,GAAGJ,MAAM,CAACG,OAAO;EAC3C;EACA,IAAIH,MAAM,CAACK,OAAO,KAAKW,SAAS,EAAE;IAChCJ,WAAW,CAACN,YAAY,GAAGN,MAAM,CAACK,OAAO;EAC3C;EACA,IAAIL,MAAM,CAACiB,WAAW,EAAE;IACtBL,WAAW,CAACK,WAAW,GAAGjB,MAAM,CAACiB,WAAW;EAC9C;EACA,IAAIjB,MAAM,CAACkB,MAAM,IAAIlB,MAAM,CAACkB,MAAM,KAAK,KAAK,EAAE;IAC5CN,WAAW,CAACO,GAAG,GAAG,IAAI;EACxB;EACA,OAAOP,WAAW;AACpB;AAEA,SAASQ,eAAeA,CAACV,EAAU,EAAEV,MAAwB,EAAE;EAC7D,MAAMY,WAAW,GAAG;IAAE,GAAGD,kBAAkB,CAACX,MAAM;EAAE,CAAC;EACrD,oBAAO,IAAApD,WAAA,CAAA6D,GAAA,EAACjE,aAAA,CAAAO,OAAY;IAAU2D,EAAE,EAAEA,EAAG;IAAA,GAAKE;EAAW,GAA3BF,EAA8B,CAAC;AAC3D;AAEA,SAASW,eAAeA,CAACX,EAAU,EAAEV,MAAwB,EAAE;EAC7D,MAAMY,WAAgD,GAAG;IACvD,GAAGD,kBAAkB,CAACX,MAAM;EAC9B,CAAC;EACD,IAAIA,MAAM,CAACsB,QAAQ,EAAE;IACnBV,WAAW,CAACU,QAAQ,GAAGtB,MAAM,CAACsB,QAAQ;EACxC;EACA,oBAAO,IAAA1E,WAAA,CAAA6D,GAAA,EAAChE,aAAA,CAAAM,OAAY;IAAU2D,EAAE,EAAEA,EAAG;IAAA,GAAKE;EAAW,GAA3BF,EAA8B,CAAC;AAC3D;AAEA,SAASa,cAAcA,CAACb,EAAU,EAAEV,MAAwB,EAAE;EAC5D,MAAMY,WAAW,GAAG;IAClBC,GAAG,EAAEb,MAAM,CAACa,GAAG;IACfW,WAAW,EAAExB,MAAM,CAACwB;EACtB,CAAC;EACD,oBAAO,IAAA5E,WAAA,CAAA6D,GAAA,EAAC/D,YAAA,CAAAK,OAAW;IAAU2D,EAAE,EAAEA,EAAG;IAAA,GAAKE;EAAW,GAA3BF,EAA8B,CAAC;AAC1D;AAIA,SAASe,cAAcA,CAACf,EAAU,EAAEV,MAAwB,EAAE;EAC5D,MAAMY,WAUS,GAAG,CAAC,CAAC;EACpB,IAAIZ,MAAM,CAAC0B,IAAI,IAAI,OAAO1B,MAAM,CAAC0B,IAAI,KAAK,QAAQ,EAAE;IAClDd,WAAW,CAACC,GAAG,GAAGb,MAAM,CAAC0B,IAAI;EAC/B,CAAC,MAAM,IAAI1B,MAAM,CAAC0B,IAAI,IAAI,OAAO1B,MAAM,CAAC0B,IAAI,KAAK,QAAQ,EAAE;IACzDd,WAAW,CAACe,KAAK,GAAG3B,MAAM,CAAC0B,IAAyB;EACtD;EACA,IAAI1B,MAAM,CAAC4B,OAAO,KAAKZ,SAAS,EAAE;IAChCJ,WAAW,CAACgB,OAAO,GAAG5B,MAAM,CAAC4B,OAAO;EACtC;EACA,IAAI5B,MAAM,CAAC6B,aAAa,KAAKb,SAAS,EAAE;IACtCJ,WAAW,CAACiB,aAAa,GAAG7B,MAAM,CAAC6B,aAAa;EAClD;EACA,IAAI7B,MAAM,CAACK,OAAO,KAAKW,SAAS,EAAE;IAChCJ,WAAW,CAACN,YAAY,GAAGN,MAAM,CAACK,OAAO;EAC3C;EACA,IAAIL,MAAM,CAAC8B,cAAc,KAAKd,SAAS,EAAE;IACvCJ,WAAW,CAACmB,mBAAmB,GAAG/B,MAAM,CAAC8B,cAAc;EACzD;EACA,IAAI9B,MAAM,CAACgC,iBAAiB,KAAKhB,SAAS,EAAE;IAC1CJ,WAAW,CAACoB,iBAAiB,GAAGhC,MAAM,CAACgC,iBAAiB;EAC1D;EACA,IAAIhC,MAAM,CAACiC,MAAM,KAAKjB,SAAS,EAAE;IAC/BJ,WAAW,CAACqB,MAAM,GAAGjC,MAAM,CAACiC,MAAM;EACpC;EACA,IAAIjC,MAAM,CAACkC,SAAS,KAAKlB,SAAS,EAAE;IAClCJ,WAAW,CAACsB,SAAS,GAAGlC,MAAM,CAACkC,SAAS;EAC1C;EACA,IAAIlC,MAAM,CAACmC,WAAW,KAAKnB,SAAS,EAAE;IACpCJ,WAAW,CAACuB,WAAW,GAAGnC,MAAM,CAACmC,WAAW;EAC9C;EACA,oBAAO,IAAAvF,WAAA,CAAA6D,GAAA,EAAC9D,YAAA,CAAAyF,WAAW;IAAU1B,EAAE,EAAEA,EAAG;IAAA,GAAKE;EAAW,GAA3BF,EAA8B,CAAC;AAC1D;AAEA,SAAS2B,iBAAiBA,CAAC3B,EAAU,EAAEV,MAAwB,EAAE;EAC/D,QAAQA,MAAM,CAACjB,IAAI;IACjB,KAAK,QAAQ;MACX,OAAOqC,eAAe,CAACV,EAAE,EAAEV,MAAM,CAAC;IACpC,KAAK,QAAQ;MACX,OAAOqB,eAAe,CAACX,EAAE,EAAEV,MAAM,CAAC;IACpC,KAAK,OAAO;MACV,OAAOuB,cAAc,CAACb,EAAE,EAAEV,MAAM,CAAC;IACnC,KAAK,SAAS;MACZ,OAAOyB,cAAc,CAACf,EAAE,EAAEV,MAAM,CAAC;EACrC;EAEAR,OAAO,CAACC,IAAI,CAAC,uBAAuBO,MAAM,CAACjB,IAAI,qBAAqB,CAAC;EAErE,OAAO,IAAI;AACb;AAqDA;AACA;AACA;AACA;AACA;AACA,MAAMuD,KAAK,GAAIC,KAAY,IAAK;EAC9B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG,IAAAC,eAAQ,EAAC,CAAC,CAAC,CAAC;EAClD,MAAMC,IAAgB,GACpB,OAAOJ,KAAK,CAACI,IAAI,KAAK,QAAQ,GAAGJ,KAAK,CAACI,IAAI,GAAGH,WAAW;;EAE3D;EACA,IAAAI,gBAAS,EAAC,MAAM;IACd,MAAMC,eAAe,GAAG,IAAIC,eAAe,CAAC,CAAC;IAC7C,MAAMC,cAAc,GAAG,MAAOJ,IAAY,IAAK;MAC7C,IAAI;QACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAACN,IAAI,EAAE;UACjCO,MAAM,EAAEL,eAAe,CAACK;QAC1B,CAAC,CAAC;QACF,MAAMC,YAAY,GAAG,MAAMH,QAAQ,CAACL,IAAI,CAAC,CAAC;QAC1CF,cAAc,CAACU,YAAY,CAAC;MAC9B,CAAC,CAAC,OAAOC,KAAc,EAAE;QACvB,MAAMvG,CAAC,GAAGuG,KAA0B;QACpC,IAAIvG,CAAC,CAACwG,IAAI,KAAK,YAAY,EAAE;UAC3B;QACF;QACA,MAAMxG,CAAC;MACT;IACF,CAAC;IACD,IAAI,OAAO0F,KAAK,CAACI,IAAI,KAAK,QAAQ,EAAE;MAClCI,cAAc,CAACR,KAAK,CAACI,IAAI,CAAC;IAC5B;IACA,OAAO,SAASW,OAAOA,CAAA,EAAG;MACxBT,eAAe,CAACU,KAAK,CAAC,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAAChB,KAAK,CAACI,IAAI,CAAC,CAAC;;EAEhB;EACA,MAAMa,eAAe,GAAG,IAAAC,cAAO,EAAC,MAAM;IACpC,IAAI,CAACd,IAAI,CAACe,MAAM,EAAE;MAChB,OAAO,EAAE;IACX;IACA,OAAOf,IAAI,CAACe,MAAM,CAACC,GAAG,CAACjE,gBAAgB,CAAC,CAACa,MAAM,CAAEqD,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;EAC7D,CAAC,EAAE,CAACjB,IAAI,CAACe,MAAM,CAAC,CAAC;;EAEjB;EACA,MAAM;IAAEG;EAAQ,CAAC,GAAGlB,IAAI;EACxB,MAAMmB,gBAAgB,GAAG,IAAAL,cAAO,EAAC,MAAM;IACrC,IAAI,CAACI,OAAO,IAAI,CAAChG,MAAM,CAACW,IAAI,CAACqF,OAAO,CAAC,EAAE;MACrC,OAAO,EAAE;IACX;IACA,OAAOhG,MAAM,CAACW,IAAI,CAACqF,OAAO,CAAC,CACxBF,GAAG,CAAEjD,EAAE,IAAK2B,iBAAiB,CAAC3B,EAAE,EAAEmD,OAAO,CAACnD,EAAE,CAAC,CAAC,CAAC,CAC/CH,MAAM,CAAEqD,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;EACvB,CAAC,EAAE,CAACC,OAAO,CAAC,CAAC;EAEb,oBACE,IAAAjH,WAAA,CAAAmH,IAAA,EAAAnH,WAAA,CAAAoH,QAAA;IAAAC,QAAA,GACGH,gBAAgB,EAChBN,eAAe;EAAA,CAChB,CAAC;AAEP,CAAC;AAAC,IAAAU,QAAA,GAAAC,OAAA,CAAApH,OAAA,GAEauF,KAAK", "ignoreList": []}