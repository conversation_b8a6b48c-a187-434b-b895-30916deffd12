{"version": 3, "names": ["React", "NativeModules", "RNMBXRasterDemSourceNativeComponent", "cloneReactChildrenWithProps", "AbstractSource", "jsx", "_jsx", "isTileTemplateUrl", "url", "includes", "MapboxGL", "RNMBXModule", "RasterDemSource", "defaultProps", "id", "StyleSource", "DefaultSourceID", "constructor", "props", "console", "warn", "render", "tileUrlTemplates", "undefined", "existing", "minZoomLevel", "maxZoomLevel", "tileSize", "ref", "setNativeRef", "children", "sourceID"], "sourceRoot": "../../../src", "sources": ["components/RasterDemSource.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,cAAc;AAE5C,OAAOC,mCAAmC,MAAM,8CAA8C;AAC9F,SAASC,2BAA2B,QAAQ,UAAU;AAEtD,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE9C,MAAMC,iBAAiB,GAAIC,GAAY,IACrC,CAAC,CAACA,GAAG,KACJA,GAAG,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC;AAE9E,MAAMC,QAAQ,GAAGT,aAAa,CAACU,WAAW;AAiD1C,MAAMC,eAAe,SAASR,cAAc,CAAqB;EAC/D,OAAOS,YAAY,GAAG;IACpBC,EAAE,EAAEJ,QAAQ,CAACK,WAAW,CAACC;EAC3B,CAAC;EAEDC,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAIX,iBAAiB,CAACW,KAAK,CAACV,GAAG,CAAC,EAAE;MAChCW,OAAO,CAACC,IAAI,CACV,mKAAmKF,KAAK,CAACV,GAAG,eAC9K,CAAC;IACH;EACF;EAEAa,MAAMA,CAAA,EAAG;IACP,IAAI;MAAEb;IAAI,CAAC,GAAG,IAAI,CAACU,KAAK;IACxB,IAAI;MAAEI;IAAiB,CAAC,GAAG,IAAI,CAACJ,KAAK;;IAErC;IACA;IACA,IAAIX,iBAAiB,CAACC,GAAG,CAAC,EAAE;MAC1Bc,gBAAgB,GAAG,CAACd,GAAG,CAAC;MACxBA,GAAG,GAAGe,SAAS;IACjB;IAEA,MAAML,KAAK,GAAG;MACZ,GAAG,IAAI,CAACA,KAAK;MACbJ,EAAE,EAAE,IAAI,CAACI,KAAK,CAACJ,EAAE;MACjBU,QAAQ,EAAE,IAAI,CAACN,KAAK,CAACM,QAAQ;MAC7BhB,GAAG;MACHc,gBAAgB;MAChBG,YAAY,EAAE,IAAI,CAACP,KAAK,CAACO,YAAY;MACrCC,YAAY,EAAE,IAAI,CAACR,KAAK,CAACQ,YAAY;MACrCC,QAAQ,EAAE,IAAI,CAACT,KAAK,CAACS;IACvB,CAAC;IACD;MAAA;MACE;MACArB,IAAA,CAACJ,mCAAmC;QAAC0B,GAAG,EAAE,IAAI,CAACC,YAAa;QAAA,GAAKX,KAAK;QAAAY,QAAA,EACnE3B,2BAA2B,CAAC,IAAI,CAACe,KAAK,CAACY,QAAQ,EAAE;UAChDC,QAAQ,EAAE,IAAI,CAACb,KAAK,CAACJ;QACvB,CAAC;MAAC,CACiC;IAAC;EAE1C;AACF;AAEA,eAAeF,eAAe", "ignoreList": []}