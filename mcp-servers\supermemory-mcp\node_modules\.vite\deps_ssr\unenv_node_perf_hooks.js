import {
  createNotImplementedError
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/internal/perf_hooks/histogram.mjs
var Histogram = class {
  min = 9223372036854776e3;
  max = 0;
  mean = Number.NaN;
  exceeds = 0;
  stddev = Number.NaN;
  count = 0;
  countBigInt = BigInt(0);
  exceedsBigInt = BigInt(0);
  maxBigInt = 0;
  minBigInt = BigInt(9223372036854775807n);
  percentiles = /* @__PURE__ */ new Map();
  percentilesBigInt = /* @__PURE__ */ new Map();
  percentileBigInt(_percentile) {
    throw createNotImplementedError("Histogram.percentileBigInt");
  }
  percentile(percentile) {
    return this.percentiles.get(percentile) ?? Number.NaN;
  }
  reset() {
    throw createNotImplementedError("Histogram.reset");
  }
};
var IntervalHistogram = class extends Histogram {
  enable() {
    return true;
  }
  disable() {
    return true;
  }
};
var RecordableHistogram = class extends Histogram {
  record(val) {
    throw createNotImplementedError("RecordableHistogram.record");
  }
  recordDelta() {
    throw createNotImplementedError("RecordableHistogram.recordDelta");
  }
  add(other) {
    throw createNotImplementedError("RecordableHistogram.add");
  }
};

// node_modules/unenv/dist/runtime/node/internal/perf_hooks/performance.mjs
var _timeOrigin = globalThis.performance?.timeOrigin ?? Date.now();
var _performanceNow = globalThis.performance?.now ? globalThis.performance.now.bind(globalThis.performance) : () => Date.now() - _timeOrigin;
var nodeTiming = {
  name: "node",
  entryType: "node",
  startTime: 0,
  duration: 0,
  nodeStart: 0,
  v8Start: 0,
  bootstrapComplete: 0,
  environment: 0,
  loopStart: 0,
  loopExit: 0,
  idleTime: 0,
  uvMetricsInfo: {
    loopCount: 0,
    events: 0,
    eventsWaiting: 0
  },
  detail: void 0,
  toJSON() {
    return this;
  }
};
var PerformanceEntry = class {
  __unenv__ = true;
  detail;
  entryType = "event";
  name;
  startTime;
  constructor(name, options) {
    this.name = name;
    this.startTime = options?.startTime || _performanceNow();
    this.detail = options?.detail;
  }
  get duration() {
    return _performanceNow() - this.startTime;
  }
  toJSON() {
    return {
      name: this.name,
      entryType: this.entryType,
      startTime: this.startTime,
      duration: this.duration,
      detail: this.detail
    };
  }
};
var PerformanceMark = class PerformanceMark2 extends PerformanceEntry {
  entryType = "mark";
  constructor() {
    super(...arguments);
  }
  get duration() {
    return 0;
  }
};
var PerformanceMeasure = class extends PerformanceEntry {
  entryType = "measure";
};
var PerformanceResourceTiming = class extends PerformanceEntry {
  entryType = "resource";
  serverTiming = [];
  connectEnd = 0;
  connectStart = 0;
  decodedBodySize = 0;
  domainLookupEnd = 0;
  domainLookupStart = 0;
  encodedBodySize = 0;
  fetchStart = 0;
  initiatorType = "";
  name = "";
  nextHopProtocol = "";
  redirectEnd = 0;
  redirectStart = 0;
  requestStart = 0;
  responseEnd = 0;
  responseStart = 0;
  secureConnectionStart = 0;
  startTime = 0;
  transferSize = 0;
  workerStart = 0;
  responseStatus = 0;
};
var PerformanceObserverEntryList = class {
  __unenv__ = true;
  getEntries() {
    return [];
  }
  getEntriesByName(_name, _type) {
    return [];
  }
  getEntriesByType(type) {
    return [];
  }
};
var Performance = class {
  __unenv__ = true;
  timeOrigin = _timeOrigin;
  eventCounts = /* @__PURE__ */ new Map();
  _entries = [];
  _resourceTimingBufferSize = 0;
  navigation = void 0;
  timing = void 0;
  timerify(_fn, _options) {
    throw createNotImplementedError("Performance.timerify");
  }
  get nodeTiming() {
    return nodeTiming;
  }
  eventLoopUtilization() {
    return {};
  }
  markResourceTiming() {
    return new PerformanceResourceTiming("");
  }
  onresourcetimingbufferfull = null;
  now() {
    if (this.timeOrigin === _timeOrigin) {
      return _performanceNow();
    }
    return Date.now() - this.timeOrigin;
  }
  clearMarks(markName) {
    this._entries = markName ? this._entries.filter((e) => e.name !== markName) : this._entries.filter((e) => e.entryType !== "mark");
  }
  clearMeasures(measureName) {
    this._entries = measureName ? this._entries.filter((e) => e.name !== measureName) : this._entries.filter((e) => e.entryType !== "measure");
  }
  clearResourceTimings() {
    this._entries = this._entries.filter((e) => e.entryType !== "resource" || e.entryType !== "navigation");
  }
  getEntries() {
    return this._entries;
  }
  getEntriesByName(name, type) {
    return this._entries.filter((e) => e.name === name && (!type || e.entryType === type));
  }
  getEntriesByType(type) {
    return this._entries.filter((e) => e.entryType === type);
  }
  mark(name, options) {
    const entry = new PerformanceMark(name, options);
    this._entries.push(entry);
    return entry;
  }
  measure(measureName, startOrMeasureOptions, endMark) {
    let start;
    let end;
    if (typeof startOrMeasureOptions === "string") {
      start = this.getEntriesByName(startOrMeasureOptions, "mark")[0]?.startTime;
      end = this.getEntriesByName(endMark, "mark")[0]?.startTime;
    } else {
      start = Number.parseFloat(startOrMeasureOptions?.start) || this.now();
      end = Number.parseFloat(startOrMeasureOptions?.end) || this.now();
    }
    const entry = new PerformanceMeasure(measureName, {
      startTime: start,
      detail: {
        start,
        end
      }
    });
    this._entries.push(entry);
    return entry;
  }
  setResourceTimingBufferSize(maxSize) {
    this._resourceTimingBufferSize = maxSize;
  }
  addEventListener(type, listener, options) {
    throw createNotImplementedError("Performance.addEventListener");
  }
  removeEventListener(type, listener, options) {
    throw createNotImplementedError("Performance.removeEventListener");
  }
  dispatchEvent(event) {
    throw createNotImplementedError("Performance.dispatchEvent");
  }
  toJSON() {
    return this;
  }
};
var PerformanceObserver = class {
  __unenv__ = true;
  static supportedEntryTypes = [];
  _callback = null;
  constructor(callback) {
    this._callback = callback;
  }
  takeRecords() {
    return [];
  }
  disconnect() {
    throw createNotImplementedError("PerformanceObserver.disconnect");
  }
  observe(options) {
    throw createNotImplementedError("PerformanceObserver.observe");
  }
  bind(fn) {
    return fn;
  }
  runInAsyncScope(fn, thisArg, ...args) {
    return fn.call(thisArg, ...args);
  }
  asyncId() {
    return 0;
  }
  triggerAsyncId() {
    return 0;
  }
  emitDestroy() {
    return this;
  }
};
var performance = globalThis.performance && "addEventListener" in globalThis.performance ? globalThis.performance : new Performance();

// node_modules/unenv/dist/runtime/node/internal/perf_hooks/constants.mjs
var NODE_PERFORMANCE_GC_MAJOR = 4;
var NODE_PERFORMANCE_GC_MINOR = 1;
var NODE_PERFORMANCE_GC_INCREMENTAL = 8;
var NODE_PERFORMANCE_GC_WEAKCB = 16;
var NODE_PERFORMANCE_GC_FLAGS_NO = 0;
var NODE_PERFORMANCE_GC_FLAGS_CONSTRUCT_RETAINED = 2;
var NODE_PERFORMANCE_GC_FLAGS_FORCED = 4;
var NODE_PERFORMANCE_GC_FLAGS_SYNCHRONOUS_PHANTOM_PROCESSING = 8;
var NODE_PERFORMANCE_GC_FLAGS_ALL_AVAILABLE_GARBAGE = 16;
var NODE_PERFORMANCE_GC_FLAGS_ALL_EXTERNAL_MEMORY = 32;
var NODE_PERFORMANCE_GC_FLAGS_SCHEDULE_IDLE = 64;
var NODE_PERFORMANCE_ENTRY_TYPE_GC = 0;
var NODE_PERFORMANCE_ENTRY_TYPE_HTTP = 1;
var NODE_PERFORMANCE_ENTRY_TYPE_HTTP2 = 2;
var NODE_PERFORMANCE_ENTRY_TYPE_NET = 3;
var NODE_PERFORMANCE_ENTRY_TYPE_DNS = 4;
var NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN_TIMESTAMP = 0;
var NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN = 1;
var NODE_PERFORMANCE_MILESTONE_ENVIRONMENT = 2;
var NODE_PERFORMANCE_MILESTONE_NODE_START = 3;
var NODE_PERFORMANCE_MILESTONE_V8_START = 4;
var NODE_PERFORMANCE_MILESTONE_LOOP_START = 5;
var NODE_PERFORMANCE_MILESTONE_LOOP_EXIT = 6;
var NODE_PERFORMANCE_MILESTONE_BOOTSTRAP_COMPLETE = 7;

// node_modules/unenv/dist/runtime/node/perf_hooks.mjs
var constants = {
  NODE_PERFORMANCE_GC_MAJOR,
  NODE_PERFORMANCE_GC_MINOR,
  NODE_PERFORMANCE_GC_INCREMENTAL,
  NODE_PERFORMANCE_GC_WEAKCB,
  NODE_PERFORMANCE_GC_FLAGS_NO,
  NODE_PERFORMANCE_GC_FLAGS_CONSTRUCT_RETAINED,
  NODE_PERFORMANCE_GC_FLAGS_FORCED,
  NODE_PERFORMANCE_GC_FLAGS_SYNCHRONOUS_PHANTOM_PROCESSING,
  NODE_PERFORMANCE_GC_FLAGS_ALL_AVAILABLE_GARBAGE,
  NODE_PERFORMANCE_GC_FLAGS_ALL_EXTERNAL_MEMORY,
  NODE_PERFORMANCE_GC_FLAGS_SCHEDULE_IDLE,
  NODE_PERFORMANCE_ENTRY_TYPE_GC,
  NODE_PERFORMANCE_ENTRY_TYPE_HTTP,
  NODE_PERFORMANCE_ENTRY_TYPE_HTTP2,
  NODE_PERFORMANCE_ENTRY_TYPE_NET,
  NODE_PERFORMANCE_ENTRY_TYPE_DNS,
  NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN_TIMESTAMP,
  NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN,
  NODE_PERFORMANCE_MILESTONE_ENVIRONMENT,
  NODE_PERFORMANCE_MILESTONE_NODE_START,
  NODE_PERFORMANCE_MILESTONE_V8_START,
  NODE_PERFORMANCE_MILESTONE_LOOP_START,
  NODE_PERFORMANCE_MILESTONE_LOOP_EXIT,
  NODE_PERFORMANCE_MILESTONE_BOOTSTRAP_COMPLETE
};
var monitorEventLoopDelay = function(_options) {
  return new IntervalHistogram();
};
var createHistogram = function(_options) {
  return new RecordableHistogram();
};
var perf_hooks_default = {
  Performance,
  PerformanceMark,
  PerformanceEntry,
  PerformanceMeasure,
  PerformanceObserverEntryList,
  PerformanceObserver,
  PerformanceResourceTiming,
  performance,
  constants,
  createHistogram,
  monitorEventLoopDelay
};
export {
  Performance,
  PerformanceEntry,
  PerformanceMark,
  PerformanceMeasure,
  PerformanceObserver,
  PerformanceObserverEntryList,
  PerformanceResourceTiming,
  constants,
  createHistogram,
  perf_hooks_default as default,
  monitorEventLoopDelay,
  performance
};
//# sourceMappingURL=unenv_node_perf_hooks.js.map
