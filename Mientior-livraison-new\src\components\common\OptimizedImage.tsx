import React, { useState, useCallback } from 'react';
import {
  Image,
  ImageProps,
  ImageStyle,
  StyleProp,
  View,
  ActivityIndicator,
  Text,
  StyleSheet,
} from 'react-native';
import { PlaceholderImages } from '../../assets/images/placeholders';
import { IconColors } from '../../constants/icons';

interface OptimizedImageProps extends Omit<ImageProps, 'source'> {
  source: { uri: string } | number;
  fallbackSource?: { uri: string } | number;
  style?: StyleProp<ImageStyle>;
  loadingIndicatorColor?: string;
  showLoadingIndicator?: boolean;
  errorText?: string;
  accessibilityLabel?: string;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  onError?: (error: any) => void;
  lazy?: boolean;
  placeholder?: string;
}

/**
 * OptimizedImage Component
 * 
 * A high-performance image component with:
 * - Lazy loading support
 * - Fallback image handling
 * - Loading states with African design aesthetic
 * - Error handling with graceful degradation
 * - Accessibility compliance
 * - Performance optimization for mobile
 */
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  source,
  fallbackSource,
  style,
  loadingIndicatorColor = IconColors.primary,
  showLoadingIndicator = true,
  errorText = 'Image non disponible',
  accessibilityLabel,
  onLoadStart,
  onLoadEnd,
  onError,
  lazy = false,
  placeholder = PlaceholderImages.LOADING_ICON_BASE64,
  ...props
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [imageSource, setImageSource] = useState(source);

  const handleLoadStart = useCallback(() => {
    setLoading(true);
    setError(false);
    onLoadStart?.();
  }, [onLoadStart]);

  const handleLoadEnd = useCallback(() => {
    setLoading(false);
    onLoadEnd?.();
  }, [onLoadEnd]);

  const handleError = useCallback((errorEvent: any) => {
    setLoading(false);
    setError(true);
    
    // Try fallback source if available
    if (fallbackSource && imageSource !== fallbackSource) {
      setImageSource(fallbackSource);
      setError(false);
      setLoading(true);
    }
    
    onError?.(errorEvent);
  }, [fallbackSource, imageSource, onError]);

  // Render loading state
  if (loading && showLoadingIndicator) {
    return (
      <View style={[styles.container, style]}>
        <Image
          source={{ uri: placeholder }}
          style={[styles.placeholder, style]}
          resizeMode="contain"
        />
        <View style={styles.loadingOverlay}>
          <ActivityIndicator 
            size="small" 
            color={loadingIndicatorColor}
            accessibilityLabel="Chargement de l'image"
          />
        </View>
      </View>
    );
  }

  // Render error state
  if (error) {
    return (
      <View style={[styles.container, styles.errorContainer, style]}>
        <Image
          source={{ uri: PlaceholderImages.ERROR_ICON_BASE64 }}
          style={styles.errorIcon}
          resizeMode="contain"
        />
        <Text style={styles.errorText}>{errorText}</Text>
      </View>
    );
  }

  // Render main image
  return (
    <Image
      source={imageSource}
      style={style}
      onLoadStart={handleLoadStart}
      onLoadEnd={handleLoadEnd}
      onError={handleError}
      accessibilityLabel={accessibilityLabel}
      {...props}
    />
  );
};

/**
 * RestaurantImage Component
 * Specialized component for restaurant images with African aesthetic
 */
export const RestaurantImage: React.FC<{
  source?: { uri: string };
  style?: StyleProp<ImageStyle>;
  accessibilityLabel?: string;
}> = ({ source, style, accessibilityLabel = 'Image du restaurant' }) => {
  return (
    <OptimizedImage
      source={source || { uri: PlaceholderImages.RESTAURANT_PLACEHOLDER_BASE64 }}
      fallbackSource={{ uri: PlaceholderImages.RESTAURANT_PLACEHOLDER_BASE64 }}
      style={style}
      accessibilityLabel={accessibilityLabel}
      placeholder={PlaceholderImages.RESTAURANT_PLACEHOLDER_BASE64}
    />
  );
};

/**
 * ProductImage Component
 * Specialized component for product images
 */
export const ProductImage: React.FC<{
  source?: { uri: string };
  style?: StyleProp<ImageStyle>;
  accessibilityLabel?: string;
}> = ({ source, style, accessibilityLabel = 'Image du produit' }) => {
  return (
    <OptimizedImage
      source={source || { uri: PlaceholderImages.PRODUCT_PLACEHOLDER_BASE64 }}
      fallbackSource={{ uri: PlaceholderImages.PRODUCT_PLACEHOLDER_BASE64 }}
      style={style}
      accessibilityLabel={accessibilityLabel}
      placeholder={PlaceholderImages.PRODUCT_PLACEHOLDER_BASE64}
    />
  );
};

/**
 * AvatarImage Component
 * Specialized component for user avatars with African design
 */
export const AvatarImage: React.FC<{
  source?: { uri: string };
  style?: StyleProp<ImageStyle>;
  size?: number;
  accessibilityLabel?: string;
}> = ({ source, style, size = 40, accessibilityLabel = 'Photo de profil' }) => {
  const avatarStyle = {
    width: size,
    height: size,
    borderRadius: size / 2,
  };

  return (
    <OptimizedImage
      source={source || { uri: PlaceholderImages.DEFAULT_AVATAR_BASE64 }}
      fallbackSource={{ uri: PlaceholderImages.DEFAULT_AVATAR_BASE64 }}
      style={[avatarStyle, style]}
      accessibilityLabel={accessibilityLabel}
      placeholder={PlaceholderImages.DEFAULT_AVATAR_BASE64}
    />
  );
};

/**
 * CategoryImage Component
 * Specialized component for service category images
 */
export const CategoryImage: React.FC<{
  source?: { uri: string };
  category: 'food' | 'package' | 'shopping';
  style?: StyleProp<ImageStyle>;
  accessibilityLabel?: string;
}> = ({ source, category, style, accessibilityLabel }) => {
  const getPlaceholder = () => {
    switch (category) {
      case 'food':
        return PlaceholderImages.FOOD_DELIVERY_BASE64;
      case 'package':
        return PlaceholderImages.PACKAGE_DELIVERY_BASE64;
      case 'shopping':
        return PlaceholderImages.SHOPPING_DELIVERY_BASE64;
      default:
        return PlaceholderImages.FOOD_DELIVERY_BASE64;
    }
  };

  const placeholder = getPlaceholder();
  const defaultAccessibilityLabel = `Image de catégorie ${category}`;

  return (
    <OptimizedImage
      source={source || { uri: placeholder }}
      fallbackSource={{ uri: placeholder }}
      style={style}
      accessibilityLabel={accessibilityLabel || defaultAccessibilityLabel}
      placeholder={placeholder}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
  placeholder: {
    opacity: 0.3,
  },
  loadingOverlay: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 8,
    padding: 8,
  },
  errorContainer: {
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorIcon: {
    width: 32,
    height: 32,
    marginBottom: 8,
    opacity: 0.6,
  },
  errorText: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default OptimizedImage;
