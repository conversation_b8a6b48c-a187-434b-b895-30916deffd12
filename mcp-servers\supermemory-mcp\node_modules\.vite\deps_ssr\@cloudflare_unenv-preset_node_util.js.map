{"version": 3, "sources": ["../../unenv/dist/runtime/node/util.mjs", "../../unenv/dist/runtime/node/internal/util/promisify.mjs", "../../unenv/dist/runtime/node/internal/util/legacy-types.mjs", "../../@cloudflare/unenv-preset/dist/runtime/node/util.mjs"], "sourcesContent": ["import types from \"node:util/types\";\nimport { notImplemented } from \"../_internal/utils.mjs\";\nimport { inherits } from \"./internal/util/inherits.mjs\";\nimport { promisify } from \"./internal/util/promisify.mjs\";\nimport { MIMEParams, MIMEType } from \"./internal/util/mime.mjs\";\nimport { isArray, isBoolean, isBuffer, isDate, isDeepStrictEqual, isError, isFunction, isNull, isNullOrUndefined, isNumber, isObject, isPrimitive, isRegExp, isString, isSymbol, isUndefined } from \"./internal/util/legacy-types.mjs\";\nimport { debug, debuglog, format, formatWithOptions, inspect, log } from \"./internal/util/log.mjs\";\nexport { MIMEParams, MIMEType } from \"./internal/util/mime.mjs\";\nexport * from \"./internal/util/legacy-types.mjs\";\nexport * from \"./internal/util/log.mjs\";\nexport { inherits } from \"./internal/util/inherits.mjs\";\nexport { promisify } from \"./internal/util/promisify.mjs\";\nexport { default as types } from \"node:util/types\";\nexport const TextDecoder = globalThis.TextDecoder;\nexport const TextEncoder = globalThis.TextEncoder;\nexport const deprecate = (fn) => fn;\nexport const _errnoException = /* @__PURE__ */ notImplemented(\"util._errnoException\");\nexport const _exceptionWithHostPort = /* @__PURE__ */ notImplemented(\"util._exceptionWithHostPort\");\nexport const _extend = /* @__PURE__ */ notImplemented(\"util._extend\");\nexport const aborted = /* @__PURE__ */ notImplemented(\"util.aborted\");\nexport const callbackify = /* @__PURE__ */ notImplemented(\"util.callbackify\");\nexport const getSystemErrorMap = /* @__PURE__ */ notImplemented(\"util.getSystemErrorMap\");\nexport const getSystemErrorName = /* @__PURE__ */ notImplemented(\"util.getSystemErrorName\");\nexport const toUSVString = /* @__PURE__ */ notImplemented(\"util.toUSVString\");\nexport const stripVTControlCharacters = /* @__PURE__ */ notImplemented(\"util.stripVTControlCharacters\");\nexport const transferableAbortController = /* @__PURE__ */ notImplemented(\"util.transferableAbortController\");\nexport const transferableAbortSignal = /* @__PURE__ */ notImplemented(\"util.transferableAbortSignal\");\nexport const parseArgs = /* @__PURE__ */ notImplemented(\"util.parseArgs\");\nexport const parseEnv = /* @__PURE__ */ notImplemented(\"util.parseEnv\");\nexport const styleText = /* @__PURE__ */ notImplemented(\"util.styleText\");\n/** @deprecated */\nexport const getCallSite = /* @__PURE__ */ notImplemented(\"util.getCallSite\");\nexport const getCallSites = /* @__PURE__ */ notImplemented(\"util.getCallSites\");\nexport const getSystemErrorMessage = /* @__PURE__ */ notImplemented(\"util.getSystemErrorMessage\");\nexport default {\n\t_errnoException,\n\t_exceptionWithHostPort,\n\t_extend,\n\taborted,\n\tcallbackify,\n\tdeprecate,\n\tgetCallSite,\n\tgetCallSites,\n\tgetSystemErrorMessage,\n\tgetSystemErrorMap,\n\tgetSystemErrorName,\n\tinherits,\n\tpromisify,\n\tstripVTControlCharacters,\n\ttoUSVString,\n\tTextDecoder,\n\tTextEncoder,\n\ttypes,\n\ttransferableAbortController,\n\ttransferableAbortSignal,\n\tparseArgs,\n\tparseEnv,\n\tstyleText,\n\tMIMEParams,\n\tMIMEType,\n\tisArray,\n\tisBoolean,\n\tisBuffer,\n\tisDate,\n\tisDeepStrictEqual,\n\tisError,\n\tisFunction,\n\tisNull,\n\tisNullOrUndefined,\n\tisNumber,\n\tisObject,\n\tisPrimitive,\n\tisRegExp,\n\tisString,\n\tisSymbol,\n\tisUndefined,\n\tdebug,\n\tdebuglog,\n\tformat,\n\tformatWithOptions,\n\tinspect,\n\tlog\n};\n", "const customSymbol = /* @__PURE__ */ Symbol(\"customPromisify\");\nfunction _promisify(fn) {\n\tif (fn[customSymbol]) {\n\t\treturn fn[customSymbol];\n\t}\n\treturn function(...args) {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\ttry {\n\t\t\t\tfn.call(this, ...args, (err, val) => {\n\t\t\t\t\tif (err) {\n\t\t\t\t\t\treturn reject(err);\n\t\t\t\t\t}\n\t\t\t\t\tresolve(val);\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\treject(error);\n\t\t\t}\n\t\t});\n\t};\n}\nexport const promisify = /* @__PURE__ */ Object.assign(_promisify, { custom: customSymbol });\n", "export const isRegExp = (val) => val instanceof RegExp;\nexport const isDate = (val) => val instanceof Date;\nexport const isArray = (val) => Array.isArray(val);\nexport const isBoolean = (val) => typeof val === \"boolean\";\nexport const isNull = (val) => val === null;\nexport const isNullOrUndefined = (val) => val === null || val === undefined;\nexport const isNumber = (val) => typeof val === \"number\";\nexport const isString = (val) => typeof val === \"string\";\nexport const isSymbol = (val) => typeof val === \"symbol\";\nexport const isUndefined = (val) => val === undefined;\nexport const isFunction = (val) => typeof val === \"function\";\nexport const isBuffer = (val) => {\n\treturn val && typeof val === \"object\" && typeof val.copy === \"function\" && typeof val.fill === \"function\" && typeof val.readUInt8 === \"function\";\n};\nexport const isDeepStrictEqual = (a, b) => JSON.stringify(a) === JSON.stringify(b);\nexport const isObject = (val) => val !== null && typeof val === \"object\" && Object.getPrototypeOf(val).isPrototypeOf(Object);\nexport const isError = (val) => val instanceof Error;\nexport const isPrimitive = (val) => {\n\tif (typeof val === \"object\") {\n\t\treturn val === null;\n\t}\n\treturn typeof val !== \"function\";\n};\n", "import {\n  _errnoException,\n  _exceptionWithHostPort,\n  getSystemErrorMap,\n  getSystemErrorName,\n  isBoolean,\n  isBuffer,\n  isDate,\n  isError,\n  isFunction,\n  isNull,\n  isNullOrUndefined,\n  isNumber,\n  isObject,\n  isPrimitive,\n  isRegExp,\n  isString,\n  isSymbol,\n  isUndefined,\n  parseEnv,\n  styleText\n} from \"unenv/node/util\";\nexport {\n  _errnoException,\n  _exceptionWithHostPort,\n  getSystemErrorMap,\n  getSystemErrorName,\n  isBoolean,\n  isBuffer,\n  isDate,\n  isError,\n  isFunction,\n  isNull,\n  isNullOrUndefined,\n  isNumber,\n  isObject,\n  isPrimitive,\n  isRegExp,\n  isString,\n  isSymbol,\n  isUndefined,\n  parseEnv,\n  styleText\n} from \"unenv/node/util\";\nconst workerdUtil = process.getBuiltinModule(\"node:util\");\nexport const {\n  MIMEParams,\n  MIMEType,\n  TextDecoder,\n  TextEncoder,\n  // @ts-expect-error missing types?\n  _extend,\n  aborted,\n  callbackify,\n  debug,\n  debuglog,\n  deprecate,\n  format,\n  formatWithOptions,\n  // @ts-expect-error unknown type\n  getCallSite,\n  inherits,\n  inspect,\n  isArray,\n  isDeepStrictEqual,\n  log,\n  parseArgs,\n  promisify,\n  stripVTControlCharacters,\n  toUSVString,\n  transferableAbortController,\n  transferableAbortSignal\n} = workerdUtil;\nexport const types = workerdUtil.types;\nexport default {\n  /**\n   * manually unroll unenv-polyfilled-symbols to make it tree-shakeable\n   */\n  _errnoException,\n  _exceptionWithHostPort,\n  // @ts-expect-error unenv has unknown type\n  getSystemErrorMap,\n  // @ts-expect-error unenv has unknown type\n  getSystemErrorName,\n  isBoolean,\n  isBuffer,\n  isDate,\n  isError,\n  isFunction,\n  isNull,\n  isNullOrUndefined,\n  isNumber,\n  isObject,\n  isPrimitive,\n  isRegExp,\n  isString,\n  isSymbol,\n  isUndefined,\n  // @ts-expect-error unenv has unknown type\n  parseEnv,\n  // @ts-expect-error unenv has unknown type\n  styleText,\n  /**\n   * manually unroll workerd-polyfilled-symbols to make it tree-shakeable\n   */\n  _extend,\n  aborted,\n  callbackify,\n  debug,\n  debuglog,\n  deprecate,\n  format,\n  formatWithOptions,\n  getCallSite,\n  inherits,\n  inspect,\n  isArray,\n  isDeepStrictEqual,\n  log,\n  MIMEParams,\n  MIMEType,\n  parseArgs,\n  promisify,\n  stripVTControlCharacters,\n  TextDecoder,\n  TextEncoder,\n  toUSVString,\n  transferableAbortController,\n  transferableAbortSignal,\n  // special-cased deep merged symbols\n  types\n};\n"], "mappings": ";;;;;;AAAA,OAAO,WAAW;;;ACAlB,IAAM,eAA+B,OAAO,iBAAiB;AAC7D,SAAS,WAAW,IAAI;AACvB,MAAI,GAAG,YAAY,GAAG;AACrB,WAAO,GAAG,YAAY;AAAA,EACvB;AACA,SAAO,YAAY,MAAM;AACxB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,UAAI;AACH,WAAG,KAAK,MAAM,GAAG,MAAM,CAAC,KAAK,QAAQ;AACpC,cAAI,KAAK;AACR,mBAAO,OAAO,GAAG;AAAA,UAClB;AACA,kBAAQ,GAAG;AAAA,QACZ,CAAC;AAAA,MACF,SAAS,OAAO;AACf,eAAO,KAAK;AAAA,MACb;AAAA,IACD,CAAC;AAAA,EACF;AACD;AACO,IAAM,YAA4B,OAAO,OAAO,YAAY,EAAE,QAAQ,aAAa,CAAC;;;ACpBpF,IAAM,WAAW,CAAC,QAAQ,eAAe;AACzC,IAAM,SAAS,CAAC,QAAQ,eAAe;AAEvC,IAAM,YAAY,CAAC,QAAQ,OAAO,QAAQ;AAC1C,IAAM,SAAS,CAAC,QAAQ,QAAQ;AAChC,IAAM,oBAAoB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ;AAC3D,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AACzC,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AACzC,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AACzC,IAAM,cAAc,CAAC,QAAQ,QAAQ;AACrC,IAAM,aAAa,CAAC,QAAQ,OAAO,QAAQ;AAC3C,IAAM,WAAW,CAAC,QAAQ;AAChC,SAAO,OAAO,OAAO,QAAQ,YAAY,OAAO,IAAI,SAAS,cAAc,OAAO,IAAI,SAAS,cAAc,OAAO,IAAI,cAAc;AACvI;AAEO,IAAM,WAAW,CAAC,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,eAAe,GAAG,EAAE,cAAc,MAAM;AACpH,IAAM,UAAU,CAAC,QAAQ,eAAe;AACxC,IAAM,cAAc,CAAC,QAAQ;AACnC,MAAI,OAAO,QAAQ,UAAU;AAC5B,WAAO,QAAQ;AAAA,EAChB;AACA,SAAO,OAAO,QAAQ;AACvB;;;AFVA,SAAoB,WAAXA,gBAAwB;AAC1B,IAAM,cAAc,WAAW;AAC/B,IAAM,cAAc,WAAW;AAE/B,IAAM,kBAAkC,eAAe,sBAAsB;AAC7E,IAAM,yBAAyC,eAAe,6BAA6B;AAC3F,IAAM,UAA0B,eAAe,cAAc;AAC7D,IAAM,UAA0B,eAAe,cAAc;AAC7D,IAAM,cAA8B,eAAe,kBAAkB;AACrE,IAAM,oBAAoC,eAAe,wBAAwB;AACjF,IAAM,qBAAqC,eAAe,yBAAyB;AACnF,IAAM,cAA8B,eAAe,kBAAkB;AACrE,IAAM,2BAA2C,eAAe,+BAA+B;AAC/F,IAAM,8BAA8C,eAAe,kCAAkC;AACrG,IAAM,0BAA0C,eAAe,8BAA8B;AAC7F,IAAM,YAA4B,eAAe,gBAAgB;AACjE,IAAM,WAA2B,eAAe,eAAe;AAC/D,IAAM,YAA4B,eAAe,gBAAgB;AAEjE,IAAM,cAA8B,eAAe,kBAAkB;AACrE,IAAM,eAA+B,eAAe,mBAAmB;AACvE,IAAM,wBAAwC,eAAe,4BAA4B;;;AGWhG,IAAM,cAAc,QAAQ,iBAAiB,WAAW;AACjD,IAAM;AAAA,EACX,YAAAC;AAAA,EACA,UAAAC;AAAA,EACA,aAAAC;AAAA,EACA,aAAAC;AAAA;AAAA,EAEA,SAAAC;AAAA,EACA,SAAAC;AAAA,EACA,aAAAC;AAAA,EACA,OAAAC;AAAA,EACA,UAAAC;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA,mBAAAC;AAAA;AAAA,EAEA,aAAAC;AAAA,EACA,UAAAC;AAAA,EACA,SAAAC;AAAA,EACA,SAAAC;AAAA,EACA,mBAAAC;AAAA,EACA,KAAAC;AAAA,EACA,WAAAC;AAAA,EACA,WAAAC;AAAA,EACA,0BAAAC;AAAA,EACA,aAAAC;AAAA,EACA,6BAAAC;AAAA,EACA,yBAAAC;AACF,IAAI;AACG,IAAMC,SAAQ,YAAY;AACjC,IAAO,eAAQ;AAAA;AAAA;AAAA;AAAA,EAIb;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA,EAIA,SAAAnB;AAAA,EACA,SAAAC;AAAA,EACA,aAAAC;AAAA,EACA,OAAAC;AAAA,EACA,UAAAC;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA,mBAAAC;AAAA,EACA,aAAAC;AAAA,EACA,UAAAC;AAAA,EACA,SAAAC;AAAA,EACA,SAAAC;AAAA,EACA,mBAAAC;AAAA,EACA,KAAAC;AAAA,EACA,YAAAhB;AAAA,EACA,UAAAC;AAAA,EACA,WAAAgB;AAAA,EACA,WAAAC;AAAA,EACA,0BAAAC;AAAA,EACA,aAAAjB;AAAA,EACA,aAAAC;AAAA,EACA,aAAAiB;AAAA,EACA,6BAAAC;AAAA,EACA,yBAAAC;AAAA;AAAA,EAEA,OAAAC;AACF;", "names": ["default", "MIMEParams", "MIMEType", "TextDecoder", "TextEncoder", "_extend", "aborted", "callbackify", "debug", "debuglog", "format", "formatWithOptions", "getCallSite", "inherits", "inspect", "isArray", "isDeepStrictEqual", "log", "parseArgs", "promisify", "stripVTControlCharacters", "toUSVString", "transferableAbortController", "transferableAbortSignal", "types"]}