{"version": 3, "sources": ["../../@standard-community/standard-json/dist/arktype-CqF_-yop.js"], "sourcesContent": ["var e=Object.defineProperty;var t=(o,c)=>e(o,\"name\",{value:c,configurable:!0});const n=t(o=>o.toJsonSchema(),\"toJsonSchema\");export{n as toJsonSchema};\n"], "mappings": ";;;AAAA,IAAI,IAAE,OAAO;AAAe,IAAI,IAAE,CAAC,GAAE,MAAI,EAAE,GAAE,QAAO,EAAC,OAAM,GAAE,cAAa,KAAE,CAAC;AAAE,IAAM,IAAE,EAAE,OAAG,EAAE,aAAa,GAAE,cAAc;", "names": []}