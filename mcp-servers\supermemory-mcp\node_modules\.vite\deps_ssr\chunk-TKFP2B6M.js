// node_modules/unenv/dist/runtime/node/internal/crypto/constants.mjs
var SSL_OP_ALL = 2147485776;
var SSL_OP_ALLOW_NO_DHE_KEX = 1024;
var SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION = 262144;
var SSL_OP_CIPHER_SERVER_PREFERENCE = 4194304;
var SSL_OP_CISCO_ANYCONNECT = 32768;
var SSL_OP_COOKIE_EXCHANGE = 8192;
var SSL_OP_CRYPTOPRO_TLSEXT_BUG = 2147483648;
var SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS = 2048;
var SSL_OP_LEGACY_SERVER_CONNECT = 4;
var SSL_OP_NO_COMPRESSION = 131072;
var SSL_OP_NO_ENCRYPT_THEN_MAC = 524288;
var SSL_OP_NO_QUERY_MTU = 4096;
var SSL_OP_NO_RENEGOTIATION = 1073741824;
var SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION = 65536;
var SSL_OP_NO_SSLv2 = 0;
var SSL_OP_NO_SSLv3 = 33554432;
var SSL_OP_NO_TICKET = 16384;
var SSL_OP_NO_TLSv1 = 67108864;
var SSL_OP_NO_TLSv1_1 = 268435456;
var SSL_OP_NO_TLSv1_2 = 134217728;
var SSL_OP_NO_TLSv1_3 = 536870912;
var SSL_OP_PRIORITIZE_CHACHA = 2097152;
var SSL_OP_TLS_ROLLBACK_BUG = 8388608;
var ENGINE_METHOD_RSA = 1;
var ENGINE_METHOD_DSA = 2;
var ENGINE_METHOD_DH = 4;
var ENGINE_METHOD_RAND = 8;
var ENGINE_METHOD_EC = 2048;
var ENGINE_METHOD_CIPHERS = 64;
var ENGINE_METHOD_DIGESTS = 128;
var ENGINE_METHOD_PKEY_METHS = 512;
var ENGINE_METHOD_PKEY_ASN1_METHS = 1024;
var ENGINE_METHOD_ALL = 65535;
var ENGINE_METHOD_NONE = 0;
var DH_CHECK_P_NOT_SAFE_PRIME = 2;
var DH_CHECK_P_NOT_PRIME = 1;
var DH_UNABLE_TO_CHECK_GENERATOR = 4;
var DH_NOT_SUITABLE_GENERATOR = 8;
var RSA_PKCS1_PADDING = 1;
var RSA_NO_PADDING = 3;
var RSA_PKCS1_OAEP_PADDING = 4;
var RSA_X931_PADDING = 5;
var RSA_PKCS1_PSS_PADDING = 6;
var RSA_PSS_SALTLEN_DIGEST = -1;
var RSA_PSS_SALTLEN_MAX_SIGN = -2;
var RSA_PSS_SALTLEN_AUTO = -2;
var POINT_CONVERSION_COMPRESSED = 2;
var POINT_CONVERSION_UNCOMPRESSED = 4;
var POINT_CONVERSION_HYBRID = 6;
var defaultCoreCipherList = "";
var defaultCipherList = "";
var OPENSSL_VERSION_NUMBER = 0;
var TLS1_VERSION = 0;
var TLS1_1_VERSION = 0;
var TLS1_2_VERSION = 0;
var TLS1_3_VERSION = 0;

export {
  SSL_OP_ALL,
  SSL_OP_ALLOW_NO_DHE_KEX,
  SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION,
  SSL_OP_CIPHER_SERVER_PREFERENCE,
  SSL_OP_CISCO_ANYCONNECT,
  SSL_OP_COOKIE_EXCHANGE,
  SSL_OP_CRYPTOPRO_TLSEXT_BUG,
  SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS,
  SSL_OP_LEGACY_SERVER_CONNECT,
  SSL_OP_NO_COMPRESSION,
  SSL_OP_NO_ENCRYPT_THEN_MAC,
  SSL_OP_NO_QUERY_MTU,
  SSL_OP_NO_RENEGOTIATION,
  SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION,
  SSL_OP_NO_SSLv2,
  SSL_OP_NO_SSLv3,
  SSL_OP_NO_TICKET,
  SSL_OP_NO_TLSv1,
  SSL_OP_NO_TLSv1_1,
  SSL_OP_NO_TLSv1_2,
  SSL_OP_NO_TLSv1_3,
  SSL_OP_PRIORITIZE_CHACHA,
  SSL_OP_TLS_ROLLBACK_BUG,
  ENGINE_METHOD_RSA,
  ENGINE_METHOD_DSA,
  ENGINE_METHOD_DH,
  ENGINE_METHOD_RAND,
  ENGINE_METHOD_EC,
  ENGINE_METHOD_CIPHERS,
  ENGINE_METHOD_DIGESTS,
  ENGINE_METHOD_PKEY_METHS,
  ENGINE_METHOD_PKEY_ASN1_METHS,
  ENGINE_METHOD_ALL,
  ENGINE_METHOD_NONE,
  DH_CHECK_P_NOT_SAFE_PRIME,
  DH_CHECK_P_NOT_PRIME,
  DH_UNABLE_TO_CHECK_GENERATOR,
  DH_NOT_SUITABLE_GENERATOR,
  RSA_PKCS1_PADDING,
  RSA_NO_PADDING,
  RSA_PKCS1_OAEP_PADDING,
  RSA_X931_PADDING,
  RSA_PKCS1_PSS_PADDING,
  RSA_PSS_SALTLEN_DIGEST,
  RSA_PSS_SALTLEN_MAX_SIGN,
  RSA_PSS_SALTLEN_AUTO,
  POINT_CONVERSION_COMPRESSED,
  POINT_CONVERSION_UNCOMPRESSED,
  POINT_CONVERSION_HYBRID,
  defaultCoreCipherList,
  defaultCipherList,
  OPENSSL_VERSION_NUMBER,
  TLS1_VERSION,
  TLS1_1_VERSION,
  TLS1_2_VERSION,
  TLS1_3_VERSION
};
//# sourceMappingURL=chunk-TKFP2B6M.js.map
