{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/trace_events/tracing.mjs", "../../unenv/dist/runtime/node/trace_events.mjs"], "sourcesContent": ["export class Tracing {\n\tcategories = \"\";\n\tenabled = false;\n\tdisable() {\n\t\tthis.enabled = false;\n\t}\n\tenable() {\n\t\tthis.enabled = true;\n\t}\n}\n", "import { Tracing } from \"./internal/trace_events/tracing.mjs\";\nexport const createTracing = function() {\n\treturn new Tracing();\n};\nexport const getEnabledCategories = () => \"\";\nexport default {\n\tcreateTracing,\n\tgetEnabledCategories\n};\n"], "mappings": ";;;AAAO,IAAM,UAAN,MAAc;AAAA,EACpB,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AACT,SAAK,UAAU;AAAA,EAChB;AAAA,EACA,SAAS;AACR,SAAK,UAAU;AAAA,EAChB;AACD;;;ACRO,IAAM,gBAAgB,WAAW;AACvC,SAAO,IAAI,QAAQ;AACpB;AACO,IAAM,uBAAuB,MAAM;AAC1C,IAAO,uBAAQ;AAAA,EACd;AAAA,EACA;AACD;", "names": []}