import {
  createNotImplementedError,
  notImplemented
} from "./chunk-AO3S7MWW.js";
import {
  __export
} from "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/internal/vm/script.mjs
var Script = class {
  runInContext(contextifiedObject, options) {
    throw createNotImplementedError("Script.runInContext");
  }
  runInNewContext(contextObject, options) {
    throw createNotImplementedError("Script.runInNewContext");
  }
  runInThisContext(options) {
    throw createNotImplementedError("Script.runInThisContext");
  }
  createCachedData() {
    throw createNotImplementedError("Script.createCachedData");
  }
};

// node_modules/unenv/dist/runtime/node/internal/vm/constants.mjs
var constants_exports = {};
__export(constants_exports, {
  DONT_CONTEXTIFY: () => DONT_CONTEXTIFY,
  USE_MAIN_CONTEXT_DEFAULT_LOADER: () => USE_MAIN_CONTEXT_DEFAULT_LOADER
});
var USE_MAIN_CONTEXT_DEFAULT_LOADER = Symbol("vm_dynamic_import_main_context_default");
var DONT_CONTEXTIFY = Symbol("vm_context_no_contextify");

// node_modules/unenv/dist/runtime/node/vm.mjs
var compileFunction = notImplemented("vm.compileFunction");
var _contextSymbol = Symbol("uenv.vm.context");
var createContext = function createContext2() {
  return Object.create(null, { [_contextSymbol]: { value: true } });
};
var createScript = function createScript2() {
  return new Script();
};
var isContext = (context) => {
  return context && context[_contextSymbol] === true;
};
var measureMemory = () => Promise.resolve({
  total: {
    jsMemoryEstimate: 0,
    jsMemoryRange: [1, 2]
  },
  WebAssembly: {
    code: 0,
    metadata: 0
  }
});
var runInContext = notImplemented("vm.runInContext");
var runInNewContext = notImplemented("vm.runInNewContext");
var runInThisContext = notImplemented("vm.runInThisContext");
var vm_default = {
  Script,
  compileFunction,
  constants: constants_exports,
  createContext,
  isContext,
  measureMemory,
  runInContext,
  runInNewContext,
  runInThisContext,
  createScript
};
export {
  Script,
  compileFunction,
  constants_exports as constants,
  createContext,
  createScript,
  vm_default as default,
  isContext,
  measureMemory,
  runInContext,
  runInNewContext,
  runInThisContext
};
//# sourceMappingURL=unenv_node_vm.js.map
