{"version": 3, "file": "offlineManager.d.ts", "sourceRoot": "", "sources": ["../../../../../src/modules/offline/offlineManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,kBAAkB,EAClB,iBAAiB,EAClB,MAAM,cAAc,CAAC;AAItB,OAAO,EACL,OAAO,IAAI,wBAAwB,EACnC,KAAK,4BAA4B,GAClC,MAAM,4BAA4B,CAAC;AACpC,OAAiC,EAC/B,KAAK,4BAA4B,EAClC,MAAM,4BAA4B,CAAC;AACpC,OAAO,WAAW,MAAM,eAAe,CAAC;AAKxC,eAAO,MAAM,yBAAyB,oBAErC,CAAC;AAEF,MAAM,MAAM,qBAAqB,GAAG;IAClC,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,UAAU,EAAE,MAAM,CAAC;IACnB,qBAAqB,EAAE,MAAM,CAAC;IAC9B,kBAAkB,EAAE,MAAM,CAAC;IAC3B,sBAAsB,EAAE,MAAM,CAAC;IAC/B,qBAAqB,EAAE,MAAM,CAAC;IAC9B,iBAAiB,EAAE,MAAM,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,KAAK,UAAU,GAAG;IAChB,OAAO,EAAE,gBAAgB,CAAC;CAC3B,CAAC;AAEF,KAAK,aAAa,GAAG;IACnB,OAAO,EAAE,qBAAqB,CAAC;CAChC,CAAC;AAEF,KAAK,gBAAgB,GAAG,CACtB,IAAI,EAAE,WAAW,EACjB,MAAM,EAAE,qBAAqB,KAC1B,IAAI,CAAC;AACV,KAAK,aAAa,GAAG,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,gBAAgB,KAAK,IAAI,CAAC;AAExE;;;;GAIG;AACH,cAAM,cAAc;IAClB,OAAO,CAAC,eAAe,CAAU;IACjC,OAAO,CAAC,aAAa,CAA8B;IACnD,OAAO,CAAC,kBAAkB,CAAmC;IAC7D,OAAO,CAAC,eAAe,CAAgC;IAChD,oBAAoB,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAC/C,iBAAiB,EAAE,iBAAiB,GAAG,IAAI,CAAC;;IAgBnD;;;;;;;;;;;;;;;;;;;;OAoBG;IACG,UAAU,CACd,OAAO,EAAE,4BAA4B,EACrC,gBAAgB,EAAE,gBAAgB,EAClC,aAAa,CAAC,EAAE,aAAa,GAC5B,OAAO,CAAC,IAAI,CAAC;IAkBhB;;;;;;;;;;OAUG;IACG,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAajD;;;;;;;;OAQG;IACG,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAc7C;;;;;;;;;;;;OAYG;IACG,sBAAsB,IAAI,OAAO,CAAC,IAAI,CAAC;IAW7C;;;;;;;;;;OAUG;IACG,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC;IASxC;;;;;;;OAOG;IACG,mBAAmB,IAAI,OAAO,CAAC,IAAI,CAAC;IAI1C;;;;;;;;;OASG;IACG,0BAA0B,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAK7D;;;;;;;OAOG;IACG,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC;IAMpC;;;;;;;OAOG;IACG,QAAQ,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;IAOxC;;;;;;;;OAQG;IACG,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC;IAK7D;;;;;;;;OAQG;IACG,mBAAmB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAKtD;;;;;;;;;OASG;IACH,iBAAiB,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAItC;;;;;;;;;OASG;IACH,wBAAwB,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI;IAIrD;;;;;;;;;;;;;OAaG;IACG,SAAS,CACb,QAAQ,EAAE,MAAM,EAChB,gBAAgB,EAAE,gBAAgB,EAClC,aAAa,CAAC,EAAE,aAAa,GAC5B,OAAO,CAAC,IAAI,CAAC;IAmChB;;;;;;;;;OASG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAmB7B,WAAW,CAAC,SAAS,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAgBxD,WAAW,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAgBnC,QAAQ,CAAC,CAAC,EAAE,UAAU,GAAG,IAAI;IAW7B,aAAa,CACX,IAAI,EAAE,MAAM,EACZ,WAAW,EACP,MAAM,CAAC,MAAM,EAAE,gBAAgB,CAAC,GAChC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,GAChC,OAAO;CAKX;AAED,QAAA,MAAM,cAAc,gBAAuB,CAAC;AAC5C,eAAe,cAAc,CAAC"}