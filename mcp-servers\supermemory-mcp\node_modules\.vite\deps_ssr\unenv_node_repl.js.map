{"version": 3, "sources": ["../../unenv/dist/runtime/node/repl.mjs"], "sourcesContent": ["import { builtinModules as _builtinModules } from \"node:module\";\nimport { notImplemented, notImplementedClass } from \"../_internal/utils.mjs\";\nexport const writer = /* @__PURE__ */ notImplementedClass(\"repl.writer\");\nexport const start = /* @__PURE__ */ notImplemented(\"repl.start\");\nexport const Recoverable = /* @__PURE__ */ notImplementedClass(\"repl.Recoverable\");\nexport const REPLServer = /* @__PURE__ */ notImplementedClass(\"repl.REPLServer\");\nexport const REPL_MODE_SLOPPY = /* @__PURE__ */ Symbol(\"repl-sloppy\");\nexport const REPL_MODE_STRICT = /* @__PURE__ */ Symbol(\"repl-strict\");\nexport const builtinModules = /* @__PURE__ */ _builtinModules.filter((m) => m[0] !== \"_\");\nexport const _builtinLibs = builtinModules;\nexport default {\n\twriter,\n\tstart,\n\tRecoverable,\n\tREPLServer,\n\tbuiltinModules,\n\t_builtinLibs,\n\tREPL_MODE_SLOPPY,\n\tREPL_MODE_STRICT\n};\n"], "mappings": ";;;;;;;AAAA,SAAS,kBAAkB,uBAAuB;AAE3C,IAAM,SAAyB,oBAAoB,aAAa;AAChE,IAAM,QAAwB,eAAe,YAAY;AACzD,IAAM,cAA8B,oBAAoB,kBAAkB;AAC1E,IAAM,aAA6B,oBAAoB,iBAAiB;AACxE,IAAM,mBAAmC,OAAO,aAAa;AAC7D,IAAM,mBAAmC,OAAO,aAAa;AAC7D,IAAM,iBAAiC,gBAAgB,OAAO,CAAC,MAAM,EAAE,CAAC,MAAM,GAAG;AACjF,IAAM,eAAe;AAC5B,IAAO,eAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}