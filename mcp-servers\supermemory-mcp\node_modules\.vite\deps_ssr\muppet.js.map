{"version": 3, "sources": ["../../hono/dist/utils/cookie.js", "../../hono/dist/helper/cookie/index.js", "../../hono/dist/http-exception.js", "../../hono/dist/utils/buffer.js", "../../hono/dist/validator/validator.js", "../../@hono/standard-validator/dist/index.js", "../../muppet/dist/muppet-BdeHS-Ba.js", "../../@standard-community/standard-json/dist/index.js", "../../muppet/dist/index.js"], "sourcesContent": ["// src/utils/cookie.ts\nimport { decodeURIComponent_ } from \"./url.js\";\nvar algorithm = { name: \"HMAC\", hash: \"SHA-256\" };\nvar getCryptoKey = async (secret) => {\n  const secretBuf = typeof secret === \"string\" ? new TextEncoder().encode(secret) : secret;\n  return await crypto.subtle.importKey(\"raw\", secretBuf, algorithm, false, [\"sign\", \"verify\"]);\n};\nvar makeSignature = async (value, secret) => {\n  const key = await getCryptoKey(secret);\n  const signature = await crypto.subtle.sign(algorithm.name, key, new TextEncoder().encode(value));\n  return btoa(String.fromCharCode(...new Uint8Array(signature)));\n};\nvar verifySignature = async (base64Signature, value, secret) => {\n  try {\n    const signatureBinStr = atob(base64Signature);\n    const signature = new Uint8Array(signatureBinStr.length);\n    for (let i = 0, len = signatureBinStr.length; i < len; i++) {\n      signature[i] = signatureBinStr.charCodeAt(i);\n    }\n    return await crypto.subtle.verify(algorithm, secret, signature, new TextEncoder().encode(value));\n  } catch {\n    return false;\n  }\n};\nvar validCookieNameRegEx = /^[\\w!#$%&'*.^`|~+-]+$/;\nvar validCookieValueRegEx = /^[ !#-:<-[\\]-~]*$/;\nvar parse = (cookie, name) => {\n  if (name && cookie.indexOf(name) === -1) {\n    return {};\n  }\n  const pairs = cookie.trim().split(\";\");\n  const parsedCookie = {};\n  for (let pairStr of pairs) {\n    pairStr = pairStr.trim();\n    const valueStartPos = pairStr.indexOf(\"=\");\n    if (valueStartPos === -1) {\n      continue;\n    }\n    const cookieName = pairStr.substring(0, valueStartPos).trim();\n    if (name && name !== cookieName || !validCookieNameRegEx.test(cookieName)) {\n      continue;\n    }\n    let cookieValue = pairStr.substring(valueStartPos + 1).trim();\n    if (cookieValue.startsWith('\"') && cookieValue.endsWith('\"')) {\n      cookieValue = cookieValue.slice(1, -1);\n    }\n    if (validCookieValueRegEx.test(cookieValue)) {\n      parsedCookie[cookieName] = decodeURIComponent_(cookieValue);\n      if (name) {\n        break;\n      }\n    }\n  }\n  return parsedCookie;\n};\nvar parseSigned = async (cookie, secret, name) => {\n  const parsedCookie = {};\n  const secretKey = await getCryptoKey(secret);\n  for (const [key, value] of Object.entries(parse(cookie, name))) {\n    const signatureStartPos = value.lastIndexOf(\".\");\n    if (signatureStartPos < 1) {\n      continue;\n    }\n    const signedValue = value.substring(0, signatureStartPos);\n    const signature = value.substring(signatureStartPos + 1);\n    if (signature.length !== 44 || !signature.endsWith(\"=\")) {\n      continue;\n    }\n    const isVerified = await verifySignature(signature, signedValue, secretKey);\n    parsedCookie[key] = isVerified ? signedValue : false;\n  }\n  return parsedCookie;\n};\nvar _serialize = (name, value, opt = {}) => {\n  let cookie = `${name}=${value}`;\n  if (name.startsWith(\"__Secure-\") && !opt.secure) {\n    throw new Error(\"__Secure- Cookie must have Secure attributes\");\n  }\n  if (name.startsWith(\"__Host-\")) {\n    if (!opt.secure) {\n      throw new Error(\"__Host- Cookie must have Secure attributes\");\n    }\n    if (opt.path !== \"/\") {\n      throw new Error('__Host- Cookie must have Path attributes with \"/\"');\n    }\n    if (opt.domain) {\n      throw new Error(\"__Host- Cookie must not have Domain attributes\");\n    }\n  }\n  if (opt && typeof opt.maxAge === \"number\" && opt.maxAge >= 0) {\n    if (opt.maxAge > 3456e4) {\n      throw new Error(\n        \"Cookies Max-Age SHOULD NOT be greater than 400 days (34560000 seconds) in duration.\"\n      );\n    }\n    cookie += `; Max-Age=${opt.maxAge | 0}`;\n  }\n  if (opt.domain && opt.prefix !== \"host\") {\n    cookie += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    cookie += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (opt.expires.getTime() - Date.now() > 3456e7) {\n      throw new Error(\n        \"Cookies Expires SHOULD NOT be greater than 400 days (34560000 seconds) in the future.\"\n      );\n    }\n    cookie += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) {\n    cookie += \"; HttpOnly\";\n  }\n  if (opt.secure) {\n    cookie += \"; Secure\";\n  }\n  if (opt.sameSite) {\n    cookie += `; SameSite=${opt.sameSite.charAt(0).toUpperCase() + opt.sameSite.slice(1)}`;\n  }\n  if (opt.priority) {\n    cookie += `; Priority=${opt.priority}`;\n  }\n  if (opt.partitioned) {\n    if (!opt.secure) {\n      throw new Error(\"Partitioned Cookie must have Secure attributes\");\n    }\n    cookie += \"; Partitioned\";\n  }\n  return cookie;\n};\nvar serialize = (name, value, opt) => {\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\nvar serializeSigned = async (name, value, secret, opt = {}) => {\n  const signature = await makeSignature(value, secret);\n  value = `${value}.${signature}`;\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\nexport {\n  parse,\n  parseSigned,\n  serialize,\n  serializeSigned\n};\n", "// src/helper/cookie/index.ts\nimport { parse, parseSigned, serialize, serializeSigned } from \"../../utils/cookie.js\";\nvar getCookie = (c, key, prefix) => {\n  const cookie = c.req.raw.headers.get(\"Cookie\");\n  if (typeof key === \"string\") {\n    if (!cookie) {\n      return void 0;\n    }\n    let finalKey = key;\n    if (prefix === \"secure\") {\n      finalKey = \"__Secure-\" + key;\n    } else if (prefix === \"host\") {\n      finalKey = \"__Host-\" + key;\n    }\n    const obj2 = parse(cookie, finalKey);\n    return obj2[finalKey];\n  }\n  if (!cookie) {\n    return {};\n  }\n  const obj = parse(cookie);\n  return obj;\n};\nvar getSignedCookie = async (c, secret, key, prefix) => {\n  const cookie = c.req.raw.headers.get(\"Cookie\");\n  if (typeof key === \"string\") {\n    if (!cookie) {\n      return void 0;\n    }\n    let finalKey = key;\n    if (prefix === \"secure\") {\n      finalKey = \"__Secure-\" + key;\n    } else if (prefix === \"host\") {\n      finalKey = \"__Host-\" + key;\n    }\n    const obj2 = await parseSigned(cookie, secret, finalKey);\n    return obj2[finalKey];\n  }\n  if (!cookie) {\n    return {};\n  }\n  const obj = await parseSigned(cookie, secret);\n  return obj;\n};\nvar setCookie = (c, name, value, opt) => {\n  let cookie;\n  if (opt?.prefix === \"secure\") {\n    cookie = serialize(\"__Secure-\" + name, value, { path: \"/\", ...opt, secure: true });\n  } else if (opt?.prefix === \"host\") {\n    cookie = serialize(\"__Host-\" + name, value, {\n      ...opt,\n      path: \"/\",\n      secure: true,\n      domain: void 0\n    });\n  } else {\n    cookie = serialize(name, value, { path: \"/\", ...opt });\n  }\n  c.header(\"Set-Cookie\", cookie, { append: true });\n};\nvar setSignedCookie = async (c, name, value, secret, opt) => {\n  let cookie;\n  if (opt?.prefix === \"secure\") {\n    cookie = await serializeSigned(\"__Secure-\" + name, value, secret, {\n      path: \"/\",\n      ...opt,\n      secure: true\n    });\n  } else if (opt?.prefix === \"host\") {\n    cookie = await serializeSigned(\"__Host-\" + name, value, secret, {\n      ...opt,\n      path: \"/\",\n      secure: true,\n      domain: void 0\n    });\n  } else {\n    cookie = await serializeSigned(name, value, secret, { path: \"/\", ...opt });\n  }\n  c.header(\"set-cookie\", cookie, { append: true });\n};\nvar deleteCookie = (c, name, opt) => {\n  const deletedCookie = getCookie(c, name, opt?.prefix);\n  setCookie(c, name, \"\", { ...opt, maxAge: 0 });\n  return deletedCookie;\n};\nexport {\n  deleteCookie,\n  getCookie,\n  getSignedCookie,\n  setCookie,\n  setSignedCookie\n};\n", "// src/http-exception.ts\nvar HTTPException = class extends Error {\n  res;\n  status;\n  constructor(status = 500, options) {\n    super(options?.message, { cause: options?.cause });\n    this.res = options?.res;\n    this.status = status;\n  }\n  getResponse() {\n    if (this.res) {\n      const newResponse = new Response(this.res.body, {\n        status: this.status,\n        headers: this.res.headers\n      });\n      return newResponse;\n    }\n    return new Response(this.message, {\n      status: this.status\n    });\n  }\n};\nexport {\n  HTTPException\n};\n", "// src/utils/buffer.ts\nimport { sha256 } from \"./crypto.js\";\nvar equal = (a, b) => {\n  if (a === b) {\n    return true;\n  }\n  if (a.byteLength !== b.byteLength) {\n    return false;\n  }\n  const va = new DataView(a);\n  const vb = new DataView(b);\n  let i = va.byteLength;\n  while (i--) {\n    if (va.getUint8(i) !== vb.getUint8(i)) {\n      return false;\n    }\n  }\n  return true;\n};\nvar timingSafeEqual = async (a, b, hashFunction) => {\n  if (!hashFunction) {\n    hashFunction = sha256;\n  }\n  const [sa, sb] = await Promise.all([hashFunction(a), hashFunction(b)]);\n  if (!sa || !sb) {\n    return false;\n  }\n  return sa === sb && a === b;\n};\nvar bufferToString = (buffer) => {\n  if (buffer instanceof ArrayBuffer) {\n    const enc = new TextDecoder(\"utf-8\");\n    return enc.decode(buffer);\n  }\n  return buffer;\n};\nvar bufferToFormData = (arrayBuffer, contentType) => {\n  const response = new Response(arrayBuffer, {\n    headers: {\n      \"Content-Type\": contentType\n    }\n  });\n  return response.formData();\n};\nexport {\n  bufferToFormData,\n  bufferToString,\n  equal,\n  timingSafeEqual\n};\n", "// src/validator/validator.ts\nimport { getCookie } from \"../helper/cookie/index.js\";\nimport { HTTPException } from \"../http-exception.js\";\nimport { bufferToFormData } from \"../utils/buffer.js\";\nvar jsonRegex = /^application\\/([a-z-\\.]+\\+)?json(;\\s*[a-zA-Z0-9\\-]+\\=([^;]+))*$/;\nvar multipartRegex = /^multipart\\/form-data(;\\s?boundary=[a-zA-Z0-9'\"()+_,\\-./:=?]+)?$/;\nvar urlencodedRegex = /^application\\/x-www-form-urlencoded(;\\s*[a-zA-Z0-9\\-]+\\=([^;]+))*$/;\nvar validator = (target, validationFunc) => {\n  return async (c, next) => {\n    let value = {};\n    const contentType = c.req.header(\"Content-Type\");\n    switch (target) {\n      case \"json\":\n        if (!contentType || !jsonRegex.test(contentType)) {\n          break;\n        }\n        try {\n          value = await c.req.json();\n        } catch {\n          const message = \"Malformed JSON in request body\";\n          throw new HTTPException(400, { message });\n        }\n        break;\n      case \"form\": {\n        if (!contentType || !(multipartRegex.test(contentType) || urlencodedRegex.test(contentType))) {\n          break;\n        }\n        let formData;\n        if (c.req.bodyCache.formData) {\n          formData = await c.req.bodyCache.formData;\n        } else {\n          try {\n            const arrayBuffer = await c.req.arrayBuffer();\n            formData = await bufferToFormData(arrayBuffer, contentType);\n            c.req.bodyCache.formData = formData;\n          } catch (e) {\n            let message = \"Malformed FormData request.\";\n            message += e instanceof Error ? ` ${e.message}` : ` ${String(e)}`;\n            throw new HTTPException(400, { message });\n          }\n        }\n        const form = {};\n        formData.forEach((value2, key) => {\n          if (key.endsWith(\"[]\")) {\n            ;\n            (form[key] ??= []).push(value2);\n          } else if (Array.isArray(form[key])) {\n            ;\n            form[key].push(value2);\n          } else if (key in form) {\n            form[key] = [form[key], value2];\n          } else {\n            form[key] = value2;\n          }\n        });\n        value = form;\n        break;\n      }\n      case \"query\":\n        value = Object.fromEntries(\n          Object.entries(c.req.queries()).map(([k, v]) => {\n            return v.length === 1 ? [k, v[0]] : [k, v];\n          })\n        );\n        break;\n      case \"param\":\n        value = c.req.param();\n        break;\n      case \"header\":\n        value = c.req.header();\n        break;\n      case \"cookie\":\n        value = getCookie(c);\n        break;\n    }\n    const res = await validationFunc(value, c);\n    if (res instanceof Response) {\n      return res;\n    }\n    c.req.addValidatedData(target, res);\n    await next();\n  };\n};\nexport {\n  validator\n};\n", "// src/index.ts\nimport { validator } from \"hono/validator\";\nvar sValidator = (target, schema, hook) => (\n  // @ts-expect-error not typed well\n  validator(target, async (value, c) => {\n    const result = await schema[\"~standard\"].validate(value);\n    if (hook) {\n      const hookResult = await hook(\n        result.issues ? { data: value, error: result.issues, success: false, target } : { data: value, success: true, target },\n        c\n      );\n      if (hookResult) {\n        if (hookResult instanceof Response) {\n          return hookResult;\n        }\n        if (\"response\" in hookResult) {\n          return hookResult.response;\n        }\n      }\n    }\n    if (result.issues) {\n      return c.json({ data: value, error: result.issues, success: false }, 400);\n    }\n    return result.value;\n  })\n);\nexport {\n  sValidator\n};\n", "var P=Object.defineProperty;var f=(i,n)=>P(i,\"name\",{value:n,configurable:!0});import{sV<PERSON><PERSON><PERSON> as d}from\"@hono/standard-validator\";import{CallToolRequestSchema as $,GetPromptRequestSchema as M,ReadResourceRequestSchema as A,InitializeRequestSchema as I,LATEST_PROTOCOL_VERSION as C,SUPPORTED_PROTOCOL_VERSIONS as N,CompleteRequestSchema as U,ErrorCode as w}from\"@modelcontextprotocol/sdk/types.js\";import{Hono as y}from\"hono\";const q=Symbol(\"muppet\"),h={RESOURCES:\"resources\",TOOLS:\"tools\",PROMPTS:\"prompts\"};async function _(i,n){const m=await L(i,n.symbols);return R({config:n,specs:m,app:i})}f(_,\"muppet\");function R(i){const{config:n,specs:m,app:c}=i,a=new y().use(async(e,t)=>{if(!(h.TOOLS in e.get(\"specs\")))throw new Error(\"No tools available\");await t()}).post(\"/list\",e=>e.json({result:{tools:Object.values(e.get(\"specs\").tools??{}).map(({name:t,description:s,inputSchema:p})=>({name:t,description:s,inputSchema:p}))}})).post(\"/call\",d(\"json\",$),async e=>{const{params:t}=e.req.valid(\"json\"),s=e.get(\"specs\").tools?.[t.name];if(!s)throw new Error(\"Unable to find the path for the tool!\");const o=await(await e.get(\"app\").request(...b({path:s.path,method:s.method,schema:s.schema,args:t.arguments}),v(e))).json();return s.resourceType===\"text\"?e.json({result:{content:[{type:\"text\",text:typeof o==\"string\"?o:JSON.stringify(o)}]}}):Array.isArray(o)?e.json({result:{content:o}}):e.json({result:o})}),r=new y().use(async(e,t)=>{if(!(h.PROMPTS in e.get(\"specs\")))throw new Error(\"No prompts available\");await t()}).post(\"/list\",e=>e.json({result:{prompts:Object.values(e.get(\"specs\").prompts??{}).map(({path:t,...s})=>s)}})).post(\"/get\",d(\"json\",M),async e=>{const{params:t}=e.req.valid(\"json\"),s=e.get(\"specs\").prompts?.[t.name];if(!s)throw new Error(\"Unable to find the path for the prompt!\");const o=await(await e.get(\"app\").request(...b({path:s.path,method:s.method,schema:s.schema,args:t.arguments}),v(e))).json();return Array.isArray(o)?e.json({result:{description:s.description,messages:o}}):e.json({result:o})}),u=new y().use(async(e,t)=>{if(!(h.RESOURCES in e.get(\"specs\")))throw new Error(\"No resources available\");await t()}).post(\"/list\",async e=>{const t=await S(e,s=>{if(s.type!==\"template\")return{name:s.name,description:s.description,mimeType:s.mimeType,uri:s.uri}});return e.json({result:{resources:t}})}).post(\"/templates/list\",async e=>{const t=await S(e,s=>{if(s.type===\"template\")return{name:s.name,description:s.description,mimeType:s.mimeType,uriTemplate:s.uri}});return e.json({result:{resourceTemplates:t}})}).post(\"/read\",d(\"json\",A),async e=>{const{params:t}=e.req.valid(\"json\"),s=t.uri.split(\":\")[0],p=e.get(\"muppet\").resources?.[s];if(!p)throw new Error(`Unable to find the handler for ${s} protocol!`);const o=await p(t.uri);return Array.isArray(o)?e.json({result:{contents:o}}):e.json({result:o})});return new y().use(async(e,t)=>{n.logger?.info({method:e.req.method,path:e.req.path},\"Incoming request\"),e.set(\"muppet\",n),e.set(\"specs\",m),e.set(\"app\",c),await t(),n.logger?.info({status:e.res.status},\"Outgoing response\")}).post(\"/initialize\",d(\"json\",I),async e=>{const{params:t}=e.req.valid(\"json\"),{name:s,version:p}=e.get(\"muppet\"),o=e.get(\"specs\"),l=h.TOOLS in o,j=h.PROMPTS in o,E=h.RESOURCES in o;return e.json({result:{protocolVersion:N.includes(t?.protocolVersion)?t.protocolVersion:C,serverInfo:{name:s,version:p},capabilities:{tools:l?{}:void 0,prompts:j?{}:void 0,resources:E?{}:void 0}}})}).post(\"/notifications/:event\",e=>(e.get(\"muppet\").events?.emit(e,`notifications/${e.req.param(\"event\")}`,void 0),e.body(null,204))).post(\"/ping\",e=>e.json({result:{}})).route(\"/tools\",a).route(\"/prompts\",r).route(\"/resources\",u).post(\"/completion/complete\",d(\"json\",U),async e=>{const{params:t}=e.req.valid(\"json\");let s;if(t.ref.type===\"ref/prompt\"?s=e.get(\"specs\").prompts?.[t.ref.name].completion:t.ref.type===\"ref/resource\"&&(s=await S(e,o=>{if(o.type===\"template\"&&o.uri===t.ref.uri)return o.completion}).then(o=>o[0])),!s)return e.json({result:{completion:{values:[],total:0,hasMore:!1}}});const p=await s(t.argument);return Array.isArray(p)?e.json({result:{completion:{values:p,total:p.length,hasMore:!1}}}):e.json({result:{completion:p}})}).notFound(e=>(e.get(\"muppet\").logger?.info(\"Method not found\"),e.json({error:{code:w.MethodNotFound,message:\"Method not found\"}}))).onError((e,t)=>(t.get(\"muppet\").logger?.error({err:e},\"Internal error\"),t.json({error:{code:Number.isSafeInteger(e.code)?e.code:w.InternalError,message:e.message??\"Internal error\"}})))}f(R,\"createMuppetServer\");async function L(i,n=[]){const m={},c=[...n,q];for(const r of i.routes){const u=c.find(j=>j in r.handler);if(!u)continue;const{validationTarget:e,toJson:t,type:s}=r.handler[u];let p;typeof t==\"function\"?p=await t():p=t??{};const o=m[r.path]?.[r.method];if(o?.type&&s&&o.type!==s)throw new Error(`Conflicting types for ${r.path}: ${o.type} and ${s}`);let l={...o??{},type:s??o?.type};e&&\"schema\"in p?l.schema={...l.schema??{},[e]:p.schema}:l={...l,...p},m[r.path]={...m[r.path]??{},[r.method]:l}}const a={};for(const[r,u]of Object.entries(m))if(u){for(const[e,t]of Object.entries(u))if(t){if(!t.type)throw new Error(`Type not found for ${r}`);if(t.type===h.TOOLS){a.tools||(a.tools={});const s=t.name??g(e,r);a.tools[s]={name:s,description:t.description,resourceType:t.resourceType,inputSchema:O(t.schema)??{},path:r,method:e,schema:t.schema}}else if(t.type===h.PROMPTS){a.prompts||(a.prompts={});const s=t.name??g(e,r),p=[],o=O(t.schema)??{};for(const l of Object.keys(o.properties??{}))p.push({name:l,description:o.properties?.[l]?.description,required:o.required?.includes(l)??!1});a.prompts[s]={name:s,description:t.description,completion:t.completion,arguments:p,path:r,method:e,schema:t.schema}}else t.type===h.RESOURCES&&(a.resources||(a.resources={}),a.resources[g(e,r)]={path:r,method:e})}}return a}f(L,\"generateSpecs\");function O(i){let n;for(const m of Object.values(i??{})){if(!n){n=m;continue}n={...n,properties:{...n.properties,...m.properties},required:[...n.required??[],...m.required??[]]}}return n}f(O,\"mergeSchemas\");async function S(i,n){return(await Promise.all(Object.values(i.get(\"specs\").resources??{}).map(async({path:c,method:a})=>i.get(\"app\").request(c,{method:a,headers:i.req.header()})))).flat(2).reduce((c,a)=>{const r=n(a);return r&&c.push(r),c},[])}f(S,\"findAllTheResources\");function g(i,n){return`${i}:${n}`}f(g,\"generateKey\");function b(i){const{path:n,method:m,schema:c,args:a}=i,r={},u={method:m};for(const[s,{properties:p}]of Object.entries(c??{}))p&&(r[s]=Object.keys(p).reduce((o,l)=>(a?.[l]!==void 0&&(o[l]=a?.[l]),o),{}));Object.values(r.header??{}).length>0&&(u.headers=r.header),Object.values(r.json??{}).length>0&&(u.body=JSON.stringify(r.json),u.headers={...u.headers,\"content-type\":\"application/json\"});const e=T(r.query);return[`${V(n,r.param)}${e.length>0?`?${e}`:\"\"}`,u]}f(b,\"getRequestInit\");function V(i,n){return i.split(\"/\").map(m=>{let c=m;if(c.startsWith(\":\")){const a=c.match(/^:([^{?]+)(?:{(.+)})?(\\?)?$/);if(a){const r=a[1],u=n?.[r];u&&(c=String(u))}else c=c.slice(1,c.length),c.endsWith(\"?\")&&(c=c.slice(0,-1))}return c}).join(\"/\")}f(V,\"placeParamValues\");function T(i,n){const{prefix:m,separator:c=\"__\"}=n??{};return Object.entries(i??{}).reduce((a,[r,u])=>{const e=`${m?`${m}${c}`:\"\"}${r}`;return u&&(Array.isArray(u)?a.push(...u.filter(t=>t!==void 0).map(t=>`${e}=${t}`)):typeof u==\"object\"?a.push(T(u,{prefix:e,separator:c})):a.push(`${e}=${u}`)),a},[]).join(\"&\")}f(T,\"querySerializer\");function v(i){return{...i.env,muppet:{req:i.req}}}f(v,\"createMuppetEnv\");export{h as M,O as a,R as c,g,_ as m,q as u};\n", "var n=Object.defineProperty;var a=(r,o)=>n(r,\"name\",{value:o,configurable:!0});const s=a(async(r,o)=>{const t=r[\"~standard\"].vendor;let e;switch(t){case\"arktype\":e=import(\"./arktype-CqF_-yop.js\");break;case\"effect\":e=import(\"./effect-pZiNZiGm.js\");break;case\"valibot\":e=import(\"./valibot-DOnkF2ES.js\");break;case\"zod\":e=import(\"./zod-DuHkA2Jp.js\");break;default:throw new Error(`standard-json: Unsupported schema vendor \"${t}\"`)}return(await e).toJsonSchema(r,o)},\"toJsonSchema\");export{s as toJsonSchema};\n", "var u=Object.defineProperty;var s=(e,o)=>u(e,\"name\",{value:o,configurable:!0});import{M as c,u as d}from\"./muppet-BdeHS-Ba.js\";import{m as x}from\"./muppet-BdeHS-Ba.js\";import{sValidator as f}from\"@hono/standard-validator\";import{toJsonSchema as g}from\"@standard-community/standard-json\";import{RequestSchema as y}from\"@modelcontextprotocol/sdk/types.js\";import\"hono\";function p(e){return o=>Object.assign(s(async(n,a)=>{await a()},\"middleware\"),{[d]:{toJson:o??{},type:e}})}s(p,\"describeRoute\");const S=p(c.PROMPTS),h=p(c.TOOLS);function w(e){return Object.assign(e,{[d]:{type:c.RESOURCES}})}s(w,\"registerResources\");function O(e,o,t){const n=f(e,o,t);return Object.assign(n,{[d]:{validationTarget:e,toJson:s(async()=>({schema:await g(o)}),\"toJson\")}})}s(O,\"mValidator\");function b(e){const{mcp:o,transport:t,logger:n}=e;let a=0;return t.onmessage=async i=>{const r=await m({mcp:await o,message:i,logger:n});\"method\"in i&&i.method===\"initialize\"&&(a=-1),r&&(a++,await t.send({...r,id:a}).then(()=>n?.info(\"Sent response\")).catch(l=>n?.error(l,\"Failed to send cancellation\")))},t.start()}s(b,\"bridge\");async function m(e){const{mcp:o,message:t,logger:n}=e;n?.info({message:t,string:JSON.stringify(t)},\"Received message\");const a=y.parse(t),i=await o.request(a.method,{method:\"POST\",body:JSON.stringify(t),headers:{\"content-type\":\"application/json\"}});if(i.status===204)return null;const r=await i.json();return r.jsonrpc=\"2.0\",n?.info({payload:r},\"Response payload\"),r}s(m,\"handleMessage\");export{c as McpPrimitives,b as bridge,S as describePrompt,h as describeTool,m as handleMessage,O as mValidator,x as muppet,w as registerResources,d as uniqueSymbol};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAwBA,IAAI,uBAAuB;AAC3B,IAAI,wBAAwB;AAC5B,IAAI,QAAQ,CAAC,QAAQ,SAAS;AAC5B,MAAI,QAAQ,OAAO,QAAQ,IAAI,MAAM,IAAI;AACvC,WAAO,CAAC;AAAA,EACV;AACA,QAAM,QAAQ,OAAO,KAAK,EAAE,MAAM,GAAG;AACrC,QAAM,eAAe,CAAC;AACtB,WAAS,WAAW,OAAO;AACzB,cAAU,QAAQ,KAAK;AACvB,UAAM,gBAAgB,QAAQ,QAAQ,GAAG;AACzC,QAAI,kBAAkB,IAAI;AACxB;AAAA,IACF;AACA,UAAM,aAAa,QAAQ,UAAU,GAAG,aAAa,EAAE,KAAK;AAC5D,QAAI,QAAQ,SAAS,cAAc,CAAC,qBAAqB,KAAK,UAAU,GAAG;AACzE;AAAA,IACF;AACA,QAAI,cAAc,QAAQ,UAAU,gBAAgB,CAAC,EAAE,KAAK;AAC5D,QAAI,YAAY,WAAW,GAAG,KAAK,YAAY,SAAS,GAAG,GAAG;AAC5D,oBAAc,YAAY,MAAM,GAAG,EAAE;AAAA,IACvC;AACA,QAAI,sBAAsB,KAAK,WAAW,GAAG;AAC3C,mBAAa,UAAU,IAAI,oBAAoB,WAAW;AAC1D,UAAI,MAAM;AACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACpDA,IAAI,YAAY,CAAC,GAAG,KAAK,WAAW;AAClC,QAAM,SAAS,EAAE,IAAI,IAAI,QAAQ,IAAI,QAAQ;AAC7C,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,QAAI,WAAW;AACf,QAAI,WAAW,UAAU;AACvB,iBAAW,cAAc;AAAA,IAC3B,WAAW,WAAW,QAAQ;AAC5B,iBAAW,YAAY;AAAA,IACzB;AACA,UAAM,OAAO,MAAM,QAAQ,QAAQ;AACnC,WAAO,KAAK,QAAQ;AAAA,EACtB;AACA,MAAI,CAAC,QAAQ;AACX,WAAO,CAAC;AAAA,EACV;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO;AACT;;;ACrBA,IAAI,gBAAgB,cAAc,MAAM;AAAA,EACtC;AAAA,EACA;AAAA,EACA,YAAY,SAAS,KAAK,SAAS;AACjC,UAAM,SAAS,SAAS,EAAE,OAAO,SAAS,MAAM,CAAC;AACjD,SAAK,MAAM,SAAS;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,KAAK;AACZ,YAAM,cAAc,IAAI,SAAS,KAAK,IAAI,MAAM;AAAA,QAC9C,QAAQ,KAAK;AAAA,QACb,SAAS,KAAK,IAAI;AAAA,MACpB,CAAC;AACD,aAAO;AAAA,IACT;AACA,WAAO,IAAI,SAAS,KAAK,SAAS;AAAA,MAChC,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACF;;;ACeA,IAAI,mBAAmB,CAAC,aAAa,gBAAgB;AACnD,QAAM,WAAW,IAAI,SAAS,aAAa;AAAA,IACzC,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO,SAAS,SAAS;AAC3B;;;ACvCA,IAAI,YAAY;AAChB,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,YAAY,CAAC,QAAQ,mBAAmB;AAC1C,SAAO,OAAO,GAAG,SAAS;AACxB,QAAI,QAAQ,CAAC;AACb,UAAM,cAAc,EAAE,IAAI,OAAO,cAAc;AAC/C,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,YAAI,CAAC,eAAe,CAAC,UAAU,KAAK,WAAW,GAAG;AAChD;AAAA,QACF;AACA,YAAI;AACF,kBAAQ,MAAM,EAAE,IAAI,KAAK;AAAA,QAC3B,QAAQ;AACN,gBAAM,UAAU;AAChB,gBAAM,IAAI,cAAc,KAAK,EAAE,QAAQ,CAAC;AAAA,QAC1C;AACA;AAAA,MACF,KAAK,QAAQ;AACX,YAAI,CAAC,eAAe,EAAE,eAAe,KAAK,WAAW,KAAK,gBAAgB,KAAK,WAAW,IAAI;AAC5F;AAAA,QACF;AACA,YAAI;AACJ,YAAI,EAAE,IAAI,UAAU,UAAU;AAC5B,qBAAW,MAAM,EAAE,IAAI,UAAU;AAAA,QACnC,OAAO;AACL,cAAI;AACF,kBAAM,cAAc,MAAM,EAAE,IAAI,YAAY;AAC5C,uBAAW,MAAM,iBAAiB,aAAa,WAAW;AAC1D,cAAE,IAAI,UAAU,WAAW;AAAA,UAC7B,SAAS,GAAG;AACV,gBAAI,UAAU;AACd,uBAAW,aAAa,QAAQ,IAAI,EAAE,OAAO,KAAK,IAAI,OAAO,CAAC,CAAC;AAC/D,kBAAM,IAAI,cAAc,KAAK,EAAE,QAAQ,CAAC;AAAA,UAC1C;AAAA,QACF;AACA,cAAM,OAAO,CAAC;AACd,iBAAS,QAAQ,CAAC,QAAQ,QAAQ;AAChC,cAAI,IAAI,SAAS,IAAI,GAAG;AACtB;AACA,aAAC,KAAK,GAAG,MAAM,CAAC,GAAG,KAAK,MAAM;AAAA,UAChC,WAAW,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG;AACnC;AACA,iBAAK,GAAG,EAAE,KAAK,MAAM;AAAA,UACvB,WAAW,OAAO,MAAM;AACtB,iBAAK,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,MAAM;AAAA,UAChC,OAAO;AACL,iBAAK,GAAG,IAAI;AAAA,UACd;AAAA,QACF,CAAC;AACD,gBAAQ;AACR;AAAA,MACF;AAAA,MACA,KAAK;AACH,gBAAQ,OAAO;AAAA,UACb,OAAO,QAAQ,EAAE,IAAI,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC,GAAGA,EAAC,MAAM;AAC9C,mBAAOA,GAAE,WAAW,IAAI,CAAC,GAAGA,GAAE,CAAC,CAAC,IAAI,CAAC,GAAGA,EAAC;AAAA,UAC3C,CAAC;AAAA,QACH;AACA;AAAA,MACF,KAAK;AACH,gBAAQ,EAAE,IAAI,MAAM;AACpB;AAAA,MACF,KAAK;AACH,gBAAQ,EAAE,IAAI,OAAO;AACrB;AAAA,MACF,KAAK;AACH,gBAAQ,UAAU,CAAC;AACnB;AAAA,IACJ;AACA,UAAM,MAAM,MAAM,eAAe,OAAO,CAAC;AACzC,QAAI,eAAe,UAAU;AAC3B,aAAO;AAAA,IACT;AACA,MAAE,IAAI,iBAAiB,QAAQ,GAAG;AAClC,UAAM,KAAK;AAAA,EACb;AACF;;;AChFA,IAAI,aAAa,CAAC,QAAQ,QAAQ;AAAA;AAAA,EAEhC,UAAU,QAAQ,OAAO,OAAO,MAAM;AACpC,UAAM,SAAS,MAAM,OAAO,WAAW,EAAE,SAAS,KAAK;AACvD,QAAI,MAAM;AACR,YAAM,aAAa,MAAM;AAAA,QACvB,OAAO,SAAS,EAAE,MAAM,OAAO,OAAO,OAAO,QAAQ,SAAS,OAAO,OAAO,IAAI,EAAE,MAAM,OAAO,SAAS,MAAM,OAAO;AAAA,QACrH;AAAA,MACF;AACA,UAAI,YAAY;AACd,YAAI,sBAAsB,UAAU;AAClC,iBAAO;AAAA,QACT;AACA,YAAI,cAAc,YAAY;AAC5B,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,QAAQ;AACjB,aAAO,EAAE,KAAK,EAAE,MAAM,OAAO,OAAO,OAAO,QAAQ,SAAS,MAAM,GAAG,GAAG;AAAA,IAC1E;AACA,WAAO,OAAO;AAAA,EAChB,CAAC;AAAA;;;ACxBH,IAAI,IAAE,OAAO;AAAe,IAAI,IAAE,CAAC,GAAEC,OAAI,EAAE,GAAE,QAAO,EAAC,OAAMA,IAAE,cAAa,KAAE,CAAC;AAA+V,IAAM,IAAE,OAAO,QAAQ;AAAvB,IAAyB,IAAE,EAAC,WAAU,aAAY,OAAM,SAAQ,SAAQ,UAAS;AAAE,eAAe,EAAE,GAAEA,IAAE;AAAC,QAAMC,KAAE,MAAM,EAAE,GAAED,GAAE,OAAO;AAAE,SAAO,EAAE,EAAC,QAAOA,IAAE,OAAMC,IAAE,KAAI,EAAC,CAAC;AAAC;AAAC,EAAE,GAAE,QAAQ;AAAE,SAAS,EAAE,GAAE;AAAC,QAAK,EAAC,QAAOD,IAAE,OAAMC,IAAE,KAAI,EAAC,IAAE,GAAEC,KAAE,IAAI,KAAE,EAAE,IAAI,OAAM,GAAE,MAAI;AAAC,QAAG,EAAE,EAAE,SAAS,EAAE,IAAI,OAAO,GAAG,OAAM,IAAI,MAAM,oBAAoB;AAAE,UAAM,EAAE;AAAA,EAAC,CAAC,EAAE,KAAK,SAAQ,OAAG,EAAE,KAAK,EAAC,QAAO,EAAC,OAAM,OAAO,OAAO,EAAE,IAAI,OAAO,EAAE,SAAO,CAAC,CAAC,EAAE,IAAI,CAAC,EAAC,MAAK,GAAE,aAAYC,IAAE,aAAYC,GAAC,OAAK,EAAC,MAAK,GAAE,aAAYD,IAAE,aAAYC,GAAC,EAAE,EAAC,EAAC,CAAC,CAAC,EAAE,KAAK,SAAQ,WAAE,QAAO,qBAAC,GAAE,OAAM,MAAG;AAAC,UAAK,EAAC,QAAO,EAAC,IAAE,EAAE,IAAI,MAAM,MAAM,GAAED,KAAE,EAAE,IAAI,OAAO,EAAE,QAAQ,EAAE,IAAI;AAAE,QAAG,CAACA,GAAE,OAAM,IAAI,MAAM,uCAAuC;AAAE,UAAM,IAAE,OAAM,MAAM,EAAE,IAAI,KAAK,EAAE,QAAQ,GAAG,EAAE,EAAC,MAAKA,GAAE,MAAK,QAAOA,GAAE,QAAO,QAAOA,GAAE,QAAO,MAAK,EAAE,UAAS,CAAC,GAAE,EAAE,CAAC,CAAC,GAAG,KAAK;AAAE,WAAOA,GAAE,iBAAe,SAAO,EAAE,KAAK,EAAC,QAAO,EAAC,SAAQ,CAAC,EAAC,MAAK,QAAO,MAAK,OAAO,KAAG,WAAS,IAAE,KAAK,UAAU,CAAC,EAAC,CAAC,EAAC,EAAC,CAAC,IAAE,MAAM,QAAQ,CAAC,IAAE,EAAE,KAAK,EAAC,QAAO,EAAC,SAAQ,EAAC,EAAC,CAAC,IAAE,EAAE,KAAK,EAAC,QAAO,EAAC,CAAC;AAAA,EAAC,CAAC,GAAE,IAAE,IAAI,KAAE,EAAE,IAAI,OAAM,GAAE,MAAI;AAAC,QAAG,EAAE,EAAE,WAAW,EAAE,IAAI,OAAO,GAAG,OAAM,IAAI,MAAM,sBAAsB;AAAE,UAAM,EAAE;AAAA,EAAC,CAAC,EAAE,KAAK,SAAQ,OAAG,EAAE,KAAK,EAAC,QAAO,EAAC,SAAQ,OAAO,OAAO,EAAE,IAAI,OAAO,EAAE,WAAS,CAAC,CAAC,EAAE,IAAI,CAAC,EAAC,MAAK,GAAE,GAAGA,GAAC,MAAIA,EAAC,EAAC,EAAC,CAAC,CAAC,EAAE,KAAK,QAAO,WAAE,QAAO,sBAAC,GAAE,OAAM,MAAG;AAAC,UAAK,EAAC,QAAO,EAAC,IAAE,EAAE,IAAI,MAAM,MAAM,GAAEA,KAAE,EAAE,IAAI,OAAO,EAAE,UAAU,EAAE,IAAI;AAAE,QAAG,CAACA,GAAE,OAAM,IAAI,MAAM,yCAAyC;AAAE,UAAM,IAAE,OAAM,MAAM,EAAE,IAAI,KAAK,EAAE,QAAQ,GAAG,EAAE,EAAC,MAAKA,GAAE,MAAK,QAAOA,GAAE,QAAO,QAAOA,GAAE,QAAO,MAAK,EAAE,UAAS,CAAC,GAAE,EAAE,CAAC,CAAC,GAAG,KAAK;AAAE,WAAO,MAAM,QAAQ,CAAC,IAAE,EAAE,KAAK,EAAC,QAAO,EAAC,aAAYA,GAAE,aAAY,UAAS,EAAC,EAAC,CAAC,IAAE,EAAE,KAAK,EAAC,QAAO,EAAC,CAAC;AAAA,EAAC,CAAC,GAAEE,KAAE,IAAI,KAAE,EAAE,IAAI,OAAM,GAAE,MAAI;AAAC,QAAG,EAAE,EAAE,aAAa,EAAE,IAAI,OAAO,GAAG,OAAM,IAAI,MAAM,wBAAwB;AAAE,UAAM,EAAE;AAAA,EAAC,CAAC,EAAE,KAAK,SAAQ,OAAM,MAAG;AAAC,UAAM,IAAE,MAAM,EAAE,GAAE,CAAAF,OAAG;AAAC,UAAGA,GAAE,SAAO,WAAW,QAAM,EAAC,MAAKA,GAAE,MAAK,aAAYA,GAAE,aAAY,UAASA,GAAE,UAAS,KAAIA,GAAE,IAAG;AAAA,IAAC,CAAC;AAAE,WAAO,EAAE,KAAK,EAAC,QAAO,EAAC,WAAU,EAAC,EAAC,CAAC;AAAA,EAAC,CAAC,EAAE,KAAK,mBAAkB,OAAM,MAAG;AAAC,UAAM,IAAE,MAAM,EAAE,GAAE,CAAAA,OAAG;AAAC,UAAGA,GAAE,SAAO,WAAW,QAAM,EAAC,MAAKA,GAAE,MAAK,aAAYA,GAAE,aAAY,UAASA,GAAE,UAAS,aAAYA,GAAE,IAAG;AAAA,IAAC,CAAC;AAAE,WAAO,EAAE,KAAK,EAAC,QAAO,EAAC,mBAAkB,EAAC,EAAC,CAAC;AAAA,EAAC,CAAC,EAAE,KAAK,SAAQ,WAAE,QAAO,yBAAC,GAAE,OAAM,MAAG;AAAC,UAAK,EAAC,QAAO,EAAC,IAAE,EAAE,IAAI,MAAM,MAAM,GAAEA,KAAE,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC,GAAEC,KAAE,EAAE,IAAI,QAAQ,EAAE,YAAYD,EAAC;AAAE,QAAG,CAACC,GAAE,OAAM,IAAI,MAAM,kCAAkCD,EAAC,YAAY;AAAE,UAAM,IAAE,MAAMC,GAAE,EAAE,GAAG;AAAE,WAAO,MAAM,QAAQ,CAAC,IAAE,EAAE,KAAK,EAAC,QAAO,EAAC,UAAS,EAAC,EAAC,CAAC,IAAE,EAAE,KAAK,EAAC,QAAO,EAAC,CAAC;AAAA,EAAC,CAAC;AAAE,SAAO,IAAI,KAAE,EAAE,IAAI,OAAM,GAAE,MAAI;AAAC,IAAAJ,GAAE,QAAQ,KAAK,EAAC,QAAO,EAAE,IAAI,QAAO,MAAK,EAAE,IAAI,KAAI,GAAE,kBAAkB,GAAE,EAAE,IAAI,UAASA,EAAC,GAAE,EAAE,IAAI,SAAQC,EAAC,GAAE,EAAE,IAAI,OAAM,CAAC,GAAE,MAAM,EAAE,GAAED,GAAE,QAAQ,KAAK,EAAC,QAAO,EAAE,IAAI,OAAM,GAAE,mBAAmB;AAAA,EAAC,CAAC,EAAE,KAAK,eAAc,WAAE,QAAO,uBAAC,GAAE,OAAM,MAAG;AAAC,UAAK,EAAC,QAAO,EAAC,IAAE,EAAE,IAAI,MAAM,MAAM,GAAE,EAAC,MAAKG,IAAE,SAAQC,GAAC,IAAE,EAAE,IAAI,QAAQ,GAAE,IAAE,EAAE,IAAI,OAAO,GAAE,IAAE,EAAE,SAAS,GAAE,IAAE,EAAE,WAAW,GAAE,IAAE,EAAE,aAAa;AAAE,WAAO,EAAE,KAAK,EAAC,QAAO,EAAC,iBAAgB,4BAAE,SAAS,GAAG,eAAe,IAAE,EAAE,kBAAgB,yBAAE,YAAW,EAAC,MAAKD,IAAE,SAAQC,GAAC,GAAE,cAAa,EAAC,OAAM,IAAE,CAAC,IAAE,QAAO,SAAQ,IAAE,CAAC,IAAE,QAAO,WAAU,IAAE,CAAC,IAAE,OAAM,EAAC,EAAC,CAAC;AAAA,EAAC,CAAC,EAAE,KAAK,yBAAwB,QAAI,EAAE,IAAI,QAAQ,EAAE,QAAQ,KAAK,GAAE,iBAAiB,EAAE,IAAI,MAAM,OAAO,CAAC,IAAG,MAAM,GAAE,EAAE,KAAK,MAAK,GAAG,EAAE,EAAE,KAAK,SAAQ,OAAG,EAAE,KAAK,EAAC,QAAO,CAAC,EAAC,CAAC,CAAC,EAAE,MAAM,UAASF,EAAC,EAAE,MAAM,YAAW,CAAC,EAAE,MAAM,cAAaG,EAAC,EAAE,KAAK,wBAAuB,WAAE,QAAO,qBAAC,GAAE,OAAM,MAAG;AAAC,UAAK,EAAC,QAAO,EAAC,IAAE,EAAE,IAAI,MAAM,MAAM;AAAE,QAAIF;AAAE,QAAG,EAAE,IAAI,SAAO,eAAaA,KAAE,EAAE,IAAI,OAAO,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,aAAW,EAAE,IAAI,SAAO,mBAAiBA,KAAE,MAAM,EAAE,GAAE,OAAG;AAAC,UAAG,EAAE,SAAO,cAAY,EAAE,QAAM,EAAE,IAAI,IAAI,QAAO,EAAE;AAAA,IAAU,CAAC,EAAE,KAAK,OAAG,EAAE,CAAC,CAAC,IAAG,CAACA,GAAE,QAAO,EAAE,KAAK,EAAC,QAAO,EAAC,YAAW,EAAC,QAAO,CAAC,GAAE,OAAM,GAAE,SAAQ,MAAE,EAAC,EAAC,CAAC;AAAE,UAAMC,KAAE,MAAMD,GAAE,EAAE,QAAQ;AAAE,WAAO,MAAM,QAAQC,EAAC,IAAE,EAAE,KAAK,EAAC,QAAO,EAAC,YAAW,EAAC,QAAOA,IAAE,OAAMA,GAAE,QAAO,SAAQ,MAAE,EAAC,EAAC,CAAC,IAAE,EAAE,KAAK,EAAC,QAAO,EAAC,YAAWA,GAAC,EAAC,CAAC;AAAA,EAAC,CAAC,EAAE,SAAS,QAAI,EAAE,IAAI,QAAQ,EAAE,QAAQ,KAAK,kBAAkB,GAAE,EAAE,KAAK,EAAC,OAAM,EAAC,MAAK,UAAE,gBAAe,SAAQ,mBAAkB,EAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAE,OAAK,EAAE,IAAI,QAAQ,EAAE,QAAQ,MAAM,EAAC,KAAI,EAAC,GAAE,gBAAgB,GAAE,EAAE,KAAK,EAAC,OAAM,EAAC,MAAK,OAAO,cAAc,EAAE,IAAI,IAAE,EAAE,OAAK,UAAE,eAAc,SAAQ,EAAE,WAAS,iBAAgB,EAAC,CAAC,EAAE;AAAC;AAAC,EAAE,GAAE,oBAAoB;AAAE,eAAe,EAAE,GAAEJ,KAAE,CAAC,GAAE;AAAC,QAAMC,KAAE,CAAC,GAAE,IAAE,CAAC,GAAGD,IAAE,CAAC;AAAE,aAAU,KAAK,EAAE,QAAO;AAAC,UAAMK,KAAE,EAAE,KAAK,OAAG,KAAK,EAAE,OAAO;AAAE,QAAG,CAACA,GAAE;AAAS,UAAK,EAAC,kBAAiB,GAAE,QAAO,GAAE,MAAKF,GAAC,IAAE,EAAE,QAAQE,EAAC;AAAE,QAAID;AAAE,WAAO,KAAG,aAAWA,KAAE,MAAM,EAAE,IAAEA,KAAE,KAAG,CAAC;AAAE,UAAM,IAAEH,GAAE,EAAE,IAAI,IAAI,EAAE,MAAM;AAAE,QAAG,GAAG,QAAME,MAAG,EAAE,SAAOA,GAAE,OAAM,IAAI,MAAM,yBAAyB,EAAE,IAAI,KAAK,EAAE,IAAI,QAAQA,EAAC,EAAE;AAAE,QAAI,IAAE,EAAC,GAAG,KAAG,CAAC,GAAE,MAAKA,MAAG,GAAG,KAAI;AAAE,SAAG,YAAWC,KAAE,EAAE,SAAO,EAAC,GAAG,EAAE,UAAQ,CAAC,GAAE,CAAC,CAAC,GAAEA,GAAE,OAAM,IAAE,IAAE,EAAC,GAAG,GAAE,GAAGA,GAAC,GAAEH,GAAE,EAAE,IAAI,IAAE,EAAC,GAAGA,GAAE,EAAE,IAAI,KAAG,CAAC,GAAE,CAAC,EAAE,MAAM,GAAE,EAAC;AAAA,EAAC;AAAC,QAAMC,KAAE,CAAC;AAAE,aAAS,CAAC,GAAEG,EAAC,KAAI,OAAO,QAAQJ,EAAC,EAAE,KAAGI,IAAE;AAAC,eAAS,CAAC,GAAE,CAAC,KAAI,OAAO,QAAQA,EAAC,EAAE,KAAG,GAAE;AAAC,UAAG,CAAC,EAAE,KAAK,OAAM,IAAI,MAAM,sBAAsB,CAAC,EAAE;AAAE,UAAG,EAAE,SAAO,EAAE,OAAM;AAAC,QAAAH,GAAE,UAAQA,GAAE,QAAM,CAAC;AAAG,cAAMC,KAAE,EAAE,QAAM,EAAE,GAAE,CAAC;AAAE,QAAAD,GAAE,MAAMC,EAAC,IAAE,EAAC,MAAKA,IAAE,aAAY,EAAE,aAAY,cAAa,EAAE,cAAa,aAAY,EAAE,EAAE,MAAM,KAAG,CAAC,GAAE,MAAK,GAAE,QAAO,GAAE,QAAO,EAAE,OAAM;AAAA,MAAC,WAAS,EAAE,SAAO,EAAE,SAAQ;AAAC,QAAAD,GAAE,YAAUA,GAAE,UAAQ,CAAC;AAAG,cAAMC,KAAE,EAAE,QAAM,EAAE,GAAE,CAAC,GAAEC,KAAE,CAAC,GAAE,IAAE,EAAE,EAAE,MAAM,KAAG,CAAC;AAAE,mBAAU,KAAK,OAAO,KAAK,EAAE,cAAY,CAAC,CAAC,EAAE,CAAAA,GAAE,KAAK,EAAC,MAAK,GAAE,aAAY,EAAE,aAAa,CAAC,GAAG,aAAY,UAAS,EAAE,UAAU,SAAS,CAAC,KAAG,MAAE,CAAC;AAAE,QAAAF,GAAE,QAAQC,EAAC,IAAE,EAAC,MAAKA,IAAE,aAAY,EAAE,aAAY,YAAW,EAAE,YAAW,WAAUC,IAAE,MAAK,GAAE,QAAO,GAAE,QAAO,EAAE,OAAM;AAAA,MAAC,MAAM,GAAE,SAAO,EAAE,cAAYF,GAAE,cAAYA,GAAE,YAAU,CAAC,IAAGA,GAAE,UAAU,EAAE,GAAE,CAAC,CAAC,IAAE,EAAC,MAAK,GAAE,QAAO,EAAC;AAAA,IAAE;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,EAAE,GAAE,eAAe;AAAE,SAAS,EAAE,GAAE;AAAC,MAAIF;AAAE,aAAUC,MAAK,OAAO,OAAO,KAAG,CAAC,CAAC,GAAE;AAAC,QAAG,CAACD,IAAE;AAAC,MAAAA,KAAEC;AAAE;AAAA,IAAQ;AAAC,IAAAD,KAAE,EAAC,GAAGA,IAAE,YAAW,EAAC,GAAGA,GAAE,YAAW,GAAGC,GAAE,WAAU,GAAE,UAAS,CAAC,GAAGD,GAAE,YAAU,CAAC,GAAE,GAAGC,GAAE,YAAU,CAAC,CAAC,EAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,EAAE,GAAE,cAAc;AAAE,eAAe,EAAE,GAAEA,IAAE;AAAC,UAAO,MAAM,QAAQ,IAAI,OAAO,OAAO,EAAE,IAAI,OAAO,EAAE,aAAW,CAAC,CAAC,EAAE,IAAI,OAAM,EAAC,MAAK,GAAE,QAAOE,GAAC,MAAI,EAAE,IAAI,KAAK,EAAE,QAAQ,GAAE,EAAC,QAAOA,IAAE,SAAQ,EAAE,IAAI,OAAO,EAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,GAAEA,OAAI;AAAC,UAAM,IAAEF,GAAEE,EAAC;AAAE,WAAO,KAAG,EAAE,KAAK,CAAC,GAAE;AAAA,EAAC,GAAE,CAAC,CAAC;AAAC;AAAC,EAAE,GAAE,qBAAqB;AAAE,SAAS,EAAE,GAAEF,IAAE;AAAC,SAAM,GAAG,CAAC,IAAIA,EAAC;AAAE;AAAC,EAAE,GAAE,aAAa;AAAE,SAAS,EAAE,GAAE;AAAC,QAAK,EAAC,MAAKA,IAAE,QAAOC,IAAE,QAAO,GAAE,MAAKC,GAAC,IAAE,GAAE,IAAE,CAAC,GAAEG,KAAE,EAAC,QAAOJ,GAAC;AAAE,aAAS,CAACE,IAAE,EAAC,YAAWC,GAAC,CAAC,KAAI,OAAO,QAAQ,KAAG,CAAC,CAAC,EAAE,CAAAA,OAAI,EAAED,EAAC,IAAE,OAAO,KAAKC,EAAC,EAAE,OAAO,CAAC,GAAE,OAAKF,KAAI,CAAC,MAAI,WAAS,EAAE,CAAC,IAAEA,KAAI,CAAC,IAAG,IAAG,CAAC,CAAC;AAAG,SAAO,OAAO,EAAE,UAAQ,CAAC,CAAC,EAAE,SAAO,MAAIG,GAAE,UAAQ,EAAE,SAAQ,OAAO,OAAO,EAAE,QAAM,CAAC,CAAC,EAAE,SAAO,MAAIA,GAAE,OAAK,KAAK,UAAU,EAAE,IAAI,GAAEA,GAAE,UAAQ,EAAC,GAAGA,GAAE,SAAQ,gBAAe,mBAAkB;AAAG,QAAM,IAAE,EAAE,EAAE,KAAK;AAAE,SAAM,CAAC,GAAG,EAAEL,IAAE,EAAE,KAAK,CAAC,GAAG,EAAE,SAAO,IAAE,IAAI,CAAC,KAAG,EAAE,IAAGK,EAAC;AAAC;AAAC,EAAE,GAAE,gBAAgB;AAAE,SAAS,EAAE,GAAEL,IAAE;AAAC,SAAO,EAAE,MAAM,GAAG,EAAE,IAAI,CAAAC,OAAG;AAAC,QAAI,IAAEA;AAAE,QAAG,EAAE,WAAW,GAAG,GAAE;AAAC,YAAMC,KAAE,EAAE,MAAM,6BAA6B;AAAE,UAAGA,IAAE;AAAC,cAAM,IAAEA,GAAE,CAAC,GAAEG,KAAEL,KAAI,CAAC;AAAE,QAAAK,OAAI,IAAE,OAAOA,EAAC;AAAA,MAAE,MAAM,KAAE,EAAE,MAAM,GAAE,EAAE,MAAM,GAAE,EAAE,SAAS,GAAG,MAAI,IAAE,EAAE,MAAM,GAAE,EAAE;AAAA,IAAE;AAAC,WAAO;AAAA,EAAC,CAAC,EAAE,KAAK,GAAG;AAAC;AAAC,EAAE,GAAE,kBAAkB;AAAE,SAAS,EAAE,GAAEL,IAAE;AAAC,QAAK,EAAC,QAAOC,IAAE,WAAU,IAAE,KAAI,IAAED,MAAG,CAAC;AAAE,SAAO,OAAO,QAAQ,KAAG,CAAC,CAAC,EAAE,OAAO,CAACE,IAAE,CAAC,GAAEG,EAAC,MAAI;AAAC,UAAM,IAAE,GAAGJ,KAAE,GAAGA,EAAC,GAAG,CAAC,KAAG,EAAE,GAAG,CAAC;AAAG,WAAOI,OAAI,MAAM,QAAQA,EAAC,IAAEH,GAAE,KAAK,GAAGG,GAAE,OAAO,OAAG,MAAI,MAAM,EAAE,IAAI,OAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAE,OAAOA,MAAG,WAASH,GAAE,KAAK,EAAEG,IAAE,EAAC,QAAO,GAAE,WAAU,EAAC,CAAC,CAAC,IAAEH,GAAE,KAAK,GAAG,CAAC,IAAIG,EAAC,EAAE,IAAGH;AAAA,EAAC,GAAE,CAAC,CAAC,EAAE,KAAK,GAAG;AAAC;AAAC,EAAE,GAAE,iBAAiB;AAAE,SAAS,EAAE,GAAE;AAAC,SAAM,EAAC,GAAG,EAAE,KAAI,QAAO,EAAC,KAAI,EAAE,IAAG,EAAC;AAAC;AAAC,EAAE,GAAE,iBAAiB;;;ACAh4O,IAAI,IAAE,OAAO;AAAe,IAAI,IAAE,CAAC,GAAE,MAAI,EAAE,GAAE,QAAO,EAAC,OAAM,GAAE,cAAa,KAAE,CAAC;AAAE,IAAM,IAAE,EAAE,OAAM,GAAE,MAAI;AAAC,QAAM,IAAE,EAAE,WAAW,EAAE;AAAO,MAAI;AAAE,UAAO,GAAE;AAAA,IAAC,KAAI;AAAU,UAAE,OAAO,gCAAuB;AAAE;AAAA,IAAM,KAAI;AAAS,UAAE,OAAO,+BAAsB;AAAE;AAAA,IAAM,KAAI;AAAU,UAAE,OAAO,gCAAuB;AAAE;AAAA,IAAM,KAAI;AAAM,UAAE,OAAO,4BAAmB;AAAE;AAAA,IAAM;AAAQ,YAAM,IAAI,MAAM,6CAA6C,CAAC,GAAG;AAAA,EAAC;AAAC,UAAO,MAAM,GAAG,aAAa,GAAE,CAAC;AAAC,GAAE,cAAc;;;ACA9d,IAAI,IAAE,OAAO;AAAe,IAAII,KAAE,CAAC,GAAE,MAAI,EAAE,GAAE,QAAO,EAAC,OAAM,GAAE,cAAa,KAAE,CAAC;AAAkS,SAAS,EAAE,GAAE;AAAC,SAAO,OAAG,OAAO,OAAOA,GAAE,OAAMC,IAAEC,OAAI;AAAC,UAAMA,GAAE;AAAA,EAAC,GAAE,YAAY,GAAE,EAAC,CAAC,CAAC,GAAE,EAAC,QAAO,KAAG,CAAC,GAAE,MAAK,EAAC,EAAC,CAAC;AAAC;AAACF,GAAE,GAAE,eAAe;AAAE,IAAMG,KAAE,EAAE,EAAE,OAAO;AAAnB,IAAqBC,KAAE,EAAE,EAAE,KAAK;AAAE,SAAS,EAAE,GAAE;AAAC,SAAO,OAAO,OAAO,GAAE,EAAC,CAAC,CAAC,GAAE,EAAC,MAAK,EAAE,UAAS,EAAC,CAAC;AAAC;AAACJ,GAAE,GAAE,mBAAmB;AAAE,SAASK,GAAE,GAAE,GAAE,GAAE;AAAC,QAAMJ,KAAE,WAAE,GAAE,GAAE,CAAC;AAAE,SAAO,OAAO,OAAOA,IAAE,EAAC,CAAC,CAAC,GAAE,EAAC,kBAAiB,GAAE,QAAOD,GAAE,aAAU,EAAC,QAAO,MAAM,EAAE,CAAC,EAAC,IAAG,QAAQ,EAAC,EAAC,CAAC;AAAC;AAACA,GAAEK,IAAE,YAAY;AAAE,SAASC,GAAE,GAAE;AAAC,QAAK,EAAC,KAAI,GAAE,WAAU,GAAE,QAAOL,GAAC,IAAE;AAAE,MAAIC,KAAE;AAAE,SAAO,EAAE,YAAU,OAAM,MAAG;AAAC,UAAM,IAAE,MAAM,EAAE,EAAC,KAAI,MAAM,GAAE,SAAQ,GAAE,QAAOD,GAAC,CAAC;AAAE,gBAAW,KAAG,EAAE,WAAS,iBAAeC,KAAE,KAAI,MAAIA,MAAI,MAAM,EAAE,KAAK,EAAC,GAAG,GAAE,IAAGA,GAAC,CAAC,EAAE,KAAK,MAAID,IAAG,KAAK,eAAe,CAAC,EAAE,MAAM,OAAGA,IAAG,MAAM,GAAE,6BAA6B,CAAC;AAAA,EAAE,GAAE,EAAE,MAAM;AAAC;AAACD,GAAEM,IAAE,QAAQ;AAAE,eAAe,EAAE,GAAE;AAAC,QAAK,EAAC,KAAI,GAAE,SAAQ,GAAE,QAAOL,GAAC,IAAE;AAAE,EAAAA,IAAG,KAAK,EAAC,SAAQ,GAAE,QAAO,KAAK,UAAU,CAAC,EAAC,GAAE,kBAAkB;AAAE,QAAMC,KAAE,cAAE,MAAM,CAAC,GAAE,IAAE,MAAM,EAAE,QAAQA,GAAE,QAAO,EAAC,QAAO,QAAO,MAAK,KAAK,UAAU,CAAC,GAAE,SAAQ,EAAC,gBAAe,mBAAkB,EAAC,CAAC;AAAE,MAAG,EAAE,WAAS,IAAI,QAAO;AAAK,QAAM,IAAE,MAAM,EAAE,KAAK;AAAE,SAAO,EAAE,UAAQ,OAAMD,IAAG,KAAK,EAAC,SAAQ,EAAC,GAAE,kBAAkB,GAAE;AAAC;AAACD,GAAE,GAAE,eAAe;", "names": ["v", "n", "m", "a", "s", "p", "u", "s", "n", "a", "S", "h", "O", "b"]}