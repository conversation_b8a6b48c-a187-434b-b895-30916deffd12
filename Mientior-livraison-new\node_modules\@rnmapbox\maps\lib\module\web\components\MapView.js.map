{"version": 3, "names": ["React", "mapboxgl", "MapContext", "jsx", "_jsx", "MapView", "Component", "state", "map", "mapContainer", "componentDidMount", "styleURL", "props", "console", "error", "Map", "container", "style", "setState", "render", "children", "width", "height", "ref", "el", "position", "Provider", "value"], "sourceRoot": "../../../../src", "sources": ["web/components/MapView.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAoB,OAAO;AACvC,OAAOC,QAAQ,MAAM,WAAW;AAEhC,OAAOC,UAAU,MAAM,eAAe;;AAEtC;AACA;AACA;AAFA,SAAAC,GAAA,IAAAC,IAAA;AAGA,MAAMC,OAAO,SAASL,KAAK,CAACM,SAAS,CAGnC;EACAC,KAAK,GAAG;IAAEC,GAAG,EAAE;EAAK,CAAC;EACrBC,YAAY,GAAuB,IAAI;EACvCD,GAAG,GAAkB,IAAI;EAEzBE,iBAAiBA,CAAA,EAAG;IAClB,MAAM;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACC,KAAK;IAC/B,IAAI,CAAC,IAAI,CAACH,YAAY,EAAE;MACtBI,OAAO,CAACC,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;IACA,MAAMN,GAAG,GAAG,IAAIP,QAAQ,CAACc,GAAG,CAAC;MAC3BC,SAAS,EAAE,IAAI,CAACP,YAAY;MAC5BQ,KAAK,EAAEN,QAAQ,IAAI;IACrB,CAAC,CAAC;IACF,IAAI,CAACH,GAAG,GAAGA,GAAG;IACd,IAAI,CAACU,QAAQ,CAAC;MAAEV;IAAI,CAAC,CAAC;EACxB;EAEAW,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACR,KAAK;IAC/B,MAAM;MAAEJ;IAAI,CAAC,GAAG,IAAI,CAACD,KAAK;IAC1B,oBACEH,IAAA;MACEa,KAAK,EAAE;QAAEI,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO,CAAE;MACzCC,GAAG,EAAGC,EAAE,IAAK;QACX,IAAI,CAACf,YAAY,GAAGe,EAAE;MACxB,CAAE;MAAAJ,QAAA,EAEDZ,GAAG,iBACFJ,IAAA;QAAKa,KAAK,EAAE;UAAEQ,QAAQ,EAAE;QAAW,CAAE;QAAAL,QAAA,eACnChB,IAAA,CAACF,UAAU,CAACwB,QAAQ;UAACC,KAAK,EAAE;YAAEnB;UAAI,CAAE;UAAAY,QAAA,EACjCA;QAAQ,CACU;MAAC,CACnB;IACN,CACE,CAAC;EAEV;AACF;AAEA,eAAef,OAAO", "ignoreList": []}