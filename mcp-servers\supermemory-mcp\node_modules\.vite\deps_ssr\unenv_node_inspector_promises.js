import {
  noop_default
} from "./chunk-DQN6H3OM.js";
import {
  notImplemented,
  notImplementedClass
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/inspector/promises.mjs
var console = {
  debug: noop_default,
  error: noop_default,
  info: noop_default,
  log: noop_default,
  warn: noop_default,
  dir: noop_default,
  dirxml: noop_default,
  table: noop_default,
  trace: noop_default,
  group: noop_default,
  groupCollapsed: noop_default,
  groupEnd: noop_default,
  clear: noop_default,
  count: noop_default,
  countReset: noop_default,
  assert: noop_default,
  profile: noop_default,
  profileEnd: noop_default,
  time: noop_default,
  timeLog: noop_default,
  timeStamp: noop_default
};
var Network = notImplementedClass("inspectorPromises.Network");
var Session = notImplementedClass("inspectorPromises.Session");
var url = notImplemented("inspectorPromises.url");
var waitForDebugger = notImplemented("inspectorPromises.waitForDebugger");
var open = notImplemented("inspectorPromises.open");
var close = notImplemented("inspectorPromises.close");
var promises_default = {
  close,
  console,
  Network,
  open,
  Session,
  url,
  waitForDebugger
};
export {
  Network,
  Session,
  close,
  console,
  promises_default as default,
  open,
  url,
  waitForDebugger
};
//# sourceMappingURL=unenv_node_inspector_promises.js.map
