import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Animated,
  TextInput,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface LocationData {
  latitude: number;
  longitude: number;
}

interface LocationScreenPerfectProps {
  navigation: any;
}

const LocationScreenPerfect: React.FC<LocationScreenPerfectProps> = ({ navigation }) => {
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>({
    latitude: 6.3702,
    longitude: 2.3912,
  });
  const [address, setAddress] = useState('QPPH+6J5, Cotonou, Bénin');
  const [loading, setLoading] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleMapPress = () => {
    // Simulation de sélection de position
    const newLat = 6.3702 + (Math.random() - 0.5) * 0.01;
    const newLng = 2.3912 + (Math.random() - 0.5) * 0.01;
    
    setSelectedLocation({ latitude: newLat, longitude: newLng });
    setAddress(`Lat: ${newLat.toFixed(4)}, Long: ${newLng.toFixed(4)}`);
  };

  const handleConfirmLocation = () => {
    if (selectedLocation) {
      Alert.alert(
        'Position confirmée',
        `Latitude: ${selectedLocation.latitude.toFixed(4)}\nLongitude: ${selectedLocation.longitude.toFixed(4)}`,
        [
          {
            text: 'Retour',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    }
  };

  const handleCurrentLocation = async () => {
    setLoading(true);
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission refusée', 'Accès à la localisation requis');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      setSelectedLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });
      setAddress(`Position actuelle: ${location.coords.latitude.toFixed(4)}, ${location.coords.longitude.toFixed(4)}`);
    } catch (error) {
      Alert.alert('Erreur', 'Impossible d\'obtenir la position');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Votre localisation</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color="#9CA3AF" style={styles.searchIcon} />
          <TextInput
            style={styles.searchPlaceholder}
            placeholder="Rechercher une adresse"
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      {/* Map Container - Interface Exacte */}
      <Animated.View style={[styles.mapContainer, { opacity: fadeAnim }]}>
        <TouchableOpacity style={styles.mapArea} onPress={handleMapPress}>
          <View style={styles.mapBackground}>
            {/* Simulation de carte avec grille */}
            <View style={styles.mapGrid}>
              {Array.from({ length: 8 }).map((_, i) => (
                <View key={`h-${i}`} style={[styles.gridLine, { top: (i + 1) * 40 }]} />
              ))}
              {Array.from({ length: 6 }).map((_, i) => (
                <View key={`v-${i}`} style={[styles.gridLineVertical, { left: (i + 1) * 60 }]} />
              ))}
            </View>
            
            {/* Marqueur central */}
            {selectedLocation && (
              <View style={styles.markerContainer}>
                <View style={styles.marker}>
                  <View style={styles.markerInner} />
                </View>
              </View>
            )}
            
            {/* Bouton localisation */}
            <TouchableOpacity
              style={styles.locationButton}
              onPress={handleCurrentLocation}
              disabled={loading}
            >
              <Ionicons
                name="locate"
                size={20}
                color={loading ? "#9CA3AF" : "#10B981"}
              />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Animated.View>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        <View style={styles.locationInfo}>
          <Text style={styles.locationCode}>QPPH+6J5</Text>
          <Text style={styles.locationAddress}>{address}</Text>
          <Text style={styles.coordinates}>
            Lat: {selectedLocation?.latitude.toFixed(4) || '6.3702'}, Long: {selectedLocation?.longitude.toFixed(4) || '2.3912'}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.confirmButton}
          onPress={handleConfirmLocation}
        >
          <Text style={styles.confirmButtonText}>Confirmer ma position</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.manualButton}
          onPress={handleMapPress}
        >
          <Text style={styles.manualButtonText}>Sélectionner manuellement</Text>
        </TouchableOpacity>

        {/* Recent Places */}
        <View style={styles.recentPlaces}>
          <Text style={styles.recentTitle}>Lieux récents</Text>
          
          <TouchableOpacity style={styles.placeItem}>
            <Ionicons name="business" size={20} color="#6B7280" />
            <View style={styles.placeInfo}>
              <Text style={styles.placeName}>Marché Dantokpa</Text>
              <Text style={styles.placeLocation}>Cotonou, Bénin</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.placeItem}>
            <Ionicons name="restaurant" size={20} color="#6B7280" />
            <View style={styles.placeInfo}>
              <Text style={styles.placeName}>Restaurant Le Bénin</Text>
              <Text style={styles.placeLocation}>Porto-Novo, Bénin</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchPlaceholder: {
    fontSize: 16,
    color: '#9CA3AF',
    flex: 1,
  },
  mapContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: '#F8F9FA',
  },
  mapArea: {
    flex: 1,
  },
  mapBackground: {
    flex: 1,
    backgroundColor: '#E8F5E8',
    position: 'relative',
  },
  mapGrid: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  gridLineVertical: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    width: 1,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  markerContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -16 }, { translateY: -16 }],
  },
  marker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#10B981',
    borderWidth: 3,
    borderColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  markerInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  locationButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bottomSheet: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  locationInfo: {
    marginBottom: 24,
  },
  locationCode: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 8,
  },
  coordinates: {
    fontSize: 14,
    color: '#9CA3AF',
  },
  confirmButton: {
    backgroundColor: '#10B981',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  manualButton: {
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 24,
  },
  manualButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#10B981',
  },
  recentPlaces: {
    gap: 16,
  },
  recentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  placeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  placeInfo: {
    flex: 1,
  },
  placeName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 2,
  },
  placeLocation: {
    fontSize: 14,
    color: '#6B7280',
  },
});

export default LocationScreenPerfect;
