import {
  JSONRPCMessageSchema
} from "./chunk-2JZEACTZ.js";
import "./chunk-T5ENI2NM.js";
import {
  HtmlEscapedCallbackPhase,
  resolveCallback
} from "./chunk-5TF4N3S3.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/hono/dist/utils/stream.js
var StreamingApi = class {
  writer;
  encoder;
  writable;
  abortSubscribers = [];
  responseReadable;
  aborted = false;
  closed = false;
  constructor(writable, _readable) {
    this.writable = writable;
    this.writer = writable.getWriter();
    this.encoder = new TextEncoder();
    const reader = _readable.getReader();
    this.abortSubscribers.push(async () => {
      await reader.cancel();
    });
    this.responseReadable = new ReadableStream({
      async pull(controller) {
        const { done, value } = await reader.read();
        done ? controller.close() : controller.enqueue(value);
      },
      cancel: () => {
        this.abort();
      }
    });
  }
  async write(input) {
    try {
      if (typeof input === "string") {
        input = this.encoder.encode(input);
      }
      await this.writer.write(input);
    } catch {
    }
    return this;
  }
  async writeln(input) {
    await this.write(input + "\n");
    return this;
  }
  sleep(ms) {
    return new Promise((res) => setTimeout(res, ms));
  }
  async close() {
    try {
      await this.writer.close();
    } catch {
    }
    this.closed = true;
  }
  async pipe(body) {
    this.writer.releaseLock();
    await body.pipeTo(this.writable, { preventClose: true });
    this.writer = this.writable.getWriter();
  }
  onAbort(listener) {
    this.abortSubscribers.push(listener);
  }
  abort() {
    if (!this.aborted) {
      this.aborted = true;
      this.abortSubscribers.forEach((subscriber) => subscriber());
    }
  }
};

// node_modules/hono/dist/helper/streaming/sse.js
var SSEStreamingApi = class extends StreamingApi {
  constructor(writable, readable) {
    super(writable, readable);
  }
  async writeSSE(message) {
    const data = await resolveCallback(message.data, HtmlEscapedCallbackPhase.Stringify, false, {});
    const dataLines = data.split("\n").map((line) => {
      return `data: ${line}`;
    }).join("\n");
    const sseData = [
      message.event && `event: ${message.event}`,
      dataLines,
      message.id && `id: ${message.id}`,
      message.retry && `retry: ${message.retry}`
    ].filter(Boolean).join("\n") + "\n\n";
    await this.write(sseData);
  }
};

// node_modules/hono/dist/helper/adapter/index.js
var knownUserAgents = {
  deno: "Deno",
  bun: "Bun",
  workerd: "Cloudflare-Workers",
  node: "Node.js"
};
var getRuntimeKey = () => {
  const global = globalThis;
  const userAgentSupported = typeof navigator !== "undefined" && typeof navigator.userAgent === "string";
  if (userAgentSupported) {
    for (const [runtimeKey, userAgent] of Object.entries(knownUserAgents)) {
      if (checkUserAgentEquals(userAgent)) {
        return runtimeKey;
      }
    }
  }
  if (typeof global?.EdgeRuntime === "string") {
    return "edge-light";
  }
  if (global?.fastly !== void 0) {
    return "fastly";
  }
  if (global?.process?.release?.name === "node") {
    return "node";
  }
  return "other";
};
var checkUserAgentEquals = (platform) => {
  const userAgent = navigator.userAgent;
  return userAgent.startsWith(platform);
};

// node_modules/muppet/dist/streaming.js
var i = Object.defineProperty;
var n = (t, e) => i(t, "name", { value: e, configurable: true });
var m = class {
  static {
    n(this, "SSEHonoTransport");
  }
  constructor(e, r) {
    this._endpoint = e, this._sessionId = r ?? crypto.randomUUID();
  }
  connectWithStream(e) {
    this.stream = e;
  }
  async start() {
    if (this._stream) throw new Error("SSEServerTransport already started! If using Server class, note that connect() calls start() automatically.");
    await this.stream.writeSSE({ data: `${encodeURI(this._endpoint)}?sessionId=${this._sessionId}`, event: "endpoint" }), this._stream = this.stream;
  }
  async handlePostMessage(e, r) {
    if (!this._stream) throw new Error("SSE connection not established");
    const s = r ?? await e.req.json();
    await this.handleMessage(typeof s == "string" ? JSON.parse(s) : s);
  }
  async handleMessage(e) {
    let r;
    try {
      r = JSONRPCMessageSchema.parse(e);
    } catch (s) {
      throw this.onerror?.(s), s;
    }
    this.onmessage?.(r);
  }
  async close() {
    this._stream?.close(), this._stream = void 0, this.onclose?.();
  }
  async send(e) {
    if (!this._stream) throw new Error("Not connected");
    this._stream.writeSSE({ data: JSON.stringify(e), event: "message" });
  }
  get sessionId() {
    return this._sessionId;
  }
};
var S = n(async (t, e, r) => {
  try {
    await e(t);
  } catch (s) {
    s instanceof Error && r ? (await r(s, t), await t.writeSSE({ event: "error", data: s.message })) : console.error(s);
  }
}, "run");
var l = /* @__PURE__ */ new WeakMap();
var w = n((t, e, r) => {
  const { readable: s, writable: o } = new TransformStream(), a = new SSEStreamingApi(o, s);
  return l.set(a.responseReadable, t), t.header("Transfer-Encoding", "chunked"), t.header("Content-Type", "text/event-stream"), t.header("Cache-Control", "no-cache"), t.header("Connection", "keep-alive"), getRuntimeKey() === "workerd" && t.header("Content-Encoding", "Identity"), S(a, e, r), t.newResponse(a.responseReadable);
}, "streamSSE");
export {
  m as SSEHonoTransport,
  w as streamSSE
};
//# sourceMappingURL=muppet_streaming.js.map
