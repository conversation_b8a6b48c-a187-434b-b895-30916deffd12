import {
  notImplemented,
  notImplementedClass
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/module.mjs
var _cache = /* @__PURE__ */ Object.create(null);
var _extensions = {
  ".js": notImplemented("module.require.extensions['.js']"),
  ".json": notImplemented("module.require.extensions['.json']"),
  ".node": notImplemented("module.require.extensions['.node']")
};
var createRequire = function(_filename) {
  return Object.assign(notImplemented("module.require"), {
    resolve: Object.assign(notImplemented("module.require.resolve"), { paths: notImplemented("module.require.resolve.paths") }),
    cache: /* @__PURE__ */ Object.create(null),
    extensions: _extensions,
    main: void 0
  });
};
var getCompileCacheDir = function() {
  return void 0;
};
var enableCompileCache = function(_cacheDir) {
  return {
    status: 0,
    message: "not implemented"
  };
};
var constants = Object.freeze({ compileCacheStatus: Object.freeze({
  FAILED: 0,
  ENABLED: 1,
  ALREADY_ENABLED: 2,
  DISABLED: 3
}) });
var builtinModules = [
  "_http_agent",
  "_http_client",
  "_http_common",
  "_http_incoming",
  "_http_outgoing",
  "_http_server",
  "_stream_duplex",
  "_stream_passthrough",
  "_stream_readable",
  "_stream_transform",
  "_stream_wrap",
  "_stream_writable",
  "_tls_common",
  "_tls_wrap",
  "assert",
  "assert/strict",
  "async_hooks",
  "buffer",
  "child_process",
  "cluster",
  "console",
  "constants",
  "crypto",
  "dgram",
  "diagnostics_channel",
  "dns",
  "dns/promises",
  "domain",
  "events",
  "fs",
  "fs/promises",
  "http",
  "http2",
  "https",
  "inspector",
  "inspector/promises",
  "module",
  "net",
  "os",
  "path",
  "path/posix",
  "path/win32",
  "perf_hooks",
  "process",
  "punycode",
  "querystring",
  "readline",
  "readline/promises",
  "repl",
  "stream",
  "stream/consumers",
  "stream/promises",
  "stream/web",
  "string_decoder",
  "sys",
  "timers",
  "timers/promises",
  "tls",
  "trace_events",
  "tty",
  "url",
  "util",
  "util/types",
  "v8",
  "vm",
  "wasi",
  "worker_threads",
  "zlib"
];
var isBuiltin = function(id) {
  return id.startsWith("node:") || builtinModules.includes(id);
};
var runMain = notImplemented("module.runMain");
var register = notImplemented("module.register");
var syncBuiltinESMExports = function() {
  return [];
};
var findSourceMap = function(path, error) {
  return void 0;
};
var flushCompileCache = function flushCompileCache2() {
};
var wrap = function(source) {
  return `(function (exports, require, module, __filename, __dirname) { ${source}
});`;
};
var wrapper = ["(function (exports, require, module, __filename, __dirname) { ", "\n});"];
var stripTypeScriptTypes = notImplemented("module.stripTypeScriptTypes");
var SourceMap = notImplementedClass("module.SourceMap");
var _debug = console.debug;
var _findPath = notImplemented("module._findPath");
var _initPaths = notImplemented("module._initPaths");
var _load = notImplemented("module._load");
var _nodeModulePaths = notImplemented("module._nodeModulePaths");
var _preloadModules = notImplemented("module._preloadModules");
var _resolveFilename = notImplemented("module._resolveFilename");
var _resolveLookupPaths = notImplemented("module._resolveLookupPaths");
var _stat = notImplemented("module._stat");
var _readPackage = notImplemented("module._readPackage");
var findPackageJSON = notImplemented("module.findPackageJSON");
var getSourceMapsSupport = notImplemented("module.getSourceMapsSupport");
var setSourceMapsSupport = notImplemented("module.setSourceMapsSupport");
var _pathCache = /* @__PURE__ */ Object.create(null);
var globalPaths = ["node_modules"];
var Module = {
  get Module() {
    return Module;
  },
  SourceMap,
  _cache,
  _extensions,
  _debug,
  _pathCache,
  _findPath,
  _initPaths,
  _load,
  _nodeModulePaths,
  _preloadModules,
  _resolveFilename,
  _resolveLookupPaths,
  _stat,
  _readPackage,
  builtinModules,
  constants,
  createRequire,
  enableCompileCache,
  findSourceMap,
  getCompileCacheDir,
  globalPaths,
  isBuiltin,
  register,
  runMain,
  syncBuiltinESMExports,
  wrap,
  wrapper,
  flushCompileCache,
  stripTypeScriptTypes,
  findPackageJSON,
  getSourceMapsSupport,
  setSourceMapsSupport
};

// node_modules/@cloudflare/unenv-preset/dist/runtime/node/module.mjs
var workerdModule = process.getBuiltinModule("node:module");
var createRequire2 = (file) => {
  return Object.assign(workerdModule.createRequire(file), {
    resolve: Object.assign(
      notImplemented("module.require.resolve"),
      {
        paths: notImplemented("module.require.resolve.paths")
      }
    ),
    cache: /* @__PURE__ */ Object.create(null),
    extensions: _extensions,
    main: void 0
  });
};
var module_default = {
  Module,
  SourceMap,
  _cache,
  _extensions,
  _debug,
  _pathCache,
  _findPath,
  _initPaths,
  _load,
  _nodeModulePaths,
  _preloadModules,
  _resolveFilename,
  _resolveLookupPaths,
  builtinModules,
  enableCompileCache,
  constants,
  createRequire: createRequire2,
  findSourceMap,
  getCompileCacheDir,
  globalPaths,
  isBuiltin,
  register,
  runMain,
  syncBuiltinESMExports,
  wrap
};
export {
  Module,
  SourceMap,
  _cache,
  _debug,
  _extensions,
  _findPath,
  _initPaths,
  _load,
  _nodeModulePaths,
  _pathCache,
  _preloadModules,
  _resolveFilename,
  _resolveLookupPaths,
  builtinModules,
  constants,
  createRequire2 as createRequire,
  module_default as default,
  enableCompileCache,
  findSourceMap,
  getCompileCacheDir,
  globalPaths,
  isBuiltin,
  register,
  runMain,
  syncBuiltinESMExports,
  wrap
};
//# sourceMappingURL=@cloudflare_unenv-preset_node_module.js.map
