{"version": 3, "names": ["React", "locationManager", "Annotation", "<PERSON><PERSON><PERSON><PERSON>", "HeadingIndicator", "LocationPuck", "jsx", "_jsx", "mapboxBlue", "layerStyles", "normal", "pulse", "circleRadius", "circleColor", "circleOpacity", "circlePitchAlignment", "background", "foreground", "normalIcon", "showsUserHeadingIndicator", "heading", "id", "style", "aboveLayerID", "key", "UserLocationRenderMode", "UserLocation", "Component", "defaultProps", "animated", "visible", "requestsAlwaysUse", "minDisplacement", "renderMode", "Normal", "constructor", "props", "state", "shouldShowUserLocation", "coordinates", "_onLocationUpdate", "bind", "_isMounted", "undefined", "locationManagerRunning", "componentDidMount", "setMinDisplacement", "setLocationManager", "running", "needsLocationManagerRunning", "Native", "componentDidUpdate", "prevProps", "setRequestsAlwaysUse", "componentWillUnmount", "addListener", "location", "getLastKnownLocation", "removeListener", "onUpdate", "coords", "longitude", "latitude", "setState", "_renderNative", "androidRenderMode", "iosShowsUserHeadingIndicator", "render", "children", "onPress", "iconRotate"], "sourceRoot": "../../../src", "sources": ["components/UserLocation.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAwB,OAAO;AAE3C,OAAOC,eAAe,MAAM,qCAAqC;AAIjE,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE1C,MAAMC,UAAU,GAAG,yBAAyB;AAE5C,MAAMC,WAA+D,GAAG;EACtEC,MAAM,EAAE;IACNC,KAAK,EAAE;MACLC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAEL,UAAU;MACvBM,aAAa,EAAE,GAAG;MAClBC,oBAAoB,EAAE;IACxB,CAAC;IACDC,UAAU,EAAE;MACVJ,YAAY,EAAE,CAAC;MACfC,WAAW,EAAE,MAAM;MACnBE,oBAAoB,EAAE;IACxB,CAAC;IACDE,UAAU,EAAE;MACVL,YAAY,EAAE,CAAC;MACfC,WAAW,EAAEL,UAAU;MACvBO,oBAAoB,EAAE;IACxB;EACF;AACF,CAAC;AAED,MAAMG,UAAU,GAAGA,CACjBC,yBAAmC,EACnCC,OAAuB,KACJ,cACnBb,IAAA,CAACJ,WAAW;EAEVkB,EAAE,EAAC,+BAA+B;EAClCC,KAAK,EAAEb,WAAW,CAACC,MAAM,CAACC;AAAM,GAF5B,+BAGL,CAAC,eACFJ,IAAA,CAACJ,WAAW;EAEVkB,EAAE,EAAC,+BAA+B;EAClCC,KAAK,EAAEb,WAAW,CAACC,MAAM,CAACM;AAAW,GAFjC,+BAGL,CAAC,eACFT,IAAA,CAACJ,WAAW;EAEVkB,EAAE,EAAC,8BAA8B;EACjCE,YAAY,EAAC,+BAA+B;EAC5CD,KAAK,EAAEb,WAAW,CAACC,MAAM,CAACO;AAAW,GAHjC,8BAIL,CAAC,EACF,IAAIE,yBAAyB,IAAI,OAAOC,OAAO,KAAK,QAAQ,GACxD,CAAChB,gBAAgB,CAAC;EAAEgB,OAAO;EAAEI,GAAG,EAAE;AAAqC,CAAC,CAAC,CAAC,GAC1E,EAAE,CAAC,CACR;AAED,WAAYC,sBAAsB,0BAAtBA,sBAAsB;EAAtBA,sBAAsB;EAAtBA,sBAAsB;EAAA,OAAtBA,sBAAsB;AAAA;AAwElC,MAAMC,YAAY,SAAS1B,KAAK,CAAC2B,SAAS,CAA2B;EACnE,OAAOC,YAAY,GAAG;IACpBC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbX,yBAAyB,EAAE,KAAK;IAChCY,iBAAiB,EAAE,KAAK;IACxBC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAER,sBAAsB,CAACS;EACrC,CAAC;EAEDC,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IAEZ,IAAI,CAACC,KAAK,GAAG;MACXC,sBAAsB,EAAE,KAAK;MAC7BC,WAAW,EAAE,IAAI;MACjBnB,OAAO,EAAE;IACX,CAAC;IAED,IAAI,CAACoB,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC;EAC5D;;EAEA;EACA;EACAC,UAAU,GAAaC,SAAS;EAEhCC,sBAAsB,GAAa,KAAK;EAExC,MAAMC,iBAAiBA,CAAA,EAAG;IACxB,IAAI,CAACH,UAAU,GAAG,IAAI;IAEtBzC,eAAe,CAAC6C,kBAAkB,CAAC,IAAI,CAACV,KAAK,CAACJ,eAAe,IAAI,CAAC,CAAC;IAEnE,MAAM,IAAI,CAACe,kBAAkB,CAAC;MAC5BC,OAAO,EAAE,IAAI,CAACC,2BAA2B,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAI,IAAI,CAACb,KAAK,CAACH,UAAU,KAAKR,sBAAsB,CAACyB,MAAM,EAAE;MAC3D;IACF;EACF;EAEA,MAAMC,kBAAkBA,CAACC,SAAgB,EAAE;IACzC,MAAM,IAAI,CAACL,kBAAkB,CAAC;MAC5BC,OAAO,EAAE,IAAI,CAACC,2BAA2B,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAI,IAAI,CAACb,KAAK,CAACJ,eAAe,KAAKoB,SAAS,CAACpB,eAAe,EAAE;MAC5D/B,eAAe,CAAC6C,kBAAkB,CAAC,IAAI,CAACV,KAAK,CAACJ,eAAe,IAAI,CAAC,CAAC;IACrE;IACA,IAAI,IAAI,CAACI,KAAK,CAACL,iBAAiB,KAAKqB,SAAS,CAACrB,iBAAiB,EAAE;MAChE9B,eAAe,CAACoD,oBAAoB,CAClC,IAAI,CAACjB,KAAK,CAACL,iBAAiB,IAAI,KAClC,CAAC;IACH;EACF;EAEA,MAAMuB,oBAAoBA,CAAA,EAAG;IAC3B,IAAI,CAACZ,UAAU,GAAG,KAAK;IACvB,MAAM,IAAI,CAACK,kBAAkB,CAAC;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC;EACnD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMD,kBAAkBA,CAAC;IAAEC;EAA+B,CAAC,EAAE;IAC3D,IAAI,IAAI,CAACJ,sBAAsB,KAAKI,OAAO,EAAE;MAC3C,IAAI,CAACJ,sBAAsB,GAAGI,OAAO;MACrC,IAAIA,OAAO,EAAE;QACX/C,eAAe,CAACsD,WAAW,CAAC,IAAI,CAACf,iBAAiB,CAAC;QACnD,MAAMgB,QAAQ,GAAG,MAAMvD,eAAe,CAACwD,oBAAoB,CAAC,CAAC;QAC7D,IAAI,CAACjB,iBAAiB,CAACgB,QAAQ,CAAC;MAClC,CAAC,MAAM;QACLvD,eAAe,CAACyD,cAAc,CAAC,IAAI,CAAClB,iBAAiB,CAAC;MACxD;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACES,2BAA2BA,CAAA,EAAG;IAC5B,OACE,CAAC,CAAC,IAAI,CAACb,KAAK,CAACuB,QAAQ,IACpB,IAAI,CAACvB,KAAK,CAACH,UAAU,KAAKR,sBAAsB,CAACS,MAAM,IACtD,IAAI,CAACE,KAAK,CAACN,OAAQ;EAEzB;EAEAU,iBAAiBA,CAACgB,QAAyB,EAAE;IAC3C,IAAI,CAAC,IAAI,CAACd,UAAU,IAAI,CAACc,QAAQ,EAAE;MACjC;IACF;IACA,IAAIjB,WAAW,GAAG,IAAI;IACtB,IAAInB,OAAO,GAAG,IAAI;IAElB,IAAIoC,QAAQ,IAAIA,QAAQ,CAACI,MAAM,EAAE;MAC/B,MAAM;QAAEC,SAAS;QAAEC;MAAS,CAAC,GAAGN,QAAQ,CAACI,MAAM;MAC/C,CAAC;QAAExC;MAAQ,CAAC,GAAGoC,QAAQ,CAACI,MAAM;MAC9BrB,WAAW,GAAG,CAACsB,SAAS,EAAEC,QAAQ,CAAC;IACrC;IAEA,IAAI,CAACC,QAAQ,CAAC;MACZxB,WAAW;MACXnB,OAAO,EAAEA,OAAO,IAAI;IACtB,CAAC,CAAC;IAEF,IAAI,IAAI,CAACgB,KAAK,CAACuB,QAAQ,EAAE;MACvB,IAAI,CAACvB,KAAK,CAACuB,QAAQ,CAACH,QAAQ,CAAC;IAC/B;EACF;EAEAQ,aAAaA,CAAA,EAAG;IACd,MAAM;MAAEC,iBAAiB;MAAE9C;IAA0B,CAAC,GAAG,IAAI,CAACiB,KAAK;IAEnE,MAAMA,KAAK,GAAG;MACZ6B,iBAAiB;MACjBC,4BAA4B,EAAE/C;IAChC,CAAC;IACD,oBAAOZ,IAAA,CAACF,YAAY;MAAA,GAAK+B;IAAK,CAAG,CAAC;EACpC;EAEA+B,MAAMA,CAAA,EAAG;IACP,MAAM;MAAE/C,OAAO;MAAEmB;IAAY,CAAC,GAAG,IAAI,CAACF,KAAK;IAC3C,MAAM;MAAE+B,QAAQ;MAAEtC,OAAO;MAAEX,yBAAyB;MAAEkD,OAAO;MAAExC;IAAS,CAAC,GACvE,IAAI,CAACO,KAAK;IAEZ,IAAI,CAACN,OAAO,EAAE;MACZ,OAAO,IAAI;IACb;IAEA,IAAI,IAAI,CAACM,KAAK,CAACH,UAAU,KAAKR,sBAAsB,CAACyB,MAAM,EAAE;MAC3D,OAAO,IAAI,CAACc,aAAa,CAAC,CAAC;IAC7B;IAEA,IAAI,CAACzB,WAAW,EAAE;MAChB,OAAO,IAAI;IACb;IAEA,oBACEhC,IAAA,CAACL,UAAU;MACTmB,EAAE,EAAC,oBAAoB;MACvBQ,QAAQ,EAAEA,QAAS;MACnBwC,OAAO,EAAEA,OAAQ;MACjB9B,WAAW,EAAEA,WAAY;MACzBjB,KAAK,EAAE;QACLgD,UAAU,EAAElD;MACd,CAAE;MAAAgD,QAAA,EAEDA,QAAQ,IAAIlD,UAAU,CAACC,yBAAyB,EAAEC,OAAO;IAAC,CACjD,CAAC;EAEjB;AACF;AAEA,eAAeM,YAAY", "ignoreList": []}