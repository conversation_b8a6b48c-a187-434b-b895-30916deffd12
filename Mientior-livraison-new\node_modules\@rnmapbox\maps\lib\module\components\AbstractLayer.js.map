{"version": 3, "names": ["React", "processColor", "getFilter", "transformStyle", "AbstractLayer", "PureComponent", "baseProps", "props", "id", "existing", "sourceID", "reactStyle", "getStyle", "style", "minZoomLevel", "maxZoomLevel", "aboveLayerID", "belowLayerID", "layerIndex", "filter", "undefined", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "instance", "getStyleTypeFormatter", "styleType", "setNativeProps", "propsToPass"], "sourceRoot": "../../../src", "sources": ["components/AbstractLayer.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAAwBC,YAAY,QAAQ,cAAc;AAE1D,SAASC,SAAS,QAAQ,sBAAsB;AAEhD,SAAqBC,cAAc,QAAQ,qBAAqB;AAgBhE,MAAMC,aAAa,SAGTJ,KAAK,CAACK,aAAa,CAAY;EACvC,IAAIC,SAASA,CAAA,EAEX;IACA,OAAO;MACL,GAAG,IAAI,CAACC,KAAK;MACbC,EAAE,EAAE,IAAI,CAACD,KAAK,CAACC,EAAE;MACjBC,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACE,QAAQ;MAC7BC,QAAQ,EAAE,IAAI,CAACH,KAAK,CAACG,QAAQ;MAC7BC,UAAU,EAAE,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACL,KAAK,CAACM,KAAK,CAAC;MAC3CC,YAAY,EAAE,IAAI,CAACP,KAAK,CAACO,YAAY;MACrCC,YAAY,EAAE,IAAI,CAACR,KAAK,CAACQ,YAAY;MACrCC,YAAY,EAAE,IAAI,CAACT,KAAK,CAACS,YAAY;MACrCC,YAAY,EAAE,IAAI,CAACV,KAAK,CAACU,YAAY;MACrCC,UAAU,EAAE,IAAI,CAACX,KAAK,CAACW,UAAU;MACjCC,MAAM,EAAEjB,SAAS,CAAC,IAAI,CAACK,KAAK,CAACY,MAAM,CAAC;MACpCN,KAAK,EAAEO;IACT,CAAC;EACH;EAEAC,WAAW,GAEA,IAAI;EAEfC,cAAc,GACZC,QAEQ,IACL;IACH,IAAI,CAACF,WAAW,GAAGE,QAAQ;EAC7B,CAAC;EAEDC,qBAAqBA,CAACC,SAAiB,EAAE;IACvC,IAAIA,SAAS,KAAK,OAAO,EAAE;MACzB,OAAOxB,YAAY;IACrB;IACA,OAAOmB,SAAS;EAClB;EAEAR,QAAQA,CACNC,KAAqC,EACM;IAC3C,OAAOV,cAAc,CAACU,KAAK,CAAC;EAC9B;EAEAa,cAAcA,CAACnB,KAAiC,EAAE;IAChD,IAAI,IAAI,CAACc,WAAW,EAAE;MACpB,IAAIM,WAAW,GAAGpB,KAAK;MACvB,IAAIA,KAAK,CAACM,KAAK,EAAE;QACfc,WAAW,GAAG;UACZ,GAAGpB,KAAK;UACRI,UAAU,EAAE,IAAI,CAACC,QAAQ,CAACL,KAAK,CAACM,KAA2B;QAC7D,CAAC;MACH;MACA,IAAI,CAACQ,WAAW,EAAEK,cAAc,CAACC,WAAW,CAAC;IAC/C;EACF;AACF;AAEA,eAAevB,aAAa", "ignoreList": []}