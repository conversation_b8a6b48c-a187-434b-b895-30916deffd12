{"version": 3, "names": ["_helperCreateRegexpFeaturesPlugin", "require", "_helper<PERSON>lugin<PERSON><PERSON>s", "_default", "exports", "default", "declare", "api", "options", "assertVersion", "useUnicodeFlag", "Error", "createRegExpFeaturePlugin", "name", "feature"], "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @babel/development/plugin-name */\nimport { createRegExpFeaturePlugin } from \"@babel/helper-create-regexp-features-plugin\";\nimport { declare } from \"@babel/helper-plugin-utils\";\n\nexport interface Options {\n  useUnicodeFlag?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const { useUnicodeFlag = true } = options;\n  if (typeof useUnicodeFlag !== \"boolean\") {\n    throw new Error(\".useUnicodeFlag must be a boolean, or undefined\");\n  }\n\n  return createRegExpFeaturePlugin({\n    name: \"transform-unicode-property-regex\",\n    feature: \"unicodePropertyEscape\",\n    options: { useUnicodeFlag },\n  });\n});\n"], "mappings": ";;;;;;AACA,IAAAA,iCAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AAAqD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMtC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAChDD,GAAG,CAACE,aAAa,uCAAoB,CAAC;EAEtC,MAAM;IAAEC,cAAc,GAAG;EAAK,CAAC,GAAGF,OAAO;EACzC,IAAI,OAAOE,cAAc,KAAK,SAAS,EAAE;IACvC,MAAM,IAAIC,KAAK,CAAC,iDAAiD,CAAC;EACpE;EAEA,OAAO,IAAAC,2DAAyB,EAAC;IAC/BC,IAAI,EAAE,kCAAkC;IACxCC,OAAO,EAAE,uBAAuB;IAChCN,OAAO,EAAE;MAAEE;IAAe;EAC5B,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}