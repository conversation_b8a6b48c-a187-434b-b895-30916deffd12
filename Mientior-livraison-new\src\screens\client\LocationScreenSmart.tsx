import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';

// Import des différentes versions de l'écran de localisation
import LocationScreenExpoMaps from './LocationScreenExpoMaps';
import LocationScreenFixed from './LocationScreenFixed';
import LocationScreenFallback from './LocationScreenFallback';

/**
 * Composant intelligent qui choisit automatiquement la meilleure solution de carte
 * selon la disponibilité et la compatibilité des différentes options
 */
const LocationScreenSmart: React.FC = () => {
  const [mapProvider, setMapProvider] = useState<'loading' | 'expo-maps' | 'react-native-maps' | 'fallback'>('loading');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    detectBestMapProvider();
  }, []);

  const detectBestMapProvider = async () => {
    try {
      // Test 1: Essayer expo-maps en premier (plus fiable avec Expo)
      try {
        const { ExpoMap } = await import('expo-maps');
        if (ExpoMap) {
          console.log('✅ expo-maps disponible - utilisation d\'expo-maps');
          setMapProvider('expo-maps');
          return;
        }
      } catch (expoMapsError) {
        console.log('❌ expo-maps non disponible:', expoMapsError);
      }

      // Test 2: Essayer react-native-maps
      try {
        const { default: MapView } = await import('react-native-maps');
        if (MapView) {
          console.log('✅ react-native-maps disponible - utilisation de react-native-maps');
          setMapProvider('react-native-maps');
          return;
        }
      } catch (reactNativeMapsError) {
        console.log('❌ react-native-maps non disponible:', reactNativeMapsError);
      }

      // Fallback: Utiliser la version sans carte
      console.log('⚠️ Aucune carte disponible - utilisation du fallback');
      setMapProvider('fallback');

    } catch (globalError) {
      console.error('❌ Erreur lors de la détection du provider de carte:', globalError);
      setError('Erreur lors du chargement de la carte');
      setMapProvider('fallback');
    }
  };

  // Écran de chargement pendant la détection
  if (mapProvider === 'loading') {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0DCAA8" />
        <Text style={styles.loadingText}>Chargement de la carte...</Text>
      </View>
    );
  }

  // Affichage d'erreur si nécessaire
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <LocationScreenFallback />
      </View>
    );
  }

  // Rendu du composant approprié selon le provider détecté
  switch (mapProvider) {
    case 'expo-maps':
      return <LocationScreenExpoMaps />;
    
    case 'react-native-maps':
      return <LocationScreenFixed />;
    
    case 'fallback':
    default:
      return <LocationScreenFallback />;
  }
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  errorText: {
    padding: 16,
    fontSize: 14,
    color: '#EF4444',
    textAlign: 'center',
    backgroundColor: '#FEF2F2',
    borderBottomWidth: 1,
    borderBottomColor: '#FECACA',
  },
});

export default LocationScreenSmart;
