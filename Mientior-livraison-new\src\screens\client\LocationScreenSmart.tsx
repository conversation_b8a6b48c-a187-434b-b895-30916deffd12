import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';

// Import des différentes versions de l'écran de localisation
import LocationScreenMapboxSimple from './LocationScreenMapboxSimple';
import LocationScreenRealMap from './LocationScreenRealMap';
import LocationScreenWebMap from './LocationScreenWebMap';
import LocationScreenOptimized from './LocationScreenOptimized';

/**
 * Composant intelligent qui choisit automatiquement la meilleure solution de carte
 * selon la disponibilité et la compatibilité des différentes options
 */
const LocationScreenSmart: React.FC = () => {
  const [mapProvider, setMapProvider] = useState<'loading' | 'mapbox' | 'react-native-maps' | 'webview' | 'fallback'>('loading');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    detectBestMapProvider();
  }, []);

  const detectBestMapProvider = async () => {
    try {
      // Essayer Mapbox en premier (plus fiable)
      console.log('🗺️ Test Mapbox...');
      setMapProvider('mapbox');
    } catch (globalError) {
      console.error('❌ Erreur lors de la détection du provider de carte:', globalError);
      setError('Erreur lors du chargement de la carte');
      setMapProvider('fallback');
    }
  };

  // Écran de chargement pendant la détection
  if (mapProvider === 'loading') {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0DCAA8" />
        <Text style={styles.loadingText}>Chargement de la carte...</Text>
      </View>
    );
  }

  // Affichage d'erreur si nécessaire
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <LocationScreenOptimized />
      </View>
    );
  }

  // Rendu du composant approprié selon le provider détecté
  switch (mapProvider) {
    case 'mapbox':
      console.log('🗺️ Rendu avec Mapbox Simple');
      return <LocationScreenMapboxSimple />;

    case 'react-native-maps':
      console.log('🗺️ Rendu avec react-native-maps');
      return <LocationScreenRealMap />;

    case 'webview':
      console.log('🌐 Rendu avec WebView');
      return <LocationScreenWebMap />;

    case 'fallback':
    default:
      console.log('🎨 Rendu avec carte visuelle');
      return <LocationScreenOptimized />;
  }
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  errorText: {
    padding: 16,
    fontSize: 14,
    color: '#EF4444',
    textAlign: 'center',
    backgroundColor: '#FEF2F2',
    borderBottomWidth: 1,
    borderBottomColor: '#FECACA',
  },
});

export default LocationScreenSmart;
