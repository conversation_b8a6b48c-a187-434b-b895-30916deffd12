import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';

// Import des différentes versions de l'écran de localisation
import LocationScreenRealMap from './LocationScreenRealMap';
import LocationScreenWebMap from './LocationScreenWebMap';
import LocationScreenOptimized from './LocationScreenOptimized';

/**
 * Composant intelligent qui choisit automatiquement la meilleure solution de carte
 * selon la disponibilité et la compatibilité des différentes options
 */
const LocationScreenSmart: React.FC = () => {
  const [mapProvider, setMapProvider] = useState<'loading' | 'expo-maps' | 'react-native-maps' | 'fallback'>('loading');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    detectBestMapProvider();
  }, []);

  const detectBestMapProvider = async () => {
    try {
      // Essayer react-native-maps en premier
      console.log('🗺️ Test react-native-maps...');
      setMapProvider('react-native-maps');
    } catch (globalError) {
      console.error('❌ Erreur lors de la détection du provider de carte:', globalError);
      setError('Erreur lors du chargement de la carte');
      setMapProvider('fallback');
    }
  };

  // Écran de chargement pendant la détection
  if (mapProvider === 'loading') {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0DCAA8" />
        <Text style={styles.loadingText}>Chargement de la carte...</Text>
      </View>
    );
  }

  // Affichage d'erreur si nécessaire
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <LocationScreenOptimized />
      </View>
    );
  }

  // Rendu du composant approprié selon le provider détecté
  switch (mapProvider) {
    case 'expo-maps':
      return <LocationScreenWebMap />;

    case 'react-native-maps':
      return <LocationScreenRealMap />;

    case 'fallback':
    default:
      return <LocationScreenOptimized />;
  }
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  errorText: {
    padding: 16,
    fontSize: 14,
    color: '#EF4444',
    textAlign: 'center',
    backgroundColor: '#FEF2F2',
    borderBottomWidth: 1,
    borderBottomColor: '#FECACA',
  },
});

export default LocationScreenSmart;
