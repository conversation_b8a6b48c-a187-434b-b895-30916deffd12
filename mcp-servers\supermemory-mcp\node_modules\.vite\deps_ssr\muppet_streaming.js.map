{"version": 3, "sources": ["../../hono/dist/utils/stream.js", "../../hono/dist/helper/streaming/sse.js", "../../hono/dist/helper/adapter/index.js", "../../muppet/dist/streaming.js"], "sourcesContent": ["// src/utils/stream.ts\nvar StreamingApi = class {\n  writer;\n  encoder;\n  writable;\n  abortSubscribers = [];\n  responseReadable;\n  aborted = false;\n  closed = false;\n  constructor(writable, _readable) {\n    this.writable = writable;\n    this.writer = writable.getWriter();\n    this.encoder = new TextEncoder();\n    const reader = _readable.getReader();\n    this.abortSubscribers.push(async () => {\n      await reader.cancel();\n    });\n    this.responseReadable = new ReadableStream({\n      async pull(controller) {\n        const { done, value } = await reader.read();\n        done ? controller.close() : controller.enqueue(value);\n      },\n      cancel: () => {\n        this.abort();\n      }\n    });\n  }\n  async write(input) {\n    try {\n      if (typeof input === \"string\") {\n        input = this.encoder.encode(input);\n      }\n      await this.writer.write(input);\n    } catch {\n    }\n    return this;\n  }\n  async writeln(input) {\n    await this.write(input + \"\\n\");\n    return this;\n  }\n  sleep(ms) {\n    return new Promise((res) => setTimeout(res, ms));\n  }\n  async close() {\n    try {\n      await this.writer.close();\n    } catch {\n    }\n    this.closed = true;\n  }\n  async pipe(body) {\n    this.writer.releaseLock();\n    await body.pipeTo(this.writable, { preventClose: true });\n    this.writer = this.writable.getWriter();\n  }\n  onAbort(listener) {\n    this.abortSubscribers.push(listener);\n  }\n  abort() {\n    if (!this.aborted) {\n      this.aborted = true;\n      this.abortSubscribers.forEach((subscriber) => subscriber());\n    }\n  }\n};\nexport {\n  StreamingApi\n};\n", "// src/helper/streaming/sse.ts\nimport { HtmlEscapedCallbackPhase, resolveCallback } from \"../../utils/html.js\";\nimport { StreamingApi } from \"../../utils/stream.js\";\nimport { isOldBunVersion } from \"./utils.js\";\nvar SSEStreamingApi = class extends StreamingApi {\n  constructor(writable, readable) {\n    super(writable, readable);\n  }\n  async writeSSE(message) {\n    const data = await resolveCallback(message.data, HtmlEscapedCallbackPhase.Stringify, false, {});\n    const dataLines = data.split(\"\\n\").map((line) => {\n      return `data: ${line}`;\n    }).join(\"\\n\");\n    const sseData = [\n      message.event && `event: ${message.event}`,\n      dataLines,\n      message.id && `id: ${message.id}`,\n      message.retry && `retry: ${message.retry}`\n    ].filter(Boolean).join(\"\\n\") + \"\\n\\n\";\n    await this.write(sseData);\n  }\n};\nvar run = async (stream, cb, onError) => {\n  try {\n    await cb(stream);\n  } catch (e) {\n    if (e instanceof Error && onError) {\n      await onError(e, stream);\n      await stream.writeSSE({\n        event: \"error\",\n        data: e.message\n      });\n    } else {\n      console.error(e);\n    }\n  } finally {\n    stream.close();\n  }\n};\nvar contextStash = /* @__PURE__ */ new WeakMap();\nvar streamSSE = (c, cb, onError) => {\n  const { readable, writable } = new TransformStream();\n  const stream = new SSEStreamingApi(writable, readable);\n  if (isOldBunVersion()) {\n    c.req.raw.signal.addEventListener(\"abort\", () => {\n      if (!stream.closed) {\n        stream.abort();\n      }\n    });\n  }\n  contextStash.set(stream.responseReadable, c);\n  c.header(\"Transfer-Encoding\", \"chunked\");\n  c.header(\"Content-Type\", \"text/event-stream\");\n  c.header(\"Cache-Control\", \"no-cache\");\n  c.header(\"Connection\", \"keep-alive\");\n  run(stream, cb, onError);\n  return c.newResponse(stream.responseReadable);\n};\nexport {\n  SSEStreamingApi,\n  streamSSE\n};\n", "// src/helper/adapter/index.ts\nvar env = (c, runtime) => {\n  const global = globalThis;\n  const globalEnv = global?.process?.env;\n  runtime ??= getRuntimeKey();\n  const runtimeEnvHandlers = {\n    bun: () => globalEnv,\n    node: () => globalEnv,\n    \"edge-light\": () => globalEnv,\n    deno: () => {\n      return Deno.env.toObject();\n    },\n    workerd: () => c.env,\n    fastly: () => ({}),\n    other: () => ({})\n  };\n  return runtimeEnvHandlers[runtime]();\n};\nvar knownUserAgents = {\n  deno: \"Deno\",\n  bun: \"Bun\",\n  workerd: \"Cloudflare-Workers\",\n  node: \"Node.js\"\n};\nvar getRuntimeKey = () => {\n  const global = globalThis;\n  const userAgentSupported = typeof navigator !== \"undefined\" && typeof navigator.userAgent === \"string\";\n  if (userAgentSupported) {\n    for (const [runtimeKey, userAgent] of Object.entries(knownUserAgents)) {\n      if (checkUserAgentEquals(userAgent)) {\n        return runtimeKey;\n      }\n    }\n  }\n  if (typeof global?.EdgeRuntime === \"string\") {\n    return \"edge-light\";\n  }\n  if (global?.fastly !== void 0) {\n    return \"fastly\";\n  }\n  if (global?.process?.release?.name === \"node\") {\n    return \"node\";\n  }\n  return \"other\";\n};\nvar checkUserAgentEquals = (platform) => {\n  const userAgent = navigator.userAgent;\n  return userAgent.startsWith(platform);\n};\nexport {\n  checkUserAgentEquals,\n  env,\n  getRuntimeKey,\n  knownUserAgents\n};\n", "var i=Object.defineProperty;var n=(t,e)=>i(t,\"name\",{value:e,configurable:!0});import{JSONRPCMessageSchema as c}from\"@modelcontextprotocol/sdk/types.js\";import{SSEStreamingApi as h}from\"hono/streaming\";import{getRunt<PERSON><PERSON>ey as d}from\"hono/adapter\";class m{static{n(this,\"SSEHonoTransport\")}constructor(e,r){this._endpoint=e,this._sessionId=r??crypto.randomUUID()}connectWithStream(e){this.stream=e}async start(){if(this._stream)throw new Error(\"SSEServerTransport already started! If using Server class, note that connect() calls start() automatically.\");await this.stream.writeSSE({data:`${encodeURI(this._endpoint)}?sessionId=${this._sessionId}`,event:\"endpoint\"}),this._stream=this.stream}async handlePostMessage(e,r){if(!this._stream)throw new Error(\"SSE connection not established\");const s=r??await e.req.json();await this.handleMessage(typeof s==\"string\"?JSON.parse(s):s)}async handleMessage(e){let r;try{r=c.parse(e)}catch(s){throw this.onerror?.(s),s}this.onmessage?.(r)}async close(){this._stream?.close(),this._stream=void 0,this.onclose?.()}async send(e){if(!this._stream)throw new Error(\"Not connected\");this._stream.writeSSE({data:JSON.stringify(e),event:\"message\"})}get sessionId(){return this._sessionId}}const S=n(async(t,e,r)=>{try{await e(t)}catch(s){s instanceof Error&&r?(await r(s,t),await t.writeSSE({event:\"error\",data:s.message})):console.error(s)}},\"run\"),l=new WeakMap,w=n((t,e,r)=>{const{readable:s,writable:o}=new TransformStream,a=new h(o,s);return l.set(a.responseReadable,t),t.header(\"Transfer-Encoding\",\"chunked\"),t.header(\"Content-Type\",\"text/event-stream\"),t.header(\"Cache-Control\",\"no-cache\"),t.header(\"Connection\",\"keep-alive\"),d()===\"workerd\"&&t.header(\"Content-Encoding\",\"Identity\"),S(a,e,r),t.newResponse(a.responseReadable)},\"streamSSE\");export{m as SSEHonoTransport,w as streamSSE};\n"], "mappings": ";;;;;;;;;;;AACA,IAAI,eAAe,MAAM;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmB,CAAC;AAAA,EACpB;AAAA,EACA,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY,UAAU,WAAW;AAC/B,SAAK,WAAW;AAChB,SAAK,SAAS,SAAS,UAAU;AACjC,SAAK,UAAU,IAAI,YAAY;AAC/B,UAAM,SAAS,UAAU,UAAU;AACnC,SAAK,iBAAiB,KAAK,YAAY;AACrC,YAAM,OAAO,OAAO;AAAA,IACtB,CAAC;AACD,SAAK,mBAAmB,IAAI,eAAe;AAAA,MACzC,MAAM,KAAK,YAAY;AACrB,cAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,eAAO,WAAW,MAAM,IAAI,WAAW,QAAQ,KAAK;AAAA,MACtD;AAAA,MACA,QAAQ,MAAM;AACZ,aAAK,MAAM;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,MAAM,MAAM,OAAO;AACjB,QAAI;AACF,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ,KAAK,QAAQ,OAAO,KAAK;AAAA,MACnC;AACA,YAAM,KAAK,OAAO,MAAM,KAAK;AAAA,IAC/B,QAAQ;AAAA,IACR;AACA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,QAAQ,OAAO;AACnB,UAAM,KAAK,MAAM,QAAQ,IAAI;AAC7B,WAAO;AAAA,EACT;AAAA,EACA,MAAM,IAAI;AACR,WAAO,IAAI,QAAQ,CAAC,QAAQ,WAAW,KAAK,EAAE,CAAC;AAAA,EACjD;AAAA,EACA,MAAM,QAAQ;AACZ,QAAI;AACF,YAAM,KAAK,OAAO,MAAM;AAAA,IAC1B,QAAQ;AAAA,IACR;AACA,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,MAAM,KAAK,MAAM;AACf,SAAK,OAAO,YAAY;AACxB,UAAM,KAAK,OAAO,KAAK,UAAU,EAAE,cAAc,KAAK,CAAC;AACvD,SAAK,SAAS,KAAK,SAAS,UAAU;AAAA,EACxC;AAAA,EACA,QAAQ,UAAU;AAChB,SAAK,iBAAiB,KAAK,QAAQ;AAAA,EACrC;AAAA,EACA,QAAQ;AACN,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AACf,WAAK,iBAAiB,QAAQ,CAAC,eAAe,WAAW,CAAC;AAAA,IAC5D;AAAA,EACF;AACF;;;AC7DA,IAAI,kBAAkB,cAAc,aAAa;AAAA,EAC/C,YAAY,UAAU,UAAU;AAC9B,UAAM,UAAU,QAAQ;AAAA,EAC1B;AAAA,EACA,MAAM,SAAS,SAAS;AACtB,UAAM,OAAO,MAAM,gBAAgB,QAAQ,MAAM,yBAAyB,WAAW,OAAO,CAAC,CAAC;AAC9F,UAAM,YAAY,KAAK,MAAM,IAAI,EAAE,IAAI,CAAC,SAAS;AAC/C,aAAO,SAAS,IAAI;AAAA,IACtB,CAAC,EAAE,KAAK,IAAI;AACZ,UAAM,UAAU;AAAA,MACd,QAAQ,SAAS,UAAU,QAAQ,KAAK;AAAA,MACxC;AAAA,MACA,QAAQ,MAAM,OAAO,QAAQ,EAAE;AAAA,MAC/B,QAAQ,SAAS,UAAU,QAAQ,KAAK;AAAA,IAC1C,EAAE,OAAO,OAAO,EAAE,KAAK,IAAI,IAAI;AAC/B,UAAM,KAAK,MAAM,OAAO;AAAA,EAC1B;AACF;;;ACHA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,SAAS;AAAA,EACT,MAAM;AACR;AACA,IAAI,gBAAgB,MAAM;AACxB,QAAM,SAAS;AACf,QAAM,qBAAqB,OAAO,cAAc,eAAe,OAAO,UAAU,cAAc;AAC9F,MAAI,oBAAoB;AACtB,eAAW,CAAC,YAAY,SAAS,KAAK,OAAO,QAAQ,eAAe,GAAG;AACrE,UAAI,qBAAqB,SAAS,GAAG;AACnC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,gBAAgB,UAAU;AAC3C,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,WAAW,QAAQ;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,SAAS,SAAS,SAAS,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAI,uBAAuB,CAAC,aAAa;AACvC,QAAM,YAAY,UAAU;AAC5B,SAAO,UAAU,WAAW,QAAQ;AACtC;;;AChDA,IAAI,IAAE,OAAO;AAAe,IAAI,IAAE,CAAC,GAAE,MAAI,EAAE,GAAE,QAAO,EAAC,OAAM,GAAE,cAAa,KAAE,CAAC;AAA0K,IAAM,IAAN,MAAO;AAAA,EAAC,OAAM;AAAC,MAAE,MAAK,kBAAkB;AAAA,EAAC;AAAA,EAAC,YAAY,GAAE,GAAE;AAAC,SAAK,YAAU,GAAE,KAAK,aAAW,KAAG,OAAO,WAAW;AAAA,EAAC;AAAA,EAAC,kBAAkB,GAAE;AAAC,SAAK,SAAO;AAAA,EAAC;AAAA,EAAC,MAAM,QAAO;AAAC,QAAG,KAAK,QAAQ,OAAM,IAAI,MAAM,6GAA6G;AAAE,UAAM,KAAK,OAAO,SAAS,EAAC,MAAK,GAAG,UAAU,KAAK,SAAS,CAAC,cAAc,KAAK,UAAU,IAAG,OAAM,WAAU,CAAC,GAAE,KAAK,UAAQ,KAAK;AAAA,EAAM;AAAA,EAAC,MAAM,kBAAkB,GAAE,GAAE;AAAC,QAAG,CAAC,KAAK,QAAQ,OAAM,IAAI,MAAM,gCAAgC;AAAE,UAAM,IAAE,KAAG,MAAM,EAAE,IAAI,KAAK;AAAE,UAAM,KAAK,cAAc,OAAO,KAAG,WAAS,KAAK,MAAM,CAAC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,cAAc,GAAE;AAAC,QAAI;AAAE,QAAG;AAAC,UAAE,qBAAE,MAAM,CAAC;AAAA,IAAC,SAAO,GAAE;AAAC,YAAM,KAAK,UAAU,CAAC,GAAE;AAAA,IAAC;AAAC,SAAK,YAAY,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,QAAO;AAAC,SAAK,SAAS,MAAM,GAAE,KAAK,UAAQ,QAAO,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,MAAM,KAAK,GAAE;AAAC,QAAG,CAAC,KAAK,QAAQ,OAAM,IAAI,MAAM,eAAe;AAAE,SAAK,QAAQ,SAAS,EAAC,MAAK,KAAK,UAAU,CAAC,GAAE,OAAM,UAAS,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAU;AAAC;AAAC,IAAM,IAAE,EAAE,OAAM,GAAE,GAAE,MAAI;AAAC,MAAG;AAAC,UAAM,EAAE,CAAC;AAAA,EAAC,SAAO,GAAE;AAAC,iBAAa,SAAO,KAAG,MAAM,EAAE,GAAE,CAAC,GAAE,MAAM,EAAE,SAAS,EAAC,OAAM,SAAQ,MAAK,EAAE,QAAO,CAAC,KAAG,QAAQ,MAAM,CAAC;AAAA,EAAC;AAAC,GAAE,KAAK;AAA/J,IAAiK,IAAE,oBAAI;AAAvK,IAA+K,IAAE,EAAE,CAAC,GAAE,GAAE,MAAI;AAAC,QAAK,EAAC,UAAS,GAAE,UAAS,EAAC,IAAE,IAAI,mBAAgB,IAAE,IAAI,gBAAE,GAAE,CAAC;AAAE,SAAO,EAAE,IAAI,EAAE,kBAAiB,CAAC,GAAE,EAAE,OAAO,qBAAoB,SAAS,GAAE,EAAE,OAAO,gBAAe,mBAAmB,GAAE,EAAE,OAAO,iBAAgB,UAAU,GAAE,EAAE,OAAO,cAAa,YAAY,GAAE,cAAE,MAAI,aAAW,EAAE,OAAO,oBAAmB,UAAU,GAAE,EAAE,GAAE,GAAE,CAAC,GAAE,EAAE,YAAY,EAAE,gBAAgB;AAAC,GAAE,WAAW;", "names": []}