{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/tls/tls-socket.mjs", "../../unenv/dist/runtime/node/internal/tls/server.mjs", "../../unenv/dist/runtime/node/internal/tls/constants.mjs", "../../unenv/dist/runtime/node/tls.mjs", "../../@cloudflare/unenv-preset/dist/runtime/node/tls.mjs"], "sourcesContent": ["import { Socket } from \"node:net\";\nimport { createNotImplementedError } from \"../../../_internal/utils.mjs\";\nexport class TLSSocket extends Socket {\n\tauthorized = false;\n\tencrypted = true;\n\talpnProtocol = null;\n\tauthorizationError = new Error(\"[unenv] TLSSocket.authorizationError is not implemented yet!\");\n\texportKeyingMaterial() {\n\t\tthrow createNotImplementedError(\"TLSSocket.exportKeyingMaterial\");\n\t}\n\tgetCipher() {\n\t\tthrow createNotImplementedError(\"TLSSocket.getCipher\");\n\t}\n\tgetPeerCertificate(_detailed) {\n\t\tthrow createNotImplementedError(\"TLSSocket.getPeerCertificate\");\n\t}\n\tgetCertificate() {\n\t\treturn null;\n\t}\n\tgetEphemeralKeyInfo() {\n\t\treturn null;\n\t}\n\tgetFinished() {}\n\tgetPeerFinished() {}\n\tgetProtocol() {\n\t\treturn null;\n\t}\n\tgetSession() {}\n\tgetSharedSigalgs() {\n\t\treturn [];\n\t}\n\tgetTLSTicket() {}\n\tisSessionReused() {\n\t\treturn false;\n\t}\n\trenegotiate(options, callback) {\n\t\tif (typeof callback === \"function\") {\n\t\t\tcallback(null);\n\t\t}\n\t}\n\tsetMaxSendFragment(size) {\n\t\treturn false;\n\t}\n\tdisableRenegotiation() {}\n\tenableTrace() {}\n\tgetPeerX509Certificate() {}\n\tgetX509Certificate() {}\n}\n", "import { createNotImplementedError } from \"../../../_internal/utils.mjs\";\nimport { Server as _Server } from \"node:net\";\nexport class Server extends _Server {\n\tconstructor(arg1, arg2) {\n\t\tsuper(arg1, arg2);\n\t}\n\taddContext(hostname, context) {}\n\tsetSecureContext(options) {}\n\tsetTicketKeys(_keys) {\n\t\tthrow createNotImplementedError(\"Server.setTicketKeys\");\n\t}\n\tgetTicketKeys() {\n\t\tthrow createNotImplementedError(\"Server.getTicketKeys\");\n\t}\n}\n", "export const CLIENT_RENEG_LIMIT = 3;\nexport const CLIENT_RENEG_WINDOW = 600;\nexport const DEFAULT_CIPHERS = \"\";\nexport const DEFAULT_ECDH_CURVE = \"auto\";\nexport const DEFAULT_MIN_VERSION = \"TLSv1.2\";\nexport const DEFAULT_MAX_VERSION = \"TLSv1.3\";\n", "import { notImplemented } from \"../_internal/utils.mjs\";\nimport { TLSSocket } from \"./internal/tls/tls-socket.mjs\";\nimport { Server } from \"./internal/tls/server.mjs\";\nimport { SecureContext } from \"./internal/tls/secure-context.mjs\";\nimport { CLIENT_RENEG_LIMIT, CLIENT_RENEG_WINDOW, DEFAULT_CIPHERS, DEFAULT_ECDH_CURVE, DEFAULT_MAX_VERSION, DEFAULT_MIN_VERSION } from \"./internal/tls/constants.mjs\";\nexport * from \"./internal/tls/constants.mjs\";\nexport { TLSSocket } from \"./internal/tls/tls-socket.mjs\";\nexport { Server } from \"./internal/tls/server.mjs\";\nexport { SecureContext } from \"./internal/tls/secure-context.mjs\";\nexport const connect = function connect() {\n\treturn new TLSSocket();\n};\nexport const createServer = function createServer() {\n\treturn new Server();\n};\nexport const checkServerIdentity = /* @__PURE__ */ notImplemented(\"tls.checkServerIdentity\");\nexport const convertALPNProtocols = /* @__PURE__ */ notImplemented(\"tls.convertALPNProtocols\");\nexport const createSecureContext = /* @__PURE__ */ notImplemented(\"tls.createSecureContext\");\nexport const createSecurePair = /* @__PURE__ */ notImplemented(\"tls.createSecurePair\");\nexport const getCiphers = /* @__PURE__ */ notImplemented(\"tls.getCiphers\");\nexport const rootCertificates = [];\nexport default {\n\tCLIENT_RENEG_LIMIT,\n\tCLIENT_RENEG_WINDOW,\n\tDEFAULT_CIPHERS,\n\tDEFAULT_ECDH_CURVE,\n\tDEFAULT_MAX_VERSION,\n\tDEFAULT_MIN_VERSION,\n\tSecureContext,\n\tServer,\n\tTLSSocket,\n\tcheckServerIdentity,\n\tconnect,\n\tconvertALPNProtocols,\n\tcreateSecureContext,\n\tcreateSecurePair,\n\tcreateServer,\n\tgetCiphers,\n\trootCertificates\n};\n", "import {\n  CLIENT_RENEG_LIMIT,\n  CLIENT_RENEG_WINDOW,\n  createSecurePair,\n  createServer,\n  DEFAULT_CIPHERS,\n  DEFAULT_ECDH_CURVE,\n  DEFAULT_MAX_VERSION,\n  DEFAULT_MIN_VERSION,\n  getCiphers,\n  rootCertificates,\n  Server\n} from \"unenv/node/tls\";\nexport {\n  CLIENT_RENEG_LIMIT,\n  CLIENT_RENEG_WINDOW,\n  createSecurePair,\n  createServer,\n  DEFAULT_CIPHERS,\n  DEFAULT_ECDH_CURVE,\n  DEFAULT_MAX_VERSION,\n  DEFAULT_MIN_VERSION,\n  getCiphers,\n  rootCertificates,\n  Server\n} from \"unenv/node/tls\";\nconst workerdTls = process.getBuiltinModule(\"node:tls\");\nexport const {\n  checkServerIdentity,\n  connect,\n  createSecureContext,\n  // @ts-expect-error @types/node does not provide this function\n  convertALPNProtocols,\n  // @ts-expect-error Node typings wrongly declare `SecureContext` as an interface\n  SecureContext,\n  TLSSocket\n} = workerdTls;\nexport default {\n  CLIENT_RENEG_LIMIT,\n  CLIENT_RENEG_WINDOW,\n  DEFAULT_CIPHERS,\n  DEFAULT_ECDH_CURVE,\n  DEFAULT_MAX_VERSION,\n  DEFAULT_MIN_VERSION,\n  // @ts-expect-error\n  SecureContext,\n  Server,\n  TLSSocket,\n  checkServerIdentity,\n  connect,\n  convertALPNProtocols,\n  createSecureContext,\n  createSecurePair,\n  createServer,\n  getCiphers,\n  rootCertificates\n};\n"], "mappings": ";;;;;;;AAAA,SAAS,cAAc;;;ACCvB,SAAS,UAAU,eAAe;AAC3B,IAAM,SAAN,cAAqB,QAAQ;AAAA,EACnC,YAAY,MAAM,MAAM;AACvB,UAAM,MAAM,IAAI;AAAA,EACjB;AAAA,EACA,WAAW,UAAU,SAAS;AAAA,EAAC;AAAA,EAC/B,iBAAiB,SAAS;AAAA,EAAC;AAAA,EAC3B,cAAc,OAAO;AACpB,UAAM,0BAA0B,sBAAsB;AAAA,EACvD;AAAA,EACA,gBAAgB;AACf,UAAM,0BAA0B,sBAAsB;AAAA,EACvD;AACD;;;ACdO,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAC5B,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;;;ACO5B,IAAM,eAAe,SAASA,gBAAe;AACnD,SAAO,IAAI,OAAO;AACnB;AACO,IAAM,sBAAsC,eAAe,yBAAyB;AACpF,IAAM,uBAAuC,eAAe,0BAA0B;AACtF,IAAM,sBAAsC,eAAe,yBAAyB;AACpF,IAAM,mBAAmC,eAAe,sBAAsB;AAC9E,IAAM,aAA6B,eAAe,gBAAgB;AAClE,IAAM,mBAAmB,CAAC;;;ACMjC,IAAM,aAAa,QAAQ,iBAAiB,UAAU;AAC/C,IAAM;AAAA,EACX,qBAAAC;AAAA,EACA;AAAA,EACA,qBAAAC;AAAA;AAAA,EAEA,sBAAAC;AAAA;AAAA,EAEA,eAAAC;AAAA,EACA,WAAAC;AACF,IAAI;AACJ,IAAO,cAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,eAAAD;AAAA,EACA;AAAA,EACA,WAAAC;AAAA,EACA,qBAAAJ;AAAA,EACA;AAAA,EACA,sBAAAE;AAAA,EACA,qBAAAD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["createServer", "checkServerIdentity", "createSecureContext", "convertALPNProtocols", "SecureContext", "TLSSocket"]}