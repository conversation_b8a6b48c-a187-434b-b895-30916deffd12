import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

// Import des écrans de localisation
import LocationScreenExact from '../screens/client/LocationScreenExact';
import LocationScreenFixed from '../screens/client/LocationScreenFixed';
import LocationScreenClean from '../screens/client/LocationScreenClean';

// Écran d'accueil avec navigation vers les écrans de localisation
const HomeScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>✅ Mientior Livraison</Text>
        <Text style={styles.subtitle}>Application fonctionnelle</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🗺️ Écrans de Localisation</Text>
        
        <TouchableOpacity
          style={styles.button}
          onPress={() => navigation.navigate('LocationExact')}
        >
          <Ionicons name="map-outline" size={24} color="#00D4AA" />
          <View style={styles.buttonContent}>
            <Text style={styles.buttonTitle}>Interface Simulée</Text>
            <Text style={styles.buttonSubtitle}>Recommandé - Aucune dépendance</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#6B7280" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={() => navigation.navigate('LocationFixed')}
        >
          <Ionicons name="location-outline" size={24} color="#00D4AA" />
          <View style={styles.buttonContent}>
            <Text style={styles.buttonTitle}>Cartes Natives</Text>
            <Text style={styles.buttonSubtitle}>react-native-maps + fallbacks</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#6B7280" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={() => navigation.navigate('LocationClean')}
        >
          <Ionicons name="phone-portrait-outline" size={24} color="#00D4AA" />
          <View style={styles.buttonContent}>
            <Text style={styles.buttonTitle}>Interface Pure</Text>
            <Text style={styles.buttonSubtitle}>Design minimaliste</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#6B7280" />
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🎯 Fonctionnalités</Text>
        <View style={styles.featureList}>
          <Text style={styles.feature}>✅ Navigation fonctionnelle</Text>
          <Text style={styles.feature}>✅ Écrans de localisation robustes</Text>
          <Text style={styles.feature}>✅ Fallbacks complets</Text>
          <Text style={styles.feature}>✅ Design moderne africain</Text>
          <Text style={styles.feature}>✅ Compatible tous environnements</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const Stack = createStackNavigator();

const AppNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#00D4AA',
        },
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen 
        name="Home" 
        component={HomeScreen}
        options={{ title: 'Mientior Livraison' }}
      />
      <Stack.Screen 
        name="LocationExact" 
        component={LocationScreenExact}
        options={{ title: 'Interface Simulée' }}
      />
      <Stack.Screen 
        name="LocationFixed" 
        component={LocationScreenFixed}
        options={{ title: 'Cartes Natives' }}
      />
      <Stack.Screen 
        name="LocationClean" 
        component={LocationScreenClean}
        options={{ title: 'Interface Pure' }}
      />
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    backgroundColor: '#00D4AA',
    paddingHorizontal: 24,
    paddingVertical: 32,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.9,
  },
  section: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  button: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonContent: {
    flex: 1,
    marginLeft: 12,
  },
  buttonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  buttonSubtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  featureList: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  feature: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 8,
    lineHeight: 20,
  },
});

export default AppNavigator;
