{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/readline/promises/interface.mjs", "../../unenv/dist/runtime/node/internal/readline/promises/readline.mjs", "../../unenv/dist/runtime/node/readline/promises.mjs"], "sourcesContent": ["import { Interface as _Interface } from \"../interface.mjs\";\nexport class Interface extends _Interface {\n\tquestion(query, options) {\n\t\treturn Promise.resolve(\"\");\n\t}\n}\n", "export class Readline {\n\tclearLine(dir) {\n\t\treturn this;\n\t}\n\tclearScreenDown() {\n\t\treturn this;\n\t}\n\tcommit() {\n\t\treturn Promise.resolve();\n\t}\n\tcursorTo(x, y) {\n\t\treturn this;\n\t}\n\tmoveCursor(dx, dy) {\n\t\treturn this;\n\t}\n\trollback() {\n\t\treturn this;\n\t}\n}\n", "import { Interface } from \"../internal/readline/promises/interface.mjs\";\nimport { Readline } from \"../internal/readline/promises/readline.mjs\";\nexport { Interface } from \"../internal/readline/promises/interface.mjs\";\nexport { Readline } from \"../internal/readline/promises/readline.mjs\";\nexport const createInterface = () => new Interface();\nexport default {\n\tInterface,\n\tReadline,\n\tcreateInterface\n};\n"], "mappings": ";;;;;;AACO,IAAMA,aAAN,cAAwB,UAAW;AAAA,EACzC,SAAS,OAAO,SAAS;AACxB,WAAO,QAAQ,QAAQ,EAAE;AAAA,EAC1B;AACD;;;ACLO,IAAM,WAAN,MAAe;AAAA,EACrB,UAAU,KAAK;AACd,WAAO;AAAA,EACR;AAAA,EACA,kBAAkB;AACjB,WAAO;AAAA,EACR;AAAA,EACA,SAAS;AACR,WAAO,QAAQ,QAAQ;AAAA,EACxB;AAAA,EACA,SAAS,GAAG,GAAG;AACd,WAAO;AAAA,EACR;AAAA,EACA,WAAW,IAAI,IAAI;AAClB,WAAO;AAAA,EACR;AAAA,EACA,WAAW;AACV,WAAO;AAAA,EACR;AACD;;;ACfO,IAAM,kBAAkB,MAAM,IAAIC,WAAU;AACnD,IAAO,mBAAQ;AAAA,EACd,WAAAA;AAAA,EACA;AAAA,EACA;AACD;", "names": ["Interface", "Interface"]}