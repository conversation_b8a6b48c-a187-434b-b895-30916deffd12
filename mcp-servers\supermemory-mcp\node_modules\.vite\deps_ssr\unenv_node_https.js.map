{"version": 3, "sources": ["../../unenv/dist/runtime/node/https.mjs"], "sourcesContent": ["import { notImplemented, notImplementedClass } from \"../_internal/utils.mjs\";\nimport { Agent as HttpAgent } from \"./internal/http/agent.mjs\";\nexport const Server = /* @__PURE__ */ notImplementedClass(\"https.Server\");\nexport const Agent = HttpAgent;\nexport const globalAgent = /* @__PURE__ */ new Agent();\nexport const get = /* @__PURE__ */ notImplemented(\"https.get\");\nexport const createServer = /* @__PURE__ */ notImplemented(\"https.createServer\");\nexport const request = /* @__PURE__ */ notImplemented(\"https.request\");\nexport default {\n\tServer,\n\tAgent,\n\tglobalAgent,\n\tget,\n\tcreateServer,\n\trequest\n};\n"], "mappings": ";;;;;;;;;;AAEO,IAAM,SAAyB,oBAAoB,cAAc;AACjE,IAAMA,SAAQ;AACd,IAAM,cAA8B,IAAIA,OAAM;AAC9C,IAAM,MAAsB,eAAe,WAAW;AACtD,IAAM,eAA+B,eAAe,oBAAoB;AACxE,IAAM,UAA0B,eAAe,eAAe;AACrE,IAAO,gBAAQ;AAAA,EACd;AAAA,EACA,OAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": ["Agent"]}