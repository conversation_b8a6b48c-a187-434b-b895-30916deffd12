import {
  notImplementedClass
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/sqlite.mjs
var DatabaseSync = notImplementedClass("sqlite.DatabaseSync");
var StatementSync = notImplementedClass("sqlite.StatementSync");
var constants = {};
var sqlite_default = {
  DatabaseSync,
  StatementSync,
  constants
};
export {
  DatabaseSync,
  StatementSync,
  constants,
  sqlite_default as default
};
//# sourceMappingURL=unenv_node_sqlite.js.map
