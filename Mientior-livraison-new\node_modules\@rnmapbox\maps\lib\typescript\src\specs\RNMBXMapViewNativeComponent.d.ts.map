{"version": 3, "file": "RNMBXMapViewNativeComponent.d.ts", "sourceRoot": "", "sources": ["../../../../src/specs/RNMBXMapViewNativeComponent.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAE7D,OAAO,EACL,oBAAoB,EACpB,kBAAkB,EAClB,KAAK,EACN,MAAM,2CAA2C,CAAC;AAEnD,OAAO,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAGzD,KAAK,YAAY,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AAEtC,KAAK,eAAe,GAAG;IACrB,wBAAwB,CAAC,EAAE,OAAO,CAAC;IACnC,2BAA2B,CAAC,EAAE,OAAO,CAAC;IACtC,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B,8BAA8B,CAAC,EAAE,OAAO,CAAC;CAC1C,CAAC;AAEF,KAAK,cAAc,GACf;IACE,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;CACrB,GACD,IAAI,CAAC;AAET,KAAK,wBAAwB,GAAG;IAC9B,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AACF,KAAK,gBAAgB,GAAG;IAAE,IAAI,EAAE,MAAM,CAAC;IAAC,OAAO,EAAE,MAAM,CAAA;CAAE,CAAC;AAC1D,KAAK,oBAAoB,GAAG;IAAE,IAAI,EAAE,MAAM,CAAC;IAAC,OAAO,EAAE,MAAM,CAAA;CAAE,CAAC;AAE9D,MAAM,WAAW,WAAY,SAAQ,SAAS;IAC5C,kBAAkB,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IAC3C,mBAAmB,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;IAEvC,WAAW,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IACpC,YAAY,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;IAEhC,cAAc,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IACvC,oBAAoB,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IAC7C,eAAe,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;IACnC,mBAAmB,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;IAC1C,kBAAkB,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;IAEzC,eAAe,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IACxC,gBAAgB,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;IAEpC,WAAW,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IACpC,aAAa,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IACtC,aAAa,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IACtC,YAAY,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IAErC,uBAAuB,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IAEhD,kCAAkC,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IAE3D,UAAU,CAAC,EAAE,YAAY,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC;IAChD,cAAc,CAAC,EAAE,WAAW,CAAC,cAAc,CAAC,CAAC;IAE7C,QAAQ,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;IAEhC,eAAe,CAAC,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;IAG/C,WAAW,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;IACpC,mBAAmB,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;IACvC,sBAAsB,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;IAC1C,uBAAuB,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;IAG3C,YAAY,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;IAEpC,OAAO,CAAC,EAAE,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;IACjD,WAAW,CAAC,EAAE,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;IACnD,WAAW,CAAC,EAAE,kBAAkB,CAAC,oBAAoB,CAAC,CAAC;IACvD,eAAe,CAAC,EAAE,kBAAkB,CAAC,wBAAwB,CAAC,CAAC;IAE/D,WAAW,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;IACnC,wBAAwB,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;CAChD;;AAED,wBAEgC;AAIhC,KAAK,QAAQ,GAAG;IACd,UAAU,EAAE;QACV,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC;QACzB,MAAM,EAAE;YACN,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC;YACrB,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC;SACtB,CAAC;QACF,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;IACF,QAAQ,EAAE;QACR,eAAe,EAAE,OAAO,CAAC;KAC1B,CAAC;IACF,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB,CAAC;AACF,KAAK,aAAa,GAAG;IACnB,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,EAAE,OAAO,CAAC;IAClB,iBAAiB,EAAE,OAAO,CAAC;IAC3B,aAAa,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC;IAClC,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAEF,KAAK,sBAAsB,GAAG;IAC5B,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC;CACnC,CAAC;AACF,KAAK,8BAA8B,GAAG;IACpC,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,QAAQ,GAAG,MAAM,CAAC;CAC5B,CAAC;AACF,KAAK,0BAA0B,GAAG;IAChC,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EACH,OAAO,CAAC,OAAO,CACb,OAAO,CAAC,KAAK,EACb,aAAa,GAAG;QAAE,8BAA8B,EAAE,OAAO,CAAA;KAAE,CAC5D,GACD,MAAM,CAAC;CACZ,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG,aAAa,CAC7C,IAAI,CAAC,WAAW,EAAE,iBAAiB,GAAG,aAAa,GAAG,aAAa,CAAC,GAAG;IACrE,eAAe,CAAC,EAAE,kBAAkB,CAAC,8BAA8B,CAAC,CAAC;IACrE,WAAW,CAAC,EAAE,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;IACzD,OAAO,CAAC,EAAE,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;IACrD,WAAW,CAAC,EAAE,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;CAC9D,CACF,CAAC"}