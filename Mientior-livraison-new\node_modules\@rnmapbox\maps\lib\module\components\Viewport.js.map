{"version": 3, "names": ["React", "forwardRef", "memo", "useEffect", "useImperativeHandle", "useMemo", "useRef", "NativeViewport", "RNMBXViewportModule", "NativeCommands", "jsx", "_jsx", "Viewport", "props", "ref", "commands", "nativeViewport", "current", "setNativeRef", "getState", "console", "log", "call", "idle", "transitionTo", "state", "transition", "onStatusChangedNative", "propsOnS<PERSON><PERSON><PERSON><PERSON><PERSON>", "onStatusChanged", "event", "nativeEvent", "payload", "undefined", "RNMBXViewport", "hasStatus<PERSON><PERSON>ed"], "sourceRoot": "../../../src", "sources": ["components/Viewport.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,IAEVC,UAAU,EACVC,IAAI,EACJC,SAAS,EACTC,mBAAmB,EACnBC,OAAO,EACPC,MAAM,QACD,OAAO;AAGd,OAAOC,cAAc,MAGd,uCAAuC;AAC9C,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAuJzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,QAAQ,gBAAGV,IAAI,cAC1BD,UAAU,CAAa,CAACY,KAAY,EAAEC,GAA4B,KAAK;EACrE,MAAMC,QAAQ,GAAGV,OAAO,CAAC,MAAM,IAAII,cAAc,CAACD,mBAAmB,CAAC,EAAE,EAAE,CAAC;EAC3E,MAAMQ,cAAc,GAAGV,MAAM,CAAuB,IAAI,CAAC;EACzDH,SAAS,CAAC,MAAM;IACd,IAAIa,cAAc,CAACC,OAAO,EAAE;MAC1BF,QAAQ,CAACG,YAAY,CAACF,cAAc,CAACC,OAAO,CAAC;IAC/C;IACA;EACF,CAAC,EAAE,CAACF,QAAQ,EAAEC,cAAc,CAACC,OAAO,CAAC,CAAC;EAEtCb,mBAAmB,CAACU,GAAG,EAAE,OAAO;IAC9BK,QAAQA,CAAA,EAAG;MACTC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,OAAON,QAAQ,CAACO,IAAI,CAAS,UAAU,EAAE,EAAE,CAAC;IAC9C,CAAC;IACD,MAAMC,IAAIA,CAAA,EAAG;MACX,OAAOR,QAAQ,CAACO,IAAI,CAAO,MAAM,EAAE,EAAE,CAAC;IACxC,CAAC;IACDE,YAAYA,CAACC,KAAK,EAAEC,UAAU,EAAE;MAC9B,OAAOX,QAAQ,CAACO,IAAI,CAAU,cAAc,EAAE,CAACG,KAAK,EAAEC,UAAU,CAAC,CAAC;IACpE;EACF,CAAC,CAAC,CAAC;EAEH,MAAMC,qBAAqB,GAAGtB,OAAO,CAAC,MAAM;IAC1C,MAAMuB,oBAAoB,GAAGf,KAAK,CAACgB,eAAe;IAClD,IAAID,oBAAoB,IAAI,IAAI,EAAE;MAChC,OAAQE,KAAyD,IAAK;QACpEF,oBAAoB,CAACE,KAAK,CAACC,WAAW,CAACC,OAAO,CAAC;MACjD,CAAC;IACH,CAAC,MAAM;MACL,OAAOC,SAAS;IAClB;EACF,CAAC,EAAE,CAACpB,KAAK,CAACgB,eAAe,CAAC,CAAC;EAE3B,oBACElB,IAAA,CAACuB,aAAa;IAAA,GACRrB,KAAK;IACTsB,gBAAgB,EAAEtB,KAAK,CAACgB,eAAe,IAAI,IAAK;IAChDA,eAAe,EAAEF,qBAAsB;IACvCb,GAAG,EAAEE;EAAe,CACrB,CAAC;AAEN,CAAC,CACH,CAAC;AAeD,MAAMkB,aAAa,GAAG3B,cAAoC", "ignoreList": []}