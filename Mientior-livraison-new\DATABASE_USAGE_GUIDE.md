# 📊 Guide d'utilisation de la base de données Supabase

**Date:** 2025-06-15  
**Projet:** Mientior Livraison React Native Expo App  

## 🚀 **Configuration et démarrage**

### **1. Variables d'environnement**
Assurez-vous que votre fichier `.env` contient :
```bash
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Mode offline (pour développement)
EXPO_PUBLIC_OFFLINE_MODE=false
```

### **2. Installation des dépendances**
```bash
cd Mientior-livraison-new
npm install
```

### **3. Peuplement de la base de données**
```bash
# Peupler avec des données de test
npm run seed-db

# Nettoyer la base de données
npm run clean-db

# Réinitialiser complètement
npm run reset-db
```

---

## 📋 **Structure de la base de données**

### **Tables principales :**

#### **1. users** (Gestion par Supabase Auth)
- Authentification et profils utilisateurs
- Rôles : client, merchant, livreur

#### **2. merchant_profiles**
- Profils des restaurants/commerces
- Informations business, horaires, localisation
- Lié à `users` via `user_id`

#### **3. products**
- Catalogue des produits
- Support multilingue (JSONB)
- Gestion des stocks et promotions
- Lié à `merchant_profiles` via `merchant_id`

#### **4. categories**
- Catégories de produits
- Support multilingue

#### **5. orders**
- Gestion des commandes
- Statuts, paiements, livraisons
- Relations avec clients, merchants, livreurs

#### **6. order_items**
- Détails des commandes
- Quantités, prix, modifications

#### **7. addresses**
- Adresses de livraison
- Coordonnées GPS (PostGIS)

---

## 🔧 **Utilisation des services**

### **1. Service Restaurant**
```typescript
import { restaurantService } from '../services/database';

// Récupérer tous les restaurants
const restaurants = await restaurantService.getAll(location, filters);

// Rechercher des restaurants
const results = await restaurantService.search('pizza', location);

// Récupérer un restaurant par ID
const restaurant = await restaurantService.getById('restaurant-id');
```

### **2. Service Produits**
```typescript
import { productService } from '../services/database';

// Produits d'un restaurant
const products = await productService.getByMerchant('merchant-id', filters);

// Catégories d'un restaurant
const categories = await productService.getCategories('merchant-id');

// Produit par ID
const product = await productService.getById('product-id');
```

### **3. Service Commandes**
```typescript
import { orderService } from '../services/database';

// Commandes d'un utilisateur
const orders = await orderService.getByUser('user-id', 'client');

// Mettre à jour le statut
const success = await orderService.updateStatus('order-id', 'en_livraison');
```

---

## 🎣 **Utilisation des hooks React Query**

### **1. Hook Restaurants**
```typescript
import { useRestaurantsQuery } from '../hooks/useRestaurantsQuery';

const {
  restaurants,
  loading,
  error,
  refresh,
  searchRestaurants,
  getRestaurantsByType,
  getFeaturedRestaurants
} = useRestaurantsQuery({
  location: userLocation,
  filters: { type_service: 'restaurant', rayon_km: 10 },
  enabled: true
});
```

### **2. Hook Produits**
```typescript
import { useProductsQuery } from '../hooks/useProductsQuery';

const {
  products,
  categories,
  loading,
  getProductsByCategory,
  searchProducts,
  getFeaturedProducts
} = useProductsQuery({
  merchantId: 'restaurant-id',
  filters: { category: 'plats-principaux' }
});
```

### **3. Hook Commandes**
```typescript
import { useOrdersQuery } from '../hooks/useOrdersQuery';

const {
  orders,
  loading,
  updateOrderStatus,
  getActiveOrders,
  getOrderStats
} = useOrdersQuery({
  userId: user.id,
  role: 'client'
});
```

---

## ⚡ **Temps réel avec Supabase**

### **1. Abonnements temps réel**
```typescript
import { realTimeService } from '../services/realTimeService';

// Écouter les mises à jour de restaurants
const subscription = realTimeService.subscribeToRestaurants(
  (payload) => {
    console.log('Restaurant mis à jour:', payload);
    // Actualiser les données
  },
  { business_type: 'restaurant' }
);

// Se désabonner
subscription?.unsubscribe();
```

### **2. Suivi des commandes**
```typescript
// Écouter les mises à jour de commandes
const orderSubscription = realTimeService.subscribeToOrders(
  userId,
  'client',
  (payload) => {
    console.log('Commande mise à jour:', payload);
    // Mettre à jour l'interface
  }
);
```

---

## 📱 **Support hors ligne**

### **1. Cache automatique**
- Toutes les données sont automatiquement mises en cache avec AsyncStorage
- Fallback automatique vers le cache en cas de perte de connexion
- Synchronisation automatique lors du retour en ligne

### **2. Gestion manuelle du cache**
```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';

// Vérifier le cache
const cached = await AsyncStorage.getItem('restaurants_cache');

// Nettoyer le cache
await AsyncStorage.removeItem('restaurants_cache');
```

---

## 🔍 **Filtres et recherche**

### **1. Filtres disponibles**
```typescript
interface FiltresRecherche {
  type_service?: 'restaurant' | 'epicerie' | 'pharmacie' | 'boutique' | 'supermarche';
  prix_min?: number;
  prix_max?: number;
  note_min?: number;
  rayon_km?: number;
  ouvert_maintenant?: boolean;
  tri?: 'distance' | 'note' | 'temps_livraison' | 'prix' | 'popularite';
}
```

### **2. Recherche avancée**
```typescript
// Recherche multi-critères
const results = await restaurantService.search('pizza africaine', location);

// Recherche avec filtres
const restaurants = await restaurantService.getAll(location, {
  type_service: 'restaurant',
  note_min: 4.0,
  rayon_km: 5,
  ouvert_maintenant: true,
  tri: 'distance'
});
```

---

## 📊 **Monitoring et performance**

### **1. Logs de débogage**
```typescript
// Activer les logs détaillés
console.log('🏪 Fetching restaurants from database...');
console.log('✅ Restaurants fetched successfully:', restaurants.length);
console.log('❌ Error fetching restaurants:', error);
```

### **2. Métriques de performance**
```typescript
// Temps de réponse des queries
const startTime = Date.now();
const data = await restaurantService.getAll();
const responseTime = Date.now() - startTime;
console.log(`⏱️ Query time: ${responseTime}ms`);
```

---

## 🛠️ **Dépannage**

### **1. Problèmes de connexion**
```typescript
// Vérifier la connexion Supabase
if (!supabase) {
  console.warn('Supabase non disponible - mode offline');
  // Utiliser les données en cache
}
```

### **2. Erreurs de données**
```typescript
// Validation des données
try {
  const restaurants = await restaurantService.getAll();
  if (!restaurants || restaurants.length === 0) {
    console.warn('Aucun restaurant trouvé');
  }
} catch (error) {
  console.error('Erreur récupération restaurants:', error);
  // Fallback vers le cache
}
```

### **3. Problèmes de cache**
```typescript
// Nettoyer le cache en cas de problème
await AsyncStorage.clear();
console.log('Cache nettoyé');
```

---

## 🎯 **Bonnes pratiques**

### **1. Gestion des erreurs**
- Toujours implémenter un fallback vers le cache
- Afficher des messages d'erreur utilisateur-friendly
- Logger les erreurs pour le débogage

### **2. Performance**
- Utiliser les filtres pour limiter les données
- Implémenter la pagination pour les grandes listes
- Mettre en cache les données fréquemment utilisées

### **3. Sécurité**
- Utiliser les RLS (Row Level Security) policies
- Valider toutes les entrées utilisateur
- Ne jamais exposer les clés de service côté client

---

## 📈 **Métriques de succès**

- ✅ **Temps de réponse** : < 2 secondes pour les requêtes
- ✅ **Disponibilité** : 99.9% avec fallback offline
- ✅ **Cache hit rate** : > 80% pour les données fréquentes
- ✅ **Synchronisation** : < 5 secondes pour les mises à jour temps réel

---

## 🔗 **Ressources utiles**

- [Documentation Supabase](https://supabase.com/docs)
- [React Query Documentation](https://tanstack.com/query/latest)
- [PostGIS Documentation](https://postgis.net/documentation/)
- [AsyncStorage Documentation](https://react-native-async-storage.github.io/async-storage/)

---

**Status:** ✅ **OPÉRATIONNEL**  
**Base de données:** ✅ **CONNECTÉE**  
**Cache:** ✅ **ACTIF**  
**Temps réel:** ✅ **FONCTIONNEL**
