{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/perf_hooks/histogram.mjs", "../../unenv/dist/runtime/node/internal/perf_hooks/performance.mjs", "../../unenv/dist/runtime/node/internal/perf_hooks/constants.mjs", "../../unenv/dist/runtime/node/perf_hooks.mjs"], "sourcesContent": ["import { createNotImplementedError } from \"../../../_internal/utils.mjs\";\nclass Histogram {\n\tmin = 0x8000000000000000;\n\tmax = 0;\n\tmean = Number.NaN;\n\texceeds = 0;\n\tstddev = Number.NaN;\n\tcount = 0;\n\tcountBigInt = BigInt(0);\n\texceedsBigInt = BigInt(0);\n\tmaxBigInt = 0;\n\tminBigInt = BigInt(9223372036854775807n);\n\tpercentiles = new Map();\n\tpercentilesBigInt = new Map();\n\tpercentileBigInt(_percentile) {\n\t\tthrow createNotImplementedError(\"Histogram.percentileBigInt\");\n\t}\n\tpercentile(percentile) {\n\t\treturn this.percentiles.get(percentile) ?? Number.NaN;\n\t}\n\treset() {\n\t\tthrow createNotImplementedError(\"Histogram.reset\");\n\t}\n}\nexport class IntervalHistogram extends Histogram {\n\tenable() {\n\t\treturn true;\n\t}\n\tdisable() {\n\t\treturn true;\n\t}\n}\nexport class RecordableHistogram extends Histogram {\n\trecord(val) {\n\t\tthrow createNotImplementedError(\"RecordableHistogram.record\");\n\t}\n\trecordDelta() {\n\t\tthrow createNotImplementedError(\"RecordableHistogram.recordDelta\");\n\t}\n\tadd(other) {\n\t\tthrow createNotImplementedError(\"RecordableHistogram.add\");\n\t}\n}\n", "import { createNotImplementedError } from \"../../../_internal/utils.mjs\";\nconst _timeOrigin = globalThis.performance?.timeOrigin ?? Date.now();\nconst _performanceNow = globalThis.performance?.now ? globalThis.performance.now.bind(globalThis.performance) : () => Date.now() - _timeOrigin;\nconst nodeTiming = {\n\tname: \"node\",\n\tentryType: \"node\",\n\tstartTime: 0,\n\tduration: 0,\n\tnodeStart: 0,\n\tv8Start: 0,\n\tbootstrapComplete: 0,\n\tenvironment: 0,\n\tloopStart: 0,\n\tloopExit: 0,\n\tidleTime: 0,\n\tuvMetricsInfo: {\n\t\tloopCount: 0,\n\t\tevents: 0,\n\t\teventsWaiting: 0\n\t},\n\tdetail: undefined,\n\ttoJSON() {\n\t\treturn this;\n\t}\n};\nexport class PerformanceEntry {\n\t__unenv__ = true;\n\tdetail;\n\tentryType = \"event\";\n\tname;\n\tstartTime;\n\tconstructor(name, options) {\n\t\tthis.name = name;\n\t\tthis.startTime = options?.startTime || _performanceNow();\n\t\tthis.detail = options?.detail;\n\t}\n\tget duration() {\n\t\treturn _performanceNow() - this.startTime;\n\t}\n\ttoJSON() {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tentryType: this.entryType,\n\t\t\tstartTime: this.startTime,\n\t\t\tduration: this.duration,\n\t\t\tdetail: this.detail\n\t\t};\n\t}\n}\nexport const PerformanceMark = class PerformanceMark extends PerformanceEntry {\n\tentryType = \"mark\";\n\tconstructor() {\n\t\tsuper(...arguments);\n\t}\n\tget duration() {\n\t\treturn 0;\n\t}\n};\nexport class PerformanceMeasure extends PerformanceEntry {\n\tentryType = \"measure\";\n}\nexport class PerformanceResourceTiming extends PerformanceEntry {\n\tentryType = \"resource\";\n\tserverTiming = [];\n\tconnectEnd = 0;\n\tconnectStart = 0;\n\tdecodedBodySize = 0;\n\tdomainLookupEnd = 0;\n\tdomainLookupStart = 0;\n\tencodedBodySize = 0;\n\tfetchStart = 0;\n\tinitiatorType = \"\";\n\tname = \"\";\n\tnextHopProtocol = \"\";\n\tredirectEnd = 0;\n\tredirectStart = 0;\n\trequestStart = 0;\n\tresponseEnd = 0;\n\tresponseStart = 0;\n\tsecureConnectionStart = 0;\n\tstartTime = 0;\n\ttransferSize = 0;\n\tworkerStart = 0;\n\tresponseStatus = 0;\n}\nexport class PerformanceObserverEntryList {\n\t__unenv__ = true;\n\tgetEntries() {\n\t\treturn [];\n\t}\n\tgetEntriesByName(_name, _type) {\n\t\treturn [];\n\t}\n\tgetEntriesByType(type) {\n\t\treturn [];\n\t}\n}\nexport class Performance {\n\t__unenv__ = true;\n\ttimeOrigin = _timeOrigin;\n\teventCounts = new Map();\n\t_entries = [];\n\t_resourceTimingBufferSize = 0;\n\tnavigation = undefined;\n\ttiming = undefined;\n\ttimerify(_fn, _options) {\n\t\tthrow createNotImplementedError(\"Performance.timerify\");\n\t}\n\tget nodeTiming() {\n\t\treturn nodeTiming;\n\t}\n\teventLoopUtilization() {\n\t\treturn {};\n\t}\n\tmarkResourceTiming() {\n\t\treturn new PerformanceResourceTiming(\"\");\n\t}\n\tonresourcetimingbufferfull = null;\n\tnow() {\n\t\tif (this.timeOrigin === _timeOrigin) {\n\t\t\treturn _performanceNow();\n\t\t}\n\t\treturn Date.now() - this.timeOrigin;\n\t}\n\tclearMarks(markName) {\n\t\tthis._entries = markName ? this._entries.filter((e) => e.name !== markName) : this._entries.filter((e) => e.entryType !== \"mark\");\n\t}\n\tclearMeasures(measureName) {\n\t\tthis._entries = measureName ? this._entries.filter((e) => e.name !== measureName) : this._entries.filter((e) => e.entryType !== \"measure\");\n\t}\n\tclearResourceTimings() {\n\t\tthis._entries = this._entries.filter((e) => e.entryType !== \"resource\" || e.entryType !== \"navigation\");\n\t}\n\tgetEntries() {\n\t\treturn this._entries;\n\t}\n\tgetEntriesByName(name, type) {\n\t\treturn this._entries.filter((e) => e.name === name && (!type || e.entryType === type));\n\t}\n\tgetEntriesByType(type) {\n\t\treturn this._entries.filter((e) => e.entryType === type);\n\t}\n\tmark(name, options) {\n\t\tconst entry = new PerformanceMark(name, options);\n\t\tthis._entries.push(entry);\n\t\treturn entry;\n\t}\n\tmeasure(measureName, startOrMeasureOptions, endMark) {\n\t\tlet start;\n\t\tlet end;\n\t\tif (typeof startOrMeasureOptions === \"string\") {\n\t\t\tstart = this.getEntriesByName(startOrMeasureOptions, \"mark\")[0]?.startTime;\n\t\t\tend = this.getEntriesByName(endMark, \"mark\")[0]?.startTime;\n\t\t} else {\n\t\t\tstart = Number.parseFloat(startOrMeasureOptions?.start) || this.now();\n\t\t\tend = Number.parseFloat(startOrMeasureOptions?.end) || this.now();\n\t\t}\n\t\tconst entry = new PerformanceMeasure(measureName, {\n\t\t\tstartTime: start,\n\t\t\tdetail: {\n\t\t\t\tstart,\n\t\t\t\tend\n\t\t\t}\n\t\t});\n\t\tthis._entries.push(entry);\n\t\treturn entry;\n\t}\n\tsetResourceTimingBufferSize(maxSize) {\n\t\tthis._resourceTimingBufferSize = maxSize;\n\t}\n\taddEventListener(type, listener, options) {\n\t\tthrow createNotImplementedError(\"Performance.addEventListener\");\n\t}\n\tremoveEventListener(type, listener, options) {\n\t\tthrow createNotImplementedError(\"Performance.removeEventListener\");\n\t}\n\tdispatchEvent(event) {\n\t\tthrow createNotImplementedError(\"Performance.dispatchEvent\");\n\t}\n\ttoJSON() {\n\t\treturn this;\n\t}\n}\nexport class PerformanceObserver {\n\t__unenv__ = true;\n\tstatic supportedEntryTypes = [];\n\t_callback = null;\n\tconstructor(callback) {\n\t\tthis._callback = callback;\n\t}\n\ttakeRecords() {\n\t\treturn [];\n\t}\n\tdisconnect() {\n\t\tthrow createNotImplementedError(\"PerformanceObserver.disconnect\");\n\t}\n\tobserve(options) {\n\t\tthrow createNotImplementedError(\"PerformanceObserver.observe\");\n\t}\n\tbind(fn) {\n\t\treturn fn;\n\t}\n\trunInAsyncScope(fn, thisArg, ...args) {\n\t\treturn fn.call(thisArg, ...args);\n\t}\n\tasyncId() {\n\t\treturn 0;\n\t}\n\ttriggerAsyncId() {\n\t\treturn 0;\n\t}\n\temitDestroy() {\n\t\treturn this;\n\t}\n}\nexport const performance = globalThis.performance && \"addEventListener\" in globalThis.performance ? globalThis.performance : new Performance();\n", "export const NODE_PERFORMANCE_GC_MAJOR = 4;\nexport const NODE_PERFORMANCE_GC_MINOR = 1;\nexport const NODE_PERFORMANCE_GC_INCREMENTAL = 8;\nexport const NODE_PERFORMANCE_GC_WEAKCB = 16;\nexport const NODE_PERFORMANCE_GC_FLAGS_NO = 0;\nexport const NODE_PERFORMANCE_GC_FLAGS_CONSTRUCT_RETAINED = 2;\nexport const NODE_PERFORMANCE_GC_FLAGS_FORCED = 4;\nexport const NODE_PERFORMANCE_GC_FLAGS_SYNCHRONOUS_PHANTOM_PROCESSING = 8;\nexport const NODE_PERFORMANCE_GC_FLAGS_ALL_AVAILABLE_GARBAGE = 16;\nexport const NODE_PERFORMANCE_GC_FLAGS_ALL_EXTERNAL_MEMORY = 32;\nexport const NODE_PERFORMANCE_GC_FLAGS_SCHEDULE_IDLE = 64;\nexport const NODE_PERFORMANCE_ENTRY_TYPE_GC = 0;\nexport const NODE_PERFORMANCE_ENTRY_TYPE_HTTP = 1;\nexport const NODE_PERFORMANCE_ENTRY_TYPE_HTTP2 = 2;\nexport const NODE_PERFORMANCE_ENTRY_TYPE_NET = 3;\nexport const NODE_PERFORMANCE_ENTRY_TYPE_DNS = 4;\nexport const NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN_TIMESTAMP = 0;\nexport const NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN = 1;\nexport const NODE_PERFORMANCE_MILESTONE_ENVIRONMENT = 2;\nexport const NODE_PERFORMANCE_MILESTONE_NODE_START = 3;\nexport const NODE_PERFORMANCE_MILESTONE_V8_START = 4;\nexport const NODE_PERFORMANCE_MILESTONE_LOOP_START = 5;\nexport const NODE_PERFORMANCE_MILESTONE_LOOP_EXIT = 6;\nexport const NODE_PERFORMANCE_MILESTONE_BOOTSTRAP_COMPLETE = 7;\n", "import { IntervalHistogram, RecordableHistogram } from \"./internal/perf_hooks/histogram.mjs\";\nimport { performance, Performance, PerformanceEntry, PerformanceMark, PerformanceMeasure, PerformanceObserverEntryList, PerformanceObserver, PerformanceResourceTiming } from \"./internal/perf_hooks/performance.mjs\";\nexport * from \"./internal/perf_hooks/performance.mjs\";\nimport { NODE_PERFORMANCE_GC_MAJOR, NODE_PERFORMANCE_GC_MINOR, NODE_PERFORMANCE_GC_INCREMENTAL, NODE_PERFORMANCE_GC_WEAKCB, NODE_PERFORMANCE_GC_FLAGS_NO, NODE_PERFORMANCE_GC_FLAGS_CONSTRUCT_RETAINED, NODE_PERFORMANCE_GC_FLAGS_FORCED, NODE_PERFORMANCE_GC_FLAGS_SYNCHRONOUS_PHANTOM_PROCESSING, NODE_PERFORMANCE_GC_FLAGS_ALL_AVAILABLE_GARBAGE, NODE_PERFORMANCE_GC_FLAGS_ALL_EXTERNAL_MEMORY, NODE_PERFORMANCE_GC_FLAGS_SCHEDULE_IDLE, NODE_PERFORMANCE_ENTRY_TYPE_GC, NODE_PERFORMANCE_ENTRY_TYPE_HTTP, NODE_PERFORMANCE_ENTRY_TYPE_HTTP2, NODE_PERFORMANCE_ENTRY_TYPE_NET, NODE_PERFORMANCE_ENTRY_TYPE_DNS, NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN_TIMESTAMP, NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN, NODE_PERFORMANCE_MILESTONE_ENVIRONMENT, NODE_PERFORMANCE_MILESTONE_NODE_START, NODE_PERFORMANCE_MILESTONE_V8_START, NODE_PERFORMANCE_MILESTONE_LOOP_START, NODE_PERFORMANCE_MILESTONE_LOOP_EXIT, NODE_PERFORMANCE_MILESTONE_BOOTSTRAP_COMPLETE } from \"./internal/perf_hooks/constants.mjs\";\nexport const constants = {\n\tNODE_PERFORMANCE_GC_MAJOR,\n\tNODE_PERFORMANCE_GC_MINOR,\n\tNODE_PERFORMANCE_GC_INCREMENTAL,\n\tNODE_PERFORMANCE_GC_WEAKCB,\n\tNODE_PERFORMANCE_GC_FLAGS_NO,\n\tNODE_PERFORMANCE_GC_FLAGS_CONSTRUCT_RETAINED,\n\tNODE_PERFORMANCE_GC_FLAGS_FORCED,\n\tNODE_PERFORMANCE_GC_FLAGS_SYNCHRONOUS_PHANTOM_PROCESSING,\n\tNODE_PERFORMANCE_GC_FLAGS_ALL_AVAILABLE_GARBAGE,\n\tNODE_PERFORMANCE_GC_FLAGS_ALL_EXTERNAL_MEMORY,\n\tNODE_PERFORMANCE_GC_FLAGS_SCHEDULE_IDLE,\n\tNODE_PERFORMANCE_ENTRY_TYPE_GC,\n\tNODE_PERFORMANCE_ENTRY_TYPE_HTTP,\n\tNODE_PERFORMANCE_ENTRY_TYPE_HTTP2,\n\tNODE_PERFORMANCE_ENTRY_TYPE_NET,\n\tNODE_PERFORMANCE_ENTRY_TYPE_DNS,\n\tNODE_PERFORMANCE_MILESTONE_TIME_ORIGIN_TIMESTAMP,\n\tNODE_PERFORMANCE_MILESTONE_TIME_ORIGIN,\n\tNODE_PERFORMANCE_MILESTONE_ENVIRONMENT,\n\tNODE_PERFORMANCE_MILESTONE_NODE_START,\n\tNODE_PERFORMANCE_MILESTONE_V8_START,\n\tNODE_PERFORMANCE_MILESTONE_LOOP_START,\n\tNODE_PERFORMANCE_MILESTONE_LOOP_EXIT,\n\tNODE_PERFORMANCE_MILESTONE_BOOTSTRAP_COMPLETE\n};\nexport const monitorEventLoopDelay = function(_options) {\n\treturn new IntervalHistogram();\n};\nexport const createHistogram = function(_options) {\n\treturn new RecordableHistogram();\n};\nexport default {\n\tPerformance,\n\tPerformanceMark,\n\tPerformanceEntry,\n\tPerformanceMeasure,\n\tPerformanceObserverEntryList,\n\tPerformanceObserver,\n\tPerformanceResourceTiming,\n\tperformance,\n\tconstants,\n\tcreateHistogram,\n\tmonitorEventLoopDelay\n};\n"], "mappings": ";;;;;;AACA,IAAM,YAAN,MAAgB;AAAA,EACf,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,OAAO;AAAA,EACd,UAAU;AAAA,EACV,SAAS,OAAO;AAAA,EAChB,QAAQ;AAAA,EACR,cAAc,OAAO,CAAC;AAAA,EACtB,gBAAgB,OAAO,CAAC;AAAA,EACxB,YAAY;AAAA,EACZ,YAAY,OAAO,oBAAoB;AAAA,EACvC,cAAc,oBAAI,IAAI;AAAA,EACtB,oBAAoB,oBAAI,IAAI;AAAA,EAC5B,iBAAiB,aAAa;AAC7B,UAAM,0BAA0B,4BAA4B;AAAA,EAC7D;AAAA,EACA,WAAW,YAAY;AACtB,WAAO,KAAK,YAAY,IAAI,UAAU,KAAK,OAAO;AAAA,EACnD;AAAA,EACA,QAAQ;AACP,UAAM,0BAA0B,iBAAiB;AAAA,EAClD;AACD;AACO,IAAM,oBAAN,cAAgC,UAAU;AAAA,EAChD,SAAS;AACR,WAAO;AAAA,EACR;AAAA,EACA,UAAU;AACT,WAAO;AAAA,EACR;AACD;AACO,IAAM,sBAAN,cAAkC,UAAU;AAAA,EAClD,OAAO,KAAK;AACX,UAAM,0BAA0B,4BAA4B;AAAA,EAC7D;AAAA,EACA,cAAc;AACb,UAAM,0BAA0B,iCAAiC;AAAA,EAClE;AAAA,EACA,IAAI,OAAO;AACV,UAAM,0BAA0B,yBAAyB;AAAA,EAC1D;AACD;;;ACzCA,IAAM,cAAc,WAAW,aAAa,cAAc,KAAK,IAAI;AACnE,IAAM,kBAAkB,WAAW,aAAa,MAAM,WAAW,YAAY,IAAI,KAAK,WAAW,WAAW,IAAI,MAAM,KAAK,IAAI,IAAI;AACnI,IAAM,aAAa;AAAA,EAClB,MAAM;AAAA,EACN,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,EACR,SAAS;AACR,WAAO;AAAA,EACR;AACD;AACO,IAAM,mBAAN,MAAuB;AAAA,EAC7B,YAAY;AAAA,EACZ;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA,YAAY,MAAM,SAAS;AAC1B,SAAK,OAAO;AACZ,SAAK,YAAY,SAAS,aAAa,gBAAgB;AACvD,SAAK,SAAS,SAAS;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACd,WAAO,gBAAgB,IAAI,KAAK;AAAA,EACjC;AAAA,EACA,SAAS;AACR,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,WAAW,KAAK;AAAA,MAChB,WAAW,KAAK;AAAA,MAChB,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,IACd;AAAA,EACD;AACD;AACO,IAAM,kBAAkB,MAAMA,yBAAwB,iBAAiB;AAAA,EAC7E,YAAY;AAAA,EACZ,cAAc;AACb,UAAM,GAAG,SAAS;AAAA,EACnB;AAAA,EACA,IAAI,WAAW;AACd,WAAO;AAAA,EACR;AACD;AACO,IAAM,qBAAN,cAAiC,iBAAiB;AAAA,EACxD,YAAY;AACb;AACO,IAAM,4BAAN,cAAwC,iBAAiB;AAAA,EAC/D,YAAY;AAAA,EACZ,eAAe,CAAC;AAAA,EAChB,aAAa;AAAA,EACb,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,wBAAwB;AAAA,EACxB,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,cAAc;AAAA,EACd,iBAAiB;AAClB;AACO,IAAM,+BAAN,MAAmC;AAAA,EACzC,YAAY;AAAA,EACZ,aAAa;AACZ,WAAO,CAAC;AAAA,EACT;AAAA,EACA,iBAAiB,OAAO,OAAO;AAC9B,WAAO,CAAC;AAAA,EACT;AAAA,EACA,iBAAiB,MAAM;AACtB,WAAO,CAAC;AAAA,EACT;AACD;AACO,IAAM,cAAN,MAAkB;AAAA,EACxB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc,oBAAI,IAAI;AAAA,EACtB,WAAW,CAAC;AAAA,EACZ,4BAA4B;AAAA,EAC5B,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS,KAAK,UAAU;AACvB,UAAM,0BAA0B,sBAAsB;AAAA,EACvD;AAAA,EACA,IAAI,aAAa;AAChB,WAAO;AAAA,EACR;AAAA,EACA,uBAAuB;AACtB,WAAO,CAAC;AAAA,EACT;AAAA,EACA,qBAAqB;AACpB,WAAO,IAAI,0BAA0B,EAAE;AAAA,EACxC;AAAA,EACA,6BAA6B;AAAA,EAC7B,MAAM;AACL,QAAI,KAAK,eAAe,aAAa;AACpC,aAAO,gBAAgB;AAAA,IACxB;AACA,WAAO,KAAK,IAAI,IAAI,KAAK;AAAA,EAC1B;AAAA,EACA,WAAW,UAAU;AACpB,SAAK,WAAW,WAAW,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,QAAQ,IAAI,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,MAAM;AAAA,EACjI;AAAA,EACA,cAAc,aAAa;AAC1B,SAAK,WAAW,cAAc,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,WAAW,IAAI,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,SAAS;AAAA,EAC1I;AAAA,EACA,uBAAuB;AACtB,SAAK,WAAW,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,cAAc,EAAE,cAAc,YAAY;AAAA,EACvG;AAAA,EACA,aAAa;AACZ,WAAO,KAAK;AAAA,EACb;AAAA,EACA,iBAAiB,MAAM,MAAM;AAC5B,WAAO,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,SAAS,CAAC,QAAQ,EAAE,cAAc,KAAK;AAAA,EACtF;AAAA,EACA,iBAAiB,MAAM;AACtB,WAAO,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,IAAI;AAAA,EACxD;AAAA,EACA,KAAK,MAAM,SAAS;AACnB,UAAM,QAAQ,IAAI,gBAAgB,MAAM,OAAO;AAC/C,SAAK,SAAS,KAAK,KAAK;AACxB,WAAO;AAAA,EACR;AAAA,EACA,QAAQ,aAAa,uBAAuB,SAAS;AACpD,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,0BAA0B,UAAU;AAC9C,cAAQ,KAAK,iBAAiB,uBAAuB,MAAM,EAAE,CAAC,GAAG;AACjE,YAAM,KAAK,iBAAiB,SAAS,MAAM,EAAE,CAAC,GAAG;AAAA,IAClD,OAAO;AACN,cAAQ,OAAO,WAAW,uBAAuB,KAAK,KAAK,KAAK,IAAI;AACpE,YAAM,OAAO,WAAW,uBAAuB,GAAG,KAAK,KAAK,IAAI;AAAA,IACjE;AACA,UAAM,QAAQ,IAAI,mBAAmB,aAAa;AAAA,MACjD,WAAW;AAAA,MACX,QAAQ;AAAA,QACP;AAAA,QACA;AAAA,MACD;AAAA,IACD,CAAC;AACD,SAAK,SAAS,KAAK,KAAK;AACxB,WAAO;AAAA,EACR;AAAA,EACA,4BAA4B,SAAS;AACpC,SAAK,4BAA4B;AAAA,EAClC;AAAA,EACA,iBAAiB,MAAM,UAAU,SAAS;AACzC,UAAM,0BAA0B,8BAA8B;AAAA,EAC/D;AAAA,EACA,oBAAoB,MAAM,UAAU,SAAS;AAC5C,UAAM,0BAA0B,iCAAiC;AAAA,EAClE;AAAA,EACA,cAAc,OAAO;AACpB,UAAM,0BAA0B,2BAA2B;AAAA,EAC5D;AAAA,EACA,SAAS;AACR,WAAO;AAAA,EACR;AACD;AACO,IAAM,sBAAN,MAA0B;AAAA,EAChC,YAAY;AAAA,EACZ,OAAO,sBAAsB,CAAC;AAAA,EAC9B,YAAY;AAAA,EACZ,YAAY,UAAU;AACrB,SAAK,YAAY;AAAA,EAClB;AAAA,EACA,cAAc;AACb,WAAO,CAAC;AAAA,EACT;AAAA,EACA,aAAa;AACZ,UAAM,0BAA0B,gCAAgC;AAAA,EACjE;AAAA,EACA,QAAQ,SAAS;AAChB,UAAM,0BAA0B,6BAA6B;AAAA,EAC9D;AAAA,EACA,KAAK,IAAI;AACR,WAAO;AAAA,EACR;AAAA,EACA,gBAAgB,IAAI,YAAY,MAAM;AACrC,WAAO,GAAG,KAAK,SAAS,GAAG,IAAI;AAAA,EAChC;AAAA,EACA,UAAU;AACT,WAAO;AAAA,EACR;AAAA,EACA,iBAAiB;AAChB,WAAO;AAAA,EACR;AAAA,EACA,cAAc;AACb,WAAO;AAAA,EACR;AACD;AACO,IAAM,cAAc,WAAW,eAAe,sBAAsB,WAAW,cAAc,WAAW,cAAc,IAAI,YAAY;;;ACvNtI,IAAM,4BAA4B;AAClC,IAAM,4BAA4B;AAClC,IAAM,kCAAkC;AACxC,IAAM,6BAA6B;AACnC,IAAM,+BAA+B;AACrC,IAAM,+CAA+C;AACrD,IAAM,mCAAmC;AACzC,IAAM,2DAA2D;AACjE,IAAM,kDAAkD;AACxD,IAAM,gDAAgD;AACtD,IAAM,0CAA0C;AAChD,IAAM,iCAAiC;AACvC,IAAM,mCAAmC;AACzC,IAAM,oCAAoC;AAC1C,IAAM,kCAAkC;AACxC,IAAM,kCAAkC;AACxC,IAAM,mDAAmD;AACzD,IAAM,yCAAyC;AAC/C,IAAM,yCAAyC;AAC/C,IAAM,wCAAwC;AAC9C,IAAM,sCAAsC;AAC5C,IAAM,wCAAwC;AAC9C,IAAM,uCAAuC;AAC7C,IAAM,gDAAgD;;;ACnBtD,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AACO,IAAM,wBAAwB,SAAS,UAAU;AACvD,SAAO,IAAI,kBAAkB;AAC9B;AACO,IAAM,kBAAkB,SAAS,UAAU;AACjD,SAAO,IAAI,oBAAoB;AAChC;AACA,IAAO,qBAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": ["PerformanceMark"]}