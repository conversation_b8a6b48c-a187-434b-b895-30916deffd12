{"version": 3, "names": ["NativeRNMBXChangeLineOffsetsShapeAnimatorModule", "ShapeAnimatorManager", "ChangeLineOffsetsShapeAnimator", "constructor", "args", "tag", "nextTag", "create", "coordinates", "startOffset", "endOffset", "__nativeTag", "setLineString", "setStartOffset", "offset", "durationMs", "setEndOffset"], "sourceRoot": "../../../src", "sources": ["shapeAnimators/ChangeLineOffsetsShapeAnimator.ts"], "mappings": ";;AAEA,OAAOA,+CAA+C,MAAM,0DAA0D;AAEtH,OAAOC,oBAAoB,MAAM,wBAAwB;AAIzD,eAAe,MAAMC,8BAA8B,CAEnD;EAGEC,WAAWA,CAACC,IAIX,EAAE;IACD,MAAMC,GAAG,GAAGJ,oBAAoB,CAACK,OAAO,CAAC,CAAC;IAC1CN,+CAA+C,CAACO,MAAM,CACpDF,GAAG,EACHD,IAAI,CAACI,WAAW,EAChBJ,IAAI,CAACK,WAAW,EAChBL,IAAI,CAACM,SACP,CAAC;IACD,IAAI,CAACC,WAAW,GAAGN,GAAG;EACxB;EAEAO,aAAaA,CAACR,IAIb,EAAE;IACDJ,+CAA+C,CAACY,aAAa,CAC3D,IAAI,CAACD,WAAW,EAChBP,IAAI,CAACI,WAAW,EAChBJ,IAAI,CAACK,WAAW,IAAI,CAAC,CAAC,EACtBL,IAAI,CAACM,SAAS,IAAI,CAAC,CACrB,CAAC;EACH;EAEAG,cAAcA,CAACT,IAA4C,EAAE;IAC3DJ,+CAA+C,CAACa,cAAc,CAC5D,IAAI,CAACF,WAAW,EAChBP,IAAI,CAACU,MAAM,EACXV,IAAI,CAACW,UACP,CAAC;EACH;EAEAC,YAAYA,CAACZ,IAA4C,EAAE;IACzDJ,+CAA+C,CAACgB,YAAY,CAC1D,IAAI,CAACL,WAAW,EAChBP,IAAI,CAACU,MAAM,EACXV,IAAI,CAACW,UACP,CAAC;EACH;AACF", "ignoreList": []}