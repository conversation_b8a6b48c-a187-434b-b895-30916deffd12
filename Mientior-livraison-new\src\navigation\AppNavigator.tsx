import React, { useEffect, useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthStore } from '../store/authStore';
import { RootStackParamList } from '../types';
import { parseLocationFromString, stringifyLocation } from '../utils/locationUtils';

// Debug utilities temporairement désactivées pour résoudre les erreurs de build

// Écrans d'authentification et onboarding
import LoadingScreen from '../screens/LoadingScreen';
import { OnboardingCarouselScreen } from '../screens/client/OnboardingCarouselScreen';
import LanguageSelectionScreen from '../screens/auth/LanguageSelectionScreen';
import LocationPermissionScreen from '../screens/auth/LocationPermissionScreen';
import { AuthChoiceScreen } from '../screens/client/AuthChoiceScreen';
import SignInScreen from '../screens/client/SignInScreen';
import SignUpScreen from '../screens/client/SignUpScreen';
import OTPVerificationScreen from '../screens/client/OTPVerificationScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';
import ResetPasswordScreen from '../screens/auth/ResetPasswordScreen';
import RoleSelectionScreen from '../screens/auth/RoleSelectionScreen';

// Navigateurs par rôle
import ClientNavigator from './ClientNavigator';
import DeliveryNavigator from './DeliveryNavigator';
import MerchantNavigator from './MerchantNavigator';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  console.log('🚀🚀🚀 APPNAVIGATOR DÉMARRÉ - DÉBUT DU COMPOSANT 🚀🚀🚀');

  const {
    user,
    isAuthenticated,
    loadUserAddresses,
    setCurrentLocation,
    setLocationPermission
  } = useAuthStore();
  const [isFirstTime, setIsFirstTime] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [initializationComplete, setInitializationComplete] = useState(false);
  const [minSplashTimeComplete, setMinSplashTimeComplete] = useState(false);

  console.log('📊 États initiaux:', { isLoading, initializationComplete, minSplashTimeComplete, isFirstTime });



  // Délai minimum pour le SplashScreen (éviter le clignotement)
  useEffect(() => {
    console.log('🕐 Démarrage timer splash minimum (2s)');

    const minSplashTimer = setTimeout(() => {
      console.log('✅ Timer splash minimum terminé');
      setMinSplashTimeComplete(true);
    }, 2000); // 2 secondes minimum

    return () => clearTimeout(minSplashTimer);
  }, []);

  // Timeout de sécurité pour éviter le chargement infini - RÉDUIT À 8 SECONDES
  useEffect(() => {
    console.log('⏰ Démarrage timeout de sécurité (8s)');

    const timeout = setTimeout(() => {
      console.warn('TIMEOUT DE SÉCURITÉ ACTIVÉ - Forcer la fin du chargement après 8 secondes');
      console.log('📊 État actuel', {
        initializationComplete,
        minSplashTimeComplete,
        isFirstTime,
        isLoading
      });

      // Mode de récupération d'urgence
      setInitializationComplete(true);
      setMinSplashTimeComplete(true);
      if (isFirstTime === null) {
        console.warn('🚨 Mode de récupération: Définir isFirstTime = true');
        setIsFirstTime(true); // Par défaut, montrer l'onboarding
      }
    }, 8000); // 8 secondes maximum au lieu de 15

    return () => clearTimeout(timeout);
  }, []); // Supprimer la dépendance isFirstTime pour éviter les re-créations

  // Initialiser l'application et charger les données utilisateur
  useEffect(() => {
    let isMounted = true; // Éviter les mises à jour si le composant est démonté

    const initializeApp = async () => {
      try {
        console.log('🚀 Début initialisation app');

        // DÉSACTIVATION TEMPORAIRE de l'initialisation auth pour éviter les timeouts
        console.log('⚠️ Initialisation auth désactivée temporairement');
        try {
          // Initialisation simplifiée sans timeout
          console.log('🔄 Initialisation auth simplifiée...');
          // await initialize(); // Désactivé temporairement
        } catch (initError) {
          console.error('Erreur initialisation auth, continuer quand même', initError);
          console.log('🔄 Continuant sans authentification initialisée');
        }

        // Vérifier si c'est la première fois avec timeout
        try {
          const hasSeenOnboarding = await Promise.race([
            AsyncStorage.getItem('hasSeenOnboarding'),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout AsyncStorage onboarding')), 2000)
            )
          ]);
          if (isMounted) {
            setIsFirstTime(hasSeenOnboarding === null);
            console.log('📱 Premier lancement:', hasSeenOnboarding === null);
          }
        } catch (error) {
          console.error('⚠️ Erreur lecture onboarding, défaut = true:', error);
          if (isMounted) {
            setIsFirstTime(true); // Par défaut si erreur
          }
        }

        // Charger les données de localisation sauvegardées avec timeout
        try {
          const locationPermission = await Promise.race([
            AsyncStorage.getItem('locationPermissionGranted'),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout AsyncStorage location')), 2000)
            )
          ]);
          if (locationPermission && isMounted) {
            setLocationPermission(locationPermission === 'true');
            console.log('📍 Permission localisation:', locationPermission);
          }
        } catch (error) {
          console.error('⚠️ Erreur lecture permission localisation:', error);
        }

        try {
          const lastLocation = await Promise.race([
            AsyncStorage.getItem('lastKnownLocation'),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout AsyncStorage lastLocation')), 2000)
            )
          ]) as string | null;
          if (lastLocation && isMounted) {
            const location = parseLocationFromString(lastLocation);
            if (location) {
              setCurrentLocation(location);
              console.log('🗺️ Dernière position chargée');
            } else {
              console.warn('⚠️ Données de localisation invalides, nettoyage');
              // Nettoyer les données corrompues
              await AsyncStorage.removeItem('lastKnownLocation');
            }
          }
        } catch (error) {
          console.error('⚠️ Erreur lecture dernière localisation:', error);
        }

      } catch (error) {
        console.error('❌ Erreur initialisation app:', error);
        if (isMounted) {
          setIsFirstTime(true); // Par défaut, montrer l'onboarding
        }
      } finally {
        console.log('🏁 Fin initialisation app');
        if (isMounted) {
          setInitializationComplete(true);
        }
      }
    };

    initializeApp();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []);

  // Gérer la fin du chargement quand l'initialisation ET le délai minimum sont terminés
  useEffect(() => {
    if (initializationComplete && minSplashTimeComplete) {
      console.log('✅ Conditions remplies - fin du chargement');
      setIsLoading(false);
    }
  }, [initializationComplete, minSplashTimeComplete]);

  // Charger les adresses utilisateur quand l'utilisateur est authentifié
  useEffect(() => {
    if (isAuthenticated && user && user.role) {
      loadUserAddresses().catch(error => {
        console.error('Erreur chargement adresses:', error);
      });
    }
  }, [isAuthenticated, user?.id, user?.role]);

  // Fonction pour marquer l'onboarding comme terminé
  const markOnboardingComplete = async () => {
    try {
      await AsyncStorage.setItem('hasSeenOnboarding', 'true');
      setIsFirstTime(false);
    } catch (error) {
      // Erreur silencieuse - l'onboarding continuera de s'afficher
    }
  };

  // Bypass simplifié pour éviter les boucles infinies - RÉDUIT À 3 SECONDES
  useEffect(() => {
    const bypassTimer = setTimeout(() => {
      setInitializationComplete(true);
      setMinSplashTimeComplete(true);
      setIsLoading(false);
      if (isFirstTime === null) {
        setIsFirstTime(true);
      }
    }, 3000); // Réduit à 3 secondes

    return () => clearTimeout(bypassTimer);
  }, []); // Pas de dépendances pour éviter les re-exécutions

  // Forcer les valeurs par défaut pour débloquer l'app
  const forcedIsFirstTime = isFirstTime === null ? true : isFirstTime;

  // Écran de chargement simplifié
  if (isLoading && initializationComplete === false) {
    return (
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Loading" component={LoadingScreen} />
      </Stack.Navigator>
    );
  }

  // Si l'utilisateur n'est pas authentifié
  if (!isAuthenticated || !user) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
        initialRouteName={forcedIsFirstTime ? "Onboarding" : "AuthChoiceScreen"}
      >
        {/* Workflow d'onboarding complet */}
        <Stack.Screen name="Onboarding" component={OnboardingCarouselScreen} />
        <Stack.Screen name="LanguageSelection" component={LanguageSelectionScreen} />
        <Stack.Screen name="LocationPermission" component={LocationPermissionScreen} />

        {/* Écrans d'authentification */}
        <Stack.Screen name="AuthChoiceScreen" component={AuthChoiceScreen} />
        <Stack.Screen name="SignInScreen" component={SignInScreen} />
        <Stack.Screen name="SignUpScreen" component={SignUpScreen} />
        <Stack.Screen name="ForgotPasswordScreen" component={ForgotPasswordScreen} />
        <Stack.Screen name="ResetPasswordScreen" component={ResetPasswordScreen} />
        <Stack.Screen name="OTPVerification" component={OTPVerificationScreen} />


      </Stack.Navigator>
    );
  }

  // Si l'utilisateur est authentifié mais n'a pas de rôle défini
  if (!user.role) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
      >
        <Stack.Screen name="RoleSelection" component={RoleSelectionScreen} />
      </Stack.Navigator>
    );
  }

  // Navigateur selon le rôle utilisateur
  const getRoleNavigator = () => {
    switch (user.role) {
      case 'client':
        return ClientNavigator;
      case 'livreur':
        return DeliveryNavigator;
      case 'marchand':
        return MerchantNavigator;
      default:
        return ClientNavigator; // Par défaut, interface client
    }
  };

  const RoleNavigator = getRoleNavigator();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: false,
      }}
    >
      <Stack.Screen name="Main" component={RoleNavigator} />
    </Stack.Navigator>
  );
};

export default AppNavigator;
