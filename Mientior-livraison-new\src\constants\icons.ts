// Icon configuration for <PERSON><PERSON>ior Livraison
// Using @expo/vector-icons with African design aesthetic
// Primary color: #0DCAA8 (African green)

import { Ionicons, MaterialIcons, FontAwesome5, Feather } from '@expo/vector-icons';

// African Design Color Palette
export const IconColors = {
  primary: '#0DCAA8',      // African green (main brand color)
  secondary: '#09A687',    // Darker green
  accent: '#FF6B6B',       // Coral red (for alerts/errors)
  warning: '#FFA500',      // Orange (for warnings)
  success: '#0DCAA8',      // Same as primary for consistency
  error: '#FF6B6B',        // Coral red
  info: '#4A90E2',         // Blue
  neutral: '#6B7280',      // Gray
  white: '#FFFFFF',
  black: '#1A1A1A',
} as const;

// Standard icon sizes following mobile design guidelines
export const IconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  xxl: 48,
} as const;

// Icon component configurations with African aesthetic
export const AppIcons = {
  // Navigation Icons
  home: {
    component: Ionicons,
    name: 'home' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  search: {
    component: Ionicons,
    name: 'search' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  orders: {
    component: Ionicons,
    name: 'receipt' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  profile: {
    component: Ionicons,
    name: 'person' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },

  // Location Icons
  location: {
    component: Ionicons,
    name: 'location' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  locationOutline: {
    component: Ionicons,
    name: 'location-outline' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  navigate: {
    component: Ionicons,
    name: 'navigate' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  map: {
    component: Ionicons,
    name: 'map' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },

  // Delivery Icons
  bicycle: {
    component: Ionicons,
    name: 'bicycle' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  car: {
    component: Ionicons,
    name: 'car' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  time: {
    component: Ionicons,
    name: 'time' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  checkmark: {
    component: Ionicons,
    name: 'checkmark-circle' as const,
    size: IconSizes.lg,
    color: IconColors.success,
  },

  // Food & Restaurant Icons
  restaurant: {
    component: Ionicons,
    name: 'restaurant' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  fastFood: {
    component: Ionicons,
    name: 'fast-food' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  pizza: {
    component: Ionicons,
    name: 'pizza' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  cafe: {
    component: Ionicons,
    name: 'cafe' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },

  // Shopping Icons
  bag: {
    component: Ionicons,
    name: 'bag' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  cart: {
    component: Ionicons,
    name: 'cart' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  basket: {
    component: Ionicons,
    name: 'basket' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },

  // Communication Icons
  call: {
    component: Ionicons,
    name: 'call' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  chatbubble: {
    component: Ionicons,
    name: 'chatbubble' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  mail: {
    component: Ionicons,
    name: 'mail' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },

  // Status Icons
  success: {
    component: Ionicons,
    name: 'checkmark-circle' as const,
    size: IconSizes.xl,
    color: IconColors.success,
  },
  error: {
    component: Ionicons,
    name: 'close-circle' as const,
    size: IconSizes.xl,
    color: IconColors.error,
  },
  warning: {
    component: Ionicons,
    name: 'warning' as const,
    size: IconSizes.xl,
    color: IconColors.warning,
  },
  info: {
    component: Ionicons,
    name: 'information-circle' as const,
    size: IconSizes.xl,
    color: IconColors.info,
  },

  // UI Control Icons
  back: {
    component: Ionicons,
    name: 'arrow-back' as const,
    size: IconSizes.lg,
    color: IconColors.black,
  },
  forward: {
    component: Ionicons,
    name: 'arrow-forward' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  close: {
    component: Ionicons,
    name: 'close' as const,
    size: IconSizes.lg,
    color: IconColors.black,
  },
  menu: {
    component: Ionicons,
    name: 'menu' as const,
    size: IconSizes.lg,
    color: IconColors.black,
  },
  more: {
    component: Ionicons,
    name: 'ellipsis-horizontal' as const,
    size: IconSizes.lg,
    color: IconColors.neutral,
  },

  // Rating & Feedback Icons
  star: {
    component: Ionicons,
    name: 'star' as const,
    size: IconSizes.md,
    color: IconColors.warning,
  },
  starOutline: {
    component: Ionicons,
    name: 'star-outline' as const,
    size: IconSizes.md,
    color: IconColors.neutral,
  },
  heart: {
    component: Ionicons,
    name: 'heart' as const,
    size: IconSizes.md,
    color: IconColors.error,
  },
  heartOutline: {
    component: Ionicons,
    name: 'heart-outline' as const,
    size: IconSizes.md,
    color: IconColors.neutral,
  },

  // Payment Icons
  card: {
    component: Ionicons,
    name: 'card' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  cash: {
    component: Ionicons,
    name: 'cash' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  wallet: {
    component: Ionicons,
    name: 'wallet' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },

  // Settings Icons
  settings: {
    component: Ionicons,
    name: 'settings' as const,
    size: IconSizes.lg,
    color: IconColors.neutral,
  },
  notifications: {
    component: Ionicons,
    name: 'notifications' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  language: {
    component: Ionicons,
    name: 'language' as const,
    size: IconSizes.lg,
    color: IconColors.primary,
  },
  help: {
    component: Ionicons,
    name: 'help-circle' as const,
    size: IconSizes.lg,
    color: IconColors.info,
  },
} as const;

// Helper function to create icon component with African design consistency
export const createIcon = (
  iconKey: keyof typeof AppIcons,
  overrides?: {
    size?: number;
    color?: string;
    accessibilityLabel?: string;
  }
) => {
  const iconConfig = AppIcons[iconKey];
  const IconComponent = iconConfig.component;
  
  return {
    Component: IconComponent,
    name: iconConfig.name,
    size: overrides?.size ?? iconConfig.size,
    color: overrides?.color ?? iconConfig.color,
    accessibilityLabel: overrides?.accessibilityLabel ?? `${iconKey} icon`,
  };
};

// African-themed icon sets for specific categories
export const CategoryIcons = {
  food: ['restaurant', 'fastFood', 'pizza', 'cafe'] as const,
  delivery: ['bicycle', 'car', 'time', 'navigate'] as const,
  shopping: ['bag', 'cart', 'basket'] as const,
  communication: ['call', 'chatbubble', 'mail'] as const,
  status: ['success', 'error', 'warning', 'info'] as const,
} as const;

// Accessibility labels for screen readers (multilingual support ready)
export const IconAccessibilityLabels = {
  en: {
    home: 'Home',
    search: 'Search',
    orders: 'Orders',
    profile: 'Profile',
    location: 'Location',
    restaurant: 'Restaurant',
    delivery: 'Delivery',
    success: 'Success',
    error: 'Error',
    // Add more as needed
  },
  fr: {
    home: 'Accueil',
    search: 'Rechercher',
    orders: 'Commandes',
    profile: 'Profil',
    location: 'Localisation',
    restaurant: 'Restaurant',
    delivery: 'Livraison',
    success: 'Succès',
    error: 'Erreur',
    // Add more as needed
  },
} as const;

export type IconKey = keyof typeof AppIcons;
export type IconColor = keyof typeof IconColors;
export type IconSize = keyof typeof IconSizes;
