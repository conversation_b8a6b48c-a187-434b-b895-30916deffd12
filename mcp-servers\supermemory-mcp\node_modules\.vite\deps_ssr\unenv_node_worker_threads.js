import {
  notImplemented
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/internal/worker_threads/broadcast-channel.mjs
var BroadcastChannel = class {
  name = "";
  onmessage = (message) => {
  };
  onmessageerror = (message) => {
  };
  close() {
  }
  postMessage(message) {
  }
  ref() {
    return this;
  }
  unref() {
    return this;
  }
};

// node_modules/unenv/dist/runtime/node/internal/worker_threads/message-port.mjs
import { EventEmitter } from "node:events";
var MessagePort = class extends EventEmitter {
  close() {
  }
  postMessage(value, transferList) {
  }
  ref() {
  }
  unref() {
  }
  start() {
  }
  addEventListener(type, listener) {
    this.on(type, listener);
  }
  removeEventListener(type, listener) {
    this.off(type, listener);
  }
  dispatchEvent(event) {
    return this.emit(event.type, event);
  }
};

// node_modules/unenv/dist/runtime/node/internal/worker_threads/message-channel.mjs
var MessageChannel = class {
  port1 = new MessagePort();
  port2 = new MessagePort();
};

// node_modules/unenv/dist/runtime/node/internal/worker_threads/worker.mjs
import { EventEmitter as EventEmitter2 } from "node:events";
import { Readable } from "node:stream";
var Worker = class extends EventEmitter2 {
  stdin = null;
  stdout = new Readable();
  stderr = new Readable();
  threadId = 0;
  performance = { eventLoopUtilization: () => ({
    idle: 0,
    active: 0,
    utilization: 0
  }) };
  postMessage(_value, _transferList) {
  }
  postMessageToThread(_threadId, _value, _transferList, _timeout) {
    return Promise.resolve();
  }
  ref() {
  }
  unref() {
  }
  terminate() {
    return Promise.resolve(0);
  }
  getHeapSnapshot() {
    return Promise.resolve(new Readable());
  }
};

// node_modules/unenv/dist/runtime/node/worker_threads.mjs
var _environmentData = /* @__PURE__ */ new Map();
var getEnvironmentData = function getEnvironmentData2(key) {
  return _environmentData.get(key);
};
var setEnvironmentData = function setEnvironmentData2(key, value) {
  _environmentData.set(key, value);
};
var isMainThread = true;
var isMarkedAsUntransferable = () => false;
var markAsUntransferable = function markAsUntransferable2(value) {
};
var markAsUncloneable = () => {
};
var moveMessagePortToContext = () => new MessagePort();
var parentPort = null;
var receiveMessageOnPort = () => void 0;
var SHARE_ENV = Symbol.for("nodejs.worker_threads.SHARE_ENV");
var resourceLimits = {};
var threadId = 0;
var workerData = null;
var postMessageToThread = notImplemented("worker_threads.postMessageToThread");
var isInternalThread = false;
var worker_threads_default = {
  BroadcastChannel,
  MessageChannel,
  MessagePort,
  Worker,
  SHARE_ENV,
  getEnvironmentData,
  isMainThread,
  isMarkedAsUntransferable,
  markAsUntransferable,
  markAsUncloneable,
  moveMessagePortToContext,
  parentPort,
  receiveMessageOnPort,
  resourceLimits,
  setEnvironmentData,
  postMessageToThread,
  threadId,
  workerData,
  isInternalThread
};
export {
  BroadcastChannel,
  MessageChannel,
  MessagePort,
  SHARE_ENV,
  Worker,
  worker_threads_default as default,
  getEnvironmentData,
  isInternalThread,
  isMainThread,
  isMarkedAsUntransferable,
  markAsUncloneable,
  markAsUntransferable,
  moveMessagePortToContext,
  parentPort,
  postMessageToThread,
  receiveMessageOnPort,
  resourceLimits,
  setEnvironmentData,
  threadId,
  workerData
};
//# sourceMappingURL=unenv_node_worker_threads.js.map
