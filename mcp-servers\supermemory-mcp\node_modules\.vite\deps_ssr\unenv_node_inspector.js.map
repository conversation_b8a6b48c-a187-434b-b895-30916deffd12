{"version": 3, "sources": ["../../unenv/dist/runtime/node/inspector.mjs"], "sourcesContent": ["import { notImplementedClass, notImplemented } from \"../_internal/utils.mjs\";\nimport noop from \"../mock/noop.mjs\";\nexport const close = noop;\nexport const console = {\n\tdebug: noop,\n\terror: noop,\n\tinfo: noop,\n\tlog: noop,\n\twarn: noop,\n\tdir: noop,\n\tdirxml: noop,\n\ttable: noop,\n\ttrace: noop,\n\tgroup: noop,\n\tgroupCollapsed: noop,\n\tgroupEnd: noop,\n\tclear: noop,\n\tcount: noop,\n\tcountReset: noop,\n\tassert: noop,\n\tprofile: noop,\n\tprofileEnd: noop,\n\ttime: noop,\n\ttimeLog: noop,\n\ttimeStamp: noop\n};\nexport const open = () => ({\n\t__unenv__: true,\n\t[Symbol.dispose]() {\n\t\treturn Promise.resolve();\n\t}\n});\nexport const url = () => undefined;\nexport const waitForDebugger = noop;\nexport const Session = /* @__PURE__ */ notImplementedClass(\"inspector.Session\");\nexport const Network = {\n\tloadingFailed: /* @__PURE__ */ notImplemented(\"inspector.Network.loadingFailed\"),\n\tloadingFinished: /* @__PURE__ */ notImplemented(\"inspector.Network.loadingFinished\"),\n\trequestWillBeSent: /* @__PURE__ */ notImplemented(\"inspector.Network.requestWillBeSent\"),\n\tresponseReceived: /* @__PURE__ */ notImplemented(\"inspector.Network.responseReceived\")\n};\nexport default {\n\tSession,\n\tclose,\n\tconsole,\n\topen,\n\turl,\n\twaitForDebugger,\n\tNetwork\n};\n"], "mappings": ";;;;;;;;;;AAEO,IAAM,QAAQ;AACd,IAAM,UAAU;AAAA,EACtB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AACZ;AACO,IAAM,OAAO,OAAO;AAAA,EAC1B,WAAW;AAAA,EACX,CAAC,OAAO,OAAO,IAAI;AAClB,WAAO,QAAQ,QAAQ;AAAA,EACxB;AACD;AACO,IAAM,MAAM,MAAM;AAClB,IAAM,kBAAkB;AACxB,IAAM,UAA0B,oBAAoB,mBAAmB;AACvE,IAAM,UAAU;AAAA,EACtB,eAA+B,eAAe,iCAAiC;AAAA,EAC/E,iBAAiC,eAAe,mCAAmC;AAAA,EACnF,mBAAmC,eAAe,qCAAqC;AAAA,EACvF,kBAAkC,eAAe,oCAAoC;AACtF;AACA,IAAO,oBAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}