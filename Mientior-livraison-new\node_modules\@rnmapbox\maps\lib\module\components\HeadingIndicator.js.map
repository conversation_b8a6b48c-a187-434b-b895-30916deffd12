{"version": 3, "names": ["React", "headingIcon", "SymbolLayer", "Images", "jsx", "_jsx", "jsxs", "_jsxs", "style", "iconImage", "iconAllowOverlap", "iconPitchAlignment", "iconRotationAlignment", "HeadingIndicator", "heading", "Fragment", "children", "images", "userLocationHeading", "id", "sourceID", "belowLayerID", "iconRotate"], "sourceRoot": "../../../src", "sources": ["components/HeadingIndicator.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAOC,WAAW,MAAM,uBAAuB;AAG/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,MAAMC,KAAK,GAAG;EACZC,SAAS,EAAE,qBAAqB;EAChCC,gBAAgB,EAAE,IAAI;EACtBC,kBAAkB,EAAE,KAAK;EACzBC,qBAAqB,EAAE;AACzB,CAAU;AAMV,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAe,CAAC,KAAK;EAC/C,oBACEP,KAAA,CAACP,KAAK,CAACe,QAAQ;IAAAC,QAAA,gBACbX,IAAA,CAACF,MAAM;MACLc,MAAM,EAAE;QAAEC,mBAAmB,EAAEjB;MAAY;IAAE,GACzC,iCACL,CAAC,eACFI,IAAA,CAACH,WAAW;MAEViB,EAAE,EAAC,oCAAoC;MACvCC,QAAQ,EAAC,oBAAoB;MAC7BC,YAAY,EAAC,+BAA+B;MAC5Cb,KAAK,EAAE;QACLc,UAAU,EAAER,OAAO;QACnB,GAAGN;MACL;IAAE,GAPE,oCAQL,CAAC;EAAA,GAdgB,2CAeJ,CAAC;AAErB,CAAC;AAED,eAAeK,gBAAgB", "ignoreList": []}