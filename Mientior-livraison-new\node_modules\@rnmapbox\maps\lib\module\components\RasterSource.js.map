{"version": 3, "names": ["React", "NativeModules", "cloneReactChildrenWithProps", "RNMBXRasterSourceNativeComponent", "AbstractSource", "jsx", "_jsx", "MapboxGL", "RNMBXModule", "isTileTemplateUrl", "url", "includes", "RasterSource", "defaultProps", "id", "StyleSource", "DefaultSourceID", "constructor", "props", "console", "warn", "render", "tileUrlTemplates", "undefined", "existing", "minZoomLevel", "maxZoomLevel", "tileSize", "tms", "attribution", "ref", "setNativeRef", "children", "sourceID"], "sourceRoot": "../../../src", "sources": ["components/RasterSource.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,cAAc;AAE5C,SAASC,2BAA2B,QAAQ,UAAU;AAEtD,OAAOC,gCAAgC,MAAM,2CAA2C;AAExF,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE9C,MAAMC,QAAQ,GAAGN,aAAa,CAACO,WAAW;AAE1C,MAAMC,iBAAiB,GAAIC,GAAY,IACrC,CAAC,CAACA,GAAG,KACJA,GAAG,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC;AAkE9E;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,SAASR,cAAc,CAAqB;EAC5D,OAAOS,YAAY,GAAU;IAC3BC,EAAE,EAAEP,QAAQ,CAACQ,WAAW,CAACC;EAC3B,CAAC;EAEDC,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAIT,iBAAiB,CAACS,KAAK,CAACR,GAAG,CAAC,EAAE;MAChCS,OAAO,CAACC,IAAI,CACV,gKAAgKF,KAAK,CAACR,GAAG,eAC3K,CAAC;IACH;EACF;EAEAW,MAAMA,CAAA,EAAG;IACP,IAAI;MAAEX;IAAI,CAAC,GAAG,IAAI,CAACQ,KAAK;IACxB,IAAI;MAAEI;IAAiB,CAAC,GAAG,IAAI,CAACJ,KAAK;;IAErC;IACA;IACA,IAAIT,iBAAiB,CAACC,GAAG,CAAC,EAAE;MAC1BY,gBAAgB,GAAG,CAACZ,GAAG,CAAC;MACxBA,GAAG,GAAGa,SAAS;IACjB;IAEA,MAAML,KAAK,GAAG;MACZ,GAAG,IAAI,CAACA,KAAK;MACbJ,EAAE,EAAE,IAAI,CAACI,KAAK,CAACJ,EAAE;MACjBU,QAAQ,EAAE,IAAI,CAACN,KAAK,CAACM,QAAQ;MAC7Bd,GAAG;MACHY,gBAAgB;MAChBG,YAAY,EAAE,IAAI,CAACP,KAAK,CAACO,YAAY;MACrCC,YAAY,EAAE,IAAI,CAACR,KAAK,CAACQ,YAAY;MACrCC,QAAQ,EAAE,IAAI,CAACT,KAAK,CAACS,QAAQ;MAC7BC,GAAG,EAAE,IAAI,CAACV,KAAK,CAACU,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACX,KAAK,CAACW;IAC1B,CAAC;IACD;MAAA;MACE;MACAvB,IAAA,CAACH,gCAAgC;QAAC2B,GAAG,EAAE,IAAI,CAACC,YAAa;QAAA,GAAKb,KAAK;QAAAc,QAAA,EAChE9B,2BAA2B,CAAC,IAAI,CAACgB,KAAK,CAACc,QAAQ,EAAE;UAChDC,QAAQ,EAAE,IAAI,CAACf,KAAK,CAACJ;QACvB,CAAC;MAAC,CAC8B;IAAC;EAEvC;AACF;AAEA,eAAeF,YAAY", "ignoreList": []}