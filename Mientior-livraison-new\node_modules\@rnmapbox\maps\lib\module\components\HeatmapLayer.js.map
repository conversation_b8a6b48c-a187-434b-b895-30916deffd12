{"version": 3, "names": ["React", "NativeModules", "RNMBXHeatmapLayerNativeComponent", "AbstractLayer", "jsx", "_jsx", "Mapbox", "RNMBXModule", "Heatmap<PERSON>ayer", "defaultProps", "sourceID", "StyleSource", "DefaultSourceID", "render", "props", "baseProps", "sourceLayerID", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../../src", "sources": ["components/HeatmapLayer.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,cAAc;AAO5C,OAAOC,gCAAgC,MAAM,2CAA2C;AAExF,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE5C,MAAMC,MAAM,GAAGL,aAAa,CAACM,WAAW;;AAExC;;AA+DA;;AAaA;AACA;AACA;AACA,MAAMC,YAAY,SAASL,aAAa,CAAyB;EAC/D,OAAOM,YAAY,GAAG;IACpBC,QAAQ,EAAEJ,MAAM,CAACK,WAAW,CAACC;EAC/B,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAMC,KAAK,GAAG;MACZ,GAAG,IAAI,CAACA,KAAK;MACb,GAAG,IAAI,CAACC,SAAS;MACjBC,aAAa,EAAE,IAAI,CAACF,KAAK,CAACE;IAC5B,CAAC;IACD;MAAA;MACE;MACAX,IAAA,CAACH,gCAAgC;QAACe,GAAG,EAAE,IAAI,CAACC,cAAe;QAAA,GAAKJ;MAAK,CAAG;IAAC;EAE7E;AACF;AAEA,eAAeN,YAAY", "ignoreList": []}