{"version": 3, "names": ["NativeEventEmitter", "NativeModules", "RNMBXLogging", "<PERSON><PERSON>", "instance", "sharedInstance", "constructor", "loggerEmitter", "startedCount", "logCallback", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setLogLevel", "level", "start", "subscribe", "stop", "unsubscribe", "subscription", "addListener", "log", "onLog", "remove", "effectiveLevel", "message", "tag", "startsWith", "_level", "_levelDisp", "_message", "console", "error", "warn"], "sourceRoot": "../../../src", "sources": ["utils/Logger.ts"], "mappings": ";;AAAA,SAEEA,kBAAkB,EAClBC,aAAa,QACR,cAAc;AACrB,MAAM;EAAEC;AAAa,CAAC,GAAGD,aAAa;AAYtC,MAAME,MAAM,CAAC;EACX,OAAOC,QAAQ,GAAkB,IAAI;EAOrC,OAAOC,cAAcA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACD,QAAQ,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACA,QAAQ,GAAG,IAAID,MAAM,CAAC,CAAC;IAC9B;IACA,OAAO,IAAI,CAACC,QAAQ;EACtB;EAEAE,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,aAAa,GAAG,IAAIP,kBAAkB,CAACE,YAAY,CAAC;IACzD,IAAI,CAACM,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,WAAW,GAAGC,SAAS;EAC9B;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOC,cAAcA,CAACF,WAAwB,EAAE;IAC9C,IAAI,CAACJ,cAAc,CAAC,CAAC,CAACM,cAAc,CAACF,WAAW,CAAC;EACnD;;EAEA;AACF;AACA;AACA;AACA;EACEE,cAAcA,CAACF,WAAwB,EAAE;IACvC,IAAI,CAACA,WAAW,GAAGA,WAAW;EAChC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE;AACF;AACA;AACA;EACE,OAAOG,WAAWA,CAACC,KAAe,EAAE;IAClCX,YAAY,CAACU,WAAW,CAACC,KAAK,CAAC;EACjC;;EAEA;AACF;AACA;EACEC,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACN,YAAY,KAAK,CAAC,EAAE;MAC3B,IAAI,CAACO,SAAS,CAAC,CAAC;IAClB;IACA,IAAI,CAACP,YAAY,IAAI,CAAC;EACxB;EAEAQ,IAAIA,CAAA,EAAG;IACL,IAAI,CAACR,YAAY,IAAI,CAAC;IACtB,IAAI,IAAI,CAACA,YAAY,KAAK,CAAC,EAAE;MAC3B,IAAI,CAACS,WAAW,CAAC,CAAC;IACpB;EACF;EAEAF,SAASA,CAAA,EAAG;IACV,IAAI,CAACG,YAAY,GAAG,IAAI,CAACX,aAAa,CAACY,WAAW,CAAC,UAAU,EAAGC,GAAG,IAAK;MACtE,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;IACjB,CAAC,CAAC;EACJ;EAEAH,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,YAAY,EAAEI,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACJ,YAAY,GAAGR,SAAS;EAC/B;EAEAa,cAAcA,CAACH,GAAc,EAAE;IAC7B,MAAM;MAAEP,KAAK;MAAEW,OAAO;MAAEC;IAAI,CAAC,GAAGL,GAAG;IAEnC,IAAIP,KAAK,KAAK,SAAS,EAAE;MACvB,IACEY,GAAG,KAAK,kBAAkB,IAC1BD,OAAO,CAACE,UAAU,CAAC,mDAAmD,CAAC,EACvE;QACA;QACA,OAAO,MAAM;MACf;IACF;IACA,OAAOb,KAAK;EACd;EAEAQ,KAAKA,CAACD,GAAc,EAAE;IACpB,IAAI,CAAC,IAAI,CAACX,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACW,GAAG,CAAC,EAAE;MAC/C,MAAM;QAAEK,GAAG;QAAED;MAAQ,CAAC,GAAGJ,GAAG;MAE5B,MAAMO,MAAM,GAAG,IAAI,CAACJ,cAAc,CAACH,GAAG,CAAC;MACvC,MAAMQ,UAAU,GAAG,WAAWD,MAAM,GAAG;MAEvC,IAAIE,QAAQ,GAAGL,OAAO;MACtB,IAAIC,GAAG,EAAE;QACPI,QAAQ,GAAG,GAAGJ,GAAG,MAAMI,QAAQ,EAAE;MACnC;MAEA,IAAIF,MAAM,KAAK,OAAO,EAAE;QACtBG,OAAO,CAACC,KAAK,CAACH,UAAU,EAAEC,QAAQ,CAAC;MACrC,CAAC,MAAM,IAAIF,MAAM,KAAK,SAAS,EAAE;QAC/BG,OAAO,CAACE,IAAI,CAACJ,UAAU,EAAEC,QAAQ,CAAC;MACpC,CAAC,MAAM;QACLC,OAAO,CAACV,GAAG,CAACQ,UAAU,EAAEC,QAAQ,CAAC;MACnC;IACF;EACF;AACF;AAEA1B,MAAM,CAACE,cAAc,CAAC,CAAC,CAACS,KAAK,CAAC,CAAC;AAE/B,eAAeX,MAAM", "ignoreList": []}