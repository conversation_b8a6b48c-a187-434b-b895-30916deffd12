{"version": 3, "names": ["React", "useMemo", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SymbolLayer", "LineLayer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FillExtrusionLayer", "<PERSON><PERSON><PERSON><PERSON>", "Heatmap<PERSON>ayer", "VectorSource", "RasterSource", "ImageSource", "ShapeSource", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "toCamelCase", "s", "replace", "$1", "toUpperCase", "toCamelCaseKeys", "oldObj", "newObj", "Object", "keys", "for<PERSON>ach", "key", "value", "includes", "getLayerComponentType", "layer", "type", "console", "warn", "asLayerComponent", "LayerComponent", "style", "paint", "layout", "layerProps", "source", "sourceID", "sourceLayerID", "minzoom", "minZoomLevel", "maxzoom", "maxZoomLevel", "filter", "length", "id", "getTileSourceProps", "sourceProps", "url", "tiles", "tileUrlTemplates", "undefined", "attribution", "scheme", "tms", "getVectorSource", "getRasterSource", "tileSize", "getImageSource", "coordinates", "getShapeSource", "data", "shape", "cluster", "clusterRadius", "clusterMaxZoom", "clusterMaxZoomLevel", "clusterProperties", "buffer", "tolerance", "lineMetrics", "asSourceComponent", "Style", "props", "fetched<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "abortController", "AbortController", "fetchStyleJson", "response", "fetch", "signal", "responseJson", "error", "e", "name", "cleanup", "abort", "layerComponents", "layers", "map", "x", "sources", "sourceComponents", "children"], "sourceRoot": "../../../src", "sources": ["components/Style.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAI3D,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,WAAW,QAAQ,eAAe;AAC3C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,WAAW,QAAQ,eAAe;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,QAAA,IAAAC,SAAA,EAAAC,IAAA,IAAAC,KAAA;AAE5C,SAASC,WAAWA,CAACC,CAAS,EAAU;EACtC,OAAOA,CAAC,CAACC,OAAO,CAAC,eAAe,EAAGC,EAAE,IAAK;IACxC,OAAOA,EAAE,CAACC,WAAW,CAAC,CAAC,CAACF,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;EAC3D,CAAC,CAAC;AACJ;;AAEA;AACA;AACA,SAASG,eAAeA,CAACC,MAAmC,EAE1D;EACA,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,CAAC,CAAC;EACX;EACA,MAAMC,MAAkC,GAAG,CAAC,CAAC;EAC7CC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAAEC,GAAG,IAAK;IACnC,MAAMC,KAAK,GAAGN,MAAM,CAACK,GAAG,CAAC;IACzB,IAAIA,GAAG,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;MACrBN,MAAM,CAACP,WAAW,CAACW,GAAG,CAAC,CAAC,GAAGC,KAAK;IAClC,CAAC,MAAM;MACLL,MAAM,CAACI,GAAG,CAAC,GAAGC,KAAK;IACrB;EACF,CAAC,CAAC;EACF,OAAOL,MAAM;AACf;AAEA,SAASO,qBAAqBA,CAACC,KAAuB,EAAE;EACtD,MAAM;IAAEC;EAAK,CAAC,GAAGD,KAAK;EAEtB,QAAQC,IAAI;IACV,KAAK,QAAQ;MACX,OAAOlC,WAAW;IACpB,KAAK,QAAQ;MACX,OAAOE,WAAW;IACpB,KAAK,QAAQ;MACX,OAAOD,WAAW;IACpB,KAAK,MAAM;MACT,OAAOE,SAAS;IAClB,KAAK,MAAM;MACT,OAAOC,SAAS;IAClB,KAAK,gBAAgB;MACnB,OAAOC,kBAAkB;IAC3B,KAAK,YAAY;MACf,OAAOC,eAAe;IACxB,KAAK,SAAS;MACZ,OAAOC,YAAY;EACvB;EAEA4B,OAAO,CAACC,IAAI,CAAC,sBAAsBF,IAAI,qBAAqB,CAAC;EAE7D,OAAO,IAAI;AACb;AAEA,SAASG,gBAAgBA,CAACJ,KAAsB,EAAE;EAEhD,MAAMK,cAA+D,GACnEN,qBAAqB,CAACC,KAAK,CAA6C;EAC1E,IAAI,CAACK,cAAc,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,MAAMC,KAAK,GAAG;IACZ,GAAGhB,eAAe,CAACU,KAAK,CAACO,KAAK,CAAC;IAC/B,GAAGjB,eAAe,CAACU,KAAK,CAACQ,MAAM;EACjC,CAAC;EAED,MAAMC,UAOL,GAAG,CAAC,CAAC;EAEN,IAAIT,KAAK,CAACU,MAAM,EAAE;IAChBD,UAAU,CAACE,QAAQ,GAAGX,KAAK,CAACU,MAAM;EACpC;EACA,IAAIV,KAAK,CAAC,cAAc,CAAC,EAAE;IACzBS,UAAU,CAACG,aAAa,GAAGZ,KAAK,CAAC,cAAc,CAAC;EAClD;EACA,IAAIA,KAAK,CAACa,OAAO,EAAE;IACjBJ,UAAU,CAACK,YAAY,GAAGd,KAAK,CAACa,OAAO;EACzC;EACA,IAAIb,KAAK,CAACe,OAAO,EAAE;IACjBN,UAAU,CAACO,YAAY,GAAGhB,KAAK,CAACe,OAAO;EACzC;EACA,IAAIf,KAAK,CAACiB,MAAM,EAAE;IAChBR,UAAU,CAACQ,MAAM,GAAGjB,KAAK,CAACiB,MAAM;EAClC;EACA,IAAIxB,MAAM,CAACC,IAAI,CAACY,KAAK,CAAC,CAACY,MAAM,EAAE;IAC7BT,UAAU,CAACH,KAAK,GAAGA,KAAK;EAC1B;EAEA,oBAAO1B,IAAA,CAACyB,cAAc;IAAgBc,EAAE,EAAEnB,KAAK,CAACmB,EAAG;IAAA,GAAKV;EAAU,GAAtCT,KAAK,CAACmB,EAAmC,CAAC;AACxE;AAWA,SAASC,kBAAkBA,CAACV,MAAwB,EAAe;EACjE,MAAMW,WAAwB,GAAG,CAAC,CAAC;EACnC,IAAIX,MAAM,CAACY,GAAG,EAAE;IACdD,WAAW,CAACC,GAAG,GAAGZ,MAAM,CAACY,GAAG;EAC9B;EACA,IAAIZ,MAAM,CAACa,KAAK,EAAE;IAChBF,WAAW,CAACG,gBAAgB,GAAGd,MAAM,CAACa,KAAK;EAC7C;EACA,IAAIb,MAAM,CAACG,OAAO,KAAKY,SAAS,EAAE;IAChCJ,WAAW,CAACP,YAAY,GAAGJ,MAAM,CAACG,OAAO;EAC3C;EACA,IAAIH,MAAM,CAACK,OAAO,KAAKU,SAAS,EAAE;IAChCJ,WAAW,CAACL,YAAY,GAAGN,MAAM,CAACK,OAAO;EAC3C;EACA,IAAIL,MAAM,CAACgB,WAAW,EAAE;IACtBL,WAAW,CAACK,WAAW,GAAGhB,MAAM,CAACgB,WAAW;EAC9C;EACA,IAAIhB,MAAM,CAACiB,MAAM,IAAIjB,MAAM,CAACiB,MAAM,KAAK,KAAK,EAAE;IAC5CN,WAAW,CAACO,GAAG,GAAG,IAAI;EACxB;EACA,OAAOP,WAAW;AACpB;AAEA,SAASQ,eAAeA,CAACV,EAAU,EAAET,MAAwB,EAAE;EAC7D,MAAMW,WAAW,GAAG;IAAE,GAAGD,kBAAkB,CAACV,MAAM;EAAE,CAAC;EACrD,oBAAO9B,IAAA,CAACL,YAAY;IAAU4C,EAAE,EAAEA,EAAG;IAAA,GAAKE;EAAW,GAA3BF,EAA8B,CAAC;AAC3D;AAEA,SAASW,eAAeA,CAACX,EAAU,EAAET,MAAwB,EAAE;EAC7D,MAAMW,WAAgD,GAAG;IACvD,GAAGD,kBAAkB,CAACV,MAAM;EAC9B,CAAC;EACD,IAAIA,MAAM,CAACqB,QAAQ,EAAE;IACnBV,WAAW,CAACU,QAAQ,GAAGrB,MAAM,CAACqB,QAAQ;EACxC;EACA,oBAAOnD,IAAA,CAACJ,YAAY;IAAU2C,EAAE,EAAEA,EAAG;IAAA,GAAKE;EAAW,GAA3BF,EAA8B,CAAC;AAC3D;AAEA,SAASa,cAAcA,CAACb,EAAU,EAAET,MAAwB,EAAE;EAC5D,MAAMW,WAAW,GAAG;IAClBC,GAAG,EAAEZ,MAAM,CAACY,GAAG;IACfW,WAAW,EAAEvB,MAAM,CAACuB;EACtB,CAAC;EACD,oBAAOrD,IAAA,CAACH,WAAW;IAAU0C,EAAE,EAAEA,EAAG;IAAA,GAAKE;EAAW,GAA3BF,EAA8B,CAAC;AAC1D;AAIA,SAASe,cAAcA,CAACf,EAAU,EAAET,MAAwB,EAAE;EAC5D,MAAMW,WAUS,GAAG,CAAC,CAAC;EACpB,IAAIX,MAAM,CAACyB,IAAI,IAAI,OAAOzB,MAAM,CAACyB,IAAI,KAAK,QAAQ,EAAE;IAClDd,WAAW,CAACC,GAAG,GAAGZ,MAAM,CAACyB,IAAI;EAC/B,CAAC,MAAM,IAAIzB,MAAM,CAACyB,IAAI,IAAI,OAAOzB,MAAM,CAACyB,IAAI,KAAK,QAAQ,EAAE;IACzDd,WAAW,CAACe,KAAK,GAAG1B,MAAM,CAACyB,IAAyB;EACtD;EACA,IAAIzB,MAAM,CAAC2B,OAAO,KAAKZ,SAAS,EAAE;IAChCJ,WAAW,CAACgB,OAAO,GAAG3B,MAAM,CAAC2B,OAAO;EACtC;EACA,IAAI3B,MAAM,CAAC4B,aAAa,KAAKb,SAAS,EAAE;IACtCJ,WAAW,CAACiB,aAAa,GAAG5B,MAAM,CAAC4B,aAAa;EAClD;EACA,IAAI5B,MAAM,CAACK,OAAO,KAAKU,SAAS,EAAE;IAChCJ,WAAW,CAACL,YAAY,GAAGN,MAAM,CAACK,OAAO;EAC3C;EACA,IAAIL,MAAM,CAAC6B,cAAc,KAAKd,SAAS,EAAE;IACvCJ,WAAW,CAACmB,mBAAmB,GAAG9B,MAAM,CAAC6B,cAAc;EACzD;EACA,IAAI7B,MAAM,CAAC+B,iBAAiB,KAAKhB,SAAS,EAAE;IAC1CJ,WAAW,CAACoB,iBAAiB,GAAG/B,MAAM,CAAC+B,iBAAiB;EAC1D;EACA,IAAI/B,MAAM,CAACgC,MAAM,KAAKjB,SAAS,EAAE;IAC/BJ,WAAW,CAACqB,MAAM,GAAGhC,MAAM,CAACgC,MAAM;EACpC;EACA,IAAIhC,MAAM,CAACiC,SAAS,KAAKlB,SAAS,EAAE;IAClCJ,WAAW,CAACsB,SAAS,GAAGjC,MAAM,CAACiC,SAAS;EAC1C;EACA,IAAIjC,MAAM,CAACkC,WAAW,KAAKnB,SAAS,EAAE;IACpCJ,WAAW,CAACuB,WAAW,GAAGlC,MAAM,CAACkC,WAAW;EAC9C;EACA,oBAAOhE,IAAA,CAACF,WAAW;IAAUyC,EAAE,EAAEA,EAAG;IAAA,GAAKE;EAAW,GAA3BF,EAA8B,CAAC;AAC1D;AAEA,SAAS0B,iBAAiBA,CAAC1B,EAAU,EAAET,MAAwB,EAAE;EAC/D,QAAQA,MAAM,CAACT,IAAI;IACjB,KAAK,QAAQ;MACX,OAAO4B,eAAe,CAACV,EAAE,EAAET,MAAM,CAAC;IACpC,KAAK,QAAQ;MACX,OAAOoB,eAAe,CAACX,EAAE,EAAET,MAAM,CAAC;IACpC,KAAK,OAAO;MACV,OAAOsB,cAAc,CAACb,EAAE,EAAET,MAAM,CAAC;IACnC,KAAK,SAAS;MACZ,OAAOwB,cAAc,CAACf,EAAE,EAAET,MAAM,CAAC;EACrC;EAEAR,OAAO,CAACC,IAAI,CAAC,uBAAuBO,MAAM,CAACT,IAAI,qBAAqB,CAAC;EAErE,OAAO,IAAI;AACb;AAqDA;AACA;AACA;AACA;AACA;AACA,MAAM6C,KAAK,GAAIC,KAAY,IAAK;EAC9B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAMqF,IAAgB,GACpB,OAAOH,KAAK,CAACG,IAAI,KAAK,QAAQ,GAAGH,KAAK,CAACG,IAAI,GAAGF,WAAW;;EAE3D;EACAlF,SAAS,CAAC,MAAM;IACd,MAAMqF,eAAe,GAAG,IAAIC,eAAe,CAAC,CAAC;IAC7C,MAAMC,cAAc,GAAG,MAAOH,IAAY,IAAK;MAC7C,IAAI;QACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACL,IAAI,EAAE;UACjCM,MAAM,EAAEL,eAAe,CAACK;QAC1B,CAAC,CAAC;QACF,MAAMC,YAAY,GAAG,MAAMH,QAAQ,CAACJ,IAAI,CAAC,CAAC;QAC1CD,cAAc,CAACQ,YAAY,CAAC;MAC9B,CAAC,CAAC,OAAOC,KAAc,EAAE;QACvB,MAAMC,CAAC,GAAGD,KAA0B;QACpC,IAAIC,CAAC,CAACC,IAAI,KAAK,YAAY,EAAE;UAC3B;QACF;QACA,MAAMD,CAAC;MACT;IACF,CAAC;IACD,IAAI,OAAOZ,KAAK,CAACG,IAAI,KAAK,QAAQ,EAAE;MAClCG,cAAc,CAACN,KAAK,CAACG,IAAI,CAAC;IAC5B;IACA,OAAO,SAASW,OAAOA,CAAA,EAAG;MACxBV,eAAe,CAACW,KAAK,CAAC,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAACf,KAAK,CAACG,IAAI,CAAC,CAAC;;EAEhB;EACA,MAAMa,eAAe,GAAGnG,OAAO,CAAC,MAAM;IACpC,IAAI,CAACsF,IAAI,CAACc,MAAM,EAAE;MAChB,OAAO,EAAE;IACX;IACA,OAAOd,IAAI,CAACc,MAAM,CAACC,GAAG,CAAC7D,gBAAgB,CAAC,CAACa,MAAM,CAAEiD,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;EAC7D,CAAC,EAAE,CAAChB,IAAI,CAACc,MAAM,CAAC,CAAC;;EAEjB;EACA,MAAM;IAAEG;EAAQ,CAAC,GAAGjB,IAAI;EACxB,MAAMkB,gBAAgB,GAAGxG,OAAO,CAAC,MAAM;IACrC,IAAI,CAACuG,OAAO,IAAI,CAAC1E,MAAM,CAACC,IAAI,CAACyE,OAAO,CAAC,EAAE;MACrC,OAAO,EAAE;IACX;IACA,OAAO1E,MAAM,CAACC,IAAI,CAACyE,OAAO,CAAC,CACxBF,GAAG,CAAE9C,EAAE,IAAK0B,iBAAiB,CAAC1B,EAAE,EAAEgD,OAAO,CAAChD,EAAE,CAAC,CAAC,CAAC,CAC/CF,MAAM,CAAEiD,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;EACvB,CAAC,EAAE,CAACC,OAAO,CAAC,CAAC;EAEb,oBACEnF,KAAA,CAAAF,SAAA;IAAAuF,QAAA,GACGD,gBAAgB,EAChBL,eAAe;EAAA,CAChB,CAAC;AAEP,CAAC;AAED,eAAejB,KAAK", "ignoreList": []}