import {
  noop_default
} from "./chunk-DQN6H3OM.js";
import {
  notImplemented,
  notImplementedClass
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/inspector.mjs
var close = noop_default;
var console = {
  debug: noop_default,
  error: noop_default,
  info: noop_default,
  log: noop_default,
  warn: noop_default,
  dir: noop_default,
  dirxml: noop_default,
  table: noop_default,
  trace: noop_default,
  group: noop_default,
  groupCollapsed: noop_default,
  groupEnd: noop_default,
  clear: noop_default,
  count: noop_default,
  countReset: noop_default,
  assert: noop_default,
  profile: noop_default,
  profileEnd: noop_default,
  time: noop_default,
  timeLog: noop_default,
  timeStamp: noop_default
};
var open = () => ({
  __unenv__: true,
  [Symbol.dispose]() {
    return Promise.resolve();
  }
});
var url = () => void 0;
var waitForDebugger = noop_default;
var Session = notImplementedClass("inspector.Session");
var Network = {
  loadingFailed: notImplemented("inspector.Network.loadingFailed"),
  loadingFinished: notImplemented("inspector.Network.loadingFinished"),
  requestWillBeSent: notImplemented("inspector.Network.requestWillBeSent"),
  responseReceived: notImplemented("inspector.Network.responseReceived")
};
var inspector_default = {
  Session,
  close,
  console,
  open,
  url,
  waitForDebugger,
  Network
};
export {
  Network,
  Session,
  close,
  console,
  inspector_default as default,
  open,
  url,
  waitForDebugger
};
//# sourceMappingURL=unenv_node_inspector.js.map
