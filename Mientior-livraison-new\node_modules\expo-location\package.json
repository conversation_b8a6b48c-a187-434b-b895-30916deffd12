{"name": "expo-location", "version": "18.1.5", "description": "Allows reading geolocation information from the device. Your app can poll for the current location or subscribe to location update events.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "location", "geolocation", "coords", "geocoding", "compass", "heading"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-location"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/location/", "jest": {"preset": "expo-module-scripts"}, "devDependencies": {"expo-module-scripts": "^4.1.7"}, "peerDependencies": {"expo": "*"}, "gitHead": "49c9d53cf0a9fc8179d1c8f5268beadd141f70ca"}