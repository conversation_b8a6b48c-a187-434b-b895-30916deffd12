var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _defineProperty2=_interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));var _slicedToArray2=_interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));var _objectWithoutProperties2=_interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));var _react=_interopRequireWildcard(require("react"));var _reactNative=require("react-native");var _invariant=_interopRequireDefault(require("invariant"));var _RNCWebViewNativeComponent=_interopRequireWildcard(require("./RNCWebViewNativeComponent"));var _NativeRNCWebViewModule=_interopRequireDefault(require("./NativeRNCWebViewModule"));var _WebViewShared=require("./WebViewShared");var _WebView=_interopRequireDefault(require("./WebView.styles"));var _jsxRuntime=require("react/jsx-runtime");var _excluded=["javaScriptEnabled","cacheEnabled","originWhitelist","useSharedProcessPool","injectedJavaScript","injectedJavaScriptBeforeContentLoaded","startInLoadingState","onNavigationStateChange","onLoadStart","onError","onLoad","onLoadEnd","onLoadProgress","onHttpError","onMessage","renderLoading","renderError","style","containerStyle","source","nativeConfig","allowsInlineMediaPlayback","allowsPictureInPictureMediaPlayback","allowsAirPlayForMediaPlayback","mediaPlaybackRequiresUserAction","incognito","onShouldStartLoadWithRequest"];var _this=this,_jsxFileName="/home/<USER>/code/src/WebView.macos.tsx";function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&Object.prototype.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}var resolveAssetSource=_reactNative.Image.resolveAssetSource;var useWarnIfChanges=function useWarnIfChanges(value,name){var ref=(0,_react.useRef)(value);if(ref.current!==value){console.warn(`Changes to property ${name} do nothing after the initial render.`);ref.current=value;}};var WebViewComponent=(0,_react.forwardRef)(function(_ref,ref){var _ref$javaScriptEnable=_ref.javaScriptEnabled,javaScriptEnabled=_ref$javaScriptEnable===void 0?true:_ref$javaScriptEnable,_ref$cacheEnabled=_ref.cacheEnabled,cacheEnabled=_ref$cacheEnabled===void 0?true:_ref$cacheEnabled,_ref$originWhitelist=_ref.originWhitelist,originWhitelist=_ref$originWhitelist===void 0?_WebViewShared.defaultOriginWhitelist:_ref$originWhitelist,_ref$useSharedProcess=_ref.useSharedProcessPool,useSharedProcessPool=_ref$useSharedProcess===void 0?true:_ref$useSharedProcess,injectedJavaScript=_ref.injectedJavaScript,injectedJavaScriptBeforeContentLoaded=_ref.injectedJavaScriptBeforeContentLoaded,startInLoadingState=_ref.startInLoadingState,onNavigationStateChange=_ref.onNavigationStateChange,onLoadStart=_ref.onLoadStart,onError=_ref.onError,onLoad=_ref.onLoad,onLoadEnd=_ref.onLoadEnd,onLoadProgress=_ref.onLoadProgress,onHttpErrorProp=_ref.onHttpError,onMessageProp=_ref.onMessage,renderLoading=_ref.renderLoading,renderError=_ref.renderError,style=_ref.style,containerStyle=_ref.containerStyle,source=_ref.source,nativeConfig=_ref.nativeConfig,allowsInlineMediaPlayback=_ref.allowsInlineMediaPlayback,_ref$allowsPictureInP=_ref.allowsPictureInPictureMediaPlayback,allowsPictureInPictureMediaPlayback=_ref$allowsPictureInP===void 0?true:_ref$allowsPictureInP,allowsAirPlayForMediaPlayback=_ref.allowsAirPlayForMediaPlayback,mediaPlaybackRequiresUserAction=_ref.mediaPlaybackRequiresUserAction,incognito=_ref.incognito,onShouldStartLoadWithRequestProp=_ref.onShouldStartLoadWithRequest,otherProps=(0,_objectWithoutProperties2.default)(_ref,_excluded);var webViewRef=(0,_react.useRef)(null);var onShouldStartLoadWithRequestCallback=(0,_react.useCallback)(function(shouldStart,_url){var lockIdentifier=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;_NativeRNCWebViewModule.default.shouldStartLoadWithLockIdentifier(!!shouldStart,lockIdentifier);},[]);var _useWebViewLogic=(0,_WebViewShared.useWebViewLogic)({onNavigationStateChange:onNavigationStateChange,onLoad:onLoad,onError:onError,onHttpErrorProp:onHttpErrorProp,onLoadEnd:onLoadEnd,onLoadProgress:onLoadProgress,onLoadStart:onLoadStart,onMessageProp:onMessageProp,startInLoadingState:startInLoadingState,originWhitelist:originWhitelist,onShouldStartLoadWithRequestProp:onShouldStartLoadWithRequestProp,onShouldStartLoadWithRequestCallback:onShouldStartLoadWithRequestCallback}),onLoadingStart=_useWebViewLogic.onLoadingStart,onShouldStartLoadWithRequest=_useWebViewLogic.onShouldStartLoadWithRequest,onMessage=_useWebViewLogic.onMessage,viewState=_useWebViewLogic.viewState,setViewState=_useWebViewLogic.setViewState,lastErrorEvent=_useWebViewLogic.lastErrorEvent,onHttpError=_useWebViewLogic.onHttpError,onLoadingError=_useWebViewLogic.onLoadingError,onLoadingFinish=_useWebViewLogic.onLoadingFinish,onLoadingProgress=_useWebViewLogic.onLoadingProgress,onContentProcessDidTerminate=_useWebViewLogic.onContentProcessDidTerminate;(0,_react.useImperativeHandle)(ref,function(){return{goForward:function goForward(){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.goForward(webViewRef.current);},goBack:function goBack(){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.goBack(webViewRef.current);},reload:function reload(){setViewState('LOADING');if(webViewRef.current){_RNCWebViewNativeComponent.Commands.reload(webViewRef.current);}},stopLoading:function stopLoading(){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.stopLoading(webViewRef.current);},postMessage:function postMessage(data){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.postMessage(webViewRef.current,data);},injectJavaScript:function injectJavaScript(data){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.injectJavaScript(webViewRef.current,data);},requestFocus:function requestFocus(){return webViewRef.current&&_RNCWebViewNativeComponent.Commands.requestFocus(webViewRef.current);}};},[setViewState,webViewRef]);useWarnIfChanges(allowsInlineMediaPlayback,'allowsInlineMediaPlayback');useWarnIfChanges(allowsPictureInPictureMediaPlayback,'allowsPictureInPictureMediaPlayback');useWarnIfChanges(allowsAirPlayForMediaPlayback,'allowsAirPlayForMediaPlayback');useWarnIfChanges(incognito,'incognito');useWarnIfChanges(mediaPlaybackRequiresUserAction,'mediaPlaybackRequiresUserAction');var otherView=null;if(viewState==='LOADING'){otherView=(renderLoading||_WebViewShared.defaultRenderLoading)();}else if(viewState==='ERROR'){var _lastErrorEvent$descr;(0,_invariant.default)(lastErrorEvent!=null,'lastErrorEvent expected to be non-null');otherView=(renderError||_WebViewShared.defaultRenderError)(lastErrorEvent==null?void 0:lastErrorEvent.domain,(lastErrorEvent==null?void 0:lastErrorEvent.code)||0,(_lastErrorEvent$descr=lastErrorEvent==null?void 0:lastErrorEvent.description)!=null?_lastErrorEvent$descr:'');}else if(viewState!=='IDLE'){console.error(`RNCWebView invalid state encountered: ${viewState}`);}var webViewStyles=[_WebView.default.container,_WebView.default.webView,style];var webViewContainerStyle=[_WebView.default.container,containerStyle];var NativeWebView=(nativeConfig==null?void 0:nativeConfig.component)||_RNCWebViewNativeComponent.default;var sourceResolved=resolveAssetSource(source);var newSource=typeof sourceResolved==='object'?Object.entries(sourceResolved).reduce(function(prev,_ref2){var _ref3=(0,_slicedToArray2.default)(_ref2,2),currKey=_ref3[0],currValue=_ref3[1];return Object.assign({},prev,(0,_defineProperty2.default)({},currKey,currKey==='headers'&&currValue&&typeof currValue==='object'?Object.entries(currValue).map(function(_ref4){var _ref5=(0,_slicedToArray2.default)(_ref4,2),key=_ref5[0],value=_ref5[1];return{name:key,value:value};}):currValue));},{}):sourceResolved;var webView=(0,_jsxRuntime.jsx)(NativeWebView,Object.assign({},otherProps,{javaScriptEnabled:javaScriptEnabled,cacheEnabled:cacheEnabled,useSharedProcessPool:useSharedProcessPool,messagingEnabled:typeof onMessageProp==='function',newSource:newSource,onLoadingError:onLoadingError,onLoadingFinish:onLoadingFinish,onLoadingProgress:onLoadingProgress,onLoadingStart:onLoadingStart,onHttpError:onHttpError,onMessage:onMessage,onShouldStartLoadWithRequest:onShouldStartLoadWithRequest,onContentProcessDidTerminate:onContentProcessDidTerminate,injectedJavaScript:injectedJavaScript,injectedJavaScriptBeforeContentLoaded:injectedJavaScriptBeforeContentLoaded,allowsAirPlayForMediaPlayback:allowsAirPlayForMediaPlayback,allowsInlineMediaPlayback:allowsInlineMediaPlayback,allowsPictureInPictureMediaPlayback:allowsPictureInPictureMediaPlayback,incognito:incognito,mediaPlaybackRequiresUserAction:mediaPlaybackRequiresUserAction,ref:webViewRef,source:sourceResolved,style:webViewStyles},nativeConfig==null?void 0:nativeConfig.props),"webViewKey");return(0,_jsxRuntime.jsxs)(_reactNative.View,{style:webViewContainerStyle,children:[webView,otherView]});});var isFileUploadSupported=function(){var _ref6=(0,_asyncToGenerator2.default)(function*(){return true;});return function isFileUploadSupported(){return _ref6.apply(this,arguments);};}();var WebView=Object.assign(WebViewComponent,{isFileUploadSupported:isFileUploadSupported});var _default=exports.default=WebView;