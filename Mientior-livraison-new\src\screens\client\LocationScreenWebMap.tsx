import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
  Dimensions,
  StatusBar,
  Animated,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { WebView } from 'react-native-webview';
import * as Location from 'expo-location';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
}

const LocationScreenWebMap: React.FC = () => {
  const navigation = useNavigation();

  // État local
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [mapRegion, setMapRegion] = useState({
    latitude: 5.348, // Côte d'Ivoire par défaut
    longitude: -4.007,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  const [loading, setLoading] = useState(false);
  const [mapReady, setMapReady] = useState(false);

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Références
  const webViewRef = useRef<WebView>(null);

  useEffect(() => {
    initializeScreen();
  }, []);

  const initializeScreen = async () => {
    // Animations d'entrée
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Demander les permissions
    await requestLocationPermission();
    
    // Marquer la carte comme prête après 2 secondes
    setTimeout(() => {
      setMapReady(true);
    }, 2000);
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission requise',
          'L\'accès à la localisation est nécessaire pour cette fonctionnalité.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.log('Erreur permission:', error);
    }
  };

  const handleCurrentLocationPress = async () => {
    setLoading(true);
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission requise',
          'L\'accès à la localisation est nécessaire pour cette fonctionnalité.',
          [{ text: 'OK' }]
        );
        return;
      }

      console.log('📍 Récupération de la position GPS...');
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const newLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      console.log('✅ Position GPS obtenue:', newLocation);
      setSelectedLocation(newLocation);

      // Obtenir l'adresse
      getAddressFromCoordinates(location.coords.latitude, location.coords.longitude);

      // Centrer la carte WebView
      if (webViewRef.current) {
        const script = `
          if (window.map) {
            window.map.setCenter({lat: ${location.coords.latitude}, lng: ${location.coords.longitude}});
            window.map.setZoom(16);
          }
        `;
        webViewRef.current.postMessage(script);
      }

    } catch (error) {
      console.log('❌ Erreur localisation:', error);
      Alert.alert(
        'Erreur de localisation',
        'Impossible d\'obtenir votre position actuelle. Vérifiez que le GPS est activé.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const getAddressFromCoordinates = async (latitude: number, longitude: number) => {
    try {
      const result = await Location.reverseGeocodeAsync({ latitude, longitude });
      if (result.length > 0) {
        const address = result[0];
        const formattedAddress = `${address.street || ''} ${address.name || ''}, ${address.city || ''}, ${address.country || ''}`.trim();
        console.log('📍 Adresse trouvée:', formattedAddress);
        setSelectedLocation(prev => prev ? { ...prev, address: formattedAddress } : null);
      }
    } catch (error) {
      console.log('Erreur géocodage inverse:', error);
    }
  };

  const handleConfirmLocation = () => {
    if (!selectedLocation) {
      Alert.alert('Aucune position sélectionnée', 'Veuillez sélectionner une position sur la carte.');
      return;
    }

    console.log('✅ Position confirmée:', selectedLocation);
    navigation.goBack();
  };

  const formatAddress = (address: string) => {
    if (address.length > 40) {
      return address.substring(0, 40) + '...';
    }
    return address;
  };

  // HTML pour la carte Google Maps
  const mapHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body { margin: 0; padding: 0; }
        #map { height: 100vh; width: 100%; }
      </style>
    </head>
    <body>
      <div id="map"></div>
      <script>
        let map;
        let marker;
        
        function initMap() {
          map = new google.maps.Map(document.getElementById('map'), {
            center: { lat: ${mapRegion.latitude}, lng: ${mapRegion.longitude} },
            zoom: 13,
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
          });
          
          map.addListener('click', function(event) {
            const lat = event.latLng.lat();
            const lng = event.latLng.lng();
            
            if (marker) {
              marker.setMap(null);
            }
            
            marker = new google.maps.Marker({
              position: { lat: lat, lng: lng },
              map: map,
              title: 'Position sélectionnée'
            });
            
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'locationSelected',
              latitude: lat,
              longitude: lng
            }));
          });
        }
        
        window.addEventListener('message', function(event) {
          try {
            eval(event.data);
          } catch (e) {
            console.log('Error executing script:', e);
          }
        });
      </script>
      <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s&callback=initMap">
      </script>
    </body>
    </html>
  `;

  const handleWebViewMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === 'locationSelected') {
        console.log('🗺️ Position sélectionnée sur WebView:', data);
        setSelectedLocation({
          latitude: data.latitude,
          longitude: data.longitude,
        });
        getAddressFromCoordinates(data.latitude, data.longitude);
      }
    } catch (error) {
      console.log('Erreur parsing message WebView:', error);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      {/* Header */}
      <Animated.View style={[
        styles.header,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>🌐 Carte Web Interactive</Text>
      </Animated.View>

      {/* Search Bar */}
      <Animated.View style={[
        styles.searchContainer,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        <View style={styles.searchInputWrapper}>
          <Ionicons name="search" size={20} color="#9CA3AF" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher une adresse"
            placeholderTextColor="#9CA3AF"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </Animated.View>

      {/* Map Container */}
      <Animated.View style={[
        styles.mapContainer,
        {
          opacity: fadeAnim,
        }
      ]}>
        <WebView
          ref={webViewRef}
          source={{ html: mapHTML }}
          style={styles.webView}
          onMessage={handleWebViewMessage}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          onLoadEnd={() => {
            console.log('🌐 WebView carte chargée');
            setMapReady(true);
          }}
        />

        {/* Map Status Indicator */}
        {!mapReady && (
          <View style={styles.mapLoadingOverlay}>
            <Ionicons name="globe-outline" size={48} color="#0DCAA8" />
            <Text style={styles.mapLoadingText}>Chargement de la carte web...</Text>
          </View>
        )}

        {/* Current Location Button */}
        <TouchableOpacity
          style={styles.currentLocationButton}
          onPress={handleCurrentLocationPress}
          disabled={loading}
        >
          <Ionicons
            name="locate"
            size={24}
            color={loading ? "#9CA3AF" : "#0DCAA8"}
          />
        </TouchableOpacity>

        {/* Map Type Info */}
        <View style={styles.mapTypeInfo}>
          <Text style={styles.mapTypeText}>🌐 Carte web</Text>
        </View>
      </Animated.View>

      {/* Bottom Sheet */}
      <Animated.View style={[
        styles.bottomSheet,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        {/* Selected Location Info */}
        {selectedLocation && (
          <View style={styles.locationInfo}>
            <View style={styles.locationHeader}>
              <Ionicons name="location" size={24} color="#0DCAA8" />
              <View style={styles.locationDetails}>
                <Text style={styles.locationTitle}>
                  {selectedLocation.address ? formatAddress(selectedLocation.address) : 'Position sélectionnée'}
                </Text>
                <Text style={styles.locationCoordinates}>
                  📍 Lat: {selectedLocation.latitude.toFixed(6)}, Long: {selectedLocation.longitude.toFixed(6)}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Instructions */}
        {!selectedLocation && (
          <View style={styles.instructions}>
            <Text style={styles.instructionsText}>
              🎯 Touchez la carte pour sélectionner une position ou utilisez le bouton GPS
            </Text>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.confirmButton,
              !selectedLocation && styles.confirmButtonDisabled
            ]}
            onPress={handleConfirmLocation}
            disabled={!selectedLocation}
          >
            <Text style={styles.confirmButtonText}>
              {selectedLocation ? '✅ Confirmer cette position' : 'Sélectionnez une position'}
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* Bottom Indicator */}
      <View style={styles.bottomIndicator}>
        <View style={styles.indicator} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  searchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: 12,
    minHeight: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    paddingVertical: 12,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  webView: {
    flex: 1,
  },
  mapLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  mapLoadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  currentLocationButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  mapTypeInfo: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: 'rgba(13, 202, 168, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  mapTypeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  bottomSheet: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 8,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    maxHeight: screenHeight * 0.4,
  },
  locationInfo: {
    marginBottom: 20,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  locationDetails: {
    flex: 1,
    marginLeft: 12,
  },
  locationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  locationCoordinates: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  instructions: {
    backgroundColor: '#F0FDF4',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#0DCAA8',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 20,
  },
  instructionsText: {
    fontSize: 16,
    color: '#0DCAA8',
    textAlign: 'center',
    fontWeight: '500',
  },
  actionButtons: {
    marginBottom: 24,
  },
  confirmButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    shadowColor: '#0DCAA8',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  confirmButtonDisabled: {
    backgroundColor: '#9CA3AF',
    shadowOpacity: 0.1,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  bottomIndicator: {
    alignItems: 'center',
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    paddingTop: 8,
    backgroundColor: '#FFFFFF',
  },
  indicator: {
    width: 134,
    height: 5,
    backgroundColor: '#000000',
    borderRadius: 3,
  },
});

export default LocationScreenWebMap;
