{"version": 3, "names": ["featureCollection", "point", "feature", "lineString", "distance", "along", "makePoint", "makeLineString", "makeLatLngBounds", "northEastCoordinates", "southWestCoordinates", "makeFeature", "makeFeatureCollection", "features", "options", "addToFeatureCollection", "newFeatureCollection", "newFeature", "calculateDistance", "pointAlongLine"], "sourceRoot": "../../../src", "sources": ["utils/geoUtils.ts"], "mappings": ";;AAAA,SACEA,iBAAiB,EACjBC,KAAK,EACLC,OAAO,EACPC,UAAU,QASL,eAAe;AACtB,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,KAAK,MAAM,aAAa;AAE/B,OAAO,MAAMC,SAAS,GAAGL,KAAK;AAE9B,OAAO,MAAMM,cAAc,GAAGJ,UAAU;AAExC,OAAO,SAASK,gBAAgBA,CAC9BC,oBAA8B,EAC9BC,oBAA8B,EACJ;EAC1B,OAAOV,iBAAiB,CAAC,CACvBC,KAAK,CAACQ,oBAAoB,CAAC,EAC3BR,KAAK,CAACS,oBAAoB,CAAC,CAC5B,CAAC;AACJ;AAEA,OAAO,MAAMC,WAAW,GAAGT,OAAO;AAElC,OAAO,SAASU,qBAAqBA,CACnCC,QAA8B,GAAG,EAAE,EACnCC,OAGC,EACD;EACA,OAAOd,iBAAiB,CAACa,QAAQ,EAAEC,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASC,sBAAsBA,CACpCC,oBAAuC,EACvCC,UAAmB,EACA;EACnB,OAAO;IACL,GAAGD,oBAAoB;IACvBH,QAAQ,EAAE,CAAC,GAAGG,oBAAoB,CAACH,QAAQ,EAAEI,UAAU;EACzD,CAAC;AACH;AAEA,OAAO,MAAMC,iBAAiB,GAAGd,QAAQ;AAEzC,OAAO,MAAMe,cAAc,GAAGd,KAAK", "ignoreList": []}