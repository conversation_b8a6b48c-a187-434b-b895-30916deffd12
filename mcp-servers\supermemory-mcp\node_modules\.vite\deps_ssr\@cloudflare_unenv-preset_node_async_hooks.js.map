{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/async_hooks/async-local-storage.mjs", "../../unenv/dist/runtime/node/internal/async_hooks/async-hook.mjs", "../../unenv/dist/runtime/node/internal/async_hooks/async-resource.mjs", "../../@cloudflare/unenv-preset/dist/runtime/node/async_hooks.mjs"], "sourcesContent": ["class _AsyncLocalStorage {\n\t__unenv__ = true;\n\t_currentStore;\n\t_enterStore;\n\t_enabled = true;\n\tgetStore() {\n\t\treturn this._currentStore ?? this._enterStore;\n\t}\n\tdisable() {\n\t\tthis._enabled = false;\n\t}\n\tenable() {\n\t\tthis._enabled = true;\n\t}\n\tenterWith(store) {\n\t\tthis._enterStore = store;\n\t}\n\trun(store, callback, ...args) {\n\t\tthis._currentStore = store;\n\t\tconst res = callback(...args);\n\t\tthis._currentStore = undefined;\n\t\treturn res;\n\t}\n\texit(callback, ...args) {\n\t\tconst _previousStore = this._currentStore;\n\t\tthis._currentStore = undefined;\n\t\tconst res = callback(...args);\n\t\tthis._currentStore = _previousStore;\n\t\treturn res;\n\t}\n\tstatic snapshot() {\n\t\tthrow new Error(\"[unenv] `AsyncLocalStorage.snapshot` is not implemented!\");\n\t}\n}\nexport const AsyncLocalStorage = globalThis.AsyncLocalStorage || _AsyncLocalStorage;\n", "const kInit = /* @__PURE__ */ Symbol(\"init\");\nconst kBefore = /* @__PURE__ */ Symbol(\"before\");\nconst kAfter = /* @__PURE__ */ Symbol(\"after\");\nconst kDestroy = /* @__PURE__ */ Symbol(\"destroy\");\nconst kPromiseResolve = /* @__PURE__ */ Symbol(\"promiseResolve\");\nclass _AsyncHook {\n\t__unenv__ = true;\n\t_enabled = false;\n\t_callbacks = {};\n\tconstructor(callbacks = {}) {\n\t\tthis._callbacks = callbacks;\n\t}\n\tenable() {\n\t\tthis._enabled = true;\n\t\treturn this;\n\t}\n\tdisable() {\n\t\tthis._enabled = false;\n\t\treturn this;\n\t}\n\tget [kInit]() {\n\t\treturn this._callbacks.init;\n\t}\n\tget [kBefore]() {\n\t\treturn this._callbacks.before;\n\t}\n\tget [kAfter]() {\n\t\treturn this._callbacks.after;\n\t}\n\tget [kDestroy]() {\n\t\treturn this._callbacks.destroy;\n\t}\n\tget [kPromiseResolve]() {\n\t\treturn this._callbacks.promiseResolve;\n\t}\n}\nexport const createHook = function createHook(callbacks) {\n\tconst asyncHook = new _AsyncHook(callbacks);\n\treturn asyncHook;\n};\nexport const executionAsyncId = function executionAsyncId() {\n\treturn 0;\n};\nexport const executionAsyncResource = function() {\n\treturn Object.create(null);\n};\nexport const triggerAsyncId = function() {\n\treturn 0;\n};\nexport const asyncWrapProviders = Object.assign(Object.create(null), {\n\tNONE: 0,\n\tDIRHANDLE: 1,\n\tDNSCHANNEL: 2,\n\tELDHISTOGRAM: 3,\n\tFILEHANDLE: 4,\n\tFILEHANDLECLOSEREQ: 5,\n\tBLOBREADER: 6,\n\tFSEVENTWRAP: 7,\n\tFSREQCALLBACK: 8,\n\tFSREQPROMISE: 9,\n\tGETADDRINFOREQWRAP: 10,\n\tGETNAMEINFOREQWRAP: 11,\n\tHEAPSNAPSHOT: 12,\n\tHTTP2SESSION: 13,\n\tHTTP2STREAM: 14,\n\tHTTP2PING: 15,\n\tHTTP2SETTINGS: 16,\n\tHTTPINCOMINGMESSAGE: 17,\n\tHTTPCLIENTREQUEST: 18,\n\tJSSTREAM: 19,\n\tJSUDPWRAP: 20,\n\tMESSAGEPORT: 21,\n\tPIPECONNECTWRAP: 22,\n\tPIPESERVERWRAP: 23,\n\tPIPEWRAP: 24,\n\tPROCESSWRAP: 25,\n\tPROMISE: 26,\n\tQUERYWRAP: 27,\n\tQUIC_ENDPOINT: 28,\n\tQUIC_LOGSTREAM: 29,\n\tQUIC_PACKET: 30,\n\tQUIC_SESSION: 31,\n\tQUIC_STREAM: 32,\n\tQUIC_UDP: 33,\n\tSHUTDOWNWRAP: 34,\n\tSIGNALWRAP: 35,\n\tSTATWATCHER: 36,\n\tSTREAMPIPE: 37,\n\tTCPCONNECTWRAP: 38,\n\tTCPSERVERWRAP: 39,\n\tTCPWRAP: 40,\n\tTTYWRAP: 41,\n\tUDPSENDWRAP: 42,\n\tUDPWRAP: 43,\n\tSIGINTWATCHDOG: 44,\n\tWORKER: 45,\n\tWORKERHEAPSNAPSHOT: 46,\n\tWRITEWRAP: 47,\n\tZLIB: 48,\n\tCHECKPRIMEREQUEST: 49,\n\tPBKDF2REQUEST: 50,\n\tKEYPAIRGENREQUEST: 51,\n\tKEYGENREQUEST: 52,\n\tKEYEXPORTREQUEST: 53,\n\tCIPHERREQUEST: 54,\n\tDERIVEBITSREQUEST: 55,\n\tHASHREQUEST: 56,\n\tRANDOMBYTESREQUEST: 57,\n\tRANDOMPRIMEREQUEST: 58,\n\tSCRYPTREQUEST: 59,\n\tSIGNREQUEST: 60,\n\tTLSWRAP: 61,\n\tVERIFYREQUEST: 62\n});\n", "import { executionAsyncId } from \"./async-hook.mjs\";\nlet _asyncIdCounter = 100;\nclass _AsyncResource {\n\t__unenv__ = true;\n\ttype;\n\t_asyncId;\n\t_triggerAsyncId;\n\tconstructor(type, triggerAsyncId = executionAsyncId()) {\n\t\tthis.type = type;\n\t\tthis._asyncId = -1 * _asyncIdCounter++;\n\t\tthis._triggerAsyncId = typeof triggerAsyncId === \"number\" ? triggerAsyncId : triggerAsyncId?.triggerAsyncId;\n\t}\n\tstatic bind(fn, type, thisArg) {\n\t\tconst resource = new AsyncResource(type ?? \"anonymous\");\n\t\treturn resource.bind(fn);\n\t}\n\tbind(fn, thisArg) {\n\t\tconst binded = (...args) => this.runInAsyncScope(fn, thisArg, ...args);\n\t\tbinded.asyncResource = this;\n\t\treturn binded;\n\t}\n\trunInAsyncScope(fn, thisArg, ...args) {\n\t\tconst result = fn.apply(thisArg, args);\n\t\treturn result;\n\t}\n\temitDestroy() {\n\t\treturn this;\n\t}\n\tasyncId() {\n\t\treturn this._asyncId;\n\t}\n\ttriggerAsyncId() {\n\t\treturn this._triggerAsyncId;\n\t}\n}\nexport const AsyncResource = globalThis.AsyncResource || _AsyncResource;\n", "import {\n  asyncWrapProviders,\n  createHook,\n  executionAsyncId,\n  executionAsyncResource,\n  triggerAsyncId\n} from \"unenv/node/async_hooks\";\nexport {\n  asyncWrapProviders,\n  createHook,\n  executionAsyncId,\n  executionAsyncResource,\n  triggerAsyncId\n} from \"unenv/node/async_hooks\";\nconst workerdAsyncHooks = process.getBuiltinModule(\"node:async_hooks\");\nexport const { AsyncLocalStorage, AsyncResource } = workerdAsyncHooks;\nexport default {\n  /**\n   * manually unroll unenv-polyfilled-symbols to make it tree-shakeable\n   */\n  asyncWrapProviders,\n  createHook,\n  executionAsyncId,\n  executionAsyncResource,\n  triggerAsyncId,\n  /**\n   * manually unroll workerd-polyfilled-symbols to make it tree-shakeable\n   */\n  AsyncLocalStorage,\n  AsyncResource\n};\n"], "mappings": ";;;AAAA,IAAM,qBAAN,MAAyB;AAAA,EACxB,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AACV,WAAO,KAAK,iBAAiB,KAAK;AAAA,EACnC;AAAA,EACA,UAAU;AACT,SAAK,WAAW;AAAA,EACjB;AAAA,EACA,SAAS;AACR,SAAK,WAAW;AAAA,EACjB;AAAA,EACA,UAAU,OAAO;AAChB,SAAK,cAAc;AAAA,EACpB;AAAA,EACA,IAAI,OAAO,aAAa,MAAM;AAC7B,SAAK,gBAAgB;AACrB,UAAM,MAAM,SAAS,GAAG,IAAI;AAC5B,SAAK,gBAAgB;AACrB,WAAO;AAAA,EACR;AAAA,EACA,KAAK,aAAa,MAAM;AACvB,UAAM,iBAAiB,KAAK;AAC5B,SAAK,gBAAgB;AACrB,UAAM,MAAM,SAAS,GAAG,IAAI;AAC5B,SAAK,gBAAgB;AACrB,WAAO;AAAA,EACR;AAAA,EACA,OAAO,WAAW;AACjB,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAC3E;AACD;AACO,IAAM,oBAAoB,WAAW,qBAAqB;;;AClCjE,IAAM,QAAwB,OAAO,MAAM;AAC3C,IAAM,UAA0B,OAAO,QAAQ;AAC/C,IAAM,SAAyB,OAAO,OAAO;AAC7C,IAAM,WAA2B,OAAO,SAAS;AACjD,IAAM,kBAAkC,OAAO,gBAAgB;AAC/D,IAAM,aAAN,MAAiB;AAAA,EAChB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa,CAAC;AAAA,EACd,YAAY,YAAY,CAAC,GAAG;AAC3B,SAAK,aAAa;AAAA,EACnB;AAAA,EACA,SAAS;AACR,SAAK,WAAW;AAChB,WAAO;AAAA,EACR;AAAA,EACA,UAAU;AACT,SAAK,WAAW;AAChB,WAAO;AAAA,EACR;AAAA,EACA,KAAK,KAAK,IAAI;AACb,WAAO,KAAK,WAAW;AAAA,EACxB;AAAA,EACA,KAAK,OAAO,IAAI;AACf,WAAO,KAAK,WAAW;AAAA,EACxB;AAAA,EACA,KAAK,MAAM,IAAI;AACd,WAAO,KAAK,WAAW;AAAA,EACxB;AAAA,EACA,KAAK,QAAQ,IAAI;AAChB,WAAO,KAAK,WAAW;AAAA,EACxB;AAAA,EACA,KAAK,eAAe,IAAI;AACvB,WAAO,KAAK,WAAW;AAAA,EACxB;AACD;AACO,IAAM,aAAa,SAASA,YAAW,WAAW;AACxD,QAAM,YAAY,IAAI,WAAW,SAAS;AAC1C,SAAO;AACR;AACO,IAAM,mBAAmB,SAASC,oBAAmB;AAC3D,SAAO;AACR;AACO,IAAM,yBAAyB,WAAW;AAChD,SAAO,uBAAO,OAAO,IAAI;AAC1B;AACO,IAAM,iBAAiB,WAAW;AACxC,SAAO;AACR;AACO,IAAM,qBAAqB,OAAO,OAAO,uBAAO,OAAO,IAAI,GAAG;AAAA,EACpE,MAAM;AAAA,EACN,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AAAA,EACX,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,aAAa;AAAA,EACb,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,oBAAoB;AAAA,EACpB,WAAW;AAAA,EACX,MAAM;AAAA,EACN,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,aAAa;AAAA,EACb,SAAS;AAAA,EACT,eAAe;AAChB,CAAC;;;AChHD,IAAI,kBAAkB;AACtB,IAAM,iBAAN,MAAqB;AAAA,EACpB,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,MAAMC,kBAAiB,iBAAiB,GAAG;AACtD,SAAK,OAAO;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,kBAAkB,OAAOA,oBAAmB,WAAWA,kBAAiBA,iBAAgB;AAAA,EAC9F;AAAA,EACA,OAAO,KAAK,IAAI,MAAM,SAAS;AAC9B,UAAM,WAAW,IAAI,cAAc,QAAQ,WAAW;AACtD,WAAO,SAAS,KAAK,EAAE;AAAA,EACxB;AAAA,EACA,KAAK,IAAI,SAAS;AACjB,UAAM,SAAS,IAAI,SAAS,KAAK,gBAAgB,IAAI,SAAS,GAAG,IAAI;AACrE,WAAO,gBAAgB;AACvB,WAAO;AAAA,EACR;AAAA,EACA,gBAAgB,IAAI,YAAY,MAAM;AACrC,UAAM,SAAS,GAAG,MAAM,SAAS,IAAI;AACrC,WAAO;AAAA,EACR;AAAA,EACA,cAAc;AACb,WAAO;AAAA,EACR;AAAA,EACA,UAAU;AACT,WAAO,KAAK;AAAA,EACb;AAAA,EACA,iBAAiB;AAChB,WAAO,KAAK;AAAA,EACb;AACD;AACO,IAAM,gBAAgB,WAAW,iBAAiB;;;ACrBzD,IAAM,oBAAoB,QAAQ,iBAAiB,kBAAkB;AAC9D,IAAM,EAAE,mBAAAC,oBAAmB,eAAAC,eAAc,IAAI;AACpD,IAAO,sBAAQ;AAAA;AAAA;AAAA;AAAA,EAIb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAAD;AAAA,EACA,eAAAC;AACF;", "names": ["createHook", "executionAsyncId", "triggerAsyncId", "AsyncLocalStorage", "AsyncResource"]}