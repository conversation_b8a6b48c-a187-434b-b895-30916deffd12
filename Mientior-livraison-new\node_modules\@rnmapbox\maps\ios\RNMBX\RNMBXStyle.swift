// DO NOT MODIFY
// THIS FILE IS AUTOGENERATED

@_spi(Experimental) import MapboxMaps

class RNMBXStyle {
  var style : Style
  weak var bridge : RCTBridge?

  init(style: Style) {
    self.style = style;
  }


func fillLayer(layer: inout FillLayer, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout FillLayer)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "fillSortKey") {
      self.setFillSortKey(&layer, styleValue:styleValue);
    } else if (prop == "visibility") {
      self.setFillStyleLayerVisibility(&layer, styleValue:styleValue);
    } else if (prop == "fillAntialias") {
      self.setFillAntialias(&layer, styleValue:styleValue);
    } else if (prop == "fillOpacity") {
      self.setFillOpacity(&layer, styleValue:styleValue);
    } else if (prop == "fillOpacityTransition") {
      self.setFillOpacityTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillColor") {
      self.setFillColor(&layer, styleValue:styleValue);
    } else if (prop == "fillColorTransition") {
      self.setFillColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillOutlineColor") {
      self.setFillOutlineColor(&layer, styleValue:styleValue);
    } else if (prop == "fillOutlineColorTransition") {
      self.setFillOutlineColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillTranslate") {
      self.setFillTranslate(&layer, styleValue:styleValue);
    } else if (prop == "fillTranslateTransition") {
      self.setFillTranslateTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillTranslateAnchor") {
      self.setFillTranslateAnchor(&layer, styleValue:styleValue);
    } else if (prop == "fillPattern") {
      styleValue.setImage(
        bridge: bridge!,
        style: style,
        oldValue: oldReactStyle?[prop],
        setImageOnLayer: { (_) in self.setFillPattern(&layer, styleValue:styleValue) },
        isLayerStillValid: isValid,
        setImageOnLayerLater: { (_) in applyUpdater { (layer: inout FillLayer) in self.setFillPattern(&layer, styleValue: styleValue) } },
        name: "Fill.\(prop)"
      )
    } else if (prop == "fillEmissiveStrength") {
      self.setFillEmissiveStrength(&layer, styleValue:styleValue);
    } else if (prop == "fillEmissiveStrengthTransition") {
      self.setFillEmissiveStrengthTransition(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: fill")
    }
  }
}

func lineLayer(layer: inout LineLayer, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout LineLayer)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "lineCap") {
      self.setLineCap(&layer, styleValue:styleValue);
    } else if (prop == "lineJoin") {
      self.setLineJoin(&layer, styleValue:styleValue);
    } else if (prop == "lineMiterLimit") {
      self.setLineMiterLimit(&layer, styleValue:styleValue);
    } else if (prop == "lineRoundLimit") {
      self.setLineRoundLimit(&layer, styleValue:styleValue);
    } else if (prop == "lineSortKey") {
      self.setLineSortKey(&layer, styleValue:styleValue);
    } else if (prop == "visibility") {
      self.setLineStyleLayerVisibility(&layer, styleValue:styleValue);
    } else if (prop == "lineOpacity") {
      self.setLineOpacity(&layer, styleValue:styleValue);
    } else if (prop == "lineOpacityTransition") {
      self.setLineOpacityTransition(&layer, styleValue:styleValue);
    } else if (prop == "lineColor") {
      self.setLineColor(&layer, styleValue:styleValue);
    } else if (prop == "lineColorTransition") {
      self.setLineColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "lineTranslate") {
      self.setLineTranslate(&layer, styleValue:styleValue);
    } else if (prop == "lineTranslateTransition") {
      self.setLineTranslateTransition(&layer, styleValue:styleValue);
    } else if (prop == "lineTranslateAnchor") {
      self.setLineTranslateAnchor(&layer, styleValue:styleValue);
    } else if (prop == "lineWidth") {
      self.setLineWidth(&layer, styleValue:styleValue);
    } else if (prop == "lineWidthTransition") {
      self.setLineWidthTransition(&layer, styleValue:styleValue);
    } else if (prop == "lineGapWidth") {
      self.setLineGapWidth(&layer, styleValue:styleValue);
    } else if (prop == "lineGapWidthTransition") {
      self.setLineGapWidthTransition(&layer, styleValue:styleValue);
    } else if (prop == "lineOffset") {
      self.setLineOffset(&layer, styleValue:styleValue);
    } else if (prop == "lineOffsetTransition") {
      self.setLineOffsetTransition(&layer, styleValue:styleValue);
    } else if (prop == "lineBlur") {
      self.setLineBlur(&layer, styleValue:styleValue);
    } else if (prop == "lineBlurTransition") {
      self.setLineBlurTransition(&layer, styleValue:styleValue);
    } else if (prop == "lineDasharray") {
      self.setLineDasharray(&layer, styleValue:styleValue);
    } else if (prop == "linePattern") {
      styleValue.setImage(
        bridge: bridge!,
        style: style,
        oldValue: oldReactStyle?[prop],
        setImageOnLayer: { (_) in self.setLinePattern(&layer, styleValue:styleValue) },
        isLayerStillValid: isValid,
        setImageOnLayerLater: { (_) in applyUpdater { (layer: inout LineLayer) in self.setLinePattern(&layer, styleValue: styleValue) } },
        name: "Line.\(prop)"
      )
    } else if (prop == "lineGradient") {
      self.setLineGradient(&layer, styleValue:styleValue);
    } else if (prop == "lineTrimOffset") {
      self.setLineTrimOffset(&layer, styleValue:styleValue);
    } else if (prop == "lineEmissiveStrength") {
      self.setLineEmissiveStrength(&layer, styleValue:styleValue);
    } else if (prop == "lineEmissiveStrengthTransition") {
      self.setLineEmissiveStrengthTransition(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: line")
    }
  }
}

func symbolLayer(layer: inout SymbolLayer, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout SymbolLayer)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "symbolPlacement") {
      self.setSymbolPlacement(&layer, styleValue:styleValue);
    } else if (prop == "symbolSpacing") {
      self.setSymbolSpacing(&layer, styleValue:styleValue);
    } else if (prop == "symbolAvoidEdges") {
      self.setSymbolAvoidEdges(&layer, styleValue:styleValue);
    } else if (prop == "symbolSortKey") {
      self.setSymbolSortKey(&layer, styleValue:styleValue);
    } else if (prop == "symbolZOrder") {
      self.setSymbolZOrder(&layer, styleValue:styleValue);
    } else if (prop == "iconAllowOverlap") {
      self.setIconAllowOverlap(&layer, styleValue:styleValue);
    } else if (prop == "iconIgnorePlacement") {
      self.setIconIgnorePlacement(&layer, styleValue:styleValue);
    } else if (prop == "iconOptional") {
      self.setIconOptional(&layer, styleValue:styleValue);
    } else if (prop == "iconRotationAlignment") {
      self.setIconRotationAlignment(&layer, styleValue:styleValue);
    } else if (prop == "iconSize") {
      self.setIconSize(&layer, styleValue:styleValue);
    } else if (prop == "iconTextFit") {
      self.setIconTextFit(&layer, styleValue:styleValue);
    } else if (prop == "iconTextFitPadding") {
      self.setIconTextFitPadding(&layer, styleValue:styleValue);
    } else if (prop == "iconImage") {
      styleValue.setImage(
        bridge: bridge!,
        style: style,
        oldValue: oldReactStyle?[prop],
        setImageOnLayer: { (_) in self.setIconImage(&layer, styleValue:styleValue) },
        isLayerStillValid: isValid,
        setImageOnLayerLater: { (_) in applyUpdater { (layer: inout SymbolLayer) in self.setIconImage(&layer, styleValue: styleValue) } },
        name: "Symbol.\(prop)"
      )
    } else if (prop == "iconRotate") {
      self.setIconRotate(&layer, styleValue:styleValue);
    } else if (prop == "iconPadding") {
      self.setIconPadding(&layer, styleValue:styleValue);
    } else if (prop == "iconKeepUpright") {
      self.setIconKeepUpright(&layer, styleValue:styleValue);
    } else if (prop == "iconOffset") {
      self.setIconOffset(&layer, styleValue:styleValue);
    } else if (prop == "iconAnchor") {
      self.setIconAnchor(&layer, styleValue:styleValue);
    } else if (prop == "iconPitchAlignment") {
      self.setIconPitchAlignment(&layer, styleValue:styleValue);
    } else if (prop == "textPitchAlignment") {
      self.setTextPitchAlignment(&layer, styleValue:styleValue);
    } else if (prop == "textRotationAlignment") {
      self.setTextRotationAlignment(&layer, styleValue:styleValue);
    } else if (prop == "textField") {
      self.setTextField(&layer, styleValue:styleValue);
    } else if (prop == "textFont") {
      self.setTextFont(&layer, styleValue:styleValue);
    } else if (prop == "textSize") {
      self.setTextSize(&layer, styleValue:styleValue);
    } else if (prop == "textMaxWidth") {
      self.setTextMaxWidth(&layer, styleValue:styleValue);
    } else if (prop == "textLineHeight") {
      self.setTextLineHeight(&layer, styleValue:styleValue);
    } else if (prop == "textLetterSpacing") {
      self.setTextLetterSpacing(&layer, styleValue:styleValue);
    } else if (prop == "textJustify") {
      self.setTextJustify(&layer, styleValue:styleValue);
    } else if (prop == "textRadialOffset") {
      self.setTextRadialOffset(&layer, styleValue:styleValue);
    } else if (prop == "textVariableAnchor") {
      self.setTextVariableAnchor(&layer, styleValue:styleValue);
    } else if (prop == "textAnchor") {
      self.setTextAnchor(&layer, styleValue:styleValue);
    } else if (prop == "textMaxAngle") {
      self.setTextMaxAngle(&layer, styleValue:styleValue);
    } else if (prop == "textWritingMode") {
      self.setTextWritingMode(&layer, styleValue:styleValue);
    } else if (prop == "textRotate") {
      self.setTextRotate(&layer, styleValue:styleValue);
    } else if (prop == "textPadding") {
      self.setTextPadding(&layer, styleValue:styleValue);
    } else if (prop == "textKeepUpright") {
      self.setTextKeepUpright(&layer, styleValue:styleValue);
    } else if (prop == "textTransform") {
      self.setTextTransform(&layer, styleValue:styleValue);
    } else if (prop == "textOffset") {
      self.setTextOffset(&layer, styleValue:styleValue);
    } else if (prop == "textAllowOverlap") {
      self.setTextAllowOverlap(&layer, styleValue:styleValue);
    } else if (prop == "textIgnorePlacement") {
      self.setTextIgnorePlacement(&layer, styleValue:styleValue);
    } else if (prop == "textOptional") {
      self.setTextOptional(&layer, styleValue:styleValue);
    } else if (prop == "visibility") {
      self.setSymbolStyleLayerVisibility(&layer, styleValue:styleValue);
    } else if (prop == "iconOpacity") {
      self.setIconOpacity(&layer, styleValue:styleValue);
    } else if (prop == "iconOpacityTransition") {
      self.setIconOpacityTransition(&layer, styleValue:styleValue);
    } else if (prop == "iconColor") {
      self.setIconColor(&layer, styleValue:styleValue);
    } else if (prop == "iconColorTransition") {
      self.setIconColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "iconHaloColor") {
      self.setIconHaloColor(&layer, styleValue:styleValue);
    } else if (prop == "iconHaloColorTransition") {
      self.setIconHaloColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "iconHaloWidth") {
      self.setIconHaloWidth(&layer, styleValue:styleValue);
    } else if (prop == "iconHaloWidthTransition") {
      self.setIconHaloWidthTransition(&layer, styleValue:styleValue);
    } else if (prop == "iconHaloBlur") {
      self.setIconHaloBlur(&layer, styleValue:styleValue);
    } else if (prop == "iconHaloBlurTransition") {
      self.setIconHaloBlurTransition(&layer, styleValue:styleValue);
    } else if (prop == "iconTranslate") {
      self.setIconTranslate(&layer, styleValue:styleValue);
    } else if (prop == "iconTranslateTransition") {
      self.setIconTranslateTransition(&layer, styleValue:styleValue);
    } else if (prop == "iconTranslateAnchor") {
      self.setIconTranslateAnchor(&layer, styleValue:styleValue);
    } else if (prop == "textOpacity") {
      self.setTextOpacity(&layer, styleValue:styleValue);
    } else if (prop == "textOpacityTransition") {
      self.setTextOpacityTransition(&layer, styleValue:styleValue);
    } else if (prop == "textColor") {
      self.setTextColor(&layer, styleValue:styleValue);
    } else if (prop == "textColorTransition") {
      self.setTextColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "textHaloColor") {
      self.setTextHaloColor(&layer, styleValue:styleValue);
    } else if (prop == "textHaloColorTransition") {
      self.setTextHaloColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "textHaloWidth") {
      self.setTextHaloWidth(&layer, styleValue:styleValue);
    } else if (prop == "textHaloWidthTransition") {
      self.setTextHaloWidthTransition(&layer, styleValue:styleValue);
    } else if (prop == "textHaloBlur") {
      self.setTextHaloBlur(&layer, styleValue:styleValue);
    } else if (prop == "textHaloBlurTransition") {
      self.setTextHaloBlurTransition(&layer, styleValue:styleValue);
    } else if (prop == "textTranslate") {
      self.setTextTranslate(&layer, styleValue:styleValue);
    } else if (prop == "textTranslateTransition") {
      self.setTextTranslateTransition(&layer, styleValue:styleValue);
    } else if (prop == "textTranslateAnchor") {
      self.setTextTranslateAnchor(&layer, styleValue:styleValue);
    } else if (prop == "symbolZElevate") {
      self.setSymbolZElevate(&layer, styleValue:styleValue);
    } else if (prop == "iconEmissiveStrength") {
      self.setIconEmissiveStrength(&layer, styleValue:styleValue);
    } else if (prop == "iconEmissiveStrengthTransition") {
      self.setIconEmissiveStrengthTransition(&layer, styleValue:styleValue);
    } else if (prop == "textEmissiveStrength") {
      self.setTextEmissiveStrength(&layer, styleValue:styleValue);
    } else if (prop == "textEmissiveStrengthTransition") {
      self.setTextEmissiveStrengthTransition(&layer, styleValue:styleValue);
    } else if (prop == "iconImageCrossFade") {
      styleValue.setImage(
        bridge: bridge!,
        style: style,
        oldValue: oldReactStyle?[prop],
        setImageOnLayer: { (_) in self.setIconImageCrossFade(&layer, styleValue:styleValue) },
        isLayerStillValid: isValid,
        setImageOnLayerLater: { (_) in applyUpdater { (layer: inout SymbolLayer) in self.setIconImageCrossFade(&layer, styleValue: styleValue) } },
        name: "Symbol.\(prop)"
      )
    } else if (prop == "iconImageCrossFadeTransition") {
      self.setIconImageCrossFadeTransition(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: symbol")
    }
  }
}

func circleLayer(layer: inout CircleLayer, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout CircleLayer)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "circleSortKey") {
      self.setCircleSortKey(&layer, styleValue:styleValue);
    } else if (prop == "visibility") {
      self.setCircleStyleLayerVisibility(&layer, styleValue:styleValue);
    } else if (prop == "circleRadius") {
      self.setCircleRadius(&layer, styleValue:styleValue);
    } else if (prop == "circleRadiusTransition") {
      self.setCircleRadiusTransition(&layer, styleValue:styleValue);
    } else if (prop == "circleColor") {
      self.setCircleColor(&layer, styleValue:styleValue);
    } else if (prop == "circleColorTransition") {
      self.setCircleColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "circleBlur") {
      self.setCircleBlur(&layer, styleValue:styleValue);
    } else if (prop == "circleBlurTransition") {
      self.setCircleBlurTransition(&layer, styleValue:styleValue);
    } else if (prop == "circleOpacity") {
      self.setCircleOpacity(&layer, styleValue:styleValue);
    } else if (prop == "circleOpacityTransition") {
      self.setCircleOpacityTransition(&layer, styleValue:styleValue);
    } else if (prop == "circleTranslate") {
      self.setCircleTranslate(&layer, styleValue:styleValue);
    } else if (prop == "circleTranslateTransition") {
      self.setCircleTranslateTransition(&layer, styleValue:styleValue);
    } else if (prop == "circleTranslateAnchor") {
      self.setCircleTranslateAnchor(&layer, styleValue:styleValue);
    } else if (prop == "circlePitchScale") {
      self.setCirclePitchScale(&layer, styleValue:styleValue);
    } else if (prop == "circlePitchAlignment") {
      self.setCirclePitchAlignment(&layer, styleValue:styleValue);
    } else if (prop == "circleStrokeWidth") {
      self.setCircleStrokeWidth(&layer, styleValue:styleValue);
    } else if (prop == "circleStrokeWidthTransition") {
      self.setCircleStrokeWidthTransition(&layer, styleValue:styleValue);
    } else if (prop == "circleStrokeColor") {
      self.setCircleStrokeColor(&layer, styleValue:styleValue);
    } else if (prop == "circleStrokeColorTransition") {
      self.setCircleStrokeColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "circleStrokeOpacity") {
      self.setCircleStrokeOpacity(&layer, styleValue:styleValue);
    } else if (prop == "circleStrokeOpacityTransition") {
      self.setCircleStrokeOpacityTransition(&layer, styleValue:styleValue);
    } else if (prop == "circleEmissiveStrength") {
      self.setCircleEmissiveStrength(&layer, styleValue:styleValue);
    } else if (prop == "circleEmissiveStrengthTransition") {
      self.setCircleEmissiveStrengthTransition(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: circle")
    }
  }
}

func heatmapLayer(layer: inout HeatmapLayer, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout HeatmapLayer)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "visibility") {
      self.setHeatmapStyleLayerVisibility(&layer, styleValue:styleValue);
    } else if (prop == "heatmapRadius") {
      self.setHeatmapRadius(&layer, styleValue:styleValue);
    } else if (prop == "heatmapRadiusTransition") {
      self.setHeatmapRadiusTransition(&layer, styleValue:styleValue);
    } else if (prop == "heatmapWeight") {
      self.setHeatmapWeight(&layer, styleValue:styleValue);
    } else if (prop == "heatmapIntensity") {
      self.setHeatmapIntensity(&layer, styleValue:styleValue);
    } else if (prop == "heatmapIntensityTransition") {
      self.setHeatmapIntensityTransition(&layer, styleValue:styleValue);
    } else if (prop == "heatmapColor") {
      self.setHeatmapColor(&layer, styleValue:styleValue);
    } else if (prop == "heatmapOpacity") {
      self.setHeatmapOpacity(&layer, styleValue:styleValue);
    } else if (prop == "heatmapOpacityTransition") {
      self.setHeatmapOpacityTransition(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: heatmap")
    }
  }
}

func fillExtrusionLayer(layer: inout FillExtrusionLayer, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout FillExtrusionLayer)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "visibility") {
      self.setFillExtrusionStyleLayerVisibility(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionOpacity") {
      self.setFillExtrusionOpacity(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionOpacityTransition") {
      self.setFillExtrusionOpacityTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionColor") {
      self.setFillExtrusionColor(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionColorTransition") {
      self.setFillExtrusionColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionTranslate") {
      self.setFillExtrusionTranslate(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionTranslateTransition") {
      self.setFillExtrusionTranslateTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionTranslateAnchor") {
      self.setFillExtrusionTranslateAnchor(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionPattern") {
      styleValue.setImage(
        bridge: bridge!,
        style: style,
        oldValue: oldReactStyle?[prop],
        setImageOnLayer: { (_) in self.setFillExtrusionPattern(&layer, styleValue:styleValue) },
        isLayerStillValid: isValid,
        setImageOnLayerLater: { (_) in applyUpdater { (layer: inout FillExtrusionLayer) in self.setFillExtrusionPattern(&layer, styleValue: styleValue) } },
        name: "FillExtrusion.\(prop)"
      )
    } else if (prop == "fillExtrusionHeight") {
      self.setFillExtrusionHeight(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionHeightTransition") {
      self.setFillExtrusionHeightTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionBase") {
      self.setFillExtrusionBase(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionBaseTransition") {
      self.setFillExtrusionBaseTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionVerticalGradient") {
      self.setFillExtrusionVerticalGradient(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionRoundedRoof") {
      self.setFillExtrusionRoundedRoof(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionAmbientOcclusionWallRadius") {
      self.setFillExtrusionAmbientOcclusionWallRadius(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionAmbientOcclusionWallRadiusTransition") {
      self.setFillExtrusionAmbientOcclusionWallRadiusTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionAmbientOcclusionGroundRadius") {
      self.setFillExtrusionAmbientOcclusionGroundRadius(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionAmbientOcclusionGroundRadiusTransition") {
      self.setFillExtrusionAmbientOcclusionGroundRadiusTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionAmbientOcclusionGroundAttenuation") {
      self.setFillExtrusionAmbientOcclusionGroundAttenuation(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionAmbientOcclusionGroundAttenuationTransition") {
      self.setFillExtrusionAmbientOcclusionGroundAttenuationTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionFloodLightColor") {
      self.setFillExtrusionFloodLightColor(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionFloodLightColorTransition") {
      self.setFillExtrusionFloodLightColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionFloodLightIntensity") {
      self.setFillExtrusionFloodLightIntensity(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionFloodLightIntensityTransition") {
      self.setFillExtrusionFloodLightIntensityTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionFloodLightWallRadius") {
      self.setFillExtrusionFloodLightWallRadius(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionFloodLightWallRadiusTransition") {
      self.setFillExtrusionFloodLightWallRadiusTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionFloodLightGroundRadius") {
      self.setFillExtrusionFloodLightGroundRadius(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionFloodLightGroundRadiusTransition") {
      self.setFillExtrusionFloodLightGroundRadiusTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionFloodLightGroundAttenuation") {
      self.setFillExtrusionFloodLightGroundAttenuation(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionFloodLightGroundAttenuationTransition") {
      self.setFillExtrusionFloodLightGroundAttenuationTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionVerticalScale") {
      self.setFillExtrusionVerticalScale(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionVerticalScaleTransition") {
      self.setFillExtrusionVerticalScaleTransition(&layer, styleValue:styleValue);
    } else if (prop == "fillExtrusionCutoffFadeRange") {
      self.setFillExtrusionCutoffFadeRange(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: fill-extrusion")
    }
  }
}

func rasterLayer(layer: inout RasterLayer, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout RasterLayer)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "visibility") {
      self.setRasterStyleLayerVisibility(&layer, styleValue:styleValue);
    } else if (prop == "rasterOpacity") {
      self.setRasterOpacity(&layer, styleValue:styleValue);
    } else if (prop == "rasterOpacityTransition") {
      self.setRasterOpacityTransition(&layer, styleValue:styleValue);
    } else if (prop == "rasterHueRotate") {
      self.setRasterHueRotate(&layer, styleValue:styleValue);
    } else if (prop == "rasterHueRotateTransition") {
      self.setRasterHueRotateTransition(&layer, styleValue:styleValue);
    } else if (prop == "rasterBrightnessMin") {
      self.setRasterBrightnessMin(&layer, styleValue:styleValue);
    } else if (prop == "rasterBrightnessMinTransition") {
      self.setRasterBrightnessMinTransition(&layer, styleValue:styleValue);
    } else if (prop == "rasterBrightnessMax") {
      self.setRasterBrightnessMax(&layer, styleValue:styleValue);
    } else if (prop == "rasterBrightnessMaxTransition") {
      self.setRasterBrightnessMaxTransition(&layer, styleValue:styleValue);
    } else if (prop == "rasterSaturation") {
      self.setRasterSaturation(&layer, styleValue:styleValue);
    } else if (prop == "rasterSaturationTransition") {
      self.setRasterSaturationTransition(&layer, styleValue:styleValue);
    } else if (prop == "rasterContrast") {
      self.setRasterContrast(&layer, styleValue:styleValue);
    } else if (prop == "rasterContrastTransition") {
      self.setRasterContrastTransition(&layer, styleValue:styleValue);
    } else if (prop == "rasterResampling") {
      self.setRasterResampling(&layer, styleValue:styleValue);
    } else if (prop == "rasterFadeDuration") {
      self.setRasterFadeDuration(&layer, styleValue:styleValue);
    } else if (prop == "rasterColor") {
      self.setRasterColor(&layer, styleValue:styleValue);
    } else if (prop == "rasterColorMix") {
      self.setRasterColorMix(&layer, styleValue:styleValue);
    } else if (prop == "rasterColorMixTransition") {
      self.setRasterColorMixTransition(&layer, styleValue:styleValue);
    } else if (prop == "rasterColorRange") {
      self.setRasterColorRange(&layer, styleValue:styleValue);
    } else if (prop == "rasterColorRangeTransition") {
      self.setRasterColorRangeTransition(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: raster")
    }
  }
}

func hillshadeLayer(layer: inout HillshadeLayer, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout HillshadeLayer)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "visibility") {
      self.setHillshadeStyleLayerVisibility(&layer, styleValue:styleValue);
    } else if (prop == "hillshadeIlluminationDirection") {
      self.setHillshadeIlluminationDirection(&layer, styleValue:styleValue);
    } else if (prop == "hillshadeIlluminationAnchor") {
      self.setHillshadeIlluminationAnchor(&layer, styleValue:styleValue);
    } else if (prop == "hillshadeExaggeration") {
      self.setHillshadeExaggeration(&layer, styleValue:styleValue);
    } else if (prop == "hillshadeExaggerationTransition") {
      self.setHillshadeExaggerationTransition(&layer, styleValue:styleValue);
    } else if (prop == "hillshadeShadowColor") {
      self.setHillshadeShadowColor(&layer, styleValue:styleValue);
    } else if (prop == "hillshadeShadowColorTransition") {
      self.setHillshadeShadowColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "hillshadeHighlightColor") {
      self.setHillshadeHighlightColor(&layer, styleValue:styleValue);
    } else if (prop == "hillshadeHighlightColorTransition") {
      self.setHillshadeHighlightColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "hillshadeAccentColor") {
      self.setHillshadeAccentColor(&layer, styleValue:styleValue);
    } else if (prop == "hillshadeAccentColorTransition") {
      self.setHillshadeAccentColorTransition(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: hillshade")
    }
  }
}

func modelLayer(layer: inout ModelLayer, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout ModelLayer)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "visibility") {
      self.setModelStyleLayerVisibility(&layer, styleValue:styleValue);
    } else if (prop == "modelId") {
      self.setModelId(&layer, styleValue:styleValue);
    } else if (prop == "modelOpacity") {
      self.setModelOpacity(&layer, styleValue:styleValue);
    } else if (prop == "modelOpacityTransition") {
      self.setModelOpacityTransition(&layer, styleValue:styleValue);
    } else if (prop == "modelRotation") {
      self.setModelRotation(&layer, styleValue:styleValue);
    } else if (prop == "modelRotationTransition") {
      self.setModelRotationTransition(&layer, styleValue:styleValue);
    } else if (prop == "modelScale") {
      self.setModelScale(&layer, styleValue:styleValue);
    } else if (prop == "modelScaleTransition") {
      self.setModelScaleTransition(&layer, styleValue:styleValue);
    } else if (prop == "modelTranslation") {
      self.setModelTranslation(&layer, styleValue:styleValue);
    } else if (prop == "modelTranslationTransition") {
      self.setModelTranslationTransition(&layer, styleValue:styleValue);
    } else if (prop == "modelColor") {
      self.setModelColor(&layer, styleValue:styleValue);
    } else if (prop == "modelColorTransition") {
      self.setModelColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "modelColorMixIntensity") {
      self.setModelColorMixIntensity(&layer, styleValue:styleValue);
    } else if (prop == "modelColorMixIntensityTransition") {
      self.setModelColorMixIntensityTransition(&layer, styleValue:styleValue);
    } else if (prop == "modelType") {
      self.setModelType(&layer, styleValue:styleValue);
    } else if (prop == "modelCastShadows") {
      self.setModelCastShadows(&layer, styleValue:styleValue);
    } else if (prop == "modelReceiveShadows") {
      self.setModelReceiveShadows(&layer, styleValue:styleValue);
    } else if (prop == "modelAmbientOcclusionIntensity") {
      self.setModelAmbientOcclusionIntensity(&layer, styleValue:styleValue);
    } else if (prop == "modelAmbientOcclusionIntensityTransition") {
      self.setModelAmbientOcclusionIntensityTransition(&layer, styleValue:styleValue);
    } else if (prop == "modelEmissiveStrength") {
      self.setModelEmissiveStrength(&layer, styleValue:styleValue);
    } else if (prop == "modelEmissiveStrengthTransition") {
      self.setModelEmissiveStrengthTransition(&layer, styleValue:styleValue);
    } else if (prop == "modelRoughness") {
      self.setModelRoughness(&layer, styleValue:styleValue);
    } else if (prop == "modelRoughnessTransition") {
      self.setModelRoughnessTransition(&layer, styleValue:styleValue);
    } else if (prop == "modelHeightBasedEmissiveStrengthMultiplier") {
      self.setModelHeightBasedEmissiveStrengthMultiplier(&layer, styleValue:styleValue);
    } else if (prop == "modelHeightBasedEmissiveStrengthMultiplierTransition") {
      self.setModelHeightBasedEmissiveStrengthMultiplierTransition(&layer, styleValue:styleValue);
    } else if (prop == "modelCutoffFadeRange") {
      self.setModelCutoffFadeRange(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: model")
    }
  }
}

func backgroundLayer(layer: inout BackgroundLayer, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout BackgroundLayer)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "visibility") {
      self.setBackgroundStyleLayerVisibility(&layer, styleValue:styleValue);
    } else if (prop == "backgroundColor") {
      self.setBackgroundColor(&layer, styleValue:styleValue);
    } else if (prop == "backgroundColorTransition") {
      self.setBackgroundColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "backgroundPattern") {
      styleValue.setImage(
        bridge: bridge!,
        style: style,
        oldValue: oldReactStyle?[prop],
        setImageOnLayer: { (_) in self.setBackgroundPattern(&layer, styleValue:styleValue) },
        isLayerStillValid: isValid,
        setImageOnLayerLater: { (_) in applyUpdater { (layer: inout BackgroundLayer) in self.setBackgroundPattern(&layer, styleValue: styleValue) } },
        name: "Background.\(prop)"
      )
    } else if (prop == "backgroundOpacity") {
      self.setBackgroundOpacity(&layer, styleValue:styleValue);
    } else if (prop == "backgroundOpacityTransition") {
      self.setBackgroundOpacityTransition(&layer, styleValue:styleValue);
    } else if (prop == "backgroundEmissiveStrength") {
      self.setBackgroundEmissiveStrength(&layer, styleValue:styleValue);
    } else if (prop == "backgroundEmissiveStrengthTransition") {
      self.setBackgroundEmissiveStrengthTransition(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: background")
    }
  }
}

func skyLayer(layer: inout SkyLayer, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout SkyLayer)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "visibility") {
      self.setSkyStyleLayerVisibility(&layer, styleValue:styleValue);
    } else if (prop == "skyType") {
      self.setSkyType(&layer, styleValue:styleValue);
    } else if (prop == "skyAtmosphereSun") {
      self.setSkyAtmosphereSun(&layer, styleValue:styleValue);
    } else if (prop == "skyAtmosphereSunIntensity") {
      self.setSkyAtmosphereSunIntensity(&layer, styleValue:styleValue);
    } else if (prop == "skyGradientCenter") {
      self.setSkyGradientCenter(&layer, styleValue:styleValue);
    } else if (prop == "skyGradientRadius") {
      self.setSkyGradientRadius(&layer, styleValue:styleValue);
    } else if (prop == "skyGradient") {
      self.setSkyGradient(&layer, styleValue:styleValue);
    } else if (prop == "skyAtmosphereHaloColor") {
      self.setSkyAtmosphereHaloColor(&layer, styleValue:styleValue);
    } else if (prop == "skyAtmosphereColor") {
      self.setSkyAtmosphereColor(&layer, styleValue:styleValue);
    } else if (prop == "skyOpacity") {
      self.setSkyOpacity(&layer, styleValue:styleValue);
    } else if (prop == "skyOpacityTransition") {
      self.setSkyOpacityTransition(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: sky")
    }
  }
}

func lightLayer(layer: inout Light, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout Light)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "anchor") {
      self.setAnchor(&layer, styleValue:styleValue);
    } else if (prop == "position") {
      self.setPosition(&layer, styleValue:styleValue);
    } else if (prop == "positionTransition") {
      self.setPositionTransition(&layer, styleValue:styleValue);
    } else if (prop == "color") {
      self.setColor(&layer, styleValue:styleValue);
    } else if (prop == "colorTransition") {
      self.setColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "intensity") {
      self.setIntensity(&layer, styleValue:styleValue);
    } else if (prop == "intensityTransition") {
      self.setIntensityTransition(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: light")
    }
  }
}

func atmosphereLayer(layer: inout Atmosphere, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout Atmosphere)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "range") {
      self.setRange(&layer, styleValue:styleValue);
    } else if (prop == "rangeTransition") {
      self.setRangeTransition(&layer, styleValue:styleValue);
    } else if (prop == "color") {
      self.setColor(&layer, styleValue:styleValue);
    } else if (prop == "colorTransition") {
      self.setColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "highColor") {
      self.setHighColor(&layer, styleValue:styleValue);
    } else if (prop == "highColorTransition") {
      self.setHighColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "spaceColor") {
      self.setSpaceColor(&layer, styleValue:styleValue);
    } else if (prop == "spaceColorTransition") {
      self.setSpaceColorTransition(&layer, styleValue:styleValue);
    } else if (prop == "horizonBlend") {
      self.setHorizonBlend(&layer, styleValue:styleValue);
    } else if (prop == "horizonBlendTransition") {
      self.setHorizonBlendTransition(&layer, styleValue:styleValue);
    } else if (prop == "starIntensity") {
      self.setStarIntensity(&layer, styleValue:styleValue);
    } else if (prop == "starIntensityTransition") {
      self.setStarIntensityTransition(&layer, styleValue:styleValue);
    } else if (prop == "verticalRange") {
      self.setVerticalRange(&layer, styleValue:styleValue);
    } else if (prop == "verticalRangeTransition") {
      self.setVerticalRangeTransition(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: atmosphere")
    }
  }
}

func terrainLayer(layer: inout Terrain, reactStyle:Dictionary<String, Any>, oldReactStyle:Dictionary<String, Any>?, applyUpdater: @escaping  ((inout Terrain)->Void)->Void, isValid: @escaping () -> Bool)
{
  guard self._hasReactStyle(reactStyle) else {
    Logger.log(level:.error, message: "Invalid style: \(reactStyle)")
    return
  }

  let styleProps = reactStyle.keys
  for prop in styleProps {
    if (prop == "__MAPBOX_STYLESHEET__") {
      continue;
    }

    let styleValue = RNMBXStyleValue.make(reactStyle[prop])

    if (prop == "exaggeration") {
      self.setExaggeration(&layer, styleValue:styleValue);
    } else {
      Logger.log(level:.error, message: "Unexpected property \(prop) for layer: terrain")
    }
  }
}




func setFillSortKey(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillSortKey = styleValue.mglStyleValueNumber();
        
      
}

func setFillStyleLayerVisibility(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
    layer.visibility = styleValue.isVisible();
}

func setFillAntialias(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillAntialias = styleValue.mglStyleValueBoolean();
        
      
}

func setFillOpacity(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillOpacity = styleValue.mglStyleValueNumber();
        
      
}

func setFillOpacityTransition(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
    layer.fillOpacityTransition = styleValue.getTransition();
}

func setFillColor(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillColor = styleValue.mglStyleValueColor();
        
      
}

func setFillColorTransition(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
    layer.fillColorTransition = styleValue.getTransition();
}

func setFillOutlineColor(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillOutlineColor = styleValue.mglStyleValueColor();
        
      
}

func setFillOutlineColorTransition(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
    layer.fillOutlineColorTransition = styleValue.getTransition();
}

func setFillTranslate(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillTranslate = styleValue.mglStyleValueArrayNumber();
         
      
}

func setFillTranslateTransition(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
    layer.fillTranslateTransition = styleValue.getTransition();
}

func setFillTranslateAnchor(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillTranslateAnchor = styleValue.mglStyleValueEnum(); 
        
      
}

func setFillPattern(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillPattern = styleValue.mglStyleValueResolvedImage();
        
      
}

func setFillEmissiveStrength(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillEmissiveStrength = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setFillEmissiveStrengthTransition(_ layer: inout FillLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.fillEmissiveStrengthTransition = styleValue.getTransition();
      #endif
}



func setLineCap(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineCap = styleValue.mglStyleValueEnum(); 
        
      
}

func setLineJoin(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineJoin = styleValue.mglStyleValueEnum(); 
        
      
}

func setLineMiterLimit(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineMiterLimit = styleValue.mglStyleValueNumber();
        
      
}

func setLineRoundLimit(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineRoundLimit = styleValue.mglStyleValueNumber();
        
      
}

func setLineSortKey(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineSortKey = styleValue.mglStyleValueNumber();
        
      
}

func setLineStyleLayerVisibility(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
    layer.visibility = styleValue.isVisible();
}

func setLineOpacity(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineOpacity = styleValue.mglStyleValueNumber();
        
      
}

func setLineOpacityTransition(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
    layer.lineOpacityTransition = styleValue.getTransition();
}

func setLineColor(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineColor = styleValue.mglStyleValueColor();
        
      
}

func setLineColorTransition(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
    layer.lineColorTransition = styleValue.getTransition();
}

func setLineTranslate(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineTranslate = styleValue.mglStyleValueArrayNumber();
         
      
}

func setLineTranslateTransition(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
    layer.lineTranslateTransition = styleValue.getTransition();
}

func setLineTranslateAnchor(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineTranslateAnchor = styleValue.mglStyleValueEnum(); 
        
      
}

func setLineWidth(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineWidth = styleValue.mglStyleValueNumber();
        
      
}

func setLineWidthTransition(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
    layer.lineWidthTransition = styleValue.getTransition();
}

func setLineGapWidth(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineGapWidth = styleValue.mglStyleValueNumber();
        
      
}

func setLineGapWidthTransition(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
    layer.lineGapWidthTransition = styleValue.getTransition();
}

func setLineOffset(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineOffset = styleValue.mglStyleValueNumber();
        
      
}

func setLineOffsetTransition(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
    layer.lineOffsetTransition = styleValue.getTransition();
}

func setLineBlur(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineBlur = styleValue.mglStyleValueNumber();
        
      
}

func setLineBlurTransition(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
    layer.lineBlurTransition = styleValue.getTransition();
}

func setLineDasharray(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineDasharray = styleValue.mglStyleValueArrayNumber();
         
      
}

func setLinePattern(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.linePattern = styleValue.mglStyleValueResolvedImage();
        
      
}

func setLineGradient(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineGradient = styleValue.mglStyleValueColor();
        
      
}

func setLineTrimOffset(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.lineTrimOffset = styleValue.mglStyleValueArrayNumber();
         
      
}

func setLineEmissiveStrength(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.lineEmissiveStrength = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setLineEmissiveStrengthTransition(_ layer: inout LineLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.lineEmissiveStrengthTransition = styleValue.getTransition();
      #endif
}



func setSymbolPlacement(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.symbolPlacement = styleValue.mglStyleValueEnum(); 
        
      
}

func setSymbolSpacing(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.symbolSpacing = styleValue.mglStyleValueNumber();
        
      
}

func setSymbolAvoidEdges(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.symbolAvoidEdges = styleValue.mglStyleValueBoolean();
        
      
}

func setSymbolSortKey(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.symbolSortKey = styleValue.mglStyleValueNumber();
        
      
}

func setSymbolZOrder(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.symbolZOrder = styleValue.mglStyleValueEnum(); 
        
      
}

func setIconAllowOverlap(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconAllowOverlap = styleValue.mglStyleValueBoolean();
        
      
}

func setIconIgnorePlacement(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconIgnorePlacement = styleValue.mglStyleValueBoolean();
        
      
}

func setIconOptional(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconOptional = styleValue.mglStyleValueBoolean();
        
      
}

func setIconRotationAlignment(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconRotationAlignment = styleValue.mglStyleValueEnum(); 
        
      
}

func setIconSize(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconSize = styleValue.mglStyleValueNumber();
        
      
}

func setIconTextFit(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconTextFit = styleValue.mglStyleValueEnum(); 
        
      
}

func setIconTextFitPadding(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconTextFitPadding = styleValue.mglStyleValueArrayNumber();
         
      
}

func setIconImage(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconImage = styleValue.mglStyleValueResolvedImage();
        
      
}

func setIconRotate(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconRotate = styleValue.mglStyleValueNumber();
        
      
}

func setIconPadding(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconPadding = styleValue.mglStyleValueNumber();
        
      
}

func setIconKeepUpright(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconKeepUpright = styleValue.mglStyleValueBoolean();
        
      
}

func setIconOffset(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconOffset = styleValue.mglStyleValueArrayNumber();
         
      
}

func setIconAnchor(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconAnchor = styleValue.mglStyleValueEnum(); 
        
      
}

func setIconPitchAlignment(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconPitchAlignment = styleValue.mglStyleValueEnum(); 
        
      
}

func setTextPitchAlignment(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textPitchAlignment = styleValue.mglStyleValueEnum(); 
        
      
}

func setTextRotationAlignment(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textRotationAlignment = styleValue.mglStyleValueEnum(); 
        
      
}

func setTextField(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textField = styleValue.mglStyleValueFormatted();
        
      
}

func setTextFont(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textFont = styleValue.mglStyleValueArrayString();
         
      
}

func setTextSize(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textSize = styleValue.mglStyleValueNumber();
        
      
}

func setTextMaxWidth(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textMaxWidth = styleValue.mglStyleValueNumber();
        
      
}

func setTextLineHeight(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textLineHeight = styleValue.mglStyleValueNumber();
        
      
}

func setTextLetterSpacing(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textLetterSpacing = styleValue.mglStyleValueNumber();
        
      
}

func setTextJustify(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textJustify = styleValue.mglStyleValueEnum(); 
        
      
}

func setTextRadialOffset(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textRadialOffset = styleValue.mglStyleValueNumber();
        
      
}

func setTextVariableAnchor(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textVariableAnchor = styleValue.mglStyleValueArrayTextVariableAnchor();
         
      
}

func setTextAnchor(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textAnchor = styleValue.mglStyleValueEnum(); 
        
      
}

func setTextMaxAngle(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textMaxAngle = styleValue.mglStyleValueNumber();
        
      
}

func setTextWritingMode(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textWritingMode = styleValue.mglStyleValueArrayTextWritingMode();
         
      
}

func setTextRotate(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textRotate = styleValue.mglStyleValueNumber();
        
      
}

func setTextPadding(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textPadding = styleValue.mglStyleValueNumber();
        
      
}

func setTextKeepUpright(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textKeepUpright = styleValue.mglStyleValueBoolean();
        
      
}

func setTextTransform(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textTransform = styleValue.mglStyleValueEnum(); 
        
      
}

func setTextOffset(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textOffset = styleValue.mglStyleValueArrayNumber();
         
      
}

func setTextAllowOverlap(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textAllowOverlap = styleValue.mglStyleValueBoolean();
        
      
}

func setTextIgnorePlacement(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textIgnorePlacement = styleValue.mglStyleValueBoolean();
        
      
}

func setTextOptional(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textOptional = styleValue.mglStyleValueBoolean();
        
      
}

func setSymbolStyleLayerVisibility(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.visibility = styleValue.isVisible();
}

func setIconOpacity(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconOpacity = styleValue.mglStyleValueNumber();
        
      
}

func setIconOpacityTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.iconOpacityTransition = styleValue.getTransition();
}

func setIconColor(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconColor = styleValue.mglStyleValueColor();
        
      
}

func setIconColorTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.iconColorTransition = styleValue.getTransition();
}

func setIconHaloColor(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconHaloColor = styleValue.mglStyleValueColor();
        
      
}

func setIconHaloColorTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.iconHaloColorTransition = styleValue.getTransition();
}

func setIconHaloWidth(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconHaloWidth = styleValue.mglStyleValueNumber();
        
      
}

func setIconHaloWidthTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.iconHaloWidthTransition = styleValue.getTransition();
}

func setIconHaloBlur(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconHaloBlur = styleValue.mglStyleValueNumber();
        
      
}

func setIconHaloBlurTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.iconHaloBlurTransition = styleValue.getTransition();
}

func setIconTranslate(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconTranslate = styleValue.mglStyleValueArrayNumber();
         
      
}

func setIconTranslateTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.iconTranslateTransition = styleValue.getTransition();
}

func setIconTranslateAnchor(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.iconTranslateAnchor = styleValue.mglStyleValueEnum(); 
        
      
}

func setTextOpacity(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textOpacity = styleValue.mglStyleValueNumber();
        
      
}

func setTextOpacityTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.textOpacityTransition = styleValue.getTransition();
}

func setTextColor(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textColor = styleValue.mglStyleValueColor();
        
      
}

func setTextColorTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.textColorTransition = styleValue.getTransition();
}

func setTextHaloColor(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textHaloColor = styleValue.mglStyleValueColor();
        
      
}

func setTextHaloColorTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.textHaloColorTransition = styleValue.getTransition();
}

func setTextHaloWidth(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textHaloWidth = styleValue.mglStyleValueNumber();
        
      
}

func setTextHaloWidthTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.textHaloWidthTransition = styleValue.getTransition();
}

func setTextHaloBlur(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textHaloBlur = styleValue.mglStyleValueNumber();
        
      
}

func setTextHaloBlurTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.textHaloBlurTransition = styleValue.getTransition();
}

func setTextTranslate(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textTranslate = styleValue.mglStyleValueArrayNumber();
         
      
}

func setTextTranslateTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
    layer.textTranslateTransition = styleValue.getTransition();
}

func setTextTranslateAnchor(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.textTranslateAnchor = styleValue.mglStyleValueEnum(); 
        
      
}

func setSymbolZElevate(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.symbolZElevate = styleValue.mglStyleValueBoolean();
        
      
      #endif
}

func setIconEmissiveStrength(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.iconEmissiveStrength = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setIconEmissiveStrengthTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.iconEmissiveStrengthTransition = styleValue.getTransition();
      #endif
}

func setTextEmissiveStrength(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.textEmissiveStrength = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setTextEmissiveStrengthTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.textEmissiveStrengthTransition = styleValue.getTransition();
      #endif
}

func setIconImageCrossFade(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.iconImageCrossFade = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setIconImageCrossFadeTransition(_ layer: inout SymbolLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.iconImageCrossFadeTransition = styleValue.getTransition();
      #endif
}



func setCircleSortKey(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circleSortKey = styleValue.mglStyleValueNumber();
        
      
}

func setCircleStyleLayerVisibility(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
    layer.visibility = styleValue.isVisible();
}

func setCircleRadius(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circleRadius = styleValue.mglStyleValueNumber();
        
      
}

func setCircleRadiusTransition(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
    layer.circleRadiusTransition = styleValue.getTransition();
}

func setCircleColor(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circleColor = styleValue.mglStyleValueColor();
        
      
}

func setCircleColorTransition(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
    layer.circleColorTransition = styleValue.getTransition();
}

func setCircleBlur(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circleBlur = styleValue.mglStyleValueNumber();
        
      
}

func setCircleBlurTransition(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
    layer.circleBlurTransition = styleValue.getTransition();
}

func setCircleOpacity(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circleOpacity = styleValue.mglStyleValueNumber();
        
      
}

func setCircleOpacityTransition(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
    layer.circleOpacityTransition = styleValue.getTransition();
}

func setCircleTranslate(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circleTranslate = styleValue.mglStyleValueArrayNumber();
         
      
}

func setCircleTranslateTransition(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
    layer.circleTranslateTransition = styleValue.getTransition();
}

func setCircleTranslateAnchor(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circleTranslateAnchor = styleValue.mglStyleValueEnum(); 
        
      
}

func setCirclePitchScale(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circlePitchScale = styleValue.mglStyleValueEnum(); 
        
      
}

func setCirclePitchAlignment(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circlePitchAlignment = styleValue.mglStyleValueEnum(); 
        
      
}

func setCircleStrokeWidth(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circleStrokeWidth = styleValue.mglStyleValueNumber();
        
      
}

func setCircleStrokeWidthTransition(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
    layer.circleStrokeWidthTransition = styleValue.getTransition();
}

func setCircleStrokeColor(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circleStrokeColor = styleValue.mglStyleValueColor();
        
      
}

func setCircleStrokeColorTransition(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
    layer.circleStrokeColorTransition = styleValue.getTransition();
}

func setCircleStrokeOpacity(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.circleStrokeOpacity = styleValue.mglStyleValueNumber();
        
      
}

func setCircleStrokeOpacityTransition(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
    layer.circleStrokeOpacityTransition = styleValue.getTransition();
}

func setCircleEmissiveStrength(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.circleEmissiveStrength = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setCircleEmissiveStrengthTransition(_ layer: inout CircleLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.circleEmissiveStrengthTransition = styleValue.getTransition();
      #endif
}



func setHeatmapStyleLayerVisibility(_ layer: inout HeatmapLayer, styleValue: RNMBXStyleValue)
{
    layer.visibility = styleValue.isVisible();
}

func setHeatmapRadius(_ layer: inout HeatmapLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.heatmapRadius = styleValue.mglStyleValueNumber();
        
      
}

func setHeatmapRadiusTransition(_ layer: inout HeatmapLayer, styleValue: RNMBXStyleValue)
{
    layer.heatmapRadiusTransition = styleValue.getTransition();
}

func setHeatmapWeight(_ layer: inout HeatmapLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.heatmapWeight = styleValue.mglStyleValueNumber();
        
      
}

func setHeatmapIntensity(_ layer: inout HeatmapLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.heatmapIntensity = styleValue.mglStyleValueNumber();
        
      
}

func setHeatmapIntensityTransition(_ layer: inout HeatmapLayer, styleValue: RNMBXStyleValue)
{
    layer.heatmapIntensityTransition = styleValue.getTransition();
}

func setHeatmapColor(_ layer: inout HeatmapLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.heatmapColor = styleValue.mglStyleValueColor();
        
      
}

func setHeatmapOpacity(_ layer: inout HeatmapLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.heatmapOpacity = styleValue.mglStyleValueNumber();
        
      
}

func setHeatmapOpacityTransition(_ layer: inout HeatmapLayer, styleValue: RNMBXStyleValue)
{
    layer.heatmapOpacityTransition = styleValue.getTransition();
}



func setFillExtrusionStyleLayerVisibility(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
    layer.visibility = styleValue.isVisible();
}

func setFillExtrusionOpacity(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillExtrusionOpacity = styleValue.mglStyleValueNumber();
        
      
}

func setFillExtrusionOpacityTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
    layer.fillExtrusionOpacityTransition = styleValue.getTransition();
}

func setFillExtrusionColor(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillExtrusionColor = styleValue.mglStyleValueColor();
        
      
}

func setFillExtrusionColorTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
    layer.fillExtrusionColorTransition = styleValue.getTransition();
}

func setFillExtrusionTranslate(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillExtrusionTranslate = styleValue.mglStyleValueArrayNumber();
         
      
}

func setFillExtrusionTranslateTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
    layer.fillExtrusionTranslateTransition = styleValue.getTransition();
}

func setFillExtrusionTranslateAnchor(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillExtrusionTranslateAnchor = styleValue.mglStyleValueEnum(); 
        
      
}

func setFillExtrusionPattern(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillExtrusionPattern = styleValue.mglStyleValueResolvedImage();
        
      
}

func setFillExtrusionHeight(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillExtrusionHeight = styleValue.mglStyleValueNumber();
        
      
}

func setFillExtrusionHeightTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
    layer.fillExtrusionHeightTransition = styleValue.getTransition();
}

func setFillExtrusionBase(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillExtrusionBase = styleValue.mglStyleValueNumber();
        
      
}

func setFillExtrusionBaseTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
    layer.fillExtrusionBaseTransition = styleValue.getTransition();
}

func setFillExtrusionVerticalGradient(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.fillExtrusionVerticalGradient = styleValue.mglStyleValueBoolean();
        
      
}

func setFillExtrusionRoundedRoof(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillExtrusionRoundedRoof = styleValue.mglStyleValueBoolean();
        
      
      #endif
}

func setFillExtrusionAmbientOcclusionWallRadius(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillExtrusionAmbientOcclusionWallRadius = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setFillExtrusionAmbientOcclusionWallRadiusTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.fillExtrusionAmbientOcclusionWallRadiusTransition = styleValue.getTransition();
      #endif
}

func setFillExtrusionAmbientOcclusionGroundRadius(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillExtrusionAmbientOcclusionGroundRadius = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setFillExtrusionAmbientOcclusionGroundRadiusTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.fillExtrusionAmbientOcclusionGroundRadiusTransition = styleValue.getTransition();
      #endif
}

func setFillExtrusionAmbientOcclusionGroundAttenuation(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillExtrusionAmbientOcclusionGroundAttenuation = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setFillExtrusionAmbientOcclusionGroundAttenuationTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.fillExtrusionAmbientOcclusionGroundAttenuationTransition = styleValue.getTransition();
      #endif
}

func setFillExtrusionFloodLightColor(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillExtrusionFloodLightColor = styleValue.mglStyleValueColor();
        
      
      #endif
}

func setFillExtrusionFloodLightColorTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.fillExtrusionFloodLightColorTransition = styleValue.getTransition();
      #endif
}

func setFillExtrusionFloodLightIntensity(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillExtrusionFloodLightIntensity = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setFillExtrusionFloodLightIntensityTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.fillExtrusionFloodLightIntensityTransition = styleValue.getTransition();
      #endif
}

func setFillExtrusionFloodLightWallRadius(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillExtrusionFloodLightWallRadius = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setFillExtrusionFloodLightWallRadiusTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.fillExtrusionFloodLightWallRadiusTransition = styleValue.getTransition();
      #endif
}

func setFillExtrusionFloodLightGroundRadius(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillExtrusionFloodLightGroundRadius = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setFillExtrusionFloodLightGroundRadiusTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.fillExtrusionFloodLightGroundRadiusTransition = styleValue.getTransition();
      #endif
}

func setFillExtrusionFloodLightGroundAttenuation(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillExtrusionFloodLightGroundAttenuation = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setFillExtrusionFloodLightGroundAttenuationTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.fillExtrusionFloodLightGroundAttenuationTransition = styleValue.getTransition();
      #endif
}

func setFillExtrusionVerticalScale(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillExtrusionVerticalScale = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setFillExtrusionVerticalScaleTransition(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.fillExtrusionVerticalScaleTransition = styleValue.getTransition();
      #endif
}

func setFillExtrusionCutoffFadeRange(_ layer: inout FillExtrusionLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.fillExtrusionCutoffFadeRange = styleValue.mglStyleValueNumber();
        
      
      #endif
}



func setRasterStyleLayerVisibility(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
    layer.visibility = styleValue.isVisible();
}

func setRasterOpacity(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.rasterOpacity = styleValue.mglStyleValueNumber();
        
      
}

func setRasterOpacityTransition(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
    layer.rasterOpacityTransition = styleValue.getTransition();
}

func setRasterHueRotate(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.rasterHueRotate = styleValue.mglStyleValueNumber();
        
      
}

func setRasterHueRotateTransition(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
    layer.rasterHueRotateTransition = styleValue.getTransition();
}

func setRasterBrightnessMin(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.rasterBrightnessMin = styleValue.mglStyleValueNumber();
        
      
}

func setRasterBrightnessMinTransition(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
    layer.rasterBrightnessMinTransition = styleValue.getTransition();
}

func setRasterBrightnessMax(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.rasterBrightnessMax = styleValue.mglStyleValueNumber();
        
      
}

func setRasterBrightnessMaxTransition(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
    layer.rasterBrightnessMaxTransition = styleValue.getTransition();
}

func setRasterSaturation(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.rasterSaturation = styleValue.mglStyleValueNumber();
        
      
}

func setRasterSaturationTransition(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
    layer.rasterSaturationTransition = styleValue.getTransition();
}

func setRasterContrast(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.rasterContrast = styleValue.mglStyleValueNumber();
        
      
}

func setRasterContrastTransition(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
    layer.rasterContrastTransition = styleValue.getTransition();
}

func setRasterResampling(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.rasterResampling = styleValue.mglStyleValueEnum(); 
        
      
}

func setRasterFadeDuration(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.rasterFadeDuration = styleValue.mglStyleValueNumber();
        
      
}

func setRasterColor(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.rasterColor = styleValue.mglStyleValueColor();
        
      
      #endif
}

func setRasterColorMix(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.rasterColorMix = styleValue.mglStyleValueArrayNumber();
         
      
      #endif
}

func setRasterColorMixTransition(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.rasterColorMixTransition = styleValue.getTransition();
      #endif
}

func setRasterColorRange(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.rasterColorRange = styleValue.mglStyleValueArrayNumber();
         
      
      #endif
}

func setRasterColorRangeTransition(_ layer: inout RasterLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.rasterColorRangeTransition = styleValue.getTransition();
      #endif
}



func setHillshadeStyleLayerVisibility(_ layer: inout HillshadeLayer, styleValue: RNMBXStyleValue)
{
    layer.visibility = styleValue.isVisible();
}

func setHillshadeIlluminationDirection(_ layer: inout HillshadeLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.hillshadeIlluminationDirection = styleValue.mglStyleValueNumber();
        
      
}

func setHillshadeIlluminationAnchor(_ layer: inout HillshadeLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.hillshadeIlluminationAnchor = styleValue.mglStyleValueEnum(); 
        
      
}

func setHillshadeExaggeration(_ layer: inout HillshadeLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.hillshadeExaggeration = styleValue.mglStyleValueNumber();
        
      
}

func setHillshadeExaggerationTransition(_ layer: inout HillshadeLayer, styleValue: RNMBXStyleValue)
{
    layer.hillshadeExaggerationTransition = styleValue.getTransition();
}

func setHillshadeShadowColor(_ layer: inout HillshadeLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.hillshadeShadowColor = styleValue.mglStyleValueColor();
        
      
}

func setHillshadeShadowColorTransition(_ layer: inout HillshadeLayer, styleValue: RNMBXStyleValue)
{
    layer.hillshadeShadowColorTransition = styleValue.getTransition();
}

func setHillshadeHighlightColor(_ layer: inout HillshadeLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.hillshadeHighlightColor = styleValue.mglStyleValueColor();
        
      
}

func setHillshadeHighlightColorTransition(_ layer: inout HillshadeLayer, styleValue: RNMBXStyleValue)
{
    layer.hillshadeHighlightColorTransition = styleValue.getTransition();
}

func setHillshadeAccentColor(_ layer: inout HillshadeLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.hillshadeAccentColor = styleValue.mglStyleValueColor();
        
      
}

func setHillshadeAccentColorTransition(_ layer: inout HillshadeLayer, styleValue: RNMBXStyleValue)
{
    layer.hillshadeAccentColorTransition = styleValue.getTransition();
}



func setModelStyleLayerVisibility(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.visibility = styleValue.isVisible();
      #endif
}

func setModelId(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.modelId = styleValue.mglStyleValueString();
        
      
}

func setModelOpacity(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelOpacity = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setModelOpacityTransition(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.modelOpacityTransition = styleValue.getTransition();
      #endif
}

func setModelRotation(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.modelRotation = styleValue.mglStyleValueArrayNumber();
         
      
}

func setModelRotationTransition(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
    layer.modelRotationTransition = styleValue.getTransition();
}

func setModelScale(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.modelScale = styleValue.mglStyleValueArrayNumber();
         
      
}

func setModelScaleTransition(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
    layer.modelScaleTransition = styleValue.getTransition();
}

func setModelTranslation(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelTranslation = styleValue.mglStyleValueArrayNumber();
         
      
      #endif
}

func setModelTranslationTransition(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.modelTranslationTransition = styleValue.getTransition();
      #endif
}

func setModelColor(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelColor = styleValue.mglStyleValueColor();
        
      
      #endif
}

func setModelColorTransition(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.modelColorTransition = styleValue.getTransition();
      #endif
}

func setModelColorMixIntensity(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelColorMixIntensity = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setModelColorMixIntensityTransition(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.modelColorMixIntensityTransition = styleValue.getTransition();
      #endif
}

func setModelType(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelType = styleValue.mglStyleValueEnum(); 
        
      
      #endif
}

func setModelCastShadows(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelCastShadows = styleValue.mglStyleValueBoolean();
        
      
      #endif
}

func setModelReceiveShadows(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelReceiveShadows = styleValue.mglStyleValueBoolean();
        
      
      #endif
}

func setModelAmbientOcclusionIntensity(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelAmbientOcclusionIntensity = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setModelAmbientOcclusionIntensityTransition(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.modelAmbientOcclusionIntensityTransition = styleValue.getTransition();
      #endif
}

func setModelEmissiveStrength(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelEmissiveStrength = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setModelEmissiveStrengthTransition(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.modelEmissiveStrengthTransition = styleValue.getTransition();
      #endif
}

func setModelRoughness(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelRoughness = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setModelRoughnessTransition(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.modelRoughnessTransition = styleValue.getTransition();
      #endif
}

func setModelHeightBasedEmissiveStrengthMultiplier(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelHeightBasedEmissiveStrengthMultiplier = styleValue.mglStyleValueArrayNumber();
         
      
      #endif
}

func setModelHeightBasedEmissiveStrengthMultiplierTransition(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.modelHeightBasedEmissiveStrengthMultiplierTransition = styleValue.getTransition();
      #endif
}

func setModelCutoffFadeRange(_ layer: inout ModelLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.modelCutoffFadeRange = styleValue.mglStyleValueNumber();
        
      
      #endif
}



func setBackgroundStyleLayerVisibility(_ layer: inout BackgroundLayer, styleValue: RNMBXStyleValue)
{
    layer.visibility = styleValue.isVisible();
}

func setBackgroundColor(_ layer: inout BackgroundLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.backgroundColor = styleValue.mglStyleValueColor();
        
      
}

func setBackgroundColorTransition(_ layer: inout BackgroundLayer, styleValue: RNMBXStyleValue)
{
    layer.backgroundColorTransition = styleValue.getTransition();
}

func setBackgroundPattern(_ layer: inout BackgroundLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.backgroundPattern = styleValue.mglStyleValueResolvedImage();
        
      
}

func setBackgroundOpacity(_ layer: inout BackgroundLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.backgroundOpacity = styleValue.mglStyleValueNumber();
        
      
}

func setBackgroundOpacityTransition(_ layer: inout BackgroundLayer, styleValue: RNMBXStyleValue)
{
    layer.backgroundOpacityTransition = styleValue.getTransition();
}

func setBackgroundEmissiveStrength(_ layer: inout BackgroundLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.backgroundEmissiveStrength = styleValue.mglStyleValueNumber();
        
      
      #endif
}

func setBackgroundEmissiveStrengthTransition(_ layer: inout BackgroundLayer, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.backgroundEmissiveStrengthTransition = styleValue.getTransition();
      #endif
}



func setSkyStyleLayerVisibility(_ layer: inout SkyLayer, styleValue: RNMBXStyleValue)
{
    layer.visibility = styleValue.isVisible();
}

func setSkyType(_ layer: inout SkyLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.skyType = styleValue.mglStyleValueEnum(); 
        
      
}

func setSkyAtmosphereSun(_ layer: inout SkyLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.skyAtmosphereSun = styleValue.mglStyleValueArrayNumber();
         
      
}

func setSkyAtmosphereSunIntensity(_ layer: inout SkyLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.skyAtmosphereSunIntensity = styleValue.mglStyleValueNumber();
        
      
}

func setSkyGradientCenter(_ layer: inout SkyLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.skyGradientCenter = styleValue.mglStyleValueArrayNumber();
         
      
}

func setSkyGradientRadius(_ layer: inout SkyLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.skyGradientRadius = styleValue.mglStyleValueNumber();
        
      
}

func setSkyGradient(_ layer: inout SkyLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.skyGradient = styleValue.mglStyleValueColor();
        
      
}

func setSkyAtmosphereHaloColor(_ layer: inout SkyLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.skyAtmosphereHaloColor = styleValue.mglStyleValueColor();
        
      
}

func setSkyAtmosphereColor(_ layer: inout SkyLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.skyAtmosphereColor = styleValue.mglStyleValueColor();
        
      
}

func setSkyOpacity(_ layer: inout SkyLayer, styleValue: RNMBXStyleValue)
{
      
        
          layer.skyOpacity = styleValue.mglStyleValueNumber();
        
      
}

func setSkyOpacityTransition(_ layer: inout SkyLayer, styleValue: RNMBXStyleValue)
{
    layer.skyOpacityTransition = styleValue.getTransition();
}



func setAnchor(_ layer: inout Light, styleValue: RNMBXStyleValue)
{
      
        
          layer.anchor = styleValue.mglStyleValueAnchorRaw(); 
        
      
}

func setPosition(_ layer: inout Light, styleValue: RNMBXStyleValue)
{
    layer.position = styleValue.getSphericalPosition();
}

func setPositionTransition(_ layer: inout Light, styleValue: RNMBXStyleValue)
{
    layer.positionTransition = styleValue.getTransition();
}

func setColor(_ layer: inout Light, styleValue: RNMBXStyleValue)
{
      
        
          layer.color = styleValue.mglStyleValueColorRaw();
        
      
}

func setColorTransition(_ layer: inout Light, styleValue: RNMBXStyleValue)
{
    layer.colorTransition = styleValue.getTransition();
}

func setIntensity(_ layer: inout Light, styleValue: RNMBXStyleValue)
{
      
        
          layer.intensity = styleValue.mglStyleValueNumberRaw();
        
      
}

func setIntensityTransition(_ layer: inout Light, styleValue: RNMBXStyleValue)
{
    layer.intensityTransition = styleValue.getTransition();
}



func setRange(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
      
        
          layer.range = styleValue.mglStyleValueArrayNumber();
         
      
}

func setRangeTransition(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
    layer.rangeTransition = styleValue.getTransition();
}

func setColor(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
      
        
          layer.color = styleValue.mglStyleValueColor();
        
      
}

func setColorTransition(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
    layer.colorTransition = styleValue.getTransition();
}

func setHighColor(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
      
        
          layer.highColor = styleValue.mglStyleValueColor();
        
      
}

func setHighColorTransition(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
    layer.highColorTransition = styleValue.getTransition();
}

func setSpaceColor(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
      
        
          layer.spaceColor = styleValue.mglStyleValueColor();
        
      
}

func setSpaceColorTransition(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
    layer.spaceColorTransition = styleValue.getTransition();
}

func setHorizonBlend(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
      
        
          layer.horizonBlend = styleValue.mglStyleValueNumber();
        
      
}

func setHorizonBlendTransition(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
    layer.horizonBlendTransition = styleValue.getTransition();
}

func setStarIntensity(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
      
        
          layer.starIntensity = styleValue.mglStyleValueNumber();
        
      
}

func setStarIntensityTransition(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
    layer.starIntensityTransition = styleValue.getTransition();
}

func setVerticalRange(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
      
        
          layer.verticalRange = styleValue.mglStyleValueArrayNumber();
         
      
      #endif
}

func setVerticalRangeTransition(_ layer: inout Atmosphere, styleValue: RNMBXStyleValue)
{
      #if RNMBX_11
    layer.verticalRangeTransition = styleValue.getTransition();
      #endif
}



func setExaggeration(_ layer: inout Terrain, styleValue: RNMBXStyleValue)
{
      
        
          layer.exaggeration = styleValue.mglStyleValueNumber();
        
      
}



func _hasReactStyle(_ reactStyle: Dictionary<String, Any>) -> Bool
{
  return reactStyle != nil && reactStyle.keys.count > 0;
}

}
