{"version": 3, "names": ["React", "AbstractSource", "PureComponent", "setNativeProps", "props", "_nativeRef", "setNativeRef", "instance"], "sourceRoot": "../../../src", "sources": ["components/AbstractSource.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AAKzB,MAAMC,cAAc,SAGVD,KAAK,CAACE,aAAa,CAAwB;EAGnDC,cAAcA,CAACC,KAAsB,EAAE;IACrC,IAAI,IAAI,CAACC,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACF,cAAc,CAACC,KAAK,CAAC;IACvC;EACF;EAEAE,YAAY,GAECC,QAAQ,IAAK;IACxB,IAAI,CAACF,UAAU,GAAGE,QAAQ;EAC5B,CAAC;AACH;AAEA,eAAeN,cAAc", "ignoreList": []}