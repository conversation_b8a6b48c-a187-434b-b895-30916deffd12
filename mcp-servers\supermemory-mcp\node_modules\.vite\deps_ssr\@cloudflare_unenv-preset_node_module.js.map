{"version": 3, "sources": ["../../unenv/dist/runtime/node/module.mjs", "../../@cloudflare/unenv-preset/dist/runtime/node/module.mjs"], "sourcesContent": ["import { notImplemented, notImplementedClass } from \"../_internal/utils.mjs\";\nexport const _cache = Object.create(null);\nexport const _extensions = {\n\t\".js\": /* @__PURE__ */ notImplemented(\"module.require.extensions['.js']\"),\n\t\".json\": /* @__PURE__ */ notImplemented(\"module.require.extensions['.json']\"),\n\t\".node\": /* @__PURE__ */ notImplemented(\"module.require.extensions['.node']\")\n};\nexport const createRequire = function(_filename) {\n\treturn Object.assign(/* @__PURE__ */ notImplemented(\"module.require\"), {\n\t\tresolve: Object.assign(/* @__PURE__ */ notImplemented(\"module.require.resolve\"), { paths: /* @__PURE__ */ notImplemented(\"module.require.resolve.paths\") }),\n\t\tcache: Object.create(null),\n\t\textensions: _extensions,\n\t\tmain: undefined\n\t});\n};\nexport const getCompileCacheDir = function() {\n\treturn undefined;\n};\nexport const enableCompileCache = function(_cacheDir) {\n\treturn {\n\t\tstatus: 0,\n\t\tmessage: \"not implemented\"\n\t};\n};\nexport const constants = Object.freeze({ compileCacheStatus: Object.freeze({\n\tFAILED: 0,\n\tENABLED: 1,\n\tALREADY_ENABLED: 2,\n\tDISABLED: 3\n}) });\nexport const builtinModules = [\n\t\"_http_agent\",\n\t\"_http_client\",\n\t\"_http_common\",\n\t\"_http_incoming\",\n\t\"_http_outgoing\",\n\t\"_http_server\",\n\t\"_stream_duplex\",\n\t\"_stream_passthrough\",\n\t\"_stream_readable\",\n\t\"_stream_transform\",\n\t\"_stream_wrap\",\n\t\"_stream_writable\",\n\t\"_tls_common\",\n\t\"_tls_wrap\",\n\t\"assert\",\n\t\"assert/strict\",\n\t\"async_hooks\",\n\t\"buffer\",\n\t\"child_process\",\n\t\"cluster\",\n\t\"console\",\n\t\"constants\",\n\t\"crypto\",\n\t\"dgram\",\n\t\"diagnostics_channel\",\n\t\"dns\",\n\t\"dns/promises\",\n\t\"domain\",\n\t\"events\",\n\t\"fs\",\n\t\"fs/promises\",\n\t\"http\",\n\t\"http2\",\n\t\"https\",\n\t\"inspector\",\n\t\"inspector/promises\",\n\t\"module\",\n\t\"net\",\n\t\"os\",\n\t\"path\",\n\t\"path/posix\",\n\t\"path/win32\",\n\t\"perf_hooks\",\n\t\"process\",\n\t\"punycode\",\n\t\"querystring\",\n\t\"readline\",\n\t\"readline/promises\",\n\t\"repl\",\n\t\"stream\",\n\t\"stream/consumers\",\n\t\"stream/promises\",\n\t\"stream/web\",\n\t\"string_decoder\",\n\t\"sys\",\n\t\"timers\",\n\t\"timers/promises\",\n\t\"tls\",\n\t\"trace_events\",\n\t\"tty\",\n\t\"url\",\n\t\"util\",\n\t\"util/types\",\n\t\"v8\",\n\t\"vm\",\n\t\"wasi\",\n\t\"worker_threads\",\n\t\"zlib\"\n];\nexport const isBuiltin = function(id) {\n\treturn id.startsWith(\"node:\") || builtinModules.includes(id);\n};\nexport const runMain = /* @__PURE__ */ notImplemented(\"module.runMain\");\nexport const register = /* @__PURE__ */ notImplemented(\"module.register\");\nexport const syncBuiltinESMExports = function() {\n\treturn [];\n};\nexport const findSourceMap = function(path, error) {\n\treturn undefined;\n};\nexport const flushCompileCache = function flushCompileCache() {};\nexport const wrap = function(source) {\n\treturn `(function (exports, require, module, __filename, __dirname) { ${source}\\n});`;\n};\nexport const wrapper = [\"(function (exports, require, module, __filename, __dirname) { \", \"\\n});\"];\nexport const stripTypeScriptTypes = /* @__PURE__ */ notImplemented(\"module.stripTypeScriptTypes\");\nexport const SourceMap = /* @__PURE__ */ notImplementedClass(\"module.SourceMap\");\nexport const _debug = console.debug;\nexport const _findPath = /* @__PURE__ */ notImplemented(\"module._findPath\");\nexport const _initPaths = /* @__PURE__ */ notImplemented(\"module._initPaths\");\nexport const _load = /* @__PURE__ */ notImplemented(\"module._load\");\nexport const _nodeModulePaths = /* @__PURE__ */ notImplemented(\"module._nodeModulePaths\");\nexport const _preloadModules = /* @__PURE__ */ notImplemented(\"module._preloadModules\");\nexport const _resolveFilename = /* @__PURE__ */ notImplemented(\"module._resolveFilename\");\nexport const _resolveLookupPaths = /* @__PURE__ */ notImplemented(\"module._resolveLookupPaths\");\nexport const _stat = /* @__PURE__ */ notImplemented(\"module._stat\");\nexport const _readPackage = /* @__PURE__ */ notImplemented(\"module._readPackage\");\nexport const findPackageJSON = /* @__PURE__ */ notImplemented(\"module.findPackageJSON\");\nexport const getSourceMapsSupport = /* @__PURE__ */ notImplemented(\"module.getSourceMapsSupport\");\nexport const setSourceMapsSupport = /* @__PURE__ */ notImplemented(\"module.setSourceMapsSupport\");\nexport const _pathCache = Object.create(null);\nexport const globalPaths = [\"node_modules\"];\nexport const Module = {\n\tget Module() {\n\t\treturn Module;\n\t},\n\tSourceMap,\n\t_cache,\n\t_extensions,\n\t_debug,\n\t_pathCache,\n\t_findPath,\n\t_initPaths,\n\t_load,\n\t_nodeModulePaths,\n\t_preloadModules,\n\t_resolveFilename,\n\t_resolveLookupPaths,\n\t_stat,\n\t_readPackage,\n\tbuiltinModules,\n\tconstants,\n\tcreateRequire,\n\tenableCompileCache,\n\tfindSourceMap,\n\tgetCompileCacheDir,\n\tglobalPaths,\n\tisBuiltin,\n\tregister,\n\trunMain,\n\tsyncBuiltinESMExports,\n\twrap,\n\twrapper,\n\tflushCompileCache,\n\tstripTypeScriptTypes,\n\tfindPackageJSON,\n\tgetSourceMapsSupport,\n\tsetSourceMapsSupport\n};\nexport default Module;\n", "import { notImplemented } from \"unenv/_internal/utils\";\nimport {\n  _cache,\n  _debug,\n  _extensions,\n  _findPath,\n  _initPaths,\n  _load,\n  _nodeModulePaths,\n  _pathCache,\n  _preloadModules,\n  _resolveFilename,\n  _resolveLookupPaths,\n  builtinModules,\n  constants,\n  enableCompileCache,\n  findSourceMap,\n  getCompileCacheDir,\n  globalPaths,\n  isBuiltin,\n  Module,\n  register,\n  runMain,\n  SourceMap,\n  syncBuiltinESMExports,\n  wrap\n} from \"unenv/node/module\";\nexport {\n  Module,\n  SourceMap,\n  _cache,\n  _extensions,\n  _debug,\n  _pathCache,\n  _findPath,\n  _initPaths,\n  _load,\n  _nodeModulePaths,\n  _preloadModules,\n  _resolveFilename,\n  _resolveLookupPaths,\n  builtinModules,\n  constants,\n  enableCompileCache,\n  findSourceMap,\n  getCompileCacheDir,\n  globalPaths,\n  isBuiltin,\n  register,\n  runMain,\n  syncBuiltinESMExports,\n  wrap\n} from \"unenv/node/module\";\nconst workerdModule = process.getBuiltinModule(\"node:module\");\nexport const createRequire = (file) => {\n  return Object.assign(workerdModule.createRequire(file), {\n    resolve: Object.assign(\n      /* @__PURE__ */ notImplemented(\"module.require.resolve\"),\n      {\n        paths: /* @__PURE__ */ notImplemented(\"module.require.resolve.paths\")\n      }\n    ),\n    cache: /* @__PURE__ */ Object.create(null),\n    extensions: _extensions,\n    main: void 0\n  });\n};\nexport default {\n  Module,\n  SourceMap,\n  _cache,\n  _extensions,\n  _debug,\n  _pathCache,\n  _findPath,\n  _initPaths,\n  _load,\n  _nodeModulePaths,\n  _preloadModules,\n  _resolveFilename,\n  _resolveLookupPaths,\n  builtinModules,\n  enableCompileCache,\n  constants,\n  createRequire,\n  findSourceMap,\n  getCompileCacheDir,\n  globalPaths,\n  isBuiltin,\n  register,\n  runMain,\n  syncBuiltinESMExports,\n  wrap\n};\n"], "mappings": ";;;;;;;AACO,IAAM,SAAS,uBAAO,OAAO,IAAI;AACjC,IAAM,cAAc;AAAA,EAC1B,OAAuB,eAAe,kCAAkC;AAAA,EACxE,SAAyB,eAAe,oCAAoC;AAAA,EAC5E,SAAyB,eAAe,oCAAoC;AAC7E;AACO,IAAM,gBAAgB,SAAS,WAAW;AAChD,SAAO,OAAO,OAAuB,eAAe,gBAAgB,GAAG;AAAA,IACtE,SAAS,OAAO,OAAuB,eAAe,wBAAwB,GAAG,EAAE,OAAuB,eAAe,8BAA8B,EAAE,CAAC;AAAA,IAC1J,OAAO,uBAAO,OAAO,IAAI;AAAA,IACzB,YAAY;AAAA,IACZ,MAAM;AAAA,EACP,CAAC;AACF;AACO,IAAM,qBAAqB,WAAW;AAC5C,SAAO;AACR;AACO,IAAM,qBAAqB,SAAS,WAAW;AACrD,SAAO;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,EACV;AACD;AACO,IAAM,YAAY,OAAO,OAAO,EAAE,oBAAoB,OAAO,OAAO;AAAA,EAC1E,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,UAAU;AACX,CAAC,EAAE,CAAC;AACG,IAAM,iBAAiB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AACO,IAAM,YAAY,SAAS,IAAI;AACrC,SAAO,GAAG,WAAW,OAAO,KAAK,eAAe,SAAS,EAAE;AAC5D;AACO,IAAM,UAA0B,eAAe,gBAAgB;AAC/D,IAAM,WAA2B,eAAe,iBAAiB;AACjE,IAAM,wBAAwB,WAAW;AAC/C,SAAO,CAAC;AACT;AACO,IAAM,gBAAgB,SAAS,MAAM,OAAO;AAClD,SAAO;AACR;AACO,IAAM,oBAAoB,SAASA,qBAAoB;AAAC;AACxD,IAAM,OAAO,SAAS,QAAQ;AACpC,SAAO,iEAAiE,MAAM;AAAA;AAC/E;AACO,IAAM,UAAU,CAAC,kEAAkE,OAAO;AAC1F,IAAM,uBAAuC,eAAe,6BAA6B;AACzF,IAAM,YAA4B,oBAAoB,kBAAkB;AACxE,IAAM,SAAS,QAAQ;AACvB,IAAM,YAA4B,eAAe,kBAAkB;AACnE,IAAM,aAA6B,eAAe,mBAAmB;AACrE,IAAM,QAAwB,eAAe,cAAc;AAC3D,IAAM,mBAAmC,eAAe,yBAAyB;AACjF,IAAM,kBAAkC,eAAe,wBAAwB;AAC/E,IAAM,mBAAmC,eAAe,yBAAyB;AACjF,IAAM,sBAAsC,eAAe,4BAA4B;AACvF,IAAM,QAAwB,eAAe,cAAc;AAC3D,IAAM,eAA+B,eAAe,qBAAqB;AACzE,IAAM,kBAAkC,eAAe,wBAAwB;AAC/E,IAAM,uBAAuC,eAAe,6BAA6B;AACzF,IAAM,uBAAuC,eAAe,6BAA6B;AACzF,IAAM,aAAa,uBAAO,OAAO,IAAI;AACrC,IAAM,cAAc,CAAC,cAAc;AACnC,IAAM,SAAS;AAAA,EACrB,IAAI,SAAS;AACZ,WAAO;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;;;ACpHA,IAAM,gBAAgB,QAAQ,iBAAiB,aAAa;AACrD,IAAMC,iBAAgB,CAAC,SAAS;AACrC,SAAO,OAAO,OAAO,cAAc,cAAc,IAAI,GAAG;AAAA,IACtD,SAAS,OAAO;AAAA,MACE,eAAe,wBAAwB;AAAA,MACvD;AAAA,QACE,OAAuB,eAAe,8BAA8B;AAAA,MACtE;AAAA,IACF;AAAA,IACA,OAAuB,uBAAO,OAAO,IAAI;AAAA,IACzC,YAAY;AAAA,IACZ,MAAM;AAAA,EACR,CAAC;AACH;AACA,IAAO,iBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["flushCompileCache", "createRequire"]}