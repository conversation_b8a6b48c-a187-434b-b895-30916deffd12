require('dotenv').config();

module.exports = {
  expo: {
    name: "Mientior - Livraison Afrique",
    slug: "mientior-livraison-afrique",
    version: "1.0.0",
    main: "index.js",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    newArchEnabled: true,
    scheme: "mientior",
    description: "Plateforme de livraison connectant clients, marchands et livreurs à travers l'Afrique",
    splash: {
      image: "./assets/splash-icon.png",
      resizeMode: "contain",
      backgroundColor: "#0DCAA8"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.mientior.livraison",
      buildNumber: "1"
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#0DCAA8"
      },
      edgeToEdgeEnabled: true,
      softwareKeyboardLayoutMode: "pan",
      package: "com.mientior.livraison",
      versionCode: 1,
      permissions: [
        "ACCESS_FINE_LOCATION",
        "ACCESS_COARSE_LOCATION",
        "CAMERA",
        "NOTIFICATIONS"
      ]
    },
    web: {
      favicon: "./assets/favicon.png",
      bundler: "metro"
    },
    plugins: [
      "expo-camera",
      "expo-notifications",
      [
        "expo-build-properties",
        {
          android: {
            enableProguardInReleaseBuilds: true,
            enableShrinkResourcesInReleaseBuilds: true
          }
        }
      ]
    ],
    extra: {
      eas: {
        projectId: "mientior-livraison-afrique"
      }
    }
  }
};