/**
 * Configuration globale pour l'application Mientior Livraison
 * Ce fichier configure les polyfills et variables globales nécessaires
 */

// Désactiver les warnings de développement en production
if (!__DEV__) {
  console.log = () => {};
  console.warn = () => {};
  console.error = () => {};
}

// Configuration basique pour les polyfills si nécessaire
// Éviter les imports qui peuvent causer des problèmes
if (typeof global.setImmediate === 'undefined') {
  global.setImmediate = (fn, ...args) => setTimeout(fn, 0, ...args);
  global.clearImmediate = clearTimeout;
}
