// node_modules/unenv/dist/runtime/node/internal/http/agent.mjs
import { EventEmitter } from "node:events";
var Agent = class extends EventEmitter {
  __unenv__ = {};
  maxFreeSockets = 256;
  maxSockets = Infinity;
  maxTotalSockets = Infinity;
  freeSockets = {};
  sockets = {};
  requests = {};
  options;
  constructor(opts = {}) {
    super();
    this.options = opts;
  }
  destroy() {
  }
};

export {
  Agent
};
//# sourceMappingURL=chunk-M5C2KFXU.js.map
