import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '../store/authStore';
import { locationService } from '../services/locationService';
import { addressService } from '../services/addressService';
import { Location as LocationType } from '../types';

const LocationTestScreen: React.FC = () => {
  const { 
    currentLocation, 
    setCurrentLocation, 
    userAddresses, 
    addAddress, 
    loadUserAddresses,
    user 
  } = useAuthStore();

  const [isLoading, setIsLoading] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<string>('unknown');
  const [lastAddress, setLastAddress] = useState<string>('');
  const [testResults, setTestResults] = useState<string[]>([]);

  useEffect(() => {
    checkPermissionStatus();
    if (user) {
      loadUserAddresses();
    }
  }, [user]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const checkPermissionStatus = async () => {
    try {
      const permission = await locationService.requestLocationPermission();
      setPermissionStatus(permission.granted ? 'granted' : 'denied');
      addTestResult(`Permission status: ${permission.status}`);
    } catch (error) {
      addTestResult(`Permission error: ${error}`);
    }
  };

  const testGetCurrentLocation = async () => {
    setIsLoading(true);
    try {
      addTestResult('Testing getCurrentLocation...');
      const location = await locationService.getCurrentLocation({
        accuracy: 'high' as any,
        timeout: 15000,
      });
      
      setCurrentLocation(location);
      addTestResult(`✅ Location obtained: ${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`);
      
      // Test reverse geocoding
      const result = await locationService.reverseGeocode(location.latitude, location.longitude);
      setLastAddress(result.address);
      addTestResult(`✅ Address: ${result.address}`);
      
    } catch (error) {
      addTestResult(`❌ Location error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testAddressSearch = async () => {
    setIsLoading(true);
    try {
      addTestResult('Testing address search...');
      const results = await addressService.searchAddresses('Abidjan', currentLocation || undefined);
      
      if (results.length > 0) {
        addTestResult(`✅ Found ${results.length} addresses`);
        results.slice(0, 3).forEach((result, index) => {
          addTestResult(`  ${index + 1}. ${result.description}`);
        });
      } else {
        addTestResult('❌ No addresses found');
      }
    } catch (error) {
      addTestResult(`❌ Search error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSaveAddress = async () => {
    if (!currentLocation || !user) {
      Alert.alert('Erreur', 'Position actuelle ou utilisateur requis');
      return;
    }

    setIsLoading(true);
    try {
      addTestResult('Testing save address...');
      
      const addressData = {
        label: 'other' as const,
        nom_complet: lastAddress || `Test Address ${Date.now()}`,
        adresse_ligne1: lastAddress || 'Test Address Line 1',
        ville: 'Abidjan',
        quartier: 'Test Quartier',
        pays: 'Côte d\'Ivoire',
        coordonnees: currentLocation,
        is_default: userAddresses.length === 0,
      };

      await addAddress(addressData);
      addTestResult('✅ Address saved successfully');
      
    } catch (error) {
      addTestResult(`❌ Save error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testDistanceCalculation = () => {
    if (!currentLocation) {
      Alert.alert('Erreur', 'Position actuelle requise');
      return;
    }

    addTestResult('Testing distance calculation...');
    
    // Test avec quelques points de référence en Afrique de l'Ouest
    const testPoints = [
      { name: 'Plateau (Abidjan)', lat: 5.3200, lng: -4.0200 },
      { name: 'Cocody (Abidjan)', lat: 5.3500, lng: -3.9800 },
      { name: 'Accra (Ghana)', lat: 5.6037, lng: -0.1870 },
    ];

    testPoints.forEach(point => {
      const distance = locationService.calculateDistance(
        currentLocation,
        { latitude: point.lat, longitude: point.lng }
      );
      addTestResult(`📍 Distance to ${point.name}: ${distance.toFixed(2)} km`);
    });
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🧪 Location Service Test</Text>
        <Text style={styles.subtitle}>Test de toutes les fonctionnalités de géolocalisation</Text>
      </View>

      {/* Status Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📊 Status</Text>
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Permission:</Text>
          <Text style={[styles.statusValue, { color: permissionStatus === 'granted' ? '#10B981' : '#EF4444' }]}>
            {permissionStatus}
          </Text>
        </View>
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Current Location:</Text>
          <Text style={styles.statusValue}>
            {currentLocation ? 
              `${currentLocation.latitude.toFixed(4)}, ${currentLocation.longitude.toFixed(4)}` : 
              'None'
            }
          </Text>
        </View>
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Saved Addresses:</Text>
          <Text style={styles.statusValue}>{userAddresses.length}</Text>
        </View>
      </View>

      {/* Test Buttons */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🔧 Tests</Text>
        
        <TouchableOpacity 
          style={styles.testButton} 
          onPress={testGetCurrentLocation}
          disabled={isLoading}
        >
          <Ionicons name="location" size={20} color="#FFFFFF" />
          <Text style={styles.testButtonText}>Test Current Location</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.testButton} 
          onPress={testAddressSearch}
          disabled={isLoading}
        >
          <Ionicons name="search" size={20} color="#FFFFFF" />
          <Text style={styles.testButtonText}>Test Address Search</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.testButton} 
          onPress={testSaveAddress}
          disabled={isLoading || !currentLocation || !user}
        >
          <Ionicons name="save" size={20} color="#FFFFFF" />
          <Text style={styles.testButtonText}>Test Save Address</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.testButton} 
          onPress={testDistanceCalculation}
          disabled={!currentLocation}
        >
          <Ionicons name="calculator" size={20} color="#FFFFFF" />
          <Text style={styles.testButtonText}>Test Distance Calculation</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.testButton, styles.clearButton]} 
          onPress={clearResults}
        >
          <Ionicons name="trash" size={20} color="#FFFFFF" />
          <Text style={styles.testButtonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      {/* Loading Indicator */}
      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0DCAA8" />
          <Text style={styles.loadingText}>Testing...</Text>
        </View>
      )}

      {/* Results Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📝 Test Results</Text>
        <View style={styles.resultsContainer}>
          {testResults.length === 0 ? (
            <Text style={styles.noResults}>No test results yet</Text>
          ) : (
            testResults.map((result, index) => (
              <Text key={index} style={styles.resultItem}>
                {result}
              </Text>
            ))
          )}
        </View>
      </View>

      {/* Saved Addresses */}
      {userAddresses.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🏠 Saved Addresses</Text>
          {userAddresses.map((address) => (
            <View key={address.id} style={styles.addressItem}>
              <Text style={styles.addressLabel}>{address.label}</Text>
              <Text style={styles.addressText}>{address.adresse_ligne1}</Text>
              <Text style={styles.addressCoords}>
                {address.coordonnees.latitude.toFixed(4)}, {address.coordonnees.longitude.toFixed(4)}
              </Text>
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  section: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0DCAA8',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  clearButton: {
    backgroundColor: '#EF4444',
  },
  testButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: '#6B7280',
  },
  resultsContainer: {
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
    maxHeight: 300,
  },
  noResults: {
    fontSize: 14,
    color: '#9CA3AF',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  resultItem: {
    fontSize: 12,
    color: '#374151',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  addressItem: {
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  addressLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  addressText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  addressCoords: {
    fontSize: 12,
    color: '#9CA3AF',
    fontFamily: 'monospace',
  },
});

export default LocationTestScreen;
