{"name": "livraison-afrique-mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx", "prebuild": "expo prebuild --clean", "db:migrate": "node scripts/run-migrations.js", "db:update-refs": "node scripts/update-references.js"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "@rnmapbox/maps": "^10.1.39", "@supabase/supabase-js": "^2.43.4", "@tanstack/react-query": "^5.40.0", "dotenv": "^16.5.0", "expo": "^53.0.0", "expo-build-properties": "^0.14.6", "expo-camera": "~16.1.6", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.4", "expo-dev-client": "~5.1.8", "expo-device": "~7.1.4", "expo-font": "~13.3.1", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-location": "~18.1.5", "expo-notifications": "^0.31.2", "expo-sms": "~13.1.4", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-animatable": "^1.4.0", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-paper": "^5.12.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.1.0", "react-native-webview": "13.13.5", "zustand": "^4.5.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "^19.0.0", "@types/react-native": "^0.73.0", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint": "^8.57.0", "eslint-config-expo": "^7.0.0", "typescript": "~5.3.3"}, "private": true}