{"version": 3, "file": "PointAnnotation.d.ts", "sourceRoot": "", "sources": ["../../../../src/components/PointAnnotation.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAE,cAAc,EAAE,KAAK,SAAS,EAAE,MAAM,OAAO,CAAC;AAC9D,OAAO,EAAc,KAAK,SAAS,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,EAAE,OAAO,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAKtE,OAAO,EAAE,KAAK,SAAS,EAAE,MAAM,oBAAoB,CAAC;AACpD,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAI7C,OAA8B,EAAE,KAAK,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAEhF,eAAO,MAAM,kBAAkB,yBAAyB,CAAC;AAUzD,KAAK,cAAc,GAAG,OAAO,CAC3B,KAAK,EACL;IACE,YAAY,EAAE,MAAM,CAAC;IACrB,YAAY,EAAE,MAAM,CAAC;CACtB,CACF,CAAC;AAEF,KAAK,KAAK,GAAG,SAAS,GAAG;IACvB;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IAEnB;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IAEpB;;OAEG;IACH,UAAU,EAAE,QAAQ,CAAC;IAErB;;;;;;OAMG;IACH,MAAM,CAAC,EAAE;QACP;;WAEG;QACH,CAAC,EAAE,MAAM,CAAC;QACV;;WAEG;QACH,CAAC,EAAE,MAAM,CAAC;KACX,CAAC;IAEF;;OAEG;IACH,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,KAAK,IAAI,CAAC;IAE/C;;OAEG;IACH,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,KAAK,IAAI,CAAC;IAEjD;;OAEG;IACH,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,KAAK,IAAI,CAAC;IAEhD;;OAEG;IACH,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,KAAK,IAAI,CAAC;IAE9C;;OAEG;IACH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,KAAK,IAAI,CAAC;IAE3C;;OAEG;IACH,QAAQ,EAAE,KAAK,CAAC,YAAY,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;IAExE,KAAK,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;CAC5B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEF;;;;;;;;;GASG;AACH,cAAM,eAAgB,SAAQ,oBAG7B;IACC,MAAM,CAAC,YAAY;;;;;;MAGjB;IAEF,UAAU,EAAE,wBAAwB,GAAG,IAAI,CAAQ;gBAEvC,KAAK,EAAE,KAAK;IAUxB,cAAc,CAAC,CAAC,SAAS,QAAQ,GAAG,IAAI,GAAG,QAAQ,EAAE,CAAC,GAAG,iBAAiB,EACxE,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,GACtC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAUxB,WAAW,CAAC,CAAC,EAAE,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;IAOjE,aAAa,CAAC,CAAC,EAAE,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;IAOnE,YAAY,CAAC,CAAC,EAAE,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;IAOlE,OAAO,CAAC,CAAC,EAAE,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;IAO7D,UAAU,CAAC,CAAC,EAAE,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;IAOhE,cAAc,IAAI,MAAM,GAAG,SAAS;IAOpC;;;;OAIG;IACH,OAAO;IAIP,aAAa,CAAC,SAAS,EAAE,wBAAwB,GAAG,IAAI;IAKxD,MAAM;CA0BP;AAED,KAAK,WAAW,GAAG,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC,GAAG;IAC7C,UAAU,EAAE,MAAM,GAAG,SAAS,CAAC;CAChC,CAAC;AAEF,KAAK,wBAAwB,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;AAEvD,eAAe,eAAe,CAAC"}