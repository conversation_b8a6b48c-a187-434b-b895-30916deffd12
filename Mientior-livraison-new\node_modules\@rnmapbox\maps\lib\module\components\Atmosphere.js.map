{"version": 3, "names": ["React", "memo", "useMemo", "transformStyle", "RNMBXAtmosphereNativeComponent", "jsx", "_jsx", "Atmosphere", "props", "baseProps", "reactStyle", "style", "undefined"], "sourceRoot": "../../../src", "sources": ["components/Atmosphere.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,IAAI,EAAEC,OAAO,QAAQ,OAAO;AAG5C,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,OAAOC,8BAA8B,MAAM,yCAAyC;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAMrF,OAAO,MAAMC,UAAU,gBAAGN,IAAI,CAAEO,KAAY,IAAK;EAC/C,MAAMC,SAAS,GAAGP,OAAO,CAAC,MAAM;IAC9B,OAAO;MACL,GAAGM,KAAK;MACRE,UAAU,EAAEP,cAAc,CAACK,KAAK,CAACG,KAAK,CAAC;MACvCA,KAAK,EAAEC;IACT,CAAC;EACH,CAAC,EAAE,CAACJ,KAAK,CAAC,CAAC;EAEX,oBAAOF,IAAA,CAACF,8BAA8B;IAAA,GAAKK;EAAS,CAAG,CAAC;AAC1D,CAAC,CAAC", "ignoreList": []}