{"version": 3, "sources": ["../../unenv/dist/runtime/_internal/utils.mjs"], "sourcesContent": ["/* @__NO_SIDE_EFFECTS__ */\nexport function rawHeaders(headers) {\n\tconst rawHeaders = [];\n\tfor (const key in headers) {\n\t\tif (Array.isArray(headers[key])) {\n\t\t\tfor (const h of headers[key]) {\n\t\t\t\trawHeaders.push(key, h);\n\t\t\t}\n\t\t} else {\n\t\t\trawHeaders.push(key, headers[key]);\n\t\t}\n\t}\n\treturn rawHeaders;\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function mergeFns(...functions) {\n\treturn function(...args) {\n\t\tfor (const fn of functions) {\n\t\t\tfn(...args);\n\t\t}\n\t};\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function createNotImplementedError(name) {\n\treturn new Error(`[unenv] ${name} is not implemented yet!`);\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplemented(name) {\n\tconst fn = () => {\n\t\tthrow createNotImplementedError(name);\n\t};\n\treturn Object.assign(fn, { __unenv__: true });\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplementedAsync(name) {\n\tconst fn = notImplemented(name);\n\tfn.__promisify__ = () => notImplemented(name + \".__promisify__\");\n\tfn.native = fn;\n\treturn fn;\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplementedClass(name) {\n\treturn class {\n\t\t__unenv__ = true;\n\t\tconstructor() {\n\t\t\tthrow new Error(`[unenv] ${name} is not implemented yet!`);\n\t\t}\n\t};\n}\n"], "mappings": ";AACO,SAAS,WAAW,SAAS;AACnC,QAAMA,cAAa,CAAC;AACpB,aAAW,OAAO,SAAS;AAC1B,QAAI,MAAM,QAAQ,QAAQ,GAAG,CAAC,GAAG;AAChC,iBAAW,KAAK,QAAQ,GAAG,GAAG;AAC7B,QAAAA,YAAW,KAAK,KAAK,CAAC;AAAA,MACvB;AAAA,IACD,OAAO;AACN,MAAAA,YAAW,KAAK,KAAK,QAAQ,GAAG,CAAC;AAAA,IAClC;AAAA,EACD;AACA,SAAOA;AACR;AAUO,SAAS,0BAA0B,MAAM;AAC/C,SAAO,IAAI,MAAM,WAAW,IAAI,0BAA0B;AAC3D;AAEO,SAAS,eAAe,MAAM;AACpC,QAAM,KAAK,MAAM;AAChB,UAAM,0BAA0B,IAAI;AAAA,EACrC;AACA,SAAO,OAAO,OAAO,IAAI,EAAE,WAAW,KAAK,CAAC;AAC7C;AAEO,SAAS,oBAAoB,MAAM;AACzC,QAAM,KAAK,eAAe,IAAI;AAC9B,KAAG,gBAAgB,MAAM,eAAe,OAAO,gBAAgB;AAC/D,KAAG,SAAS;AACZ,SAAO;AACR;AAEO,SAAS,oBAAoB,MAAM;AACzC,SAAO,MAAM;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AACb,YAAM,IAAI,MAAM,WAAW,IAAI,0BAA0B;AAAA,IAC1D;AAAA,EACD;AACD;", "names": ["rawHeaders"]}