{"version": 3, "names": ["React", "NativeModules", "RNMBXFillLayerNativeComponent", "AbstractLayer", "jsx", "_jsx", "Mapbox", "RNMBXModule", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultProps", "sourceID", "StyleSource", "DefaultSourceID", "render", "props", "baseProps", "sourceLayerID", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../../src", "sources": ["components/FillLayer.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,cAAc;AAI5C,OAAOC,6BAA6B,MAAM,wCAAwC;AAElF,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE5C,MAAMC,MAAM,GAAGL,aAAa,CAACM,WAAW;;AAExC;;AA+DA;;AAaA;AACA;AACA;AACA,MAAMC,SAAS,SAASL,aAAa,CAAyB;EAC5D,OAAOM,YAAY,GAAG;IACpBC,QAAQ,EAAEJ,MAAM,CAACK,WAAW,CAACC;EAC/B,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAMC,KAAK,GAAG;MACZ,GAAG,IAAI,CAACC,SAAS;MACjBC,aAAa,EAAE,IAAI,CAACF,KAAK,CAACE;IAC5B,CAAC;IACD;MAAA;MACE;MACAX,IAAA,CAACH,6BAA6B;QAACe,GAAG,EAAE,IAAI,CAACC,cAAe;QAAA,GAAKJ;MAAK,CAAG;IAAC;EAE1E;AACF;AAEA,eAAeN,SAAS", "ignoreList": []}