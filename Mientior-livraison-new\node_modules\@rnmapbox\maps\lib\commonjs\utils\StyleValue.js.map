{"version": 3, "names": ["_reactNative", "require", "_BridgeValue", "_interopRequireDefault", "_styleMap", "e", "__esModule", "default", "transformStyle", "style", "nativeStyle", "styleProps", "Object", "keys", "styleProp", "styleType", "getStyleType", "rawStyle", "color", "processColor", "undefined", "console", "error", "Image", "resolveAssetSource", "Array", "isArray", "startsWith", "warn", "bridgeValue", "BridgeValue", "styletype", "stylevalue", "toJSON"], "sourceRoot": "../../../src", "sources": ["utils/StyleValue.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AAKA,IAAAG,SAAA,GAAAH,OAAA;AAA0C,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAOnC,SAASG,cAAcA,CAC5BC,KAAqC,EACM;EAC3C,IAAI,CAACA,KAAK,EAAE;IACV;EACF;EAEA,MAAMC,WAA0C,GAAG,CAAC,CAAC;EACrD,MAAMC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACJ,KAAK,CAA8B;EAClE,KAAK,MAAMK,SAAS,IAAIH,UAAU,EAAE;IAClC,MAAMI,SAAS,GAAG,IAAAC,sBAAY,EAACF,SAAS,CAAC;IACzC,IAAIG,QAAkC,GAAGR,KAAK,CAACK,SAAS,CAAC;IAEzD,IAAIC,SAAS,KAAK,OAAO,IAAI,OAAOE,QAAQ,KAAK,QAAQ,EAAE;MACzD,MAAMC,KAAK,GAAG,IAAAC,yBAAY,EAACF,QAAQ,CAAC;MACpC,IAAIC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;QACtEG,OAAO,CAACC,KAAK,CAAC,kCAAkCL,QAAQ,YAAY,CAAC;QACrEA,QAAQ,GAAG,QAAQ;MACrB,CAAC,MAAM;QACLA,QAAQ,GAAGC,KAAK;MAClB;IACF,CAAC,MAAM,IAAIH,SAAS,KAAK,OAAO,IAAI,OAAOE,QAAQ,KAAK,QAAQ,EAAE;MAChEA,QAAQ,GACLM,kBAAK,CAACC,kBAAkB,CAACP,QAAQ,CAAC,IAAgC,CAAC,CAAC;IACzE;IACA,IACEF,SAAS,KAAK,OAAO,IACrB,EACEU,KAAK,CAACC,OAAO,CAACT,QAAQ,CAAC,IACtB,OAAOA,QAAQ,KAAK,QAAQ,IAC3B,CAACA,QAAQ,CAACU,UAAU,CAAC,SAAS,CAAC,IAC/B,CAACV,QAAQ,CAACU,UAAU,CAAC,UAAU,CAAE,CACpC,EACD;MACAN,OAAO,CAACO,IAAI,CACV,6BAA6Bd,SAAS,4GACxC,CAAC;IACH;IAEA,IAAIG,QAAQ,KAAKG,SAAS,EAAE;MAC1B,MAAMS,WAAW,GAAG,IAAIC,oBAAW,CAACb,QAAQ,CAAC;MAC7CP,WAAW,CAACI,SAAS,CAAC,GAAG;QACvBiB,SAAS,EAAEhB,SAAS;QACpBiB,UAAU,EAAEH,WAAW,CAACI,MAAM,CAAC;MACjC,CAAC;IACH;EACF;EAEA,OAAOvB,WAAW;AACpB", "ignoreList": []}