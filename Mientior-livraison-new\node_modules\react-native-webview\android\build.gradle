import java.nio.file.Paths

buildscript {
    ext.safeExtGet = {prop ->
        rootProject.ext.has(prop) ? rootProject.ext.get(prop) : project.properties['ReactNativeWebView_' + prop]
    }
    repositories {
        google()
        gradlePluginPortal()
    }
    dependencies {
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${safeExtGet('kotlinVersion')}")
        classpath("com.android.tools.build:gradle:7.0.4")
    }
}

def getExtOrIntegerDefault(prop) {
  return rootProject.ext.has(prop) ? rootProject.ext.get(prop) : (project.properties['ReactNativeWebView_' + prop]).toInteger()
}

static def findNodeModulePath(baseDir, packageName) {
    def basePath = baseDir.toPath().normalize()
    // Node's module resolution algorithm searches up to the root directory,
    // after which the base path will be null
    while (basePath) {
        def candidatePath = Paths.get(basePath.toString(), "node_modules", packageName)
        if (candidatePath.toFile().exists()) {
            return candidatePath.toString()
        }
        basePath = basePath.getParent()
    }
    return null
}

def isNewArchitectureEnabled() {
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

def supportsNamespace() {
  def parsed = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION.tokenize('.')
  def major = parsed[0].toInteger()
  def minor = parsed[1].toInteger()

  // Namespace support was added in 7.3.0
  if (major == 7 && minor >= 3) {
    return true
  }

  return major >= 8
}

apply plugin: 'com.android.library'
if (isNewArchitectureEnabled()) {
    apply plugin: 'com.facebook.react'
}
apply plugin: 'kotlin-android'

android {
    if (supportsNamespace()) {
        namespace "com.reactnativecommunity.webview"
        buildFeatures {
            buildConfig true
        }

        sourceSets {
            main {
                manifest.srcFile "src/main/AndroidManifestNew.xml"
            }
        }
    }

    compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')

    defaultConfig {
        minSdkVersion getExtOrIntegerDefault('minSdkVersion')
        targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
        buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()
    }

    sourceSets {
        main {
            if (isNewArchitectureEnabled()) {
                java.srcDirs += ['src/newarch']
            } else {
                java.srcDirs += ['src/oldarch']
            }
        }
    }
}

def reactNativePath = findNodeModulePath(projectDir, "react-native")
def codegenPath = findNodeModulePath(projectDir, "@react-native/codegen")
if (codegenPath == null) {
  // Compat for 0.71 and lower (to be removed)
  codegenPath = findNodeModulePath(projectDir, "react-native-codegen")
}

repositories {
    maven {
        url "${reactNativePath}/android"
    }
    mavenCentral()
    google()
}

dependencies {
    implementation 'com.facebook.react:react-native:+'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:${safeExtGet('kotlinVersion')}"
    implementation "androidx.webkit:webkit:${safeExtGet('webkitVersion')}"
}
