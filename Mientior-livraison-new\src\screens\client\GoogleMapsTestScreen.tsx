import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { WebView } from 'react-native-webview';

interface GoogleMapsTestScreenProps {
  navigation: any;
}

const GoogleMapsTestScreen: React.FC<GoogleMapsTestScreenProps> = ({ navigation }) => {
  const [mapReady, setMapReady] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<{lat: number, lng: number} | null>(null);
  const webViewRef = useRef<WebView>(null);

  // HTML pour tester Google Maps
  const testMapHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body, html { 
          margin: 0; 
          padding: 0; 
          height: 100%; 
          font-family: Arial, sans-serif;
        }
        #map { 
          height: 100%; 
          width: 100%; 
        }
        .info-panel {
          position: absolute;
          top: 10px;
          left: 10px;
          background: white;
          padding: 10px;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.2);
          z-index: 1000;
        }
      </style>
    </head>
    <body>
      <div class="info-panel">
        <div id="status">🔄 Chargement...</div>
        <div id="location">📍 Aucune sélection</div>
      </div>
      <div id="map"></div>
      
      <script>
        let map;
        let marker;
        
        function updateStatus(message) {
          document.getElementById('status').innerHTML = message;
        }
        
        function updateLocation(lat, lng) {
          document.getElementById('location').innerHTML = 
            '📍 Lat: ' + lat.toFixed(4) + ', Lng: ' + lng.toFixed(4);
        }
        
        function initMap() {
          try {
            updateStatus('🗺️ Initialisation...');
            
            map = new google.maps.Map(document.getElementById('map'), {
              center: { lat: 6.3702, lng: 2.3912 }, // Cotonou, Bénin
              zoom: 13,
              mapTypeControl: true,
              streetViewControl: true,
              fullscreenControl: true,
              zoomControl: true,
            });
            
            updateStatus('✅ Carte chargée');
            
            // Marqueur initial
            marker = new google.maps.Marker({
              position: { lat: 6.3702, lng: 2.3912 },
              map: map,
              title: 'Cotonou, Bénin',
              icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(\`
                  <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="16" cy="16" r="14" fill="#10B981" stroke="white" stroke-width="3"/>
                    <circle cx="16" cy="16" r="6" fill="white"/>
                  </svg>
                \`),
                scaledSize: new google.maps.Size(32, 32),
                anchor: new google.maps.Point(16, 16)
              }
            });
            
            updateLocation(6.3702, 2.3912);
            
            // Écouter les clics
            map.addListener('click', function(event) {
              const lat = event.latLng.lat();
              const lng = event.latLng.lng();
              
              // Déplacer le marqueur
              if (marker) {
                marker.setPosition({ lat: lat, lng: lng });
              }
              
              updateLocation(lat, lng);
              updateStatus('🎯 Position sélectionnée');
              
              // Envoyer à React Native
              if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'locationSelected',
                  latitude: lat,
                  longitude: lng
                }));
              }
            });
            
            // Confirmer que tout fonctionne
            setTimeout(() => {
              updateStatus('🚀 Prêt - Cliquez sur la carte');
            }, 1000);
            
          } catch (error) {
            updateStatus('❌ Erreur: ' + error.message);
            console.error('Erreur Google Maps:', error);
          }
        }
        
        // Gestion des erreurs
        window.onerror = function(msg, url, line, col, error) {
          updateStatus('❌ Erreur JS: ' + msg);
          return false;
        };
        
      </script>
      <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s&callback=initMap"
        onerror="document.getElementById('status').innerHTML = '❌ Erreur chargement API'">
      </script>
    </body>
    </html>
  `;

  const handleWebViewMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === 'locationSelected') {
        setSelectedLocation({
          lat: data.latitude,
          lng: data.longitude
        });
        Alert.alert(
          'Position sélectionnée',
          `Latitude: ${data.latitude.toFixed(4)}\nLongitude: ${data.longitude.toFixed(4)}`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.log('Erreur parsing message:', error);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Test Google Maps</Text>
      </View>

      {/* Info Panel */}
      <View style={styles.infoPanel}>
        <Text style={styles.infoText}>
          🗺️ Test de l'API Google Maps
        </Text>
        <Text style={styles.infoSubtext}>
          Clé API: AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s
        </Text>
        {selectedLocation && (
          <Text style={styles.locationText}>
            📍 Sélectionné: {selectedLocation.lat.toFixed(4)}, {selectedLocation.lng.toFixed(4)}
          </Text>
        )}
      </View>

      {/* Map Container */}
      <View style={styles.mapContainer}>
        <WebView
          ref={webViewRef}
          source={{ html: testMapHTML }}
          style={styles.webView}
          onMessage={handleWebViewMessage}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          onLoadEnd={() => {
            console.log('🌐 Test Google Maps chargé');
            setMapReady(true);
          }}
          onError={(error) => {
            console.log('❌ Erreur WebView:', error);
            Alert.alert('Erreur', 'Impossible de charger Google Maps');
          }}
        />
        
        {/* Loading Overlay */}
        {!mapReady && (
          <View style={styles.loadingOverlay}>
            <Ionicons name="globe-outline" size={48} color="#10B981" />
            <Text style={styles.loadingText}>Test Google Maps...</Text>
          </View>
        )}
      </View>

      {/* Test Button */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.testButton}
          onPress={() => {
            Alert.alert(
              'Test Google Maps',
              'Clé API configurée et WebView prête.\nCliquez sur la carte pour tester l\'interaction.',
              [{ text: 'OK' }]
            );
          }}
        >
          <Text style={styles.testButtonText}>🧪 Tester l'interaction</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
  },
  infoPanel: {
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  infoText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  infoSubtext: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 4,
  },
  locationText: {
    fontSize: 14,
    color: '#10B981',
    fontWeight: '500',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  webView: {
    flex: 1,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
    marginTop: 12,
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  testButton: {
    backgroundColor: '#10B981',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  testButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default GoogleMapsTestScreen;
