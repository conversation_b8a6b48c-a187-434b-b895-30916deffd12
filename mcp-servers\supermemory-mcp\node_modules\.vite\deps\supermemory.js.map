{"version": 3, "sources": ["../../supermemory/internal/tslib.mjs", "../../supermemory/src/internal/utils/uuid.ts", "../../supermemory/src/internal/errors.ts", "../../supermemory/src/core/error.ts", "../../supermemory/src/internal/utils/values.ts", "../../supermemory/src/internal/utils/sleep.ts", "../../supermemory/src/internal/utils/log.ts", "../../supermemory/src/version.ts", "../../supermemory/src/internal/detect-platform.ts", "../../supermemory/src/internal/shims.ts", "../../supermemory/src/internal/request-options.ts", "../../supermemory/src/internal/uploads.ts", "../../supermemory/src/internal/to-file.ts", "../../supermemory/src/core/resource.ts", "../../supermemory/src/internal/headers.ts", "../../supermemory/src/internal/utils/path.ts", "../../supermemory/src/resources/connections.ts", "../../supermemory/src/resources/memories.ts", "../../supermemory/src/resources/search.ts", "../../supermemory/src/resources/settings.ts", "../../supermemory/src/internal/parse.ts", "../../supermemory/src/core/api-promise.ts", "../../supermemory/src/internal/utils/env.ts", "../../supermemory/src/client.ts"], "sourcesContent": ["function __classPrivateFieldSet(receiver, state, value, kind, f) {\n    if (kind === \"m\")\n        throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f)\n        throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver))\n        throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return kind === \"a\" ? f.call(receiver, value) : f ? (f.value = value) : state.set(receiver, value), value;\n}\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n    if (kind === \"a\" && !f)\n        throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver))\n        throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\nexport { __classPrivateFieldSet, __classPrivateFieldGet };\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n/**\n * https://stackoverflow.com/a/2117523\n */\nexport let uuid4 = function () {\n  const { crypto } = globalThis as any;\n  if (crypto?.randomUUID) {\n    uuid4 = crypto.randomUUID.bind(crypto);\n    return crypto.randomUUID();\n  }\n  const u8 = new Uint8Array(1);\n  const randomByte = crypto ? () => crypto.getRandomValues(u8)[0]! : () => (Math.random() * 0xff) & 0xff;\n  return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, (c) =>\n    (+c ^ (randomByte() & (15 >> (+c / 4)))).toString(16),\n  );\n};\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nexport function isAbortError(err: unknown) {\n  return (\n    typeof err === 'object' &&\n    err !== null &&\n    // Spec-compliant fetch implementations\n    (('name' in err && (err as any).name === 'AbortError') ||\n      // Expo fetch\n      ('message' in err && String((err as any).message).includes('FetchRequestCanceledException')))\n  );\n}\n\nexport const castToError = (err: any): Error => {\n  if (err instanceof Error) return err;\n  if (typeof err === 'object' && err !== null) {\n    try {\n      if (Object.prototype.toString.call(err) === '[object Error]') {\n        // @ts-ignore - not all envs have native support for cause yet\n        const error = new Error(err.message, err.cause ? { cause: err.cause } : {});\n        if (err.stack) error.stack = err.stack;\n        // @ts-ignore - not all envs have native support for cause yet\n        if (err.cause && !error.cause) error.cause = err.cause;\n        if (err.name) error.name = err.name;\n        return error;\n      }\n    } catch {}\n    try {\n      return new Error(JSON.stringify(err));\n    } catch {}\n  }\n  return new Error(err);\n};\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { castToError } from '../internal/errors';\n\nexport class SupermemoryError extends Error {}\n\nexport class APIError<\n  TStatus extends number | undefined = number | undefined,\n  THeaders extends Headers | undefined = Headers | undefined,\n  T<PERSON>rror extends Object | undefined = Object | undefined,\n> extends SupermemoryError {\n  /** HTTP status for the response that caused the error */\n  readonly status: TStatus;\n  /** HTTP headers for the response that caused the error */\n  readonly headers: THeaders;\n  /** JSON body of the response that caused the error */\n  readonly error: TError;\n\n  constructor(status: TStatus, error: TError, message: string | undefined, headers: THeaders) {\n    super(`${APIError.makeMessage(status, error, message)}`);\n    this.status = status;\n    this.headers = headers;\n    this.error = error;\n  }\n\n  private static makeMessage(status: number | undefined, error: any, message: string | undefined) {\n    const msg =\n      error?.message ?\n        typeof error.message === 'string' ?\n          error.message\n        : JSON.stringify(error.message)\n      : error ? JSON.stringify(error)\n      : message;\n\n    if (status && msg) {\n      return `${status} ${msg}`;\n    }\n    if (status) {\n      return `${status} status code (no body)`;\n    }\n    if (msg) {\n      return msg;\n    }\n    return '(no status code or body)';\n  }\n\n  static generate(\n    status: number | undefined,\n    errorResponse: Object | undefined,\n    message: string | undefined,\n    headers: Headers | undefined,\n  ): APIError {\n    if (!status || !headers) {\n      return new APIConnectionError({ message, cause: castToError(errorResponse) });\n    }\n\n    const error = errorResponse as Record<string, any>;\n\n    if (status === 400) {\n      return new BadRequestError(status, error, message, headers);\n    }\n\n    if (status === 401) {\n      return new AuthenticationError(status, error, message, headers);\n    }\n\n    if (status === 403) {\n      return new PermissionDeniedError(status, error, message, headers);\n    }\n\n    if (status === 404) {\n      return new NotFoundError(status, error, message, headers);\n    }\n\n    if (status === 409) {\n      return new ConflictError(status, error, message, headers);\n    }\n\n    if (status === 422) {\n      return new UnprocessableEntityError(status, error, message, headers);\n    }\n\n    if (status === 429) {\n      return new RateLimitError(status, error, message, headers);\n    }\n\n    if (status >= 500) {\n      return new InternalServerError(status, error, message, headers);\n    }\n\n    return new APIError(status, error, message, headers);\n  }\n}\n\nexport class APIUserAbortError extends APIError<undefined, undefined, undefined> {\n  constructor({ message }: { message?: string } = {}) {\n    super(undefined, undefined, message || 'Request was aborted.', undefined);\n  }\n}\n\nexport class APIConnectionError extends APIError<undefined, undefined, undefined> {\n  constructor({ message, cause }: { message?: string | undefined; cause?: Error | undefined }) {\n    super(undefined, undefined, message || 'Connection error.', undefined);\n    // in some environments the 'cause' property is already declared\n    // @ts-ignore\n    if (cause) this.cause = cause;\n  }\n}\n\nexport class APIConnectionTimeoutError extends APIConnectionError {\n  constructor({ message }: { message?: string } = {}) {\n    super({ message: message ?? 'Request timed out.' });\n  }\n}\n\nexport class BadRequestError extends APIError<400, Headers> {}\n\nexport class AuthenticationError extends APIError<401, Headers> {}\n\nexport class PermissionDeniedError extends APIError<403, Headers> {}\n\nexport class NotFoundError extends APIError<404, Headers> {}\n\nexport class ConflictError extends APIError<409, Headers> {}\n\nexport class UnprocessableEntityError extends APIError<422, Headers> {}\n\nexport class RateLimitError extends APIError<429, Headers> {}\n\nexport class InternalServerError extends APIError<number, Headers> {}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { SupermemoryError } from '../../core/error';\n\n// https://url.spec.whatwg.org/#url-scheme-string\nconst startsWithSchemeRegexp = /^[a-z][a-z0-9+.-]*:/i;\n\nexport const isAbsoluteURL = (url: string): boolean => {\n  return startsWithSchemeRegexp.test(url);\n};\n\n/** Returns an object if the given value isn't an object, otherwise returns as-is */\nexport function maybeObj(x: unknown): object {\n  if (typeof x !== 'object') {\n    return {};\n  }\n\n  return x ?? {};\n}\n\n// https://stackoverflow.com/a/34491287\nexport function isEmptyObj(obj: Object | null | undefined): boolean {\n  if (!obj) return true;\n  for (const _k in obj) return false;\n  return true;\n}\n\n// https://eslint.org/docs/latest/rules/no-prototype-builtins\nexport function hasOwn<T extends object = object>(obj: T, key: PropertyKey): key is keyof T {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexport function isObj(obj: unknown): obj is Record<string, unknown> {\n  return obj != null && typeof obj === 'object' && !Array.isArray(obj);\n}\n\nexport const ensurePresent = <T>(value: T | null | undefined): T => {\n  if (value == null) {\n    throw new SupermemoryError(`Expected a value to be given but received ${value} instead.`);\n  }\n\n  return value;\n};\n\nexport const validatePositiveInteger = (name: string, n: unknown): number => {\n  if (typeof n !== 'number' || !Number.isInteger(n)) {\n    throw new SupermemoryError(`${name} must be an integer`);\n  }\n  if (n < 0) {\n    throw new SupermemoryError(`${name} must be a positive integer`);\n  }\n  return n;\n};\n\nexport const coerceInteger = (value: unknown): number => {\n  if (typeof value === 'number') return Math.round(value);\n  if (typeof value === 'string') return parseInt(value, 10);\n\n  throw new SupermemoryError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\n\nexport const coerceFloat = (value: unknown): number => {\n  if (typeof value === 'number') return value;\n  if (typeof value === 'string') return parseFloat(value);\n\n  throw new SupermemoryError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\n\nexport const coerceBoolean = (value: unknown): boolean => {\n  if (typeof value === 'boolean') return value;\n  if (typeof value === 'string') return value === 'true';\n  return Boolean(value);\n};\n\nexport const maybeCoerceInteger = (value: unknown): number | undefined => {\n  if (value === undefined) {\n    return undefined;\n  }\n  return coerceInteger(value);\n};\n\nexport const maybeCoerceFloat = (value: unknown): number | undefined => {\n  if (value === undefined) {\n    return undefined;\n  }\n  return coerceFloat(value);\n};\n\nexport const maybeCoerceBoolean = (value: unknown): boolean | undefined => {\n  if (value === undefined) {\n    return undefined;\n  }\n  return coerceBoolean(value);\n};\n\nexport const safeJSON = (text: string) => {\n  try {\n    return JSON.parse(text);\n  } catch (err) {\n    return undefined;\n  }\n};\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nexport const sleep = (ms: number) => new Promise<void>((resolve) => setTimeout(resolve, ms));\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { hasOwn } from './values';\nimport { type Supermemory } from '../../client';\nimport { RequestOptions } from '../request-options';\n\ntype LogFn = (message: string, ...rest: unknown[]) => void;\nexport type Logger = {\n  error: LogFn;\n  warn: LogFn;\n  info: LogFn;\n  debug: LogFn;\n};\nexport type LogLevel = 'off' | 'error' | 'warn' | 'info' | 'debug';\n\nconst levelNumbers = {\n  off: 0,\n  error: 200,\n  warn: 300,\n  info: 400,\n  debug: 500,\n};\n\nexport const parseLogLevel = (\n  maybeLevel: string | undefined,\n  sourceName: string,\n  client: Supermemory,\n): LogLevel | undefined => {\n  if (!maybeLevel) {\n    return undefined;\n  }\n  if (hasOwn(levelNumbers, maybeLevel)) {\n    return maybeLevel;\n  }\n  loggerFor(client).warn(\n    `${sourceName} was set to ${JSON.stringify(maybeLevel)}, expected one of ${JSON.stringify(\n      Object.keys(levelNumbers),\n    )}`,\n  );\n  return undefined;\n};\n\nfunction noop() {}\n\nfunction makeLogFn(fnLevel: keyof Logger, logger: Logger | undefined, logLevel: LogLevel) {\n  if (!logger || levelNumbers[fnLevel] > levelNumbers[logLevel]) {\n    return noop;\n  } else {\n    // Don't wrap logger functions, we want the stacktrace intact!\n    return logger[fnLevel].bind(logger);\n  }\n}\n\nconst noopLogger = {\n  error: noop,\n  warn: noop,\n  info: noop,\n  debug: noop,\n};\n\nlet cachedLoggers = new WeakMap<Logger, [LogLevel, Logger]>();\n\nexport function loggerFor(client: Supermemory): Logger {\n  const logger = client.logger;\n  const logLevel = client.logLevel ?? 'off';\n  if (!logger) {\n    return noopLogger;\n  }\n\n  const cachedLogger = cachedLoggers.get(logger);\n  if (cachedLogger && cachedLogger[0] === logLevel) {\n    return cachedLogger[1];\n  }\n\n  const levelLogger = {\n    error: makeLogFn('error', logger, logLevel),\n    warn: makeLogFn('warn', logger, logLevel),\n    info: makeLogFn('info', logger, logLevel),\n    debug: makeLogFn('debug', logger, logLevel),\n  };\n\n  cachedLoggers.set(logger, [logLevel, levelLogger]);\n\n  return levelLogger;\n}\n\nexport const formatRequestDetails = (details: {\n  options?: RequestOptions | undefined;\n  headers?: Headers | Record<string, string> | undefined;\n  retryOfRequestLogID?: string | undefined;\n  retryOf?: string | undefined;\n  url?: string | undefined;\n  status?: number | undefined;\n  method?: string | undefined;\n  durationMs?: number | undefined;\n  message?: unknown;\n  body?: unknown;\n}) => {\n  if (details.options) {\n    details.options = { ...details.options };\n    delete details.options['headers']; // redundant + leaks internals\n  }\n  if (details.headers) {\n    details.headers = Object.fromEntries(\n      (details.headers instanceof Headers ? [...details.headers] : Object.entries(details.headers)).map(\n        ([name, value]) => [\n          name,\n          (\n            name.toLowerCase() === 'authorization' ||\n            name.toLowerCase() === 'cookie' ||\n            name.toLowerCase() === 'set-cookie'\n          ) ?\n            '***'\n          : value,\n        ],\n      ),\n    );\n  }\n  if ('retryOfRequestLogID' in details) {\n    if (details.retryOfRequestLogID) {\n      details.retryOf = details.retryOfRequestLogID;\n    }\n    delete details.retryOfRequestLogID;\n  }\n  return details;\n};\n", "export const VERSION = '3.0.0-alpha.17'; // x-release-please-version\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { VERSION } from '../version';\n\nexport const isRunningInBrowser = () => {\n  return (\n    // @ts-ignore\n    typeof window !== 'undefined' &&\n    // @ts-ignore\n    typeof window.document !== 'undefined' &&\n    // @ts-ignore\n    typeof navigator !== 'undefined'\n  );\n};\n\ntype DetectedPlatform = 'deno' | 'node' | 'edge' | 'unknown';\n\n/**\n * Note this does not detect 'browser'; for that, use getBrowserInfo().\n */\nfunction getDetectedPlatform(): DetectedPlatform {\n  if (typeof Deno !== 'undefined' && Deno.build != null) {\n    return 'deno';\n  }\n  if (typeof EdgeRuntime !== 'undefined') {\n    return 'edge';\n  }\n  if (\n    Object.prototype.toString.call(\n      typeof (globalThis as any).process !== 'undefined' ? (globalThis as any).process : 0,\n    ) === '[object process]'\n  ) {\n    return 'node';\n  }\n  return 'unknown';\n}\n\ndeclare const Deno: any;\ndeclare const EdgeRuntime: any;\ntype Arch = 'x32' | 'x64' | 'arm' | 'arm64' | `other:${string}` | 'unknown';\ntype PlatformName =\n  | 'MacOS'\n  | 'Linux'\n  | 'Windows'\n  | 'FreeBSD'\n  | 'OpenBSD'\n  | 'iOS'\n  | 'Android'\n  | `Other:${string}`\n  | 'Unknown';\ntype Browser = 'ie' | 'edge' | 'chrome' | 'firefox' | 'safari';\ntype PlatformProperties = {\n  'X-Stainless-Lang': 'js';\n  'X-Stainless-Package-Version': string;\n  'X-Stainless-OS': PlatformName;\n  'X-Stainless-Arch': Arch;\n  'X-Stainless-Runtime': 'node' | 'deno' | 'edge' | `browser:${Browser}` | 'unknown';\n  'X-Stainless-Runtime-Version': string;\n};\nconst getPlatformProperties = (): PlatformProperties => {\n  const detectedPlatform = getDetectedPlatform();\n  if (detectedPlatform === 'deno') {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': normalizePlatform(Deno.build.os),\n      'X-Stainless-Arch': normalizeArch(Deno.build.arch),\n      'X-Stainless-Runtime': 'deno',\n      'X-Stainless-Runtime-Version':\n        typeof Deno.version === 'string' ? Deno.version : Deno.version?.deno ?? 'unknown',\n    };\n  }\n  if (typeof EdgeRuntime !== 'undefined') {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': 'Unknown',\n      'X-Stainless-Arch': `other:${EdgeRuntime}`,\n      'X-Stainless-Runtime': 'edge',\n      'X-Stainless-Runtime-Version': (globalThis as any).process.version,\n    };\n  }\n  // Check if Node.js\n  if (detectedPlatform === 'node') {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': normalizePlatform((globalThis as any).process.platform ?? 'unknown'),\n      'X-Stainless-Arch': normalizeArch((globalThis as any).process.arch ?? 'unknown'),\n      'X-Stainless-Runtime': 'node',\n      'X-Stainless-Runtime-Version': (globalThis as any).process.version ?? 'unknown',\n    };\n  }\n\n  const browserInfo = getBrowserInfo();\n  if (browserInfo) {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': 'Unknown',\n      'X-Stainless-Arch': 'unknown',\n      'X-Stainless-Runtime': `browser:${browserInfo.browser}`,\n      'X-Stainless-Runtime-Version': browserInfo.version,\n    };\n  }\n\n  // TODO add support for Cloudflare workers, etc.\n  return {\n    'X-Stainless-Lang': 'js',\n    'X-Stainless-Package-Version': VERSION,\n    'X-Stainless-OS': 'Unknown',\n    'X-Stainless-Arch': 'unknown',\n    'X-Stainless-Runtime': 'unknown',\n    'X-Stainless-Runtime-Version': 'unknown',\n  };\n};\n\ntype BrowserInfo = {\n  browser: Browser;\n  version: string;\n};\n\ndeclare const navigator: { userAgent: string } | undefined;\n\n// Note: modified from https://github.com/JS-DevTools/host-environment/blob/b1ab79ecde37db5d6e163c050e54fe7d287d7c92/src/isomorphic.browser.ts\nfunction getBrowserInfo(): BrowserInfo | null {\n  if (typeof navigator === 'undefined' || !navigator) {\n    return null;\n  }\n\n  // NOTE: The order matters here!\n  const browserPatterns = [\n    { key: 'edge' as const, pattern: /Edge(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'ie' as const, pattern: /MSIE(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'ie' as const, pattern: /Trident(?:.*rv\\:(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'chrome' as const, pattern: /Chrome(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'firefox' as const, pattern: /Firefox(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'safari' as const, pattern: /(?:Version\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?(?:\\W+Mobile\\S*)?\\W+Safari/ },\n  ];\n\n  // Find the FIRST matching browser\n  for (const { key, pattern } of browserPatterns) {\n    const match = pattern.exec(navigator.userAgent);\n    if (match) {\n      const major = match[1] || 0;\n      const minor = match[2] || 0;\n      const patch = match[3] || 0;\n\n      return { browser: key, version: `${major}.${minor}.${patch}` };\n    }\n  }\n\n  return null;\n}\n\nconst normalizeArch = (arch: string): Arch => {\n  // Node docs:\n  // - https://nodejs.org/api/process.html#processarch\n  // Deno docs:\n  // - https://doc.deno.land/deno/stable/~/Deno.build\n  if (arch === 'x32') return 'x32';\n  if (arch === 'x86_64' || arch === 'x64') return 'x64';\n  if (arch === 'arm') return 'arm';\n  if (arch === 'aarch64' || arch === 'arm64') return 'arm64';\n  if (arch) return `other:${arch}`;\n  return 'unknown';\n};\n\nconst normalizePlatform = (platform: string): PlatformName => {\n  // Node platforms:\n  // - https://nodejs.org/api/process.html#processplatform\n  // Deno platforms:\n  // - https://doc.deno.land/deno/stable/~/Deno.build\n  // - https://github.com/denoland/deno/issues/14799\n\n  platform = platform.toLowerCase();\n\n  // NOTE: this iOS check is untested and may not work\n  // Node does not work natively on IOS, there is a fork at\n  // https://github.com/nodejs-mobile/nodejs-mobile\n  // however it is unknown at the time of writing how to detect if it is running\n  if (platform.includes('ios')) return 'iOS';\n  if (platform === 'android') return 'Android';\n  if (platform === 'darwin') return 'MacOS';\n  if (platform === 'win32') return 'Windows';\n  if (platform === 'freebsd') return 'FreeBSD';\n  if (platform === 'openbsd') return 'OpenBSD';\n  if (platform === 'linux') return 'Linux';\n  if (platform) return `Other:${platform}`;\n  return 'Unknown';\n};\n\nlet _platformHeaders: PlatformProperties;\nexport const getPlatformHeaders = () => {\n  return (_platformHeaders ??= getPlatformProperties());\n};\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n/**\n * This module provides internal shims and utility functions for environments where certain Node.js or global types may not be available.\n *\n * These are used to ensure we can provide a consistent behaviour between different JavaScript environments and good error\n * messages in cases where an environment isn't fully supported.\n */\n\nimport type { Fetch } from './builtin-types';\nimport type { ReadableStream } from './shim-types';\n\nexport function getDefaultFetch(): Fetch {\n  if (typeof fetch !== 'undefined') {\n    return fetch as any;\n  }\n\n  throw new Error(\n    '`fetch` is not defined as a global; Either pass `fetch` to the client, `new Supermemory({ fetch })` or polyfill the global, `globalThis.fetch = fetch`',\n  );\n}\n\ntype ReadableStreamArgs = ConstructorParameters<typeof ReadableStream>;\n\nexport function makeReadableStream(...args: ReadableStreamArgs): ReadableStream {\n  const ReadableStream = (globalThis as any).ReadableStream;\n  if (typeof ReadableStream === 'undefined') {\n    // Note: All of the platforms / runtimes we officially support already define\n    // `ReadableStream` as a global, so this should only ever be hit on unsupported runtimes.\n    throw new Error(\n      '`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`',\n    );\n  }\n\n  return new ReadableStream(...args);\n}\n\nexport function ReadableStreamFrom<T>(iterable: Iterable<T> | AsyncIterable<T>): ReadableStream<T> {\n  let iter: AsyncIterator<T> | Iterator<T> =\n    Symbol.asyncIterator in iterable ? iterable[Symbol.asyncIterator]() : iterable[Symbol.iterator]();\n\n  return makeReadableStream({\n    start() {},\n    async pull(controller: any) {\n      const { done, value } = await iter.next();\n      if (done) {\n        controller.close();\n      } else {\n        controller.enqueue(value);\n      }\n    },\n    async cancel() {\n      await iter.return?.();\n    },\n  });\n}\n\n/**\n * Most browsers don't yet have async iterable support for ReadableStream,\n * and Node has a very different way of reading bytes from its \"ReadableStream\".\n *\n * This polyfill was pulled from https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */\nexport function ReadableStreamToAsyncIterable<T>(stream: any): AsyncIterableIterator<T> {\n  if (stream[Symbol.asyncIterator]) return stream;\n\n  const reader = stream.getReader();\n  return {\n    async next() {\n      try {\n        const result = await reader.read();\n        if (result?.done) reader.releaseLock(); // release lock when stream becomes closed\n        return result;\n      } catch (e) {\n        reader.releaseLock(); // release lock when stream becomes errored\n        throw e;\n      }\n    },\n    async return() {\n      const cancelPromise = reader.cancel();\n      reader.releaseLock();\n      await cancelPromise;\n      return { done: true, value: undefined };\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    },\n  };\n}\n\n/**\n * Cancels a ReadableStream we don't need to consume.\n * See https://undici.nodejs.org/#/?id=garbage-collection\n */\nexport async function CancelReadableStream(stream: any): Promise<void> {\n  if (stream === null || typeof stream !== 'object') return;\n\n  if (stream[Symbol.asyncIterator]) {\n    await stream[Symbol.asyncIterator]().return?.();\n    return;\n  }\n\n  const reader = stream.getReader();\n  const cancelPromise = reader.cancel();\n  reader.releaseLock();\n  await cancelPromise;\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { NullableHeaders } from './headers';\n\nimport type { BodyInit } from './builtin-types';\nimport type { HTTPMethod, MergedRequestInit } from './types';\nimport { type HeadersLike } from './headers';\n\nexport type FinalRequestOptions = RequestOptions & { method: HTTPMethod; path: string };\n\nexport type RequestOptions = {\n  method?: HTTPMethod;\n  path?: string;\n  query?: object | undefined | null;\n  body?: unknown;\n  headers?: HeadersLike;\n  maxRetries?: number;\n  stream?: boolean | undefined;\n  timeout?: number;\n  fetchOptions?: MergedRequestInit;\n  signal?: AbortSignal | undefined | null;\n  idempotencyKey?: string;\n\n  __binaryResponse?: boolean | undefined;\n};\n\nexport type EncodedContent = { bodyHeaders: HeadersLike; body: BodyInit };\nexport type RequestEncoder = (request: { headers: NullableHeaders; body: unknown }) => EncodedContent;\n\nexport const FallbackEncoder: RequestEncoder = ({ headers, body }) => {\n  return {\n    bodyHeaders: {\n      'content-type': 'application/json',\n    },\n    body: JSON.stringify(body),\n  };\n};\n", "import { type RequestOptions } from './request-options';\nimport type { FilePropertyBag, Fetch } from './builtin-types';\nimport type { Supermemory } from '../client';\nimport { ReadableStreamFrom } from './shims';\n\nexport type BlobPart = string | ArrayBuffer | ArrayBufferView | Blob | DataView;\ntype FsReadStream = AsyncIterable<Uint8Array> & { path: string | { toString(): string } };\n\n// https://github.com/oven-sh/bun/issues/5980\ninterface BunFile extends Blob {\n  readonly name?: string | undefined;\n}\n\nexport const checkFileSupport = () => {\n  if (typeof File === 'undefined') {\n    const { process } = globalThis as any;\n    const isOldNode =\n      typeof process?.versions?.node === 'string' && parseInt(process.versions.node.split('.')) < 20;\n    throw new Error(\n      '`File` is not defined as a global, which is required for file uploads.' +\n        (isOldNode ?\n          \" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.\"\n        : ''),\n    );\n  }\n};\n\n/**\n * Typically, this is a native \"File\" class.\n *\n * We provide the {@link toFile} utility to convert a variety of objects\n * into the File class.\n *\n * For convenience, you can also pass a fetch Response, or in Node,\n * the result of fs.createReadStream().\n */\nexport type Uploadable = File | Response | FsReadStream | BunFile;\n\n/**\n * Construct a `File` instance. This is used to ensure a helpful error is thrown\n * for environments that don't define a global `File` yet.\n */\nexport function makeFile(\n  fileBits: BlobPart[],\n  fileName: string | undefined,\n  options?: FilePropertyBag,\n): File {\n  checkFileSupport();\n  return new File(fileBits as any, fileName ?? 'unknown_file', options);\n}\n\nexport function getName(value: any): string | undefined {\n  return (\n    (\n      (typeof value === 'object' &&\n        value !== null &&\n        (('name' in value && value.name && String(value.name)) ||\n          ('url' in value && value.url && String(value.url)) ||\n          ('filename' in value && value.filename && String(value.filename)) ||\n          ('path' in value && value.path && String(value.path)))) ||\n      ''\n    )\n      .split(/[\\\\/]/)\n      .pop() || undefined\n  );\n}\n\nexport const isAsyncIterable = (value: any): value is AsyncIterable<any> =>\n  value != null && typeof value === 'object' && typeof value[Symbol.asyncIterator] === 'function';\n\n/**\n * Returns a multipart/form-data request if any part of the given request body contains a File / Blob value.\n * Otherwise returns the request as is.\n */\nexport const maybeMultipartFormRequestOptions = async (\n  opts: RequestOptions,\n  fetch: Supermemory | Fetch,\n): Promise<RequestOptions> => {\n  if (!hasUploadableValue(opts.body)) return opts;\n\n  return { ...opts, body: await createForm(opts.body, fetch) };\n};\n\ntype MultipartFormRequestOptions = Omit<RequestOptions, 'body'> & { body: unknown };\n\nexport const multipartFormRequestOptions = async (\n  opts: MultipartFormRequestOptions,\n  fetch: Supermemory | Fetch,\n): Promise<RequestOptions> => {\n  return { ...opts, body: await createForm(opts.body, fetch) };\n};\n\nconst supportsFormDataMap = new WeakMap<Fetch, Promise<boolean>>();\n\n/**\n * node-fetch doesn't support the global FormData object in recent node versions. Instead of sending\n * properly-encoded form data, it just stringifies the object, resulting in a request body of \"[object FormData]\".\n * This function detects if the fetch function provided supports the global FormData object to avoid\n * confusing error messages later on.\n */\nfunction supportsFormData(fetchObject: Supermemory | Fetch): Promise<boolean> {\n  const fetch: Fetch = typeof fetchObject === 'function' ? fetchObject : (fetchObject as any).fetch;\n  const cached = supportsFormDataMap.get(fetch);\n  if (cached) return cached;\n  const promise = (async () => {\n    try {\n      const FetchResponse = (\n        'Response' in fetch ?\n          fetch.Response\n        : (await fetch('data:,')).constructor) as typeof Response;\n      const data = new FormData();\n      if (data.toString() === (await new FetchResponse(data).text())) {\n        return false;\n      }\n      return true;\n    } catch {\n      // avoid false negatives\n      return true;\n    }\n  })();\n  supportsFormDataMap.set(fetch, promise);\n  return promise;\n}\n\nexport const createForm = async <T = Record<string, unknown>>(\n  body: T | undefined,\n  fetch: Supermemory | Fetch,\n): Promise<FormData> => {\n  if (!(await supportsFormData(fetch))) {\n    throw new TypeError(\n      'The provided fetch function does not support file uploads with the current global FormData class.',\n    );\n  }\n  const form = new FormData();\n  await Promise.all(Object.entries(body || {}).map(([key, value]) => addFormValue(form, key, value)));\n  return form;\n};\n\n// We check for Blob not File because Bun.File doesn't inherit from File,\n// but they both inherit from Blob and have a `name` property at runtime.\nconst isNamedBlob = (value: unknown) => value instanceof Blob && 'name' in value;\n\nconst isUploadable = (value: unknown) =>\n  typeof value === 'object' &&\n  value !== null &&\n  (value instanceof Response || isAsyncIterable(value) || isNamedBlob(value));\n\nconst hasUploadableValue = (value: unknown): boolean => {\n  if (isUploadable(value)) return true;\n  if (Array.isArray(value)) return value.some(hasUploadableValue);\n  if (value && typeof value === 'object') {\n    for (const k in value) {\n      if (hasUploadableValue((value as any)[k])) return true;\n    }\n  }\n  return false;\n};\n\nconst addFormValue = async (form: FormData, key: string, value: unknown): Promise<void> => {\n  if (value === undefined) return;\n  if (value == null) {\n    throw new TypeError(\n      `Received null for \"${key}\"; to pass null in FormData, you must use the string 'null'`,\n    );\n  }\n\n  // TODO: make nested formats configurable\n  if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n    form.append(key, String(value));\n  } else if (value instanceof Response) {\n    form.append(key, makeFile([await value.blob()], getName(value)));\n  } else if (isAsyncIterable(value)) {\n    form.append(key, makeFile([await new Response(ReadableStreamFrom(value)).blob()], getName(value)));\n  } else if (isNamedBlob(value)) {\n    form.append(key, value, getName(value));\n  } else if (Array.isArray(value)) {\n    await Promise.all(value.map((entry) => addFormValue(form, key + '[]', entry)));\n  } else if (typeof value === 'object') {\n    await Promise.all(\n      Object.entries(value).map(([name, prop]) => addFormValue(form, `${key}[${name}]`, prop)),\n    );\n  } else {\n    throw new TypeError(\n      `Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${value} instead`,\n    );\n  }\n};\n", "import { BlobPart, getName, makeFile, isAsyncIterable } from './uploads';\nimport type { FilePropertyBag } from './builtin-types';\nimport { checkFileSupport } from './uploads';\n\ntype BlobLikePart = string | ArrayBuffer | ArrayBufferView | BlobLike | DataView;\n\n/**\n * Intended to match DOM Blob, node-fetch Blob, node:buffer Blob, etc.\n * Don't add arrayBuffer here, node-fetch doesn't have it\n */\ninterface BlobLike {\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/size) */\n  readonly size: number;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/type) */\n  readonly type: string;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/text) */\n  text(): Promise<string>;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/slice) */\n  slice(start?: number, end?: number): BlobLike;\n}\n\n/**\n * This check adds the arrayBuffer() method type because it is available and used at runtime\n */\nconst isBlobLike = (value: any): value is BlobLike & { arrayBuffer(): Promise<ArrayBuffer> } =>\n  value != null &&\n  typeof value === 'object' &&\n  typeof value.size === 'number' &&\n  typeof value.type === 'string' &&\n  typeof value.text === 'function' &&\n  typeof value.slice === 'function' &&\n  typeof value.arrayBuffer === 'function';\n\n/**\n * Intended to match DOM File, node:buffer File, undici File, etc.\n */\ninterface FileLike extends BlobLike {\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/File/lastModified) */\n  readonly lastModified: number;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/File/name) */\n  readonly name?: string | undefined;\n}\n\n/**\n * This check adds the arrayBuffer() method type because it is available and used at runtime\n */\nconst isFileLike = (value: any): value is FileLike & { arrayBuffer(): Promise<ArrayBuffer> } =>\n  value != null &&\n  typeof value === 'object' &&\n  typeof value.name === 'string' &&\n  typeof value.lastModified === 'number' &&\n  isBlobLike(value);\n\n/**\n * Intended to match DOM Response, node-fetch Response, undici Response, etc.\n */\nexport interface ResponseLike {\n  url: string;\n  blob(): Promise<BlobLike>;\n}\n\nconst isResponseLike = (value: any): value is ResponseLike =>\n  value != null &&\n  typeof value === 'object' &&\n  typeof value.url === 'string' &&\n  typeof value.blob === 'function';\n\nexport type ToFileInput =\n  | FileLike\n  | ResponseLike\n  | Exclude<BlobLikePart, string>\n  | AsyncIterable<BlobLikePart>;\n\n/**\n * Helper for creating a {@link File} to pass to an SDK upload method from a variety of different data formats\n * @param value the raw content of the file.  Can be an {@link Uploadable}, {@link BlobLikePart}, or {@link AsyncIterable} of {@link BlobLikePart}s\n * @param {string=} name the name of the file. If omitted, toFile will try to determine a file name from bits if possible\n * @param {Object=} options additional properties\n * @param {string=} options.type the MIME type of the content\n * @param {number=} options.lastModified the last modified timestamp\n * @returns a {@link File} with the given properties\n */\nexport async function toFile(\n  value: ToFileInput | PromiseLike<ToFileInput>,\n  name?: string | null | undefined,\n  options?: FilePropertyBag | undefined,\n): Promise<File> {\n  checkFileSupport();\n\n  // If it's a promise, resolve it.\n  value = await value;\n\n  // If we've been given a `File` we don't need to do anything\n  if (isFileLike(value)) {\n    if (value instanceof File) {\n      return value;\n    }\n    return makeFile([await value.arrayBuffer()], value.name);\n  }\n\n  if (isResponseLike(value)) {\n    const blob = await value.blob();\n    name ||= new URL(value.url).pathname.split(/[\\\\/]/).pop();\n\n    return makeFile(await getBytes(blob), name, options);\n  }\n\n  const parts = await getBytes(value);\n\n  name ||= getName(value);\n\n  if (!options?.type) {\n    const type = parts.find((part) => typeof part === 'object' && 'type' in part && part.type);\n    if (typeof type === 'string') {\n      options = { ...options, type };\n    }\n  }\n\n  return makeFile(parts, name, options);\n}\n\nasync function getBytes(value: BlobLikePart | AsyncIterable<BlobLikePart>): Promise<Array<BlobPart>> {\n  let parts: Array<BlobPart> = [];\n  if (\n    typeof value === 'string' ||\n    ArrayBuffer.isView(value) || // includes Uint8Array, Buffer, etc.\n    value instanceof ArrayBuffer\n  ) {\n    parts.push(value);\n  } else if (isBlobLike(value)) {\n    parts.push(value instanceof Blob ? value : await value.arrayBuffer());\n  } else if (\n    isAsyncIterable(value) // includes Readable, ReadableStream, etc.\n  ) {\n    for await (const chunk of value) {\n      parts.push(...(await getBytes(chunk as BlobLikePart))); // TODO, consider validating?\n    }\n  } else {\n    const constructor = value?.constructor?.name;\n    throw new Error(\n      `Unexpected data type: ${typeof value}${\n        constructor ? `; constructor: ${constructor}` : ''\n      }${propsForError(value)}`,\n    );\n  }\n\n  return parts;\n}\n\nfunction propsForError(value: unknown): string {\n  if (typeof value !== 'object' || value === null) return '';\n  const props = Object.getOwnPropertyNames(value);\n  return `; props: [${props.map((p) => `\"${p}\"`).join(', ')}]`;\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport type { Supermemory } from '../client';\n\nexport class APIResource {\n  protected _client: Supermemory;\n\n  constructor(client: Supermemory) {\n    this._client = client;\n  }\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\ntype HeaderValue = string | undefined | null;\nexport type HeadersLike =\n  | Headers\n  | readonly HeaderValue[][]\n  | Record<string, HeaderValue | readonly HeaderValue[]>\n  | undefined\n  | null\n  | NullableHeaders;\n\nconst brand_privateNullableHeaders = Symbol('brand.privateNullableHeaders');\n\n/**\n * @internal\n * Users can pass explicit nulls to unset default headers. When we parse them\n * into a standard headers type we need to preserve that information.\n */\nexport type NullableHeaders = {\n  /** Brand check, prevent users from creating a NullableHeaders. */\n  [brand_privateNullableHeaders]: true;\n  /** Parsed headers. */\n  values: Headers;\n  /** Set of lowercase header names explicitly set to null. */\n  nulls: Set<string>;\n};\n\nconst isArray = Array.isArray as (val: unknown) => val is readonly unknown[];\n\nfunction* iterateHeaders(headers: HeadersLike): IterableIterator<readonly [string, string | null]> {\n  if (!headers) return;\n\n  if (brand_privateNullableHeaders in headers) {\n    const { values, nulls } = headers;\n    yield* values.entries();\n    for (const name of nulls) {\n      yield [name, null];\n    }\n    return;\n  }\n\n  let shouldClear = false;\n  let iter: Iterable<readonly (HeaderValue | readonly HeaderValue[])[]>;\n  if (headers instanceof Headers) {\n    iter = headers.entries();\n  } else if (isArray(headers)) {\n    iter = headers;\n  } else {\n    shouldClear = true;\n    iter = Object.entries(headers ?? {});\n  }\n  for (let row of iter) {\n    const name = row[0];\n    if (typeof name !== 'string') throw new TypeError('expected header name to be a string');\n    const values = isArray(row[1]) ? row[1] : [row[1]];\n    let didClear = false;\n    for (const value of values) {\n      if (value === undefined) continue;\n\n      // Objects keys always overwrite older headers, they never append.\n      // Yield a null to clear the header before adding the new values.\n      if (shouldClear && !didClear) {\n        didClear = true;\n        yield [name, null];\n      }\n      yield [name, value];\n    }\n  }\n}\n\nexport const buildHeaders = (newHeaders: HeadersLike[]): NullableHeaders => {\n  const targetHeaders = new Headers();\n  const nullHeaders = new Set<string>();\n  for (const headers of newHeaders) {\n    const seenHeaders = new Set<string>();\n    for (const [name, value] of iterateHeaders(headers)) {\n      const lowerName = name.toLowerCase();\n      if (!seenHeaders.has(lowerName)) {\n        targetHeaders.delete(name);\n        seenHeaders.add(lowerName);\n      }\n      if (value === null) {\n        targetHeaders.delete(name);\n        nullHeaders.add(lowerName);\n      } else {\n        targetHeaders.append(name, value);\n        nullHeaders.delete(lowerName);\n      }\n    }\n  }\n  return { [brand_privateNullableHeaders]: true, values: targetHeaders, nulls: nullHeaders };\n};\n\nexport const isEmptyHeaders = (headers: HeadersLike) => {\n  for (const _ of iterateHeaders(headers)) return false;\n  return true;\n};\n", "import { SupermemoryError } from '../../core/error';\n\n/**\n * Percent-encode everything that isn't safe to have in a path without encoding safe chars.\n *\n * Taken from https://datatracker.ietf.org/doc/html/rfc3986#section-3.3:\n * > unreserved  = ALPHA / DIGIT / \"-\" / \".\" / \"_\" / \"~\"\n * > sub-delims  = \"!\" / \"$\" / \"&\" / \"'\" / \"(\" / \")\" / \"*\" / \"+\" / \",\" / \";\" / \"=\"\n * > pchar       = unreserved / pct-encoded / sub-delims / \":\" / \"@\"\n */\nexport function encodeURIPath(str: string) {\n  return str.replace(/[^A-Za-z0-9\\-._~!$&'()*+,;=:@]+/g, encodeURIComponent);\n}\n\nexport const createPathTagFunction = (pathEncoder = encodeURIPath) =>\n  function path(statics: readonly string[], ...params: readonly unknown[]): string {\n    // If there are no params, no processing is needed.\n    if (statics.length === 1) return statics[0]!;\n\n    let postPath = false;\n    const path = statics.reduce((previousValue, currentValue, index) => {\n      if (/[?#]/.test(currentValue)) {\n        postPath = true;\n      }\n      return (\n        previousValue +\n        currentValue +\n        (index === params.length ? '' : (postPath ? encodeURIComponent : pathEncoder)(String(params[index])))\n      );\n    }, '');\n\n    const pathOnly = path.split(/[?#]/, 1)[0]!;\n    const invalidSegments = [];\n    const invalidSegmentPattern = /(?<=^|\\/)(?:\\.|%2e){1,2}(?=\\/|$)/gi;\n    let match;\n\n    // Find all invalid segments\n    while ((match = invalidSegmentPattern.exec(pathOnly)) !== null) {\n      invalidSegments.push({\n        start: match.index,\n        length: match[0].length,\n      });\n    }\n\n    if (invalidSegments.length > 0) {\n      let lastEnd = 0;\n      const underline = invalidSegments.reduce((acc, segment) => {\n        const spaces = ' '.repeat(segment.start - lastEnd);\n        const arrows = '^'.repeat(segment.length);\n        lastEnd = segment.start + segment.length;\n        return acc + spaces + arrows;\n      }, '');\n\n      throw new SupermemoryError(\n        `Path parameters result in path with invalid segments:\\n${path}\\n${underline}`,\n      );\n    }\n\n    return path;\n  };\n\n/**\n * URI-encodes path params and ensures no unsafe /./ or /../ path segments are introduced.\n */\nexport const path = createPathTagFunction(encodeURIPath);\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../core/resource';\nimport { APIPromise } from '../core/api-promise';\nimport { buildHeaders } from '../internal/headers';\nimport { RequestOptions } from '../internal/request-options';\nimport { path } from '../internal/utils/path';\n\nexport class Connections extends APIResource {\n  /**\n   * Initialize connection and get authorization URL\n   *\n   * @example\n   * ```ts\n   * const connection = await client.connections.create(\n   *   'notion',\n   * );\n   * ```\n   */\n  create(\n    provider: 'notion' | 'google-drive' | 'onedrive',\n    body: ConnectionCreateParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<ConnectionCreateResponse> {\n    return this._client.post(path`/v3/connections/${provider}`, { body, ...options });\n  }\n\n  /**\n   * List all connections\n   *\n   * @example\n   * ```ts\n   * const connections = await client.connections.list();\n   * ```\n   */\n  list(\n    body: ConnectionListParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<ConnectionListResponse> {\n    return this._client.post('/v3/connections/list', { body, ...options });\n  }\n\n  /**\n   * Delete connection for a specific provider and container tags\n   *\n   * @example\n   * ```ts\n   * const connection = await client.connections.delete(\n   *   'notion',\n   * );\n   * ```\n   */\n  delete(\n    provider: 'notion' | 'google-drive' | 'onedrive',\n    body: ConnectionDeleteParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<ConnectionDeleteResponse> {\n    return this._client.delete(path`/v3/connections/${provider}`, { body, ...options });\n  }\n\n  /**\n   * Get connection details with id\n   *\n   * @example\n   * ```ts\n   * const response = await client.connections.getByID(\n   *   'connectionId',\n   * );\n   * ```\n   */\n  getByID(connectionID: string, options?: RequestOptions): APIPromise<ConnectionGetByIDResponse> {\n    return this._client.get(path`/v3/connections/${connectionID}`, options);\n  }\n\n  /**\n   * Get connection details with provider and container tags\n   *\n   * @example\n   * ```ts\n   * const response = await client.connections.getByTags(\n   *   'notion',\n   *   { containerTags: ['user_123', 'project_123'] },\n   * );\n   * ```\n   */\n  getByTags(\n    provider: 'notion' | 'google-drive' | 'onedrive',\n    body: ConnectionGetByTagsParams,\n    options?: RequestOptions,\n  ): APIPromise<ConnectionGetByTagsResponse> {\n    return this._client.post(path`/v3/connections/${provider}/connection`, { body, ...options });\n  }\n\n  /**\n   * Initiate a manual sync of connections\n   *\n   * @example\n   * ```ts\n   * await client.connections.import('notion');\n   * ```\n   */\n  import(\n    provider: 'notion' | 'google-drive' | 'onedrive',\n    body: ConnectionImportParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<void> {\n    return this._client.post(path`/v3/connections/${provider}/import`, {\n      body,\n      ...options,\n      headers: buildHeaders([{ Accept: '*/*' }, options?.headers]),\n    });\n  }\n\n  /**\n   * List documents indexed for a provider and container tags\n   *\n   * @example\n   * ```ts\n   * const response = await client.connections.listDocuments(\n   *   'notion',\n   * );\n   * ```\n   */\n  listDocuments(\n    provider: 'notion' | 'google-drive' | 'onedrive',\n    body: ConnectionListDocumentsParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<ConnectionListDocumentsResponse> {\n    return this._client.post(path`/v3/connections/${provider}/documents`, { body, ...options });\n  }\n}\n\nexport interface ConnectionCreateResponse {\n  id: string;\n\n  authLink: string;\n\n  expiresIn: string;\n\n  redirectsTo?: string;\n}\n\nexport type ConnectionListResponse = Array<ConnectionListResponse.ConnectionListResponseItem>;\n\nexport namespace ConnectionListResponse {\n  export interface ConnectionListResponseItem {\n    id: string;\n\n    createdAt: number;\n\n    provider: string;\n\n    documentLimit?: number;\n\n    expiresAt?: number;\n\n    metadata?: Record<string, unknown>;\n  }\n}\n\nexport interface ConnectionDeleteResponse {\n  deletedConnections: Array<ConnectionDeleteResponse.DeletedConnection>;\n\n  deletedCount: number;\n\n  success: boolean;\n}\n\nexport namespace ConnectionDeleteResponse {\n  export interface DeletedConnection {\n    id: string;\n\n    provider: string;\n  }\n}\n\nexport interface ConnectionGetByIDResponse {\n  id: string;\n\n  createdAt: number;\n\n  provider: string;\n\n  documentLimit?: number;\n\n  expiresAt?: number;\n\n  metadata?: Record<string, unknown>;\n}\n\nexport interface ConnectionGetByTagsResponse {\n  id: string;\n\n  createdAt: number;\n\n  provider: string;\n\n  documentLimit?: number;\n\n  expiresAt?: number;\n\n  metadata?: Record<string, unknown>;\n}\n\nexport type ConnectionListDocumentsResponse =\n  Array<ConnectionListDocumentsResponse.ConnectionListDocumentsResponseItem>;\n\nexport namespace ConnectionListDocumentsResponse {\n  export interface ConnectionListDocumentsResponseItem {\n    id: string;\n\n    createdAt: string;\n\n    status: string;\n\n    summary: string | null;\n\n    title: string | null;\n\n    type: string;\n\n    updatedAt: string;\n  }\n}\n\nexport interface ConnectionCreateParams {\n  containerTags?: Array<string>;\n\n  documentLimit?: number;\n\n  metadata?: Record<string, string | number | boolean> | null;\n\n  redirectUrl?: string;\n}\n\nexport interface ConnectionListParams {\n  /**\n   * Optional comma-separated list of container tags to filter documents by\n   */\n  containerTags?: Array<string>;\n}\n\nexport interface ConnectionDeleteParams {\n  /**\n   * Optional comma-separated list of container tags to filter connections by\n   */\n  containerTags?: Array<string>;\n}\n\nexport interface ConnectionGetByTagsParams {\n  /**\n   * Comma-separated list of container tags to filter connection by\n   */\n  containerTags: Array<string>;\n}\n\nexport interface ConnectionImportParams {\n  /**\n   * Optional comma-separated list of container tags to filter connections by\n   */\n  containerTags?: Array<string>;\n}\n\nexport interface ConnectionListDocumentsParams {\n  /**\n   * Optional comma-separated list of container tags to filter documents by\n   */\n  containerTags?: Array<string>;\n}\n\nexport declare namespace Connections {\n  export {\n    type ConnectionCreateResponse as ConnectionCreateResponse,\n    type ConnectionListResponse as ConnectionListResponse,\n    type ConnectionDeleteResponse as ConnectionDeleteResponse,\n    type ConnectionGetByIDResponse as ConnectionGetByIDResponse,\n    type ConnectionGetByTagsResponse as ConnectionGetByTagsResponse,\n    type ConnectionListDocumentsResponse as ConnectionListDocumentsResponse,\n    type ConnectionCreateParams as ConnectionCreateParams,\n    type ConnectionListParams as ConnectionListParams,\n    type ConnectionDeleteParams as ConnectionDeleteParams,\n    type ConnectionGetByTagsParams as ConnectionGetByTagsParams,\n    type ConnectionImportParams as ConnectionImportParams,\n    type ConnectionListDocumentsParams as ConnectionListDocumentsParams,\n  };\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../core/resource';\nimport { APIPromise } from '../core/api-promise';\nimport { buildHeaders } from '../internal/headers';\nimport { RequestOptions } from '../internal/request-options';\nimport { path } from '../internal/utils/path';\n\nexport class Memories extends APIResource {\n  /**\n   * Update a memory with any content type (text, url, file, etc.) and metadata\n   *\n   * @example\n   * ```ts\n   * const memory = await client.memories.update('id', {\n   *   content:\n   *     'This is a detailed article about machine learning concepts...',\n   * });\n   * ```\n   */\n  update(id: string, body: MemoryUpdateParams, options?: RequestOptions): APIPromise<MemoryUpdateResponse> {\n    return this._client.patch(path`/v3/memories/${id}`, { body, ...options });\n  }\n\n  /**\n   * Retrieves a paginated list of memories with their metadata and workflow status\n   *\n   * @example\n   * ```ts\n   * const memories = await client.memories.list();\n   * ```\n   */\n  list(\n    body: MemoryListParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<MemoryListResponse> {\n    return this._client.post('/v3/memories/list', { body, ...options });\n  }\n\n  /**\n   * Delete a memory\n   *\n   * @example\n   * ```ts\n   * await client.memories.delete('id');\n   * ```\n   */\n  delete(id: string, options?: RequestOptions): APIPromise<void> {\n    return this._client.delete(path`/v3/memories/${id}`, {\n      ...options,\n      headers: buildHeaders([{ Accept: '*/*' }, options?.headers]),\n    });\n  }\n\n  /**\n   * Add a memory with any content type (text, url, file, etc.) and metadata\n   *\n   * @example\n   * ```ts\n   * const response = await client.memories.add({\n   *   content:\n   *     'This is a detailed article about machine learning concepts...',\n   * });\n   * ```\n   */\n  add(body: MemoryAddParams, options?: RequestOptions): APIPromise<MemoryAddResponse> {\n    return this._client.post('/v3/memories', { body, ...options });\n  }\n\n  /**\n   * Get a memory by ID\n   *\n   * @example\n   * ```ts\n   * const memory = await client.memories.get('id');\n   * ```\n   */\n  get(id: string, options?: RequestOptions): APIPromise<MemoryGetResponse> {\n    return this._client.get(path`/v3/memories/${id}`, options);\n  }\n}\n\nexport interface MemoryUpdateResponse {\n  id: string;\n\n  status: string;\n}\n\n/**\n * List of memories\n */\nexport interface MemoryListResponse {\n  memories: Array<MemoryListResponse.Memory>;\n\n  /**\n   * Pagination metadata\n   */\n  pagination: MemoryListResponse.Pagination;\n}\n\nexport namespace MemoryListResponse {\n  /**\n   * Memory object\n   */\n  export interface Memory {\n    /**\n     * Unique identifier of the memory.\n     */\n    id: string;\n\n    /**\n     * Creation timestamp\n     */\n    createdAt: string;\n\n    /**\n     * Optional custom ID of the memory. This could be an ID from your database that\n     * will uniquely identify this memory.\n     */\n    customId: string | null;\n\n    /**\n     * Optional metadata for the memory. This is used to store additional information\n     * about the memory. You can use this to store any additional information you need\n     * about the memory. Metadata can be filtered through. Keys must be strings and are\n     * case sensitive. Values can be strings, numbers, or booleans. You cannot nest\n     * objects.\n     */\n    metadata: string | number | boolean | Record<string, unknown> | Array<unknown> | null;\n\n    /**\n     * Status of the memory\n     */\n    status: 'unknown' | 'queued' | 'extracting' | 'chunking' | 'embedding' | 'indexing' | 'done' | 'failed';\n\n    /**\n     * Summary of the memory content\n     */\n    summary: string | null;\n\n    /**\n     * Title of the memory\n     */\n    title: string | null;\n\n    /**\n     * Last update timestamp\n     */\n    updatedAt: string;\n\n    /**\n     * Optional tags this memory should be containerized by. This can be an ID for your\n     * user, a project ID, or any other identifier you wish to use to group memories.\n     */\n    containerTags?: Array<string>;\n  }\n\n  /**\n   * Pagination metadata\n   */\n  export interface Pagination {\n    currentPage: number;\n\n    limit: number;\n\n    totalItems: number;\n\n    totalPages: number;\n  }\n}\n\nexport interface MemoryAddResponse {\n  id: string;\n\n  status: string;\n}\n\n/**\n * Memory object\n */\nexport interface MemoryGetResponse {\n  /**\n   * Unique identifier of the memory.\n   */\n  id: string;\n\n  /**\n   * The content to extract and process into a memory. This can be a URL to a\n   * website, a PDF, an image, or a video.\n   *\n   * Plaintext: Any plaintext format\n   *\n   * URL: A URL to a website, PDF, image, or video\n   *\n   * We automatically detect the content type from the url's response format.\n   */\n  content: string | null;\n\n  /**\n   * Creation timestamp\n   */\n  createdAt: string;\n\n  /**\n   * Optional custom ID of the memory. This could be an ID from your database that\n   * will uniquely identify this memory.\n   */\n  customId: string | null;\n\n  /**\n   * Optional metadata for the memory. This is used to store additional information\n   * about the memory. You can use this to store any additional information you need\n   * about the memory. Metadata can be filtered through. Keys must be strings and are\n   * case sensitive. Values can be strings, numbers, or booleans. You cannot nest\n   * objects.\n   */\n  metadata: string | number | boolean | Record<string, unknown> | Array<unknown> | null;\n\n  ogImage: string | null;\n\n  /**\n   * Source of the memory\n   */\n  source: string | null;\n\n  /**\n   * Status of the memory\n   */\n  status: 'unknown' | 'queued' | 'extracting' | 'chunking' | 'embedding' | 'indexing' | 'done' | 'failed';\n\n  /**\n   * Summary of the memory content\n   */\n  summary: string | null;\n\n  /**\n   * Title of the memory\n   */\n  title: string | null;\n\n  /**\n   * Type of the memory\n   */\n  type: 'text' | 'pdf' | 'tweet' | 'google_doc' | 'image' | 'video' | 'notion_doc' | 'webpage';\n\n  /**\n   * Last update timestamp\n   */\n  updatedAt: string;\n\n  /**\n   * URL of the memory\n   */\n  url: string | null;\n\n  /**\n   * Optional tags this memory should be containerized by. This can be an ID for your\n   * user, a project ID, or any other identifier you wish to use to group memories.\n   */\n  containerTags?: Array<string>;\n\n  /**\n   * Raw content of the memory\n   */\n  raw?: null;\n}\n\nexport interface MemoryUpdateParams {\n  /**\n   * The content to extract and process into a memory. This can be a URL to a\n   * website, a PDF, an image, or a video.\n   *\n   * Plaintext: Any plaintext format\n   *\n   * URL: A URL to a website, PDF, image, or video\n   *\n   * We automatically detect the content type from the url's response format.\n   */\n  content: string;\n\n  /**\n   * Optional tags this memory should be containerized by. This can be an ID for your\n   * user, a project ID, or any other identifier you wish to use to group memories.\n   */\n  containerTags?: Array<string>;\n\n  /**\n   * Optional custom ID of the memory. This could be an ID from your database that\n   * will uniquely identify this memory.\n   */\n  customId?: string;\n\n  /**\n   * Optional metadata for the memory. This is used to store additional information\n   * about the memory. You can use this to store any additional information you need\n   * about the memory. Metadata can be filtered through. Keys must be strings and are\n   * case sensitive. Values can be strings, numbers, or booleans. You cannot nest\n   * objects.\n   */\n  metadata?: Record<string, string | number | boolean>;\n}\n\nexport interface MemoryListParams {\n  /**\n   * Optional tags this memory should be containerized by. This can be an ID for your\n   * user, a project ID, or any other identifier you wish to use to group memories.\n   */\n  containerTags?: Array<string>;\n\n  /**\n   * Optional filters to apply to the search\n   */\n  filters?: string;\n\n  /**\n   * Number of items per page\n   */\n  limit?: string | number;\n\n  /**\n   * Sort order\n   */\n  order?: 'asc' | 'desc';\n\n  /**\n   * Page number to fetch\n   */\n  page?: string | number;\n\n  /**\n   * Field to sort by\n   */\n  sort?: 'createdAt' | 'updatedAt';\n}\n\nexport interface MemoryAddParams {\n  /**\n   * The content to extract and process into a memory. This can be a URL to a\n   * website, a PDF, an image, or a video.\n   *\n   * Plaintext: Any plaintext format\n   *\n   * URL: A URL to a website, PDF, image, or video\n   *\n   * We automatically detect the content type from the url's response format.\n   */\n  content: string;\n\n  /**\n   * Optional tags this memory should be containerized by. This can be an ID for your\n   * user, a project ID, or any other identifier you wish to use to group memories.\n   */\n  containerTags?: Array<string>;\n\n  /**\n   * Optional custom ID of the memory. This could be an ID from your database that\n   * will uniquely identify this memory.\n   */\n  customId?: string;\n\n  /**\n   * Optional metadata for the memory. This is used to store additional information\n   * about the memory. You can use this to store any additional information you need\n   * about the memory. Metadata can be filtered through. Keys must be strings and are\n   * case sensitive. Values can be strings, numbers, or booleans. You cannot nest\n   * objects.\n   */\n  metadata?: Record<string, string | number | boolean>;\n}\n\nexport declare namespace Memories {\n  export {\n    type MemoryUpdateResponse as MemoryUpdateResponse,\n    type MemoryListResponse as MemoryListResponse,\n    type MemoryAddResponse as MemoryAddResponse,\n    type MemoryGetResponse as MemoryGetResponse,\n    type MemoryUpdateParams as MemoryUpdateParams,\n    type MemoryListParams as MemoryListParams,\n    type MemoryAddParams as MemoryAddParams,\n  };\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../core/resource';\nimport { APIPromise } from '../core/api-promise';\nimport { RequestOptions } from '../internal/request-options';\n\nexport class Search extends APIResource {\n  /**\n   * Search memories with advanced filtering\n   *\n   * @example\n   * ```ts\n   * const response = await client.search.execute({\n   *   q: 'machine learning concepts',\n   * });\n   * ```\n   */\n  execute(body: SearchExecuteParams, options?: RequestOptions): APIPromise<SearchExecuteResponse> {\n    return this._client.post('/v3/search', { body, ...options });\n  }\n}\n\nexport interface SearchExecuteResponse {\n  results: Array<SearchExecuteResponse.Result>;\n\n  timing: number;\n\n  total: number;\n}\n\nexport namespace SearchExecuteResponse {\n  export interface Result {\n    /**\n     * Matching content chunks from the document\n     */\n    chunks: Array<Result.Chunk>;\n\n    /**\n     * Document creation date\n     */\n    createdAt: string;\n\n    /**\n     * ID of the matching document\n     */\n    documentId: string;\n\n    /**\n     * Document metadata\n     */\n    metadata: Record<string, unknown> | null;\n\n    /**\n     * Relevance score of the match\n     */\n    score: number;\n\n    /**\n     * Document title\n     */\n    title: string | null;\n\n    /**\n     * Document last update date\n     */\n    updatedAt: string;\n\n    /**\n     * Document summary\n     */\n    summary?: string | null;\n  }\n\n  export namespace Result {\n    /**\n     * Matching content chunk\n     */\n    export interface Chunk {\n      /**\n       * Content of the matching chunk\n       */\n      content: string;\n\n      /**\n       * Whether this chunk is relevant to the query\n       */\n      isRelevant: boolean;\n\n      /**\n       * Similarity score for this chunk\n       */\n      score: number;\n    }\n  }\n}\n\nexport interface SearchExecuteParams {\n  /**\n   * Search query string\n   */\n  q: string;\n\n  /**\n   * Optional category filters\n   */\n  categoriesFilter?: Array<'technology' | 'science' | 'business' | 'health'>;\n\n  /**\n   * Threshold / sensitivity for chunk selection. 0 is least sensitive (returns most\n   * chunks, more results), 1 is most sensitive (returns lesser chunks, accurate\n   * results)\n   */\n  chunkThreshold?: number;\n\n  /**\n   * Optional tags this search should be containerized by. This can be an ID for your\n   * user, a project ID, or any other identifier you wish to use to filter memories.\n   */\n  containerTags?: Array<string>;\n\n  /**\n   * Optional document ID to search within. You can use this to find chunks in a very\n   * large document.\n   */\n  docId?: string;\n\n  /**\n   * Threshold / sensitivity for document selection. 0 is least sensitive (returns\n   * most documents, more results), 1 is most sensitive (returns lesser documents,\n   * accurate results)\n   */\n  documentThreshold?: number;\n\n  /**\n   * Optional filters to apply to the search\n   */\n  filters?: SearchExecuteParams.UnionMember0 | Record<string, unknown>;\n\n  /**\n   * If true, include full document in the response. This is helpful if you want a\n   * chatbot to know the full context of the document.\n   */\n  includeFullDocs?: boolean;\n\n  /**\n   * If true, include document summary in the response. This is helpful if you want a\n   * chatbot to know the full context of the document.\n   */\n  includeSummary?: boolean;\n\n  /**\n   * Maximum number of results to return\n   */\n  limit?: number;\n\n  /**\n   * If true, only return matching chunks without context. Normally, we send the\n   * previous and next chunk to provide more context for LLMs. If you only want the\n   * matching chunk, set this to true.\n   */\n  onlyMatchingChunks?: boolean;\n\n  /**\n   * If true, rerank the results based on the query. This is helpful if you want to\n   * ensure the most relevant results are returned.\n   */\n  rerank?: boolean;\n\n  /**\n   * If true, rewrites the query to make it easier to find documents. This increases\n   * the latency by about 400ms\n   */\n  rewriteQuery?: boolean;\n}\n\nexport namespace SearchExecuteParams {\n  export interface UnionMember0 {\n    AND?: Array<unknown>;\n\n    OR?: Array<unknown>;\n  }\n}\n\nexport declare namespace Search {\n  export {\n    type SearchExecuteResponse as SearchExecuteResponse,\n    type SearchExecuteParams as SearchExecuteParams,\n  };\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../core/resource';\nimport { APIPromise } from '../core/api-promise';\nimport { RequestOptions } from '../internal/request-options';\n\nexport class Settings extends APIResource {\n  /**\n   * Update settings for an organization\n   */\n  update(\n    body: SettingUpdateParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<SettingUpdateResponse> {\n    return this._client.patch('/v3/settings', { body, ...options });\n  }\n\n  /**\n   * Get settings for an organization\n   */\n  get(options?: RequestOptions): APIPromise<SettingGetResponse> {\n    return this._client.get('/v3/settings', options);\n  }\n}\n\nexport interface SettingUpdateResponse {\n  orgId: string;\n\n  orgSlug: string;\n\n  updated: SettingUpdateResponse.Updated;\n}\n\nexport namespace SettingUpdateResponse {\n  export interface Updated {\n    excludeItems?: Array<string>;\n\n    filterPrompt?: string;\n\n    filterTags?: Record<string, Array<string>>;\n\n    includeItems?: Array<string>;\n\n    shouldLLMFilter?: boolean;\n  }\n}\n\nexport interface SettingGetResponse {\n  excludeItems?: Array<string>;\n\n  filterPrompt?: string;\n\n  filterTags?: Record<string, Array<string>>;\n\n  includeItems?: Array<string>;\n\n  shouldLLMFilter?: boolean;\n}\n\nexport interface SettingUpdateParams {\n  excludeItems?: Array<string>;\n\n  filterPrompt?: string;\n\n  filterTags?: Record<string, Array<string>>;\n\n  includeItems?: Array<string>;\n\n  shouldLLMFilter?: boolean;\n}\n\nexport declare namespace Settings {\n  export {\n    type SettingUpdateResponse as SettingUpdateResponse,\n    type SettingGetResponse as SettingGetResponse,\n    type SettingUpdateParams as SettingUpdateParams,\n  };\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport type { FinalRequestOptions } from './request-options';\nimport { type Supermemory } from '../client';\nimport { formatRequestDetails, loggerFor } from './utils/log';\n\nexport type APIResponseProps = {\n  response: Response;\n  options: FinalRequestOptions;\n  controller: AbortController;\n  requestLogID: string;\n  retryOfRequestLogID: string | undefined;\n  startTime: number;\n};\n\nexport async function defaultParseResponse<T>(client: Supermemory, props: APIResponseProps): Promise<T> {\n  const { response, requestLogID, retryOfRequestLogID, startTime } = props;\n  const body = await (async () => {\n    // fetch refuses to read the body when the status code is 204.\n    if (response.status === 204) {\n      return null as T;\n    }\n\n    if (props.options.__binaryResponse) {\n      return response as unknown as T;\n    }\n\n    const contentType = response.headers.get('content-type');\n    const mediaType = contentType?.split(';')[0]?.trim();\n    const isJSON = mediaType?.includes('application/json') || mediaType?.endsWith('+json');\n    if (isJSON) {\n      const json = await response.json();\n      return json as T;\n    }\n\n    const text = await response.text();\n    return text as unknown as T;\n  })();\n  loggerFor(client).debug(\n    `[${requestLogID}] response parsed`,\n    formatRequestDetails({\n      retryOfRequestLogID,\n      url: response.url,\n      status: response.status,\n      body,\n      durationMs: Date.now() - startTime,\n    }),\n  );\n  return body;\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { type Supermemory } from '../client';\n\nimport { type PromiseOrValue } from '../internal/types';\nimport { APIResponseProps, defaultParseResponse } from '../internal/parse';\n\n/**\n * A subclass of `Promise` providing additional helper methods\n * for interacting with the SDK.\n */\nexport class APIPromise<T> extends Promise<T> {\n  private parsedPromise: Promise<T> | undefined;\n  #client: Supermemory;\n\n  constructor(\n    client: Supermemory,\n    private responsePromise: Promise<APIResponseProps>,\n    private parseResponse: (\n      client: Supermemory,\n      props: APIResponseProps,\n    ) => PromiseOrValue<T> = defaultParseResponse,\n  ) {\n    super((resolve) => {\n      // this is maybe a bit weird but this has to be a no-op to not implicitly\n      // parse the response body; instead .then, .catch, .finally are overridden\n      // to parse the response\n      resolve(null as any);\n    });\n    this.#client = client;\n  }\n\n  _thenUnwrap<U>(transform: (data: T, props: APIResponseProps) => U): APIPromise<U> {\n    return new APIPromise(this.#client, this.responsePromise, async (client, props) =>\n      transform(await this.parseResponse(client, props), props),\n    );\n  }\n\n  /**\n   * Gets the raw `Response` instance instead of parsing the response\n   * data.\n   *\n   * If you want to parse the response body but still get the `Response`\n   * instance, you can use {@link withResponse()}.\n   *\n   * 👋 Getting the wrong TypeScript type for `Response`?\n   * Try setting `\"moduleResolution\": \"NodeNext\"` or add `\"lib\": [\"DOM\"]`\n   * to your `tsconfig.json`.\n   */\n  asResponse(): Promise<Response> {\n    return this.responsePromise.then((p) => p.response);\n  }\n\n  /**\n   * Gets the parsed response data and the raw `Response` instance.\n   *\n   * If you just want to get the raw `Response` instance without parsing it,\n   * you can use {@link asResponse()}.\n   *\n   * 👋 Getting the wrong TypeScript type for `Response`?\n   * Try setting `\"moduleResolution\": \"NodeNext\"` or add `\"lib\": [\"DOM\"]`\n   * to your `tsconfig.json`.\n   */\n  async withResponse(): Promise<{ data: T; response: Response }> {\n    const [data, response] = await Promise.all([this.parse(), this.asResponse()]);\n    return { data, response };\n  }\n\n  private parse(): Promise<T> {\n    if (!this.parsedPromise) {\n      this.parsedPromise = this.responsePromise.then((data) => this.parseResponse(this.#client, data));\n    }\n    return this.parsedPromise;\n  }\n\n  override then<TResult1 = T, TResult2 = never>(\n    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null,\n  ): Promise<TResult1 | TResult2> {\n    return this.parse().then(onfulfilled, onrejected);\n  }\n\n  override catch<TResult = never>(\n    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null,\n  ): Promise<T | TResult> {\n    return this.parse().catch(onrejected);\n  }\n\n  override finally(onfinally?: (() => void) | undefined | null): Promise<T> {\n    return this.parse().finally(onfinally);\n  }\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n/**\n * Read an environment variable.\n *\n * Trims beginning and trailing whitespace.\n *\n * Will return undefined if the environment variable doesn't exist or cannot be accessed.\n */\nexport const readEnv = (env: string): string | undefined => {\n  if (typeof (globalThis as any).process !== 'undefined') {\n    return (globalThis as any).process.env?.[env]?.trim() ?? undefined;\n  }\n  if (typeof (globalThis as any).Deno !== 'undefined') {\n    return (globalThis as any).Deno.env?.get?.(env)?.trim();\n  }\n  return undefined;\n};\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport type { RequestInit, RequestInfo, BodyInit } from './internal/builtin-types';\nimport type { HTTPMethod, PromiseOrValue, MergedRequestInit, FinalizedRequestInit } from './internal/types';\nimport { uuid4 } from './internal/utils/uuid';\nimport { validatePositiveInteger, isAbsoluteURL, safeJSON } from './internal/utils/values';\nimport { sleep } from './internal/utils/sleep';\nimport { type Logger, type LogLevel, parseLogLevel } from './internal/utils/log';\nexport type { Logger, LogLevel } from './internal/utils/log';\nimport { castToError, isAbortError } from './internal/errors';\nimport type { APIResponseProps } from './internal/parse';\nimport { getPlatformHeaders } from './internal/detect-platform';\nimport * as Shims from './internal/shims';\nimport * as Opts from './internal/request-options';\nimport { VERSION } from './version';\nimport * as Errors from './core/error';\nimport * as Uploads from './core/uploads';\nimport * as API from './resources/index';\nimport { APIPromise } from './core/api-promise';\nimport { type Fetch } from './internal/builtin-types';\nimport { HeadersLike, NullableHeaders, buildHeaders } from './internal/headers';\nimport { FinalRequestOptions, RequestOptions } from './internal/request-options';\nimport {\n  ConnectionCreateParams,\n  ConnectionCreateResponse,\n  ConnectionDeleteParams,\n  ConnectionDeleteResponse,\n  ConnectionGetByIDResponse,\n  ConnectionGetByTagsParams,\n  ConnectionGetByTagsResponse,\n  ConnectionImportParams,\n  ConnectionListDocumentsParams,\n  ConnectionListDocumentsResponse,\n  ConnectionListParams,\n  ConnectionListResponse,\n  Connections,\n} from './resources/connections';\nimport {\n  Memories,\n  MemoryAddParams,\n  MemoryAddResponse,\n  MemoryGetResponse,\n  MemoryListParams,\n  MemoryListResponse,\n  MemoryUpdateParams,\n  MemoryUpdateResponse,\n} from './resources/memories';\nimport { Search, SearchExecuteParams, SearchExecuteResponse } from './resources/search';\nimport {\n  SettingGetResponse,\n  SettingUpdateParams,\n  SettingUpdateResponse,\n  Settings,\n} from './resources/settings';\nimport { readEnv } from './internal/utils/env';\nimport { formatRequestDetails, loggerFor } from './internal/utils/log';\nimport { isEmptyObj } from './internal/utils/values';\n\nexport interface ClientOptions {\n  /**\n   * Defaults to process.env['SUPERMEMORY_API_KEY'].\n   */\n  apiKey?: string | undefined;\n\n  /**\n   * Override the default base URL for the API, e.g., \"https://api.example.com/v2/\"\n   *\n   * Defaults to process.env['SUPERMEMORY_BASE_URL'].\n   */\n  baseURL?: string | null | undefined;\n\n  /**\n   * The maximum amount of time (in milliseconds) that the client should wait for a response\n   * from the server before timing out a single request.\n   *\n   * Note that request timeouts are retried by default, so in a worst-case scenario you may wait\n   * much longer than this timeout before the promise succeeds or fails.\n   */\n  timeout?: number | undefined;\n  /**\n   * Additional `RequestInit` options to be passed to `fetch` calls.\n   * Properties will be overridden by per-request `fetchOptions`.\n   */\n  fetchOptions?: MergedRequestInit | undefined;\n\n  /**\n   * Specify a custom `fetch` function implementation.\n   *\n   * If not provided, we expect that `fetch` is defined globally.\n   */\n  fetch?: Fetch | undefined;\n\n  /**\n   * The maximum number of times that the client will retry a request in case of a\n   * temporary failure, like a network error or a 5XX error from the server.\n   *\n   * @default 2\n   */\n  maxRetries?: number | undefined;\n\n  /**\n   * Default headers to include with every request to the API.\n   *\n   * These can be removed in individual requests by explicitly setting the\n   * header to `null` in request options.\n   */\n  defaultHeaders?: HeadersLike | undefined;\n\n  /**\n   * Default query parameters to include with every request to the API.\n   *\n   * These can be removed in individual requests by explicitly setting the\n   * param to `undefined` in request options.\n   */\n  defaultQuery?: Record<string, string | undefined> | undefined;\n\n  /**\n   * Set the log level.\n   *\n   * Defaults to process.env['SUPERMEMORY_LOG'] or 'warn' if it isn't set.\n   */\n  logLevel?: LogLevel | undefined;\n\n  /**\n   * Set the logger.\n   *\n   * Defaults to globalThis.console.\n   */\n  logger?: Logger | undefined;\n}\n\n/**\n * API Client for interfacing with the Supermemory API.\n */\nexport class Supermemory {\n  apiKey: string;\n\n  baseURL: string;\n  maxRetries: number;\n  timeout: number;\n  logger: Logger | undefined;\n  logLevel: LogLevel | undefined;\n  fetchOptions: MergedRequestInit | undefined;\n\n  private fetch: Fetch;\n  #encoder: Opts.RequestEncoder;\n  protected idempotencyHeader?: string;\n  private _options: ClientOptions;\n\n  /**\n   * API Client for interfacing with the Supermemory API.\n   *\n   * @param {string | undefined} [opts.apiKey=process.env['SUPERMEMORY_API_KEY'] ?? undefined]\n   * @param {string} [opts.baseURL=process.env['SUPERMEMORY_BASE_URL'] ?? https://api.supermemory.ai] - Override the default base URL for the API.\n   * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.\n   * @param {MergedRequestInit} [opts.fetchOptions] - Additional `RequestInit` options to be passed to `fetch` calls.\n   * @param {Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.\n   * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.\n   * @param {HeadersLike} opts.defaultHeaders - Default headers to include with every request to the API.\n   * @param {Record<string, string | undefined>} opts.defaultQuery - Default query parameters to include with every request to the API.\n   */\n  constructor({\n    baseURL = readEnv('SUPERMEMORY_BASE_URL'),\n    apiKey = readEnv('SUPERMEMORY_API_KEY'),\n    ...opts\n  }: ClientOptions = {}) {\n    if (apiKey === undefined) {\n      throw new Errors.SupermemoryError(\n        \"The SUPERMEMORY_API_KEY environment variable is missing or empty; either provide it, or instantiate the Supermemory client with an apiKey option, like new Supermemory({ apiKey: 'My API Key' }).\",\n      );\n    }\n\n    const options: ClientOptions = {\n      apiKey,\n      ...opts,\n      baseURL: baseURL || `https://api.supermemory.ai`,\n    };\n\n    this.baseURL = options.baseURL!;\n    this.timeout = options.timeout ?? Supermemory.DEFAULT_TIMEOUT /* 1 minute */;\n    this.logger = options.logger ?? console;\n    const defaultLogLevel = 'warn';\n    // Set default logLevel early so that we can log a warning in parseLogLevel.\n    this.logLevel = defaultLogLevel;\n    this.logLevel =\n      parseLogLevel(options.logLevel, 'ClientOptions.logLevel', this) ??\n      parseLogLevel(readEnv('SUPERMEMORY_LOG'), \"process.env['SUPERMEMORY_LOG']\", this) ??\n      defaultLogLevel;\n    this.fetchOptions = options.fetchOptions;\n    this.maxRetries = options.maxRetries ?? 2;\n    this.fetch = options.fetch ?? Shims.getDefaultFetch();\n    this.#encoder = Opts.FallbackEncoder;\n\n    this._options = options;\n\n    this.apiKey = apiKey;\n  }\n\n  /**\n   * Create a new client instance re-using the same options given to the current client with optional overriding.\n   */\n  withOptions(options: Partial<ClientOptions>): this {\n    return new (this.constructor as any as new (props: ClientOptions) => typeof this)({\n      ...this._options,\n      baseURL: this.baseURL,\n      maxRetries: this.maxRetries,\n      timeout: this.timeout,\n      logger: this.logger,\n      logLevel: this.logLevel,\n      fetchOptions: this.fetchOptions,\n      apiKey: this.apiKey,\n      ...options,\n    });\n  }\n\n  protected defaultQuery(): Record<string, string | undefined> | undefined {\n    return this._options.defaultQuery;\n  }\n\n  protected validateHeaders({ values, nulls }: NullableHeaders) {\n    return;\n  }\n\n  protected authHeaders(opts: FinalRequestOptions): NullableHeaders | undefined {\n    return buildHeaders([{ Authorization: `Bearer ${this.apiKey}` }]);\n  }\n\n  /**\n   * Basic re-implementation of `qs.stringify` for primitive types.\n   */\n  protected stringifyQuery(query: Record<string, unknown>): string {\n    return Object.entries(query)\n      .filter(([_, value]) => typeof value !== 'undefined')\n      .map(([key, value]) => {\n        if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n          return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;\n        }\n        if (value === null) {\n          return `${encodeURIComponent(key)}=`;\n        }\n        throw new Errors.SupermemoryError(\n          `Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`,\n        );\n      })\n      .join('&');\n  }\n\n  private getUserAgent(): string {\n    return `${this.constructor.name}/JS ${VERSION}`;\n  }\n\n  protected defaultIdempotencyKey(): string {\n    return `stainless-node-retry-${uuid4()}`;\n  }\n\n  protected makeStatusError(\n    status: number,\n    error: Object,\n    message: string | undefined,\n    headers: Headers,\n  ): Errors.APIError {\n    return Errors.APIError.generate(status, error, message, headers);\n  }\n\n  buildURL(path: string, query: Record<string, unknown> | null | undefined): string {\n    const url =\n      isAbsoluteURL(path) ?\n        new URL(path)\n      : new URL(this.baseURL + (this.baseURL.endsWith('/') && path.startsWith('/') ? path.slice(1) : path));\n\n    const defaultQuery = this.defaultQuery();\n    if (!isEmptyObj(defaultQuery)) {\n      query = { ...defaultQuery, ...query };\n    }\n\n    if (typeof query === 'object' && query && !Array.isArray(query)) {\n      url.search = this.stringifyQuery(query as Record<string, unknown>);\n    }\n\n    return url.toString();\n  }\n\n  /**\n   * Used as a callback for mutating the given `FinalRequestOptions` object.\n   */\n  protected async prepareOptions(options: FinalRequestOptions): Promise<void> {}\n\n  /**\n   * Used as a callback for mutating the given `RequestInit` object.\n   *\n   * This is useful for cases where you want to add certain headers based off of\n   * the request properties, e.g. `method` or `url`.\n   */\n  protected async prepareRequest(\n    request: RequestInit,\n    { url, options }: { url: string; options: FinalRequestOptions },\n  ): Promise<void> {}\n\n  get<Rsp>(path: string, opts?: PromiseOrValue<RequestOptions>): APIPromise<Rsp> {\n    return this.methodRequest('get', path, opts);\n  }\n\n  post<Rsp>(path: string, opts?: PromiseOrValue<RequestOptions>): APIPromise<Rsp> {\n    return this.methodRequest('post', path, opts);\n  }\n\n  patch<Rsp>(path: string, opts?: PromiseOrValue<RequestOptions>): APIPromise<Rsp> {\n    return this.methodRequest('patch', path, opts);\n  }\n\n  put<Rsp>(path: string, opts?: PromiseOrValue<RequestOptions>): APIPromise<Rsp> {\n    return this.methodRequest('put', path, opts);\n  }\n\n  delete<Rsp>(path: string, opts?: PromiseOrValue<RequestOptions>): APIPromise<Rsp> {\n    return this.methodRequest('delete', path, opts);\n  }\n\n  private methodRequest<Rsp>(\n    method: HTTPMethod,\n    path: string,\n    opts?: PromiseOrValue<RequestOptions>,\n  ): APIPromise<Rsp> {\n    return this.request(\n      Promise.resolve(opts).then((opts) => {\n        return { method, path, ...opts };\n      }),\n    );\n  }\n\n  request<Rsp>(\n    options: PromiseOrValue<FinalRequestOptions>,\n    remainingRetries: number | null = null,\n  ): APIPromise<Rsp> {\n    return new APIPromise(this, this.makeRequest(options, remainingRetries, undefined));\n  }\n\n  private async makeRequest(\n    optionsInput: PromiseOrValue<FinalRequestOptions>,\n    retriesRemaining: number | null,\n    retryOfRequestLogID: string | undefined,\n  ): Promise<APIResponseProps> {\n    const options = await optionsInput;\n    const maxRetries = options.maxRetries ?? this.maxRetries;\n    if (retriesRemaining == null) {\n      retriesRemaining = maxRetries;\n    }\n\n    await this.prepareOptions(options);\n\n    const { req, url, timeout } = this.buildRequest(options, { retryCount: maxRetries - retriesRemaining });\n\n    await this.prepareRequest(req, { url, options });\n\n    /** Not an API request ID, just for correlating local log entries. */\n    const requestLogID = 'log_' + ((Math.random() * (1 << 24)) | 0).toString(16).padStart(6, '0');\n    const retryLogStr = retryOfRequestLogID === undefined ? '' : `, retryOf: ${retryOfRequestLogID}`;\n    const startTime = Date.now();\n\n    loggerFor(this).debug(\n      `[${requestLogID}] sending request`,\n      formatRequestDetails({\n        retryOfRequestLogID,\n        method: options.method,\n        url,\n        options,\n        headers: req.headers,\n      }),\n    );\n\n    if (options.signal?.aborted) {\n      throw new Errors.APIUserAbortError();\n    }\n\n    const controller = new AbortController();\n    const response = await this.fetchWithTimeout(url, req, timeout, controller).catch(castToError);\n    const headersTime = Date.now();\n\n    if (response instanceof Error) {\n      const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n      if (options.signal?.aborted) {\n        throw new Errors.APIUserAbortError();\n      }\n      // detect native connection timeout errors\n      // deno throws \"TypeError: error sending request for url (https://example/): client error (Connect): tcp connect error: Operation timed out (os error 60): Operation timed out (os error 60)\"\n      // undici throws \"TypeError: fetch failed\" with cause \"ConnectTimeoutError: Connect Timeout Error (attempted address: example:443, timeout: 1ms)\"\n      // others do not provide enough information to distinguish timeouts from other connection errors\n      const isTimeout =\n        isAbortError(response) ||\n        /timed? ?out/i.test(String(response) + ('cause' in response ? String(response.cause) : ''));\n      if (retriesRemaining) {\n        loggerFor(this).info(\n          `[${requestLogID}] connection ${isTimeout ? 'timed out' : 'failed'} - ${retryMessage}`,\n        );\n        loggerFor(this).debug(\n          `[${requestLogID}] connection ${isTimeout ? 'timed out' : 'failed'} (${retryMessage})`,\n          formatRequestDetails({\n            retryOfRequestLogID,\n            url,\n            durationMs: headersTime - startTime,\n            message: response.message,\n          }),\n        );\n        return this.retryRequest(options, retriesRemaining, retryOfRequestLogID ?? requestLogID);\n      }\n      loggerFor(this).info(\n        `[${requestLogID}] connection ${isTimeout ? 'timed out' : 'failed'} - error; no more retries left`,\n      );\n      loggerFor(this).debug(\n        `[${requestLogID}] connection ${isTimeout ? 'timed out' : 'failed'} (error; no more retries left)`,\n        formatRequestDetails({\n          retryOfRequestLogID,\n          url,\n          durationMs: headersTime - startTime,\n          message: response.message,\n        }),\n      );\n      if (isTimeout) {\n        throw new Errors.APIConnectionTimeoutError();\n      }\n      throw new Errors.APIConnectionError({ cause: response });\n    }\n\n    const responseInfo = `[${requestLogID}${retryLogStr}] ${req.method} ${url} ${\n      response.ok ? 'succeeded' : 'failed'\n    } with status ${response.status} in ${headersTime - startTime}ms`;\n\n    if (!response.ok) {\n      const shouldRetry = this.shouldRetry(response);\n      if (retriesRemaining && shouldRetry) {\n        const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n\n        // We don't need the body of this response.\n        await Shims.CancelReadableStream(response.body);\n        loggerFor(this).info(`${responseInfo} - ${retryMessage}`);\n        loggerFor(this).debug(\n          `[${requestLogID}] response error (${retryMessage})`,\n          formatRequestDetails({\n            retryOfRequestLogID,\n            url: response.url,\n            status: response.status,\n            headers: response.headers,\n            durationMs: headersTime - startTime,\n          }),\n        );\n        return this.retryRequest(\n          options,\n          retriesRemaining,\n          retryOfRequestLogID ?? requestLogID,\n          response.headers,\n        );\n      }\n\n      const retryMessage = shouldRetry ? `error; no more retries left` : `error; not retryable`;\n\n      loggerFor(this).info(`${responseInfo} - ${retryMessage}`);\n\n      const errText = await response.text().catch((err: any) => castToError(err).message);\n      const errJSON = safeJSON(errText);\n      const errMessage = errJSON ? undefined : errText;\n\n      loggerFor(this).debug(\n        `[${requestLogID}] response error (${retryMessage})`,\n        formatRequestDetails({\n          retryOfRequestLogID,\n          url: response.url,\n          status: response.status,\n          headers: response.headers,\n          message: errMessage,\n          durationMs: Date.now() - startTime,\n        }),\n      );\n\n      const err = this.makeStatusError(response.status, errJSON, errMessage, response.headers);\n      throw err;\n    }\n\n    loggerFor(this).info(responseInfo);\n    loggerFor(this).debug(\n      `[${requestLogID}] response start`,\n      formatRequestDetails({\n        retryOfRequestLogID,\n        url: response.url,\n        status: response.status,\n        headers: response.headers,\n        durationMs: headersTime - startTime,\n      }),\n    );\n\n    return { response, options, controller, requestLogID, retryOfRequestLogID, startTime };\n  }\n\n  async fetchWithTimeout(\n    url: RequestInfo,\n    init: RequestInit | undefined,\n    ms: number,\n    controller: AbortController,\n  ): Promise<Response> {\n    const { signal, method, ...options } = init || {};\n    if (signal) signal.addEventListener('abort', () => controller.abort());\n\n    const timeout = setTimeout(() => controller.abort(), ms);\n\n    const isReadableBody =\n      ((globalThis as any).ReadableStream && options.body instanceof (globalThis as any).ReadableStream) ||\n      (typeof options.body === 'object' && options.body !== null && Symbol.asyncIterator in options.body);\n\n    const fetchOptions: RequestInit = {\n      signal: controller.signal as any,\n      ...(isReadableBody ? { duplex: 'half' } : {}),\n      method: 'GET',\n      ...options,\n    };\n    if (method) {\n      // Custom methods like 'patch' need to be uppercased\n      // See https://github.com/nodejs/undici/issues/2294\n      fetchOptions.method = method.toUpperCase();\n    }\n\n    try {\n      // use undefined this binding; fetch errors if bound to something else in browser/cloudflare\n      return await this.fetch.call(undefined, url, fetchOptions);\n    } finally {\n      clearTimeout(timeout);\n    }\n  }\n\n  private shouldRetry(response: Response): boolean {\n    // Note this is not a standard header.\n    const shouldRetryHeader = response.headers.get('x-should-retry');\n\n    // If the server explicitly says whether or not to retry, obey.\n    if (shouldRetryHeader === 'true') return true;\n    if (shouldRetryHeader === 'false') return false;\n\n    // Retry on request timeouts.\n    if (response.status === 408) return true;\n\n    // Retry on lock timeouts.\n    if (response.status === 409) return true;\n\n    // Retry on rate limits.\n    if (response.status === 429) return true;\n\n    // Retry internal errors.\n    if (response.status >= 500) return true;\n\n    return false;\n  }\n\n  private async retryRequest(\n    options: FinalRequestOptions,\n    retriesRemaining: number,\n    requestLogID: string,\n    responseHeaders?: Headers | undefined,\n  ): Promise<APIResponseProps> {\n    let timeoutMillis: number | undefined;\n\n    // Note the `retry-after-ms` header may not be standard, but is a good idea and we'd like proactive support for it.\n    const retryAfterMillisHeader = responseHeaders?.get('retry-after-ms');\n    if (retryAfterMillisHeader) {\n      const timeoutMs = parseFloat(retryAfterMillisHeader);\n      if (!Number.isNaN(timeoutMs)) {\n        timeoutMillis = timeoutMs;\n      }\n    }\n\n    // About the Retry-After header: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n    const retryAfterHeader = responseHeaders?.get('retry-after');\n    if (retryAfterHeader && !timeoutMillis) {\n      const timeoutSeconds = parseFloat(retryAfterHeader);\n      if (!Number.isNaN(timeoutSeconds)) {\n        timeoutMillis = timeoutSeconds * 1000;\n      } else {\n        timeoutMillis = Date.parse(retryAfterHeader) - Date.now();\n      }\n    }\n\n    // If the API asks us to wait a certain amount of time (and it's a reasonable amount),\n    // just do what it says, but otherwise calculate a default\n    if (!(timeoutMillis && 0 <= timeoutMillis && timeoutMillis < 60 * 1000)) {\n      const maxRetries = options.maxRetries ?? this.maxRetries;\n      timeoutMillis = this.calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries);\n    }\n    await sleep(timeoutMillis);\n\n    return this.makeRequest(options, retriesRemaining - 1, requestLogID);\n  }\n\n  private calculateDefaultRetryTimeoutMillis(retriesRemaining: number, maxRetries: number): number {\n    const initialRetryDelay = 0.5;\n    const maxRetryDelay = 8.0;\n\n    const numRetries = maxRetries - retriesRemaining;\n\n    // Apply exponential backoff, but not more than the max.\n    const sleepSeconds = Math.min(initialRetryDelay * Math.pow(2, numRetries), maxRetryDelay);\n\n    // Apply some jitter, take up to at most 25 percent of the retry time.\n    const jitter = 1 - Math.random() * 0.25;\n\n    return sleepSeconds * jitter * 1000;\n  }\n\n  buildRequest(\n    inputOptions: FinalRequestOptions,\n    { retryCount = 0 }: { retryCount?: number } = {},\n  ): { req: FinalizedRequestInit; url: string; timeout: number } {\n    const options = { ...inputOptions };\n    const { method, path, query } = options;\n\n    const url = this.buildURL(path!, query as Record<string, unknown>);\n    if ('timeout' in options) validatePositiveInteger('timeout', options.timeout);\n    options.timeout = options.timeout ?? this.timeout;\n    const { bodyHeaders, body } = this.buildBody({ options });\n    const reqHeaders = this.buildHeaders({ options: inputOptions, method, bodyHeaders, retryCount });\n\n    const req: FinalizedRequestInit = {\n      method,\n      headers: reqHeaders,\n      ...(options.signal && { signal: options.signal }),\n      ...((globalThis as any).ReadableStream &&\n        body instanceof (globalThis as any).ReadableStream && { duplex: 'half' }),\n      ...(body && { body }),\n      ...((this.fetchOptions as any) ?? {}),\n      ...((options.fetchOptions as any) ?? {}),\n    };\n\n    return { req, url, timeout: options.timeout };\n  }\n\n  private buildHeaders({\n    options,\n    method,\n    bodyHeaders,\n    retryCount,\n  }: {\n    options: FinalRequestOptions;\n    method: HTTPMethod;\n    bodyHeaders: HeadersLike;\n    retryCount: number;\n  }): Headers {\n    let idempotencyHeaders: HeadersLike = {};\n    if (this.idempotencyHeader && method !== 'get') {\n      if (!options.idempotencyKey) options.idempotencyKey = this.defaultIdempotencyKey();\n      idempotencyHeaders[this.idempotencyHeader] = options.idempotencyKey;\n    }\n\n    const headers = buildHeaders([\n      idempotencyHeaders,\n      {\n        Accept: 'application/json',\n        'User-Agent': this.getUserAgent(),\n        'X-Stainless-Retry-Count': String(retryCount),\n        ...(options.timeout ? { 'X-Stainless-Timeout': String(Math.trunc(options.timeout / 1000)) } : {}),\n        ...getPlatformHeaders(),\n      },\n      this.authHeaders(options),\n      this._options.defaultHeaders,\n      bodyHeaders,\n      options.headers,\n    ]);\n\n    this.validateHeaders(headers);\n\n    return headers.values;\n  }\n\n  private buildBody({ options: { body, headers: rawHeaders } }: { options: FinalRequestOptions }): {\n    bodyHeaders: HeadersLike;\n    body: BodyInit | undefined;\n  } {\n    if (!body) {\n      return { bodyHeaders: undefined, body: undefined };\n    }\n    const headers = buildHeaders([rawHeaders]);\n    if (\n      // Pass raw type verbatim\n      ArrayBuffer.isView(body) ||\n      body instanceof ArrayBuffer ||\n      body instanceof DataView ||\n      (typeof body === 'string' &&\n        // Preserve legacy string encoding behavior for now\n        headers.values.has('content-type')) ||\n      // `Blob` is superset of `File`\n      body instanceof Blob ||\n      // `FormData` -> `multipart/form-data`\n      body instanceof FormData ||\n      // `URLSearchParams` -> `application/x-www-form-urlencoded`\n      body instanceof URLSearchParams ||\n      // Send chunked stream (each chunk has own `length`)\n      ((globalThis as any).ReadableStream && body instanceof (globalThis as any).ReadableStream)\n    ) {\n      return { bodyHeaders: undefined, body: body as BodyInit };\n    } else if (\n      typeof body === 'object' &&\n      (Symbol.asyncIterator in body ||\n        (Symbol.iterator in body && 'next' in body && typeof body.next === 'function'))\n    ) {\n      return { bodyHeaders: undefined, body: Shims.ReadableStreamFrom(body as AsyncIterable<Uint8Array>) };\n    } else {\n      return this.#encoder({ body, headers });\n    }\n  }\n\n  static Supermemory = this;\n  static DEFAULT_TIMEOUT = 60000; // 1 minute\n\n  static SupermemoryError = Errors.SupermemoryError;\n  static APIError = Errors.APIError;\n  static APIConnectionError = Errors.APIConnectionError;\n  static APIConnectionTimeoutError = Errors.APIConnectionTimeoutError;\n  static APIUserAbortError = Errors.APIUserAbortError;\n  static NotFoundError = Errors.NotFoundError;\n  static ConflictError = Errors.ConflictError;\n  static RateLimitError = Errors.RateLimitError;\n  static BadRequestError = Errors.BadRequestError;\n  static AuthenticationError = Errors.AuthenticationError;\n  static InternalServerError = Errors.InternalServerError;\n  static PermissionDeniedError = Errors.PermissionDeniedError;\n  static UnprocessableEntityError = Errors.UnprocessableEntityError;\n\n  static toFile = Uploads.toFile;\n\n  memories: API.Memories = new API.Memories(this);\n  search: API.Search = new API.Search(this);\n  settings: API.Settings = new API.Settings(this);\n  connections: API.Connections = new API.Connections(this);\n}\nSupermemory.Memories = Memories;\nSupermemory.Search = Search;\nSupermemory.Settings = Settings;\nSupermemory.Connections = Connections;\nexport declare namespace Supermemory {\n  export type RequestOptions = Opts.RequestOptions;\n\n  export {\n    Memories as Memories,\n    type MemoryUpdateResponse as MemoryUpdateResponse,\n    type MemoryListResponse as MemoryListResponse,\n    type MemoryAddResponse as MemoryAddResponse,\n    type MemoryGetResponse as MemoryGetResponse,\n    type MemoryUpdateParams as MemoryUpdateParams,\n    type MemoryListParams as MemoryListParams,\n    type MemoryAddParams as MemoryAddParams,\n  };\n\n  export {\n    Search as Search,\n    type SearchExecuteResponse as SearchExecuteResponse,\n    type SearchExecuteParams as SearchExecuteParams,\n  };\n\n  export {\n    Settings as Settings,\n    type SettingUpdateResponse as SettingUpdateResponse,\n    type SettingGetResponse as SettingGetResponse,\n    type SettingUpdateParams as SettingUpdateParams,\n  };\n\n  export {\n    Connections as Connections,\n    type ConnectionCreateResponse as ConnectionCreateResponse,\n    type ConnectionListResponse as ConnectionListResponse,\n    type ConnectionDeleteResponse as ConnectionDeleteResponse,\n    type ConnectionGetByIDResponse as ConnectionGetByIDResponse,\n    type ConnectionGetByTagsResponse as ConnectionGetByTagsResponse,\n    type ConnectionListDocumentsResponse as ConnectionListDocumentsResponse,\n    type ConnectionCreateParams as ConnectionCreateParams,\n    type ConnectionListParams as ConnectionListParams,\n    type ConnectionDeleteParams as ConnectionDeleteParams,\n    type ConnectionGetByTagsParams as ConnectionGetByTagsParams,\n    type ConnectionImportParams as ConnectionImportParams,\n    type ConnectionListDocumentsParams as ConnectionListDocumentsParams,\n  };\n}\n"], "mappings": ";;;AAAA,SAAS,uBAAuB,UAAU,OAAO,OAAO,MAAM,GAAG;AAC7D,MAAI,SAAS;AACT,UAAM,IAAI,UAAU,gCAAgC;AACxD,MAAI,SAAS,OAAO,CAAC;AACjB,UAAM,IAAI,UAAU,+CAA+C;AACvE,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ;AAC5E,UAAM,IAAI,UAAU,yEAAyE;AACjG,SAAO,SAAS,MAAM,EAAE,KAAK,UAAU,KAAK,IAAI,IAAK,EAAE,QAAQ,QAAS,MAAM,IAAI,UAAU,KAAK,GAAG;AACxG;AACA,SAAS,uBAAuB,UAAU,OAAO,MAAM,GAAG;AACtD,MAAI,SAAS,OAAO,CAAC;AACjB,UAAM,IAAI,UAAU,+CAA+C;AACvE,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ;AAC5E,UAAM,IAAI,UAAU,0EAA0E;AAClG,SAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,KAAK,QAAQ,IAAI,IAAI,EAAE,QAAQ,MAAM,IAAI,QAAQ;AAChG;;;ACVO,IAAI,QAAQ,WAAA;AACjB,QAAM,EAAE,OAAM,IAAK;AACnB,MAAI,iCAAQ,YAAY;AACtB,YAAQ,OAAO,WAAW,KAAK,MAAM;AACrC,WAAO,OAAO,WAAU;EAC1B;AACA,QAAM,KAAK,IAAI,WAAW,CAAC;AAC3B,QAAM,aAAa,SAAS,MAAM,OAAO,gBAAgB,EAAE,EAAE,CAAC,IAAK,MAAO,KAAK,OAAM,IAAK,MAAQ;AAClG,SAAO,uCAAuC,QAAQ,UAAU,CAAC,OAC9D,CAAC,IAAK,WAAU,IAAM,MAAO,CAAC,IAAI,GAAM,SAAS,EAAE,CAAC;AAEzD;;;ACdM,SAAU,aAAa,KAAY;AACvC,SACE,OAAO,QAAQ,YACf,QAAQ;GAEN,UAAU,OAAQ,IAAY,SAAS;EAEtC,aAAa,OAAO,OAAQ,IAAY,OAAO,EAAE,SAAS,+BAA+B;AAEhG;AAEO,IAAM,cAAc,CAAC,QAAmB;AAC7C,MAAI,eAAe;AAAO,WAAO;AACjC,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,QAAI;AACF,UAAI,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM,kBAAkB;AAE5D,cAAM,QAAQ,IAAI,MAAM,IAAI,SAAS,IAAI,QAAQ,EAAE,OAAO,IAAI,MAAK,IAAK,CAAA,CAAE;AAC1E,YAAI,IAAI;AAAO,gBAAM,QAAQ,IAAI;AAEjC,YAAI,IAAI,SAAS,CAAC,MAAM;AAAO,gBAAM,QAAQ,IAAI;AACjD,YAAI,IAAI;AAAM,gBAAM,OAAO,IAAI;AAC/B,eAAO;MACT;IACF,QAAQ;IAAC;AACT,QAAI;AACF,aAAO,IAAI,MAAM,KAAK,UAAU,GAAG,CAAC;IACtC,QAAQ;IAAC;EACX;AACA,SAAO,IAAI,MAAM,GAAG;AACtB;;;AC5BM,IAAO,mBAAP,cAAgC,MAAK;;AAErC,IAAO,WAAP,MAAO,kBAIH,iBAAgB;EAQxB,YAAY,QAAiB,OAAe,SAA6B,SAAiB;AACxF,UAAM,GAAG,UAAS,YAAY,QAAQ,OAAO,OAAO,CAAC,EAAE;AACvD,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,QAAQ;EACf;EAEQ,OAAO,YAAY,QAA4B,OAAY,SAA2B;AAC5F,UAAM,OACJ,+BAAO,WACL,OAAO,MAAM,YAAY,WACvB,MAAM,UACN,KAAK,UAAU,MAAM,OAAO,IAC9B,QAAQ,KAAK,UAAU,KAAK,IAC5B;AAEJ,QAAI,UAAU,KAAK;AACjB,aAAO,GAAG,MAAM,IAAI,GAAG;IACzB;AACA,QAAI,QAAQ;AACV,aAAO,GAAG,MAAM;IAClB;AACA,QAAI,KAAK;AACP,aAAO;IACT;AACA,WAAO;EACT;EAEA,OAAO,SACL,QACA,eACA,SACA,SAA4B;AAE5B,QAAI,CAAC,UAAU,CAAC,SAAS;AACvB,aAAO,IAAI,mBAAmB,EAAE,SAAS,OAAO,YAAY,aAAa,EAAC,CAAE;IAC9E;AAEA,UAAM,QAAQ;AAEd,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,gBAAgB,QAAQ,OAAO,SAAS,OAAO;IAC5D;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,oBAAoB,QAAQ,OAAO,SAAS,OAAO;IAChE;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,sBAAsB,QAAQ,OAAO,SAAS,OAAO;IAClE;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,cAAc,QAAQ,OAAO,SAAS,OAAO;IAC1D;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,cAAc,QAAQ,OAAO,SAAS,OAAO;IAC1D;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,yBAAyB,QAAQ,OAAO,SAAS,OAAO;IACrE;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,eAAe,QAAQ,OAAO,SAAS,OAAO;IAC3D;AAEA,QAAI,UAAU,KAAK;AACjB,aAAO,IAAI,oBAAoB,QAAQ,OAAO,SAAS,OAAO;IAChE;AAEA,WAAO,IAAI,UAAS,QAAQ,OAAO,SAAS,OAAO;EACrD;;AAGI,IAAO,oBAAP,cAAiC,SAAyC;EAC9E,YAAY,EAAE,QAAO,IAA2B,CAAA,GAAE;AAChD,UAAM,QAAW,QAAW,WAAW,wBAAwB,MAAS;EAC1E;;AAGI,IAAO,qBAAP,cAAkC,SAAyC;EAC/E,YAAY,EAAE,SAAS,MAAK,GAA+D;AACzF,UAAM,QAAW,QAAW,WAAW,qBAAqB,MAAS;AAGrE,QAAI;AAAO,WAAK,QAAQ;EAC1B;;AAGI,IAAO,4BAAP,cAAyC,mBAAkB;EAC/D,YAAY,EAAE,QAAO,IAA2B,CAAA,GAAE;AAChD,UAAM,EAAE,SAAS,WAAW,qBAAoB,CAAE;EACpD;;AAGI,IAAO,kBAAP,cAA+B,SAAsB;;AAErD,IAAO,sBAAP,cAAmC,SAAsB;;AAEzD,IAAO,wBAAP,cAAqC,SAAsB;;AAE3D,IAAO,gBAAP,cAA6B,SAAsB;;AAEnD,IAAO,gBAAP,cAA6B,SAAsB;;AAEnD,IAAO,2BAAP,cAAwC,SAAsB;;AAE9D,IAAO,iBAAP,cAA8B,SAAsB;;AAEpD,IAAO,sBAAP,cAAmC,SAAyB;;;;AC5HlE,IAAM,yBAAyB;AAExB,IAAM,gBAAgB,CAAC,QAAwB;AACpD,SAAO,uBAAuB,KAAK,GAAG;AACxC;AAYM,SAAU,WAAW,KAA8B;AACvD,MAAI,CAAC;AAAK,WAAO;AACjB,aAAW,MAAM;AAAK,WAAO;AAC7B,SAAO;AACT;AAGM,SAAU,OAAkC,KAAQ,KAAgB;AACxE,SAAO,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG;AACtD;AAcO,IAAM,0BAA0B,CAAC,MAAc,MAAsB;AAC1E,MAAI,OAAO,MAAM,YAAY,CAAC,OAAO,UAAU,CAAC,GAAG;AACjD,UAAM,IAAI,iBAAiB,GAAG,IAAI,qBAAqB;EACzD;AACA,MAAI,IAAI,GAAG;AACT,UAAM,IAAI,iBAAiB,GAAG,IAAI,6BAA6B;EACjE;AACA,SAAO;AACT;AA2CO,IAAM,WAAW,CAAC,SAAgB;AACvC,MAAI;AACF,WAAO,KAAK,MAAM,IAAI;EACxB,SAAS,KAAK;AACZ,WAAO;EACT;AACF;;;ACnGO,IAAM,QAAQ,CAAC,OAAe,IAAI,QAAc,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;;;ACa3F,IAAM,eAAe;EACnB,KAAK;EACL,OAAO;EACP,MAAM;EACN,MAAM;EACN,OAAO;;AAGF,IAAM,gBAAgB,CAC3B,YACA,YACA,WACwB;AACxB,MAAI,CAAC,YAAY;AACf,WAAO;EACT;AACA,MAAI,OAAO,cAAc,UAAU,GAAG;AACpC,WAAO;EACT;AACA,YAAU,MAAM,EAAE,KAChB,GAAG,UAAU,eAAe,KAAK,UAAU,UAAU,CAAC,qBAAqB,KAAK,UAC9E,OAAO,KAAK,YAAY,CAAC,CAC1B,EAAE;AAEL,SAAO;AACT;AAEA,SAAS,OAAI;AAAI;AAEjB,SAAS,UAAU,SAAuB,QAA4B,UAAkB;AACtF,MAAI,CAAC,UAAU,aAAa,OAAO,IAAI,aAAa,QAAQ,GAAG;AAC7D,WAAO;EACT,OAAO;AAEL,WAAO,OAAO,OAAO,EAAE,KAAK,MAAM;EACpC;AACF;AAEA,IAAM,aAAa;EACjB,OAAO;EACP,MAAM;EACN,MAAM;EACN,OAAO;;AAGT,IAAI,gBAAgB,oBAAI,QAAO;AAEzB,SAAU,UAAU,QAAmB;AAC3C,QAAM,SAAS,OAAO;AACtB,QAAM,WAAW,OAAO,YAAY;AACpC,MAAI,CAAC,QAAQ;AACX,WAAO;EACT;AAEA,QAAM,eAAe,cAAc,IAAI,MAAM;AAC7C,MAAI,gBAAgB,aAAa,CAAC,MAAM,UAAU;AAChD,WAAO,aAAa,CAAC;EACvB;AAEA,QAAM,cAAc;IAClB,OAAO,UAAU,SAAS,QAAQ,QAAQ;IAC1C,MAAM,UAAU,QAAQ,QAAQ,QAAQ;IACxC,MAAM,UAAU,QAAQ,QAAQ,QAAQ;IACxC,OAAO,UAAU,SAAS,QAAQ,QAAQ;;AAG5C,gBAAc,IAAI,QAAQ,CAAC,UAAU,WAAW,CAAC;AAEjD,SAAO;AACT;AAEO,IAAM,uBAAuB,CAAC,YAWhC;AACH,MAAI,QAAQ,SAAS;AACnB,YAAQ,UAAU,EAAE,GAAG,QAAQ,QAAO;AACtC,WAAO,QAAQ,QAAQ,SAAS;EAClC;AACA,MAAI,QAAQ,SAAS;AACnB,YAAQ,UAAU,OAAO,aACtB,QAAQ,mBAAmB,UAAU,CAAC,GAAG,QAAQ,OAAO,IAAI,OAAO,QAAQ,QAAQ,OAAO,GAAG,IAC5F,CAAC,CAAC,MAAM,KAAK,MAAM;MACjB;MAEE,KAAK,YAAW,MAAO,mBACvB,KAAK,YAAW,MAAO,YACvB,KAAK,YAAW,MAAO,eAEvB,QACA;KACH,CACF;EAEL;AACA,MAAI,yBAAyB,SAAS;AACpC,QAAI,QAAQ,qBAAqB;AAC/B,cAAQ,UAAU,QAAQ;IAC5B;AACA,WAAO,QAAQ;EACjB;AACA,SAAO;AACT;;;AC7HO,IAAM,UAAU;;;ACoBvB,SAAS,sBAAmB;AAC1B,MAAI,OAAO,SAAS,eAAe,KAAK,SAAS,MAAM;AACrD,WAAO;EACT;AACA,MAAI,OAAO,gBAAgB,aAAa;AACtC,WAAO;EACT;AACA,MACE,OAAO,UAAU,SAAS,KACxB,OAAQ,WAAmB,YAAY,cAAe,WAAmB,UAAU,CAAC,MAChF,oBACN;AACA,WAAO;EACT;AACA,SAAO;AACT;AAwBA,IAAM,wBAAwB,MAAyB;AA3DvD,MAAAA;AA4DE,QAAM,mBAAmB,oBAAmB;AAC5C,MAAI,qBAAqB,QAAQ;AAC/B,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB,kBAAkB,KAAK,MAAM,EAAE;MACjD,oBAAoB,cAAc,KAAK,MAAM,IAAI;MACjD,uBAAuB;MACvB,+BACE,OAAO,KAAK,YAAY,WAAW,KAAK,YAAUA,MAAA,KAAK,YAAL,gBAAAA,IAAc,SAAQ;;EAE9E;AACA,MAAI,OAAO,gBAAgB,aAAa;AACtC,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB;MAClB,oBAAoB,SAAS,WAAW;MACxC,uBAAuB;MACvB,+BAAgC,WAAmB,QAAQ;;EAE/D;AAEA,MAAI,qBAAqB,QAAQ;AAC/B,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB,kBAAmB,WAAmB,QAAQ,YAAY,SAAS;MACrF,oBAAoB,cAAe,WAAmB,QAAQ,QAAQ,SAAS;MAC/E,uBAAuB;MACvB,+BAAgC,WAAmB,QAAQ,WAAW;;EAE1E;AAEA,QAAM,cAAc,eAAc;AAClC,MAAI,aAAa;AACf,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB;MAClB,oBAAoB;MACpB,uBAAuB,WAAW,YAAY,OAAO;MACrD,+BAA+B,YAAY;;EAE/C;AAGA,SAAO;IACL,oBAAoB;IACpB,+BAA+B;IAC/B,kBAAkB;IAClB,oBAAoB;IACpB,uBAAuB;IACvB,+BAA+B;;AAEnC;AAUA,SAAS,iBAAc;AACrB,MAAI,OAAO,cAAc,eAAe,CAAC,WAAW;AAClD,WAAO;EACT;AAGA,QAAM,kBAAkB;IACtB,EAAE,KAAK,QAAiB,SAAS,uCAAsC;IACvE,EAAE,KAAK,MAAe,SAAS,uCAAsC;IACrE,EAAE,KAAK,MAAe,SAAS,6CAA4C;IAC3E,EAAE,KAAK,UAAmB,SAAS,yCAAwC;IAC3E,EAAE,KAAK,WAAoB,SAAS,0CAAyC;IAC7E,EAAE,KAAK,UAAmB,SAAS,oEAAmE;;AAIxG,aAAW,EAAE,KAAK,QAAO,KAAM,iBAAiB;AAC9C,UAAM,QAAQ,QAAQ,KAAK,UAAU,SAAS;AAC9C,QAAI,OAAO;AACT,YAAM,QAAQ,MAAM,CAAC,KAAK;AAC1B,YAAM,QAAQ,MAAM,CAAC,KAAK;AAC1B,YAAM,QAAQ,MAAM,CAAC,KAAK;AAE1B,aAAO,EAAE,SAAS,KAAK,SAAS,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,GAAE;IAC9D;EACF;AAEA,SAAO;AACT;AAEA,IAAM,gBAAgB,CAAC,SAAsB;AAK3C,MAAI,SAAS;AAAO,WAAO;AAC3B,MAAI,SAAS,YAAY,SAAS;AAAO,WAAO;AAChD,MAAI,SAAS;AAAO,WAAO;AAC3B,MAAI,SAAS,aAAa,SAAS;AAAS,WAAO;AACnD,MAAI;AAAM,WAAO,SAAS,IAAI;AAC9B,SAAO;AACT;AAEA,IAAM,oBAAoB,CAAC,aAAkC;AAO3D,aAAW,SAAS,YAAW;AAM/B,MAAI,SAAS,SAAS,KAAK;AAAG,WAAO;AACrC,MAAI,aAAa;AAAW,WAAO;AACnC,MAAI,aAAa;AAAU,WAAO;AAClC,MAAI,aAAa;AAAS,WAAO;AACjC,MAAI,aAAa;AAAW,WAAO;AACnC,MAAI,aAAa;AAAW,WAAO;AACnC,MAAI,aAAa;AAAS,WAAO;AACjC,MAAI;AAAU,WAAO,SAAS,QAAQ;AACtC,SAAO;AACT;AAEA,IAAI;AACG,IAAM,qBAAqB,MAAK;AACrC,SAAQ,qBAAA,mBAAqB,sBAAqB;AACpD;;;ACvLM,SAAU,kBAAe;AAC7B,MAAI,OAAO,UAAU,aAAa;AAChC,WAAO;EACT;AAEA,QAAM,IAAI,MACR,wJAAwJ;AAE5J;AAIM,SAAU,sBAAsB,MAAwB;AAC5D,QAAM,iBAAkB,WAAmB;AAC3C,MAAI,OAAO,mBAAmB,aAAa;AAGzC,UAAM,IAAI,MACR,yHAAyH;EAE7H;AAEA,SAAO,IAAI,eAAe,GAAG,IAAI;AACnC;AAEM,SAAU,mBAAsB,UAAwC;AAC5E,MAAI,OACF,OAAO,iBAAiB,WAAW,SAAS,OAAO,aAAa,EAAC,IAAK,SAAS,OAAO,QAAQ,EAAC;AAEjG,SAAO,mBAAmB;IACxB,QAAK;IAAI;IACT,MAAM,KAAK,YAAe;AACxB,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,KAAI;AACvC,UAAI,MAAM;AACR,mBAAW,MAAK;MAClB,OAAO;AACL,mBAAW,QAAQ,KAAK;MAC1B;IACF;IACA,MAAM,SAAM;AAnDhB,UAAAC;AAoDM,cAAMA,MAAA,KAAK,WAAL,gBAAAA,IAAA;IACR;GACD;AACH;AAuCA,eAAsB,qBAAqB,QAAW;AA9FtD,MAAAC,KAAA;AA+FE,MAAI,WAAW,QAAQ,OAAO,WAAW;AAAU;AAEnD,MAAI,OAAO,OAAO,aAAa,GAAG;AAChC,YAAM,MAAAA,MAAA,OAAO,OAAO,aAAa,EAAC,GAAG,WAA/B,wBAAAA;AACN;EACF;AAEA,QAAM,SAAS,OAAO,UAAS;AAC/B,QAAM,gBAAgB,OAAO,OAAM;AACnC,SAAO,YAAW;AAClB,QAAM;AACR;;;AC7EO,IAAM,kBAAkC,CAAC,EAAE,SAAS,KAAI,MAAM;AACnE,SAAO;IACL,aAAa;MACX,gBAAgB;;IAElB,MAAM,KAAK,UAAU,IAAI;;AAE7B;;;ACvBO,IAAM,mBAAmB,MAAK;;AACnC,MAAI,OAAO,SAAS,aAAa;AAC/B,UAAM,EAAE,QAAO,IAAK;AACpB,UAAM,YACJ,SAAOC,MAAA,mCAAS,aAAT,gBAAAA,IAAmB,UAAS,YAAY,SAAS,QAAQ,SAAS,KAAK,MAAM,GAAG,CAAC,IAAI;AAC9F,UAAM,IAAI,MACR,4EACG,YACC,+FACA,GAAG;EAEX;AACF;AAiBM,SAAU,SACd,UACA,UACA,SAAyB;AAEzB,mBAAgB;AAChB,SAAO,IAAI,KAAK,UAAiB,YAAY,gBAAgB,OAAO;AACtE;AAEM,SAAU,QAAQ,OAAU;AAChC,UAEK,OAAO,UAAU,YAChB,UAAU,SACR,UAAU,SAAS,MAAM,QAAQ,OAAO,MAAM,IAAI,KACjD,SAAS,SAAS,MAAM,OAAO,OAAO,MAAM,GAAG,KAC/C,cAAc,SAAS,MAAM,YAAY,OAAO,MAAM,QAAQ,KAC9D,UAAU,SAAS,MAAM,QAAQ,OAAO,MAAM,IAAI,MACvD,IAEC,MAAM,OAAO,EACb,IAAG,KAAM;AAEhB;AAEO,IAAM,kBAAkB,CAAC,UAC9B,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,MAAM,OAAO,aAAa,MAAM;;;AC5CvF,IAAM,aAAa,CAAC,UAClB,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,MAAM,SAAS,YACtB,OAAO,MAAM,SAAS,YACtB,OAAO,MAAM,SAAS,cACtB,OAAO,MAAM,UAAU,cACvB,OAAO,MAAM,gBAAgB;AAe/B,IAAM,aAAa,CAAC,UAClB,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,MAAM,SAAS,YACtB,OAAO,MAAM,iBAAiB,YAC9B,WAAW,KAAK;AAUlB,IAAM,iBAAiB,CAAC,UACtB,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,MAAM,QAAQ,YACrB,OAAO,MAAM,SAAS;AAiBxB,eAAsB,OACpB,OACA,MACA,SAAqC;AAErC,mBAAgB;AAGhB,UAAQ,MAAM;AAGd,MAAI,WAAW,KAAK,GAAG;AACrB,QAAI,iBAAiB,MAAM;AACzB,aAAO;IACT;AACA,WAAO,SAAS,CAAC,MAAM,MAAM,YAAW,CAAE,GAAG,MAAM,IAAI;EACzD;AAEA,MAAI,eAAe,KAAK,GAAG;AACzB,UAAM,OAAO,MAAM,MAAM,KAAI;AAC7B,aAAA,OAAS,IAAI,IAAI,MAAM,GAAG,EAAE,SAAS,MAAM,OAAO,EAAE,IAAG;AAEvD,WAAO,SAAS,MAAM,SAAS,IAAI,GAAG,MAAM,OAAO;EACrD;AAEA,QAAM,QAAQ,MAAM,SAAS,KAAK;AAElC,WAAA,OAAS,QAAQ,KAAK;AAEtB,MAAI,EAAC,mCAAS,OAAM;AAClB,UAAM,OAAO,MAAM,KAAK,CAAC,SAAS,OAAO,SAAS,YAAY,UAAU,QAAQ,KAAK,IAAI;AACzF,QAAI,OAAO,SAAS,UAAU;AAC5B,gBAAU,EAAE,GAAG,SAAS,KAAI;IAC9B;EACF;AAEA,SAAO,SAAS,OAAO,MAAM,OAAO;AACtC;AAEA,eAAe,SAAS,OAAiD;;AACvE,MAAI,QAAyB,CAAA;AAC7B,MACE,OAAO,UAAU,YACjB,YAAY,OAAO,KAAK;EACxB,iBAAiB,aACjB;AACA,UAAM,KAAK,KAAK;EAClB,WAAW,WAAW,KAAK,GAAG;AAC5B,UAAM,KAAK,iBAAiB,OAAO,QAAQ,MAAM,MAAM,YAAW,CAAE;EACtE,WACE,gBAAgB,KAAK,GACrB;AACA,qBAAiB,SAAS,OAAO;AAC/B,YAAM,KAAK,GAAI,MAAM,SAAS,KAAqB,CAAE;IACvD;EACF,OAAO;AACL,UAAM,eAAcC,MAAA,+BAAO,gBAAP,gBAAAA,IAAoB;AACxC,UAAM,IAAI,MACR,yBAAyB,OAAO,KAAK,GACnC,cAAc,kBAAkB,WAAW,KAAK,EAClD,GAAG,cAAc,KAAK,CAAC,EAAE;EAE7B;AAEA,SAAO;AACT;AAEA,SAAS,cAAc,OAAc;AACnC,MAAI,OAAO,UAAU,YAAY,UAAU;AAAM,WAAO;AACxD,QAAM,QAAQ,OAAO,oBAAoB,KAAK;AAC9C,SAAO,aAAa,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAC3D;;;ACrJM,IAAO,cAAP,MAAkB;EAGtB,YAAY,QAAmB;AAC7B,SAAK,UAAU;EACjB;;;;ACEF,IAAM,+BAA+B,OAAO,8BAA8B;AAgB1E,IAAM,UAAU,MAAM;AAEtB,UAAU,eAAe,SAAoB;AAC3C,MAAI,CAAC;AAAS;AAEd,MAAI,gCAAgC,SAAS;AAC3C,UAAM,EAAE,QAAQ,MAAK,IAAK;AAC1B,WAAO,OAAO,QAAO;AACrB,eAAW,QAAQ,OAAO;AACxB,YAAM,CAAC,MAAM,IAAI;IACnB;AACA;EACF;AAEA,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI,mBAAmB,SAAS;AAC9B,WAAO,QAAQ,QAAO;EACxB,WAAW,QAAQ,OAAO,GAAG;AAC3B,WAAO;EACT,OAAO;AACL,kBAAc;AACd,WAAO,OAAO,QAAQ,WAAW,CAAA,CAAE;EACrC;AACA,WAAS,OAAO,MAAM;AACpB,UAAM,OAAO,IAAI,CAAC;AAClB,QAAI,OAAO,SAAS;AAAU,YAAM,IAAI,UAAU,qCAAqC;AACvF,UAAM,SAAS,QAAQ,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjD,QAAI,WAAW;AACf,eAAW,SAAS,QAAQ;AAC1B,UAAI,UAAU;AAAW;AAIzB,UAAI,eAAe,CAAC,UAAU;AAC5B,mBAAW;AACX,cAAM,CAAC,MAAM,IAAI;MACnB;AACA,YAAM,CAAC,MAAM,KAAK;IACpB;EACF;AACF;AAEO,IAAM,eAAe,CAAC,eAA8C;AACzE,QAAM,gBAAgB,IAAI,QAAO;AACjC,QAAM,cAAc,oBAAI,IAAG;AAC3B,aAAW,WAAW,YAAY;AAChC,UAAM,cAAc,oBAAI,IAAG;AAC3B,eAAW,CAAC,MAAM,KAAK,KAAK,eAAe,OAAO,GAAG;AACnD,YAAM,YAAY,KAAK,YAAW;AAClC,UAAI,CAAC,YAAY,IAAI,SAAS,GAAG;AAC/B,sBAAc,OAAO,IAAI;AACzB,oBAAY,IAAI,SAAS;MAC3B;AACA,UAAI,UAAU,MAAM;AAClB,sBAAc,OAAO,IAAI;AACzB,oBAAY,IAAI,SAAS;MAC3B,OAAO;AACL,sBAAc,OAAO,MAAM,KAAK;AAChC,oBAAY,OAAO,SAAS;MAC9B;IACF;EACF;AACA,SAAO,EAAE,CAAC,4BAA4B,GAAG,MAAM,QAAQ,eAAe,OAAO,YAAW;AAC1F;;;ACjFM,SAAU,cAAc,KAAW;AACvC,SAAO,IAAI,QAAQ,oCAAoC,kBAAkB;AAC3E;AAEO,IAAM,wBAAwB,CAAC,cAAc,kBAClD,SAASC,MAAK,YAA+B,QAA0B;AAErE,MAAI,QAAQ,WAAW;AAAG,WAAO,QAAQ,CAAC;AAE1C,MAAI,WAAW;AACf,QAAMA,QAAO,QAAQ,OAAO,CAAC,eAAe,cAAc,UAAS;AACjE,QAAI,OAAO,KAAK,YAAY,GAAG;AAC7B,iBAAW;IACb;AACA,WACE,gBACA,gBACC,UAAU,OAAO,SAAS,MAAM,WAAW,qBAAqB,aAAa,OAAO,OAAO,KAAK,CAAC,CAAC;EAEvG,GAAG,EAAE;AAEL,QAAM,WAAWA,MAAK,MAAM,QAAQ,CAAC,EAAE,CAAC;AACxC,QAAM,kBAAkB,CAAA;AACxB,QAAM,wBAAwB,WAAA,uCAAA,IAAoC;AAClE,MAAI;AAGJ,UAAQ,QAAQ,sBAAsB,KAAK,QAAQ,OAAO,MAAM;AAC9D,oBAAgB,KAAK;MACnB,OAAO,MAAM;MACb,QAAQ,MAAM,CAAC,EAAE;KAClB;EACH;AAEA,MAAI,gBAAgB,SAAS,GAAG;AAC9B,QAAI,UAAU;AACd,UAAM,YAAY,gBAAgB,OAAO,CAAC,KAAK,YAAW;AACxD,YAAM,SAAS,IAAI,OAAO,QAAQ,QAAQ,OAAO;AACjD,YAAM,SAAS,IAAI,OAAO,QAAQ,MAAM;AACxC,gBAAU,QAAQ,QAAQ,QAAQ;AAClC,aAAO,MAAM,SAAS;IACxB,GAAG,EAAE;AAEL,UAAM,IAAI,iBACR;EAA0DA,KAAI;EAAK,SAAS,EAAE;EAElF;AAEA,SAAOA;AACT;AAKK,IAAM,OAAO,sBAAsB,aAAa;;;ACxDjD,IAAO,cAAP,cAA2B,YAAW;;;;;;;;;;;EAW1C,OACE,UACA,OAAkD,CAAA,GAClD,SAAwB;AAExB,WAAO,KAAK,QAAQ,KAAK,uBAAuB,QAAQ,IAAI,EAAE,MAAM,GAAG,QAAO,CAAE;EAClF;;;;;;;;;EAUA,KACE,OAAgD,CAAA,GAChD,SAAwB;AAExB,WAAO,KAAK,QAAQ,KAAK,wBAAwB,EAAE,MAAM,GAAG,QAAO,CAAE;EACvE;;;;;;;;;;;EAYA,OACE,UACA,OAAkD,CAAA,GAClD,SAAwB;AAExB,WAAO,KAAK,QAAQ,OAAO,uBAAuB,QAAQ,IAAI,EAAE,MAAM,GAAG,QAAO,CAAE;EACpF;;;;;;;;;;;EAYA,QAAQ,cAAsB,SAAwB;AACpD,WAAO,KAAK,QAAQ,IAAI,uBAAuB,YAAY,IAAI,OAAO;EACxE;;;;;;;;;;;;EAaA,UACE,UACA,MACA,SAAwB;AAExB,WAAO,KAAK,QAAQ,KAAK,uBAAuB,QAAQ,eAAe,EAAE,MAAM,GAAG,QAAO,CAAE;EAC7F;;;;;;;;;EAUA,OACE,UACA,OAAkD,CAAA,GAClD,SAAwB;AAExB,WAAO,KAAK,QAAQ,KAAK,uBAAuB,QAAQ,WAAW;MACjE;MACA,GAAG;MACH,SAAS,aAAa,CAAC,EAAE,QAAQ,MAAK,GAAI,mCAAS,OAAO,CAAC;KAC5D;EACH;;;;;;;;;;;EAYA,cACE,UACA,OAAyD,CAAA,GACzD,SAAwB;AAExB,WAAO,KAAK,QAAQ,KAAK,uBAAuB,QAAQ,cAAc,EAAE,MAAM,GAAG,QAAO,CAAE;EAC5F;;;;ACzHI,IAAO,WAAP,cAAwB,YAAW;;;;;;;;;;;;EAYvC,OAAO,IAAY,MAA0B,SAAwB;AACnE,WAAO,KAAK,QAAQ,MAAM,oBAAoB,EAAE,IAAI,EAAE,MAAM,GAAG,QAAO,CAAE;EAC1E;;;;;;;;;EAUA,KACE,OAA4C,CAAA,GAC5C,SAAwB;AAExB,WAAO,KAAK,QAAQ,KAAK,qBAAqB,EAAE,MAAM,GAAG,QAAO,CAAE;EACpE;;;;;;;;;EAUA,OAAO,IAAY,SAAwB;AACzC,WAAO,KAAK,QAAQ,OAAO,oBAAoB,EAAE,IAAI;MACnD,GAAG;MACH,SAAS,aAAa,CAAC,EAAE,QAAQ,MAAK,GAAI,mCAAS,OAAO,CAAC;KAC5D;EACH;;;;;;;;;;;;EAaA,IAAI,MAAuB,SAAwB;AACjD,WAAO,KAAK,QAAQ,KAAK,gBAAgB,EAAE,MAAM,GAAG,QAAO,CAAE;EAC/D;;;;;;;;;EAUA,IAAI,IAAY,SAAwB;AACtC,WAAO,KAAK,QAAQ,IAAI,oBAAoB,EAAE,IAAI,OAAO;EAC3D;;;;ACzEI,IAAO,SAAP,cAAsB,YAAW;;;;;;;;;;;EAWrC,QAAQ,MAA2B,SAAwB;AACzD,WAAO,KAAK,QAAQ,KAAK,cAAc,EAAE,MAAM,GAAG,QAAO,CAAE;EAC7D;;;;ACbI,IAAO,WAAP,cAAwB,YAAW;;;;EAIvC,OACE,OAA+C,CAAA,GAC/C,SAAwB;AAExB,WAAO,KAAK,QAAQ,MAAM,gBAAgB,EAAE,MAAM,GAAG,QAAO,CAAE;EAChE;;;;EAKA,IAAI,SAAwB;AAC1B,WAAO,KAAK,QAAQ,IAAI,gBAAgB,OAAO;EACjD;;;;ACPF,eAAsB,qBAAwB,QAAqB,OAAuB;AACxF,QAAM,EAAE,UAAU,cAAc,qBAAqB,UAAS,IAAK;AACnE,QAAM,OAAO,OAAO,YAAW;AAjBjC,QAAAC;AAmBI,QAAI,SAAS,WAAW,KAAK;AAC3B,aAAO;IACT;AAEA,QAAI,MAAM,QAAQ,kBAAkB;AAClC,aAAO;IACT;AAEA,UAAM,cAAc,SAAS,QAAQ,IAAI,cAAc;AACvD,UAAM,aAAYA,MAAA,2CAAa,MAAM,KAAK,OAAxB,gBAAAA,IAA4B;AAC9C,UAAM,UAAS,uCAAW,SAAS,yBAAuB,uCAAW,SAAS;AAC9E,QAAI,QAAQ;AACV,YAAM,OAAO,MAAM,SAAS,KAAI;AAChC,aAAO;IACT;AAEA,UAAM,OAAO,MAAM,SAAS,KAAI;AAChC,WAAO;EACT,GAAE;AACF,YAAU,MAAM,EAAE,MAChB,IAAI,YAAY,qBAChB,qBAAqB;IACnB;IACA,KAAK,SAAS;IACd,QAAQ,SAAS;IACjB;IACA,YAAY,KAAK,IAAG,IAAK;GAC1B,CAAC;AAEJ,SAAO;AACT;;;;ACtCM,IAAO,aAAP,MAAO,oBAAsB,QAAU;EAI3C,YACE,QACQ,iBACA,gBAGiB,sBAAoB;AAE7C,UAAM,CAAC,YAAW;AAIhB,cAAQ,IAAW;IACrB,CAAC;AAXO,SAAA,kBAAA;AACA,SAAA,gBAAA;AALV,uBAAA,IAAA,MAAA,MAAA;AAgBE,2BAAA,MAAI,oBAAW,QAAM,GAAA;EACvB;EAEA,YAAe,WAAkD;AAC/D,WAAO,IAAI,YAAW,uBAAA,MAAI,oBAAA,GAAA,GAAU,KAAK,iBAAiB,OAAO,QAAQ,UACvE,UAAU,MAAM,KAAK,cAAc,QAAQ,KAAK,GAAG,KAAK,CAAC;EAE7D;;;;;;;;;;;;EAaA,aAAU;AACR,WAAO,KAAK,gBAAgB,KAAK,CAAC,MAAM,EAAE,QAAQ;EACpD;;;;;;;;;;;EAYA,MAAM,eAAY;AAChB,UAAM,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,IAAI,CAAC,KAAK,MAAK,GAAI,KAAK,WAAU,CAAE,CAAC;AAC5E,WAAO,EAAE,MAAM,SAAQ;EACzB;EAEQ,QAAK;AACX,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,gBAAgB,KAAK,gBAAgB,KAAK,CAAC,SAAS,KAAK,cAAc,uBAAA,MAAI,oBAAA,GAAA,GAAU,IAAI,CAAC;IACjG;AACA,WAAO,KAAK;EACd;EAES,KACP,aACA,YAAmF;AAEnF,WAAO,KAAK,MAAK,EAAG,KAAK,aAAa,UAAU;EAClD;EAES,MACP,YAAiF;AAEjF,WAAO,KAAK,MAAK,EAAG,MAAM,UAAU;EACtC;EAES,QAAQ,WAA2C;AAC1D,WAAO,KAAK,MAAK,EAAG,QAAQ,SAAS;EACvC;;;;;ACjFK,IAAM,UAAU,CAAC,QAAmC;AAT3D,MAAAC,KAAA;AAUE,MAAI,OAAQ,WAAmB,YAAY,aAAa;AACtD,aAAQ,MAAAA,MAAA,WAAmB,QAAQ,QAA3B,gBAAAA,IAAiC,SAAjC,mBAAuC,WAAU;EAC3D;AACA,MAAI,OAAQ,WAAmB,SAAS,aAAa;AACnD,YAAQ,4BAAmB,KAAK,QAAxB,mBAA6B,QAA7B,4BAAmC,SAAnC,mBAAyC;EACnD;AACA,SAAO;AACT;;;;;ACqHM,IAAO,cAAP,MAAkB;;;;;;;;;;;;;EA2BtB,YAAY,EACV,UAAU,QAAQ,sBAAsB,GACxC,SAAS,QAAQ,qBAAqB,GACtC,GAAG,KAAI,IACU,CAAA,GAAE;AApBrB,yBAAA,IAAA,MAAA,MAAA;AAmkBA,SAAA,WAAyB,IAAQ,SAAS,IAAI;AAC9C,SAAA,SAAqB,IAAQ,OAAO,IAAI;AACxC,SAAA,WAAyB,IAAQ,SAAS,IAAI;AAC9C,SAAA,cAA+B,IAAQ,YAAY,IAAI;AAjjBrD,QAAI,WAAW,QAAW;AACxB,YAAM,IAAW,iBACf,mMAAmM;IAEvM;AAEA,UAAM,UAAyB;MAC7B;MACA,GAAG;MACH,SAAS,WAAW;;AAGtB,SAAK,UAAU,QAAQ;AACvB,SAAK,UAAU,QAAQ,WAAW,GAAY;AAC9C,SAAK,SAAS,QAAQ,UAAU;AAChC,UAAM,kBAAkB;AAExB,SAAK,WAAW;AAChB,SAAK,WACH,cAAc,QAAQ,UAAU,0BAA0B,IAAI,KAC9D,cAAc,QAAQ,iBAAiB,GAAG,kCAAkC,IAAI,KAChF;AACF,SAAK,eAAe,QAAQ;AAC5B,SAAK,aAAa,QAAQ,cAAc;AACxC,SAAK,QAAQ,QAAQ,SAAe,gBAAe;AACnD,2BAAA,MAAI,sBAAiB,iBAAe,GAAA;AAEpC,SAAK,WAAW;AAEhB,SAAK,SAAS;EAChB;;;;EAKA,YAAY,SAA+B;AACzC,WAAO,IAAK,KAAK,YAAiE;MAChF,GAAG,KAAK;MACR,SAAS,KAAK;MACd,YAAY,KAAK;MACjB,SAAS,KAAK;MACd,QAAQ,KAAK;MACb,UAAU,KAAK;MACf,cAAc,KAAK;MACnB,QAAQ,KAAK;MACb,GAAG;KACJ;EACH;EAEU,eAAY;AACpB,WAAO,KAAK,SAAS;EACvB;EAEU,gBAAgB,EAAE,QAAQ,MAAK,GAAmB;AAC1D;EACF;EAEU,YAAY,MAAyB;AAC7C,WAAO,aAAa,CAAC,EAAE,eAAe,UAAU,KAAK,MAAM,GAAE,CAAE,CAAC;EAClE;;;;EAKU,eAAe,OAA8B;AACrD,WAAO,OAAO,QAAQ,KAAK,EACxB,OAAO,CAAC,CAAC,GAAG,KAAK,MAAM,OAAO,UAAU,WAAW,EACnD,IAAI,CAAC,CAAC,KAAK,KAAK,MAAK;AACpB,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;AACxF,eAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,KAAK,CAAC;MAChE;AACA,UAAI,UAAU,MAAM;AAClB,eAAO,GAAG,mBAAmB,GAAG,CAAC;MACnC;AACA,YAAM,IAAW,iBACf,yBAAyB,OAAO,KAAK,mQAAmQ;IAE5S,CAAC,EACA,KAAK,GAAG;EACb;EAEQ,eAAY;AAClB,WAAO,GAAG,KAAK,YAAY,IAAI,OAAO,OAAO;EAC/C;EAEU,wBAAqB;AAC7B,WAAO,wBAAwB,MAAK,CAAE;EACxC;EAEU,gBACR,QACA,OACA,SACA,SAAgB;AAEhB,WAAc,SAAS,SAAS,QAAQ,OAAO,SAAS,OAAO;EACjE;EAEA,SAASC,OAAc,OAAiD;AACtE,UAAM,MACJ,cAAcA,KAAI,IAChB,IAAI,IAAIA,KAAI,IACZ,IAAI,IAAI,KAAK,WAAW,KAAK,QAAQ,SAAS,GAAG,KAAKA,MAAK,WAAW,GAAG,IAAIA,MAAK,MAAM,CAAC,IAAIA,MAAK;AAEtG,UAAM,eAAe,KAAK,aAAY;AACtC,QAAI,CAAC,WAAW,YAAY,GAAG;AAC7B,cAAQ,EAAE,GAAG,cAAc,GAAG,MAAK;IACrC;AAEA,QAAI,OAAO,UAAU,YAAY,SAAS,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC/D,UAAI,SAAS,KAAK,eAAe,KAAgC;IACnE;AAEA,WAAO,IAAI,SAAQ;EACrB;;;;EAKU,MAAM,eAAe,SAA4B;EAAkB;;;;;;;EAQnE,MAAM,eACd,SACA,EAAE,KAAK,QAAO,GAAiD;EAC/C;EAElB,IAASA,OAAc,MAAqC;AAC1D,WAAO,KAAK,cAAc,OAAOA,OAAM,IAAI;EAC7C;EAEA,KAAUA,OAAc,MAAqC;AAC3D,WAAO,KAAK,cAAc,QAAQA,OAAM,IAAI;EAC9C;EAEA,MAAWA,OAAc,MAAqC;AAC5D,WAAO,KAAK,cAAc,SAASA,OAAM,IAAI;EAC/C;EAEA,IAASA,OAAc,MAAqC;AAC1D,WAAO,KAAK,cAAc,OAAOA,OAAM,IAAI;EAC7C;EAEA,OAAYA,OAAc,MAAqC;AAC7D,WAAO,KAAK,cAAc,UAAUA,OAAM,IAAI;EAChD;EAEQ,cACN,QACAA,OACA,MAAqC;AAErC,WAAO,KAAK,QACV,QAAQ,QAAQ,IAAI,EAAE,KAAK,CAACC,UAAQ;AAClC,aAAO,EAAE,QAAQ,MAAAD,OAAM,GAAGC,MAAI;IAChC,CAAC,CAAC;EAEN;EAEA,QACE,SACA,mBAAkC,MAAI;AAEtC,WAAO,IAAI,WAAW,MAAM,KAAK,YAAY,SAAS,kBAAkB,MAAS,CAAC;EACpF;EAEQ,MAAM,YACZ,cACA,kBACA,qBAAuC;AApV3C,QAAAC,KAAA;AAsVI,UAAM,UAAU,MAAM;AACtB,UAAM,aAAa,QAAQ,cAAc,KAAK;AAC9C,QAAI,oBAAoB,MAAM;AAC5B,yBAAmB;IACrB;AAEA,UAAM,KAAK,eAAe,OAAO;AAEjC,UAAM,EAAE,KAAK,KAAK,QAAO,IAAK,KAAK,aAAa,SAAS,EAAE,YAAY,aAAa,iBAAgB,CAAE;AAEtG,UAAM,KAAK,eAAe,KAAK,EAAE,KAAK,QAAO,CAAE;AAG/C,UAAM,eAAe,UAAW,KAAK,OAAM,KAAM,KAAK,MAAO,GAAG,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AAC5F,UAAM,cAAc,wBAAwB,SAAY,KAAK,cAAc,mBAAmB;AAC9F,UAAM,YAAY,KAAK,IAAG;AAE1B,cAAU,IAAI,EAAE,MACd,IAAI,YAAY,qBAChB,qBAAqB;MACnB;MACA,QAAQ,QAAQ;MAChB;MACA;MACA,SAAS,IAAI;KACd,CAAC;AAGJ,SAAIA,MAAA,QAAQ,WAAR,gBAAAA,IAAgB,SAAS;AAC3B,YAAM,IAAW,kBAAiB;IACpC;AAEA,UAAM,aAAa,IAAI,gBAAe;AACtC,UAAM,WAAW,MAAM,KAAK,iBAAiB,KAAK,KAAK,SAAS,UAAU,EAAE,MAAM,WAAW;AAC7F,UAAM,cAAc,KAAK,IAAG;AAE5B,QAAI,oBAAoB,OAAO;AAC7B,YAAM,eAAe,aAAa,gBAAgB;AAClD,WAAI,aAAQ,WAAR,mBAAgB,SAAS;AAC3B,cAAM,IAAW,kBAAiB;MACpC;AAKA,YAAM,YACJ,aAAa,QAAQ,KACrB,eAAe,KAAK,OAAO,QAAQ,KAAK,WAAW,WAAW,OAAO,SAAS,KAAK,IAAI,GAAG;AAC5F,UAAI,kBAAkB;AACpB,kBAAU,IAAI,EAAE,KACd,IAAI,YAAY,gBAAgB,YAAY,cAAc,QAAQ,MAAM,YAAY,EAAE;AAExF,kBAAU,IAAI,EAAE,MACd,IAAI,YAAY,gBAAgB,YAAY,cAAc,QAAQ,KAAK,YAAY,KACnF,qBAAqB;UACnB;UACA;UACA,YAAY,cAAc;UAC1B,SAAS,SAAS;SACnB,CAAC;AAEJ,eAAO,KAAK,aAAa,SAAS,kBAAkB,uBAAuB,YAAY;MACzF;AACA,gBAAU,IAAI,EAAE,KACd,IAAI,YAAY,gBAAgB,YAAY,cAAc,QAAQ,gCAAgC;AAEpG,gBAAU,IAAI,EAAE,MACd,IAAI,YAAY,gBAAgB,YAAY,cAAc,QAAQ,kCAClE,qBAAqB;QACnB;QACA;QACA,YAAY,cAAc;QAC1B,SAAS,SAAS;OACnB,CAAC;AAEJ,UAAI,WAAW;AACb,cAAM,IAAW,0BAAyB;MAC5C;AACA,YAAM,IAAW,mBAAmB,EAAE,OAAO,SAAQ,CAAE;IACzD;AAEA,UAAM,eAAe,IAAI,YAAY,GAAG,WAAW,KAAK,IAAI,MAAM,IAAI,GAAG,IACvE,SAAS,KAAK,cAAc,QAC9B,gBAAgB,SAAS,MAAM,OAAO,cAAc,SAAS;AAE7D,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,cAAc,KAAK,YAAY,QAAQ;AAC7C,UAAI,oBAAoB,aAAa;AACnC,cAAMC,gBAAe,aAAa,gBAAgB;AAGlD,cAAY,qBAAqB,SAAS,IAAI;AAC9C,kBAAU,IAAI,EAAE,KAAK,GAAG,YAAY,MAAMA,aAAY,EAAE;AACxD,kBAAU,IAAI,EAAE,MACd,IAAI,YAAY,qBAAqBA,aAAY,KACjD,qBAAqB;UACnB;UACA,KAAK,SAAS;UACd,QAAQ,SAAS;UACjB,SAAS,SAAS;UAClB,YAAY,cAAc;SAC3B,CAAC;AAEJ,eAAO,KAAK,aACV,SACA,kBACA,uBAAuB,cACvB,SAAS,OAAO;MAEpB;AAEA,YAAM,eAAe,cAAc,gCAAgC;AAEnE,gBAAU,IAAI,EAAE,KAAK,GAAG,YAAY,MAAM,YAAY,EAAE;AAExD,YAAM,UAAU,MAAM,SAAS,KAAI,EAAG,MAAM,CAACC,SAAa,YAAYA,IAAG,EAAE,OAAO;AAClF,YAAM,UAAU,SAAS,OAAO;AAChC,YAAM,aAAa,UAAU,SAAY;AAEzC,gBAAU,IAAI,EAAE,MACd,IAAI,YAAY,qBAAqB,YAAY,KACjD,qBAAqB;QACnB;QACA,KAAK,SAAS;QACd,QAAQ,SAAS;QACjB,SAAS,SAAS;QAClB,SAAS;QACT,YAAY,KAAK,IAAG,IAAK;OAC1B,CAAC;AAGJ,YAAM,MAAM,KAAK,gBAAgB,SAAS,QAAQ,SAAS,YAAY,SAAS,OAAO;AACvF,YAAM;IACR;AAEA,cAAU,IAAI,EAAE,KAAK,YAAY;AACjC,cAAU,IAAI,EAAE,MACd,IAAI,YAAY,oBAChB,qBAAqB;MACnB;MACA,KAAK,SAAS;MACd,QAAQ,SAAS;MACjB,SAAS,SAAS;MAClB,YAAY,cAAc;KAC3B,CAAC;AAGJ,WAAO,EAAE,UAAU,SAAS,YAAY,cAAc,qBAAqB,UAAS;EACtF;EAEA,MAAM,iBACJ,KACA,MACA,IACA,YAA2B;AAE3B,UAAM,EAAE,QAAQ,QAAQ,GAAG,QAAO,IAAK,QAAQ,CAAA;AAC/C,QAAI;AAAQ,aAAO,iBAAiB,SAAS,MAAM,WAAW,MAAK,CAAE;AAErE,UAAM,UAAU,WAAW,MAAM,WAAW,MAAK,GAAI,EAAE;AAEvD,UAAM,iBACF,WAAmB,kBAAkB,QAAQ,gBAAiB,WAAmB,kBAClF,OAAO,QAAQ,SAAS,YAAY,QAAQ,SAAS,QAAQ,OAAO,iBAAiB,QAAQ;AAEhG,UAAM,eAA4B;MAChC,QAAQ,WAAW;MACnB,GAAI,iBAAiB,EAAE,QAAQ,OAAM,IAAK,CAAA;MAC1C,QAAQ;MACR,GAAG;;AAEL,QAAI,QAAQ;AAGV,mBAAa,SAAS,OAAO,YAAW;IAC1C;AAEA,QAAI;AAEF,aAAO,MAAM,KAAK,MAAM,KAAK,QAAW,KAAK,YAAY;IAC3D;AACE,mBAAa,OAAO;IACtB;EACF;EAEQ,YAAY,UAAkB;AAEpC,UAAM,oBAAoB,SAAS,QAAQ,IAAI,gBAAgB;AAG/D,QAAI,sBAAsB;AAAQ,aAAO;AACzC,QAAI,sBAAsB;AAAS,aAAO;AAG1C,QAAI,SAAS,WAAW;AAAK,aAAO;AAGpC,QAAI,SAAS,WAAW;AAAK,aAAO;AAGpC,QAAI,SAAS,WAAW;AAAK,aAAO;AAGpC,QAAI,SAAS,UAAU;AAAK,aAAO;AAEnC,WAAO;EACT;EAEQ,MAAM,aACZ,SACA,kBACA,cACA,iBAAqC;AAErC,QAAI;AAGJ,UAAM,yBAAyB,mDAAiB,IAAI;AACpD,QAAI,wBAAwB;AAC1B,YAAM,YAAY,WAAW,sBAAsB;AACnD,UAAI,CAAC,OAAO,MAAM,SAAS,GAAG;AAC5B,wBAAgB;MAClB;IACF;AAGA,UAAM,mBAAmB,mDAAiB,IAAI;AAC9C,QAAI,oBAAoB,CAAC,eAAe;AACtC,YAAM,iBAAiB,WAAW,gBAAgB;AAClD,UAAI,CAAC,OAAO,MAAM,cAAc,GAAG;AACjC,wBAAgB,iBAAiB;MACnC,OAAO;AACL,wBAAgB,KAAK,MAAM,gBAAgB,IAAI,KAAK,IAAG;MACzD;IACF;AAIA,QAAI,EAAE,iBAAiB,KAAK,iBAAiB,gBAAgB,KAAK,MAAO;AACvE,YAAM,aAAa,QAAQ,cAAc,KAAK;AAC9C,sBAAgB,KAAK,mCAAmC,kBAAkB,UAAU;IACtF;AACA,UAAM,MAAM,aAAa;AAEzB,WAAO,KAAK,YAAY,SAAS,mBAAmB,GAAG,YAAY;EACrE;EAEQ,mCAAmC,kBAA0B,YAAkB;AACrF,UAAM,oBAAoB;AAC1B,UAAM,gBAAgB;AAEtB,UAAM,aAAa,aAAa;AAGhC,UAAM,eAAe,KAAK,IAAI,oBAAoB,KAAK,IAAI,GAAG,UAAU,GAAG,aAAa;AAGxF,UAAM,SAAS,IAAI,KAAK,OAAM,IAAK;AAEnC,WAAO,eAAe,SAAS;EACjC;EAEA,aACE,cACA,EAAE,aAAa,EAAC,IAA8B,CAAA,GAAE;AAEhD,UAAM,UAAU,EAAE,GAAG,aAAY;AACjC,UAAM,EAAE,QAAQ,MAAAJ,OAAM,MAAK,IAAK;AAEhC,UAAM,MAAM,KAAK,SAASA,OAAO,KAAgC;AACjE,QAAI,aAAa;AAAS,8BAAwB,WAAW,QAAQ,OAAO;AAC5E,YAAQ,UAAU,QAAQ,WAAW,KAAK;AAC1C,UAAM,EAAE,aAAa,KAAI,IAAK,KAAK,UAAU,EAAE,QAAO,CAAE;AACxD,UAAM,aAAa,KAAK,aAAa,EAAE,SAAS,cAAc,QAAQ,aAAa,WAAU,CAAE;AAE/F,UAAM,MAA4B;MAChC;MACA,SAAS;MACT,GAAI,QAAQ,UAAU,EAAE,QAAQ,QAAQ,OAAM;MAC9C,GAAK,WAAmB,kBACtB,gBAAiB,WAAmB,kBAAkB,EAAE,QAAQ,OAAM;MACxE,GAAI,QAAQ,EAAE,KAAI;MAClB,GAAK,KAAK,gBAAwB,CAAA;MAClC,GAAK,QAAQ,gBAAwB,CAAA;;AAGvC,WAAO,EAAE,KAAK,KAAK,SAAS,QAAQ,QAAO;EAC7C;EAEQ,aAAa,EACnB,SACA,QACA,aACA,WAAU,GAMX;AACC,QAAI,qBAAkC,CAAA;AACtC,QAAI,KAAK,qBAAqB,WAAW,OAAO;AAC9C,UAAI,CAAC,QAAQ;AAAgB,gBAAQ,iBAAiB,KAAK,sBAAqB;AAChF,yBAAmB,KAAK,iBAAiB,IAAI,QAAQ;IACvD;AAEA,UAAM,UAAU,aAAa;MAC3B;MACA;QACE,QAAQ;QACR,cAAc,KAAK,aAAY;QAC/B,2BAA2B,OAAO,UAAU;QAC5C,GAAI,QAAQ,UAAU,EAAE,uBAAuB,OAAO,KAAK,MAAM,QAAQ,UAAU,GAAI,CAAC,EAAC,IAAK,CAAA;QAC9F,GAAG,mBAAkB;;MAEvB,KAAK,YAAY,OAAO;MACxB,KAAK,SAAS;MACd;MACA,QAAQ;KACT;AAED,SAAK,gBAAgB,OAAO;AAE5B,WAAO,QAAQ;EACjB;EAEQ,UAAU,EAAE,SAAS,EAAE,MAAM,SAAS,WAAU,EAAE,GAAoC;AAI5F,QAAI,CAAC,MAAM;AACT,aAAO,EAAE,aAAa,QAAW,MAAM,OAAS;IAClD;AACA,UAAM,UAAU,aAAa,CAAC,UAAU,CAAC;AACzC;;MAEE,YAAY,OAAO,IAAI,KACvB,gBAAgB,eAChB,gBAAgB,YACf,OAAO,SAAS;MAEf,QAAQ,OAAO,IAAI,cAAc;MAEnC,gBAAgB;MAEhB,gBAAgB;MAEhB,gBAAgB;MAEd,WAAmB,kBAAkB,gBAAiB,WAAmB;MAC3E;AACA,aAAO,EAAE,aAAa,QAAW,KAAsB;IACzD,WACE,OAAO,SAAS,aACf,OAAO,iBAAiB,QACtB,OAAO,YAAY,QAAQ,UAAU,QAAQ,OAAO,KAAK,SAAS,aACrE;AACA,aAAO,EAAE,aAAa,QAAW,MAAY,mBAAmB,IAAiC,EAAC;IACpG,OAAO;AACL,aAAO,uBAAA,MAAI,sBAAA,GAAA,EAAS,KAAb,MAAc,EAAE,MAAM,QAAO,CAAE;IACxC;EACF;;;AAEO,YAAA,cAAc;AACd,YAAA,kBAAkB;AAElB,YAAA,mBAA0B;AAC1B,YAAA,WAAkB;AAClB,YAAA,qBAA4B;AAC5B,YAAA,4BAAmC;AACnC,YAAA,oBAA2B;AAC3B,YAAA,gBAAuB;AACvB,YAAA,gBAAuB;AACvB,YAAA,iBAAwB;AACxB,YAAA,kBAAyB;AACzB,YAAA,sBAA6B;AAC7B,YAAA,sBAA6B;AAC7B,YAAA,wBAA+B;AAC/B,YAAA,2BAAkC;AAElC,YAAA,SAAiB;AAO1B,YAAY,WAAW;AACvB,YAAY,SAAS;AACrB,YAAY,WAAW;AACvB,YAAY,cAAc;", "names": ["_a", "_a", "_a", "_a", "_a", "path", "_a", "_a", "path", "opts", "_a", "retryMessage", "err"]}