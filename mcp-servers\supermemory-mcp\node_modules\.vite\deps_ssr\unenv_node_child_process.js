import {
  notImplemented,
  notImplementedClass
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/child_process.mjs
var ChildProcess = notImplementedClass("child_process.ChildProcess");
var _forkChild = notImplemented("child_process.ChildProcess");
var exec = notImplemented("child_process.exec");
var execFile = notImplemented("child_process.execFile");
var execFileSync = notImplemented("child_process.execFileSync");
var execSync = notImplemented("child_process.execSyn");
var fork = notImplemented("child_process.fork");
var spawn = notImplemented("child_process.spawn");
var spawnSync = notImplemented("child_process.spawnSync");
var child_process_default = {
  ChildProcess,
  _forkChild,
  exec,
  execFile,
  execFileSync,
  execSync,
  fork,
  spawn,
  spawnSync
};
export {
  ChildProcess,
  _forkChild,
  child_process_default as default,
  exec,
  execFile,
  execFileSync,
  execSync,
  fork,
  spawn,
  spawnSync
};
//# sourceMappingURL=unenv_node_child_process.js.map
