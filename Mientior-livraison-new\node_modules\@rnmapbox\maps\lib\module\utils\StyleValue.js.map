{"version": 3, "names": ["Image", "processColor", "BridgeValue", "getStyleType", "transformStyle", "style", "nativeStyle", "styleProps", "Object", "keys", "styleProp", "styleType", "rawStyle", "color", "undefined", "console", "error", "resolveAssetSource", "Array", "isArray", "startsWith", "warn", "bridgeValue", "styletype", "stylevalue", "toJSON"], "sourceRoot": "../../../src", "sources": ["utils/StyleValue.ts"], "mappings": ";;AAAA,SAASA,KAAK,EAAEC,YAAY,QAAQ,cAAc;AAElD,OAAOC,WAAW,MAGX,eAAe;AAEtB,SAASC,YAAY,QAAQ,YAAY;AAOzC,OAAO,SAASC,cAAcA,CAC5BC,KAAqC,EACM;EAC3C,IAAI,CAACA,KAAK,EAAE;IACV;EACF;EAEA,MAAMC,WAA0C,GAAG,CAAC,CAAC;EACrD,MAAMC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACJ,KAAK,CAA8B;EAClE,KAAK,MAAMK,SAAS,IAAIH,UAAU,EAAE;IAClC,MAAMI,SAAS,GAAGR,YAAY,CAACO,SAAS,CAAC;IACzC,IAAIE,QAAkC,GAAGP,KAAK,CAACK,SAAS,CAAC;IAEzD,IAAIC,SAAS,KAAK,OAAO,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAE;MACzD,MAAMC,KAAK,GAAGZ,YAAY,CAACW,QAAQ,CAAC;MACpC,IAAIC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;QACtEE,OAAO,CAACC,KAAK,CAAC,kCAAkCJ,QAAQ,YAAY,CAAC;QACrEA,QAAQ,GAAG,QAAQ;MACrB,CAAC,MAAM;QACLA,QAAQ,GAAGC,KAAK;MAClB;IACF,CAAC,MAAM,IAAIF,SAAS,KAAK,OAAO,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAE;MAChEA,QAAQ,GACLZ,KAAK,CAACiB,kBAAkB,CAACL,QAAQ,CAAC,IAAgC,CAAC,CAAC;IACzE;IACA,IACED,SAAS,KAAK,OAAO,IACrB,EACEO,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,IACtB,OAAOA,QAAQ,KAAK,QAAQ,IAC3B,CAACA,QAAQ,CAACQ,UAAU,CAAC,SAAS,CAAC,IAC/B,CAACR,QAAQ,CAACQ,UAAU,CAAC,UAAU,CAAE,CACpC,EACD;MACAL,OAAO,CAACM,IAAI,CACV,6BAA6BX,SAAS,4GACxC,CAAC;IACH;IAEA,IAAIE,QAAQ,KAAKE,SAAS,EAAE;MAC1B,MAAMQ,WAAW,GAAG,IAAIpB,WAAW,CAACU,QAAQ,CAAC;MAC7CN,WAAW,CAACI,SAAS,CAAC,GAAG;QACvBa,SAAS,EAAEZ,SAAS;QACpBa,UAAU,EAAEF,WAAW,CAACG,MAAM,CAAC;MACjC,CAAC;IACH;EACF;EAEA,OAAOnB,WAAW;AACpB", "ignoreList": []}