{"version": 3, "sources": ["../../unenv/dist/runtime/node/fs.mjs", "../../unenv/dist/runtime/node/internal/fs/classes.mjs", "../../unenv/dist/runtime/node/internal/fs/fs.mjs"], "sourcesContent": ["import promises from \"node:fs/promises\";\nimport { Dir, Dirent, FileReadStream, FileWriteStream, ReadStream, Stats, WriteStream } from \"./internal/fs/classes.mjs\";\nimport { _toUnixTimestamp, access, accessSync, appendFile, appendFileSync, chmod, chmodSync, chown, chownSync, close, closeSync, copyFile, copyFileSync, cp, cpSync, createReadStream, createWriteStream, exists, existsSync, fchmod, fchmodSync, fchown, fchownSync, fdatasync, fdatasyncSync, fstat, fstatSync, fsync, fsyncSync, ftruncate, ftruncateSync, futimes, futimesSync, glob, lchmod, globSync, lchmodSync, lchown, lchownSync, link, linkSync, lstat, lstatSync, lutimes, lutimesSync, mkdir, mkdirSync, mkdtemp, mkdtempSync, open, openAsBlob, openSync, opendir, opendirSync, read, readFile, readFileSync, readSync, readdir, readdirSync, readlink, readlinkSync, readv, readvSync, realpath, realpathSync, rename, renameSync, rm, rmSync, rmdir, rmdirSync, stat, statSync, statfs, statfsSync, symlink, symlinkSync, truncate, truncateSync, unlink, unlinkSync, unwatchFile, utimes, utimesSync, watch, watchFile, write, writeFile, writeFileSync, writeSync, writev, writevSync } from \"./internal/fs/fs.mjs\";\nimport * as constants from \"./internal/fs/constants.mjs\";\nimport { F_OK, R_OK, W_OK, X_OK } from \"./internal/fs/constants.mjs\";\nexport { F_OK, R_OK, W_OK, X_OK } from \"./internal/fs/constants.mjs\";\nexport { promises, constants };\nexport * from \"./internal/fs/fs.mjs\";\nexport * from \"./internal/fs/classes.mjs\";\nexport default {\n\tF_OK,\n\tR_OK,\n\tW_OK,\n\tX_OK,\n\tconstants,\n\tpromises,\n\tDir,\n\tDirent,\n\tFileReadStream,\n\tFileWriteStream,\n\tReadStream,\n\tStats,\n\tWriteStream,\n\t_toUnixTimestamp,\n\taccess,\n\taccessSync,\n\tappendFile,\n\tappendFileSync,\n\tchmod,\n\tchmodSync,\n\tchown,\n\tchownSync,\n\tclose,\n\tcloseSync,\n\tcopyFile,\n\tcopyFileSync,\n\tcp,\n\tcpSync,\n\tcreateReadStream,\n\tcreateWriteStream,\n\texists,\n\texistsSync,\n\tfchmod,\n\tfchmodSync,\n\tfchown,\n\tfchownSync,\n\tfdatasync,\n\tfdatasyncSync,\n\tfstat,\n\tfstatSync,\n\tfsync,\n\tfsyncSync,\n\tftruncate,\n\tftruncateSync,\n\tfutimes,\n\tfutimesSync,\n\tglob,\n\tlchmod,\n\tglobSync,\n\tlchmodSync,\n\tlchown,\n\tlchownSync,\n\tlink,\n\tlinkSync,\n\tlstat,\n\tlstatSync,\n\tlutimes,\n\tlutimesSync,\n\tmkdir,\n\tmkdirSync,\n\tmkdtemp,\n\tmkdtempSync,\n\topen,\n\topenAsBlob,\n\topenSync,\n\topendir,\n\topendirSync,\n\tread,\n\treadFile,\n\treadFileSync,\n\treadSync,\n\treaddir,\n\treaddirSync,\n\treadlink,\n\treadlinkSync,\n\treadv,\n\treadvSync,\n\trealpath,\n\trealpathSync,\n\trename,\n\trenameSync,\n\trm,\n\trmSync,\n\trmdir,\n\trmdirSync,\n\tstat,\n\tstatSync,\n\tstatfs,\n\tstatfsSync,\n\tsymlink,\n\tsymlinkSync,\n\ttruncate,\n\ttruncateSync,\n\tunlink,\n\tunlinkSync,\n\tunwatchFile,\n\tutimes,\n\tutimesSync,\n\twatch,\n\twatchFile,\n\twrite,\n\twriteFile,\n\twriteFileSync,\n\twriteSync,\n\twritev,\n\twritevSync\n};\n", "import { notImplementedClass } from \"../../../_internal/utils.mjs\";\nexport const Dir = /* @__PURE__ */ notImplementedClass(\"fs.Dir\");\nexport const Dirent = /* @__PURE__ */ notImplementedClass(\"fs.Dirent\");\nexport const Stats = /* @__PURE__ */ notImplementedClass(\"fs.Stats\");\nexport const ReadStream = /* @__PURE__ */ notImplementedClass(\"fs.ReadStream\");\nexport const WriteStream = /* @__PURE__ */ notImplementedClass(\"fs.WriteStream\");\nexport const FileReadStream = ReadStream;\nexport const FileWriteStream = WriteStream;\n", "import { notImplemented, notImplementedAsync } from \"../../../_internal/utils.mjs\";\nimport * as fsp from \"./promises.mjs\";\nfunction callbackify(fn) {\n\tconst fnc = function(...args) {\n\t\tconst cb = args.pop();\n\t\tfn().catch((error) => cb(error)).then((val) => cb(undefined, val));\n\t};\n\tfnc.__promisify__ = fn;\n\tfnc.native = fnc;\n\treturn fnc;\n}\nexport const access = callbackify(fsp.access);\nexport const appendFile = callbackify(fsp.appendFile);\nexport const chown = callbackify(fsp.chown);\nexport const chmod = callbackify(fsp.chmod);\nexport const copyFile = callbackify(fsp.copyFile);\nexport const cp = callbackify(fsp.cp);\nexport const lchown = callbackify(fsp.lchown);\nexport const lchmod = callbackify(fsp.lchmod);\nexport const link = callbackify(fsp.link);\nexport const lstat = callbackify(fsp.lstat);\nexport const lutimes = callbackify(fsp.lutimes);\nexport const mkdir = callbackify(fsp.mkdir);\nexport const mkdtemp = callbackify(fsp.mkdtemp);\nexport const realpath = callbackify(fsp.realpath);\nexport const open = callbackify(fsp.open);\nexport const opendir = callbackify(fsp.opendir);\nexport const readdir = callbackify(fsp.readdir);\nexport const readFile = callbackify(fsp.readFile);\nexport const readlink = callbackify(fsp.readlink);\nexport const rename = callbackify(fsp.rename);\nexport const rm = callbackify(fsp.rm);\nexport const rmdir = callbackify(fsp.rmdir);\nexport const stat = callbackify(fsp.stat);\nexport const symlink = callbackify(fsp.symlink);\nexport const truncate = callbackify(fsp.truncate);\nexport const unlink = callbackify(fsp.unlink);\nexport const utimes = callbackify(fsp.utimes);\nexport const writeFile = callbackify(fsp.writeFile);\nexport const statfs = callbackify(fsp.statfs);\nexport const close = /* @__PURE__ */ notImplementedAsync(\"fs.close\");\nexport const createReadStream = /* @__PURE__ */ notImplementedAsync(\"fs.createReadStream\");\nexport const createWriteStream = /* @__PURE__ */ notImplementedAsync(\"fs.createWriteStream\");\nexport const exists = /* @__PURE__ */ notImplementedAsync(\"fs.exists\");\nexport const fchown = /* @__PURE__ */ notImplementedAsync(\"fs.fchown\");\nexport const fchmod = /* @__PURE__ */ notImplementedAsync(\"fs.fchmod\");\nexport const fdatasync = /* @__PURE__ */ notImplementedAsync(\"fs.fdatasync\");\nexport const fstat = /* @__PURE__ */ notImplementedAsync(\"fs.fstat\");\nexport const fsync = /* @__PURE__ */ notImplementedAsync(\"fs.fsync\");\nexport const ftruncate = /* @__PURE__ */ notImplementedAsync(\"fs.ftruncate\");\nexport const futimes = /* @__PURE__ */ notImplementedAsync(\"fs.futimes\");\nexport const lstatSync = /* @__PURE__ */ notImplementedAsync(\"fs.lstatSync\");\nexport const read = /* @__PURE__ */ notImplementedAsync(\"fs.read\");\nexport const readv = /* @__PURE__ */ notImplementedAsync(\"fs.readv\");\nexport const realpathSync = /* @__PURE__ */ notImplementedAsync(\"fs.realpathSync\");\nexport const statSync = /* @__PURE__ */ notImplementedAsync(\"fs.statSync\");\nexport const unwatchFile = /* @__PURE__ */ notImplementedAsync(\"fs.unwatchFile\");\nexport const watch = /* @__PURE__ */ notImplementedAsync(\"fs.watch\");\nexport const watchFile = /* @__PURE__ */ notImplementedAsync(\"fs.watchFile\");\nexport const write = /* @__PURE__ */ notImplementedAsync(\"fs.write\");\nexport const writev = /* @__PURE__ */ notImplementedAsync(\"fs.writev\");\nexport const _toUnixTimestamp = /* @__PURE__ */ notImplementedAsync(\"fs._toUnixTimestamp\");\nexport const openAsBlob = /* @__PURE__ */ notImplementedAsync(\"fs.openAsBlob\");\nexport const glob = /* @__PURE__ */ notImplementedAsync(\"fs.glob\");\nexport const appendFileSync = /* @__PURE__ */ notImplemented(\"fs.appendFileSync\");\nexport const accessSync = /* @__PURE__ */ notImplemented(\"fs.accessSync\");\nexport const chownSync = /* @__PURE__ */ notImplemented(\"fs.chownSync\");\nexport const chmodSync = /* @__PURE__ */ notImplemented(\"fs.chmodSync\");\nexport const closeSync = /* @__PURE__ */ notImplemented(\"fs.closeSync\");\nexport const copyFileSync = /* @__PURE__ */ notImplemented(\"fs.copyFileSync\");\nexport const cpSync = /* @__PURE__ */ notImplemented(\"fs.cpSync\");\nexport const existsSync = () => false;\nexport const fchownSync = /* @__PURE__ */ notImplemented(\"fs.fchownSync\");\nexport const fchmodSync = /* @__PURE__ */ notImplemented(\"fs.fchmodSync\");\nexport const fdatasyncSync = /* @__PURE__ */ notImplemented(\"fs.fdatasyncSync\");\nexport const fstatSync = /* @__PURE__ */ notImplemented(\"fs.fstatSync\");\nexport const fsyncSync = /* @__PURE__ */ notImplemented(\"fs.fsyncSync\");\nexport const ftruncateSync = /* @__PURE__ */ notImplemented(\"fs.ftruncateSync\");\nexport const futimesSync = /* @__PURE__ */ notImplemented(\"fs.futimesSync\");\nexport const lchownSync = /* @__PURE__ */ notImplemented(\"fs.lchownSync\");\nexport const lchmodSync = /* @__PURE__ */ notImplemented(\"fs.lchmodSync\");\nexport const linkSync = /* @__PURE__ */ notImplemented(\"fs.linkSync\");\nexport const lutimesSync = /* @__PURE__ */ notImplemented(\"fs.lutimesSync\");\nexport const mkdirSync = /* @__PURE__ */ notImplemented(\"fs.mkdirSync\");\nexport const mkdtempSync = /* @__PURE__ */ notImplemented(\"fs.mkdtempSync\");\nexport const openSync = /* @__PURE__ */ notImplemented(\"fs.openSync\");\nexport const opendirSync = /* @__PURE__ */ notImplemented(\"fs.opendirSync\");\nexport const readdirSync = /* @__PURE__ */ notImplemented(\"fs.readdirSync\");\nexport const readSync = /* @__PURE__ */ notImplemented(\"fs.readSync\");\nexport const readvSync = /* @__PURE__ */ notImplemented(\"fs.readvSync\");\nexport const readFileSync = /* @__PURE__ */ notImplemented(\"fs.readFileSync\");\nexport const readlinkSync = /* @__PURE__ */ notImplemented(\"fs.readlinkSync\");\nexport const renameSync = /* @__PURE__ */ notImplemented(\"fs.renameSync\");\nexport const rmSync = /* @__PURE__ */ notImplemented(\"fs.rmSync\");\nexport const rmdirSync = /* @__PURE__ */ notImplemented(\"fs.rmdirSync\");\nexport const symlinkSync = /* @__PURE__ */ notImplemented(\"fs.symlinkSync\");\nexport const truncateSync = /* @__PURE__ */ notImplemented(\"fs.truncateSync\");\nexport const unlinkSync = /* @__PURE__ */ notImplemented(\"fs.unlinkSync\");\nexport const utimesSync = /* @__PURE__ */ notImplemented(\"fs.utimesSync\");\nexport const writeFileSync = /* @__PURE__ */ notImplemented(\"fs.writeFileSync\");\nexport const writeSync = /* @__PURE__ */ notImplemented(\"fs.writeSync\");\nexport const writevSync = /* @__PURE__ */ notImplemented(\"fs.writevSync\");\nexport const statfsSync = /* @__PURE__ */ notImplemented(\"fs.statfsSync\");\nexport const globSync = /* @__PURE__ */ notImplemented(\"fs.globSync\");\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,cAAc;;;ACCd,IAAM,MAAsB,oBAAoB,QAAQ;AACxD,IAAM,SAAyB,oBAAoB,WAAW;AAC9D,IAAM,QAAwB,oBAAoB,UAAU;AAC5D,IAAM,aAA6B,oBAAoB,eAAe;AACtE,IAAM,cAA8B,oBAAoB,gBAAgB;AACxE,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;;;ACL/B,SAAS,YAAY,IAAI;AACxB,QAAM,MAAM,YAAY,MAAM;AAC7B,UAAM,KAAK,KAAK,IAAI;AACpB,OAAG,EAAE,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,KAAK,CAAC,QAAQ,GAAG,QAAW,GAAG,CAAC;AAAA,EAClE;AACA,MAAI,gBAAgB;AACpB,MAAI,SAAS;AACb,SAAO;AACR;AACO,IAAMA,UAAS,YAAgB,MAAM;AACrC,IAAMC,cAAa,YAAgB,UAAU;AAC7C,IAAMC,SAAQ,YAAgB,KAAK;AACnC,IAAMC,SAAQ,YAAgB,KAAK;AACnC,IAAMC,YAAW,YAAgB,QAAQ;AACzC,IAAMC,MAAK,YAAgB,EAAE;AAC7B,IAAMC,UAAS,YAAgB,MAAM;AACrC,IAAMC,UAAS,YAAgB,MAAM;AACrC,IAAMC,QAAO,YAAgB,IAAI;AACjC,IAAMC,SAAQ,YAAgB,KAAK;AACnC,IAAMC,WAAU,YAAgB,OAAO;AACvC,IAAMC,SAAQ,YAAgB,KAAK;AACnC,IAAMC,WAAU,YAAgB,OAAO;AACvC,IAAMC,YAAW,YAAgB,QAAQ;AACzC,IAAMC,QAAO,YAAgB,IAAI;AACjC,IAAMC,WAAU,YAAgB,OAAO;AACvC,IAAMC,WAAU,YAAgB,OAAO;AACvC,IAAMC,YAAW,YAAgB,QAAQ;AACzC,IAAMC,YAAW,YAAgB,QAAQ;AACzC,IAAMC,UAAS,YAAgB,MAAM;AACrC,IAAMC,MAAK,YAAgB,EAAE;AAC7B,IAAMC,SAAQ,YAAgB,KAAK;AACnC,IAAMC,QAAO,YAAgB,IAAI;AACjC,IAAMC,WAAU,YAAgB,OAAO;AACvC,IAAMC,YAAW,YAAgB,QAAQ;AACzC,IAAMC,UAAS,YAAgB,MAAM;AACrC,IAAMC,UAAS,YAAgB,MAAM;AACrC,IAAMC,aAAY,YAAgB,SAAS;AAC3C,IAAMC,UAAS,YAAgB,MAAM;AACrC,IAAM,QAAwB,oBAAoB,UAAU;AAC5D,IAAM,mBAAmC,oBAAoB,qBAAqB;AAClF,IAAM,oBAAoC,oBAAoB,sBAAsB;AACpF,IAAM,SAAyB,oBAAoB,WAAW;AAC9D,IAAM,SAAyB,oBAAoB,WAAW;AAC9D,IAAM,SAAyB,oBAAoB,WAAW;AAC9D,IAAM,YAA4B,oBAAoB,cAAc;AACpE,IAAM,QAAwB,oBAAoB,UAAU;AAC5D,IAAM,QAAwB,oBAAoB,UAAU;AAC5D,IAAM,YAA4B,oBAAoB,cAAc;AACpE,IAAM,UAA0B,oBAAoB,YAAY;AAChE,IAAM,YAA4B,oBAAoB,cAAc;AACpE,IAAM,OAAuB,oBAAoB,SAAS;AAC1D,IAAM,QAAwB,oBAAoB,UAAU;AAC5D,IAAM,eAA+B,oBAAoB,iBAAiB;AAC1E,IAAM,WAA2B,oBAAoB,aAAa;AAClE,IAAM,cAA8B,oBAAoB,gBAAgB;AACxE,IAAM,QAAwB,oBAAoB,UAAU;AAC5D,IAAM,YAA4B,oBAAoB,cAAc;AACpE,IAAM,QAAwB,oBAAoB,UAAU;AAC5D,IAAM,SAAyB,oBAAoB,WAAW;AAC9D,IAAM,mBAAmC,oBAAoB,qBAAqB;AAClF,IAAM,aAA6B,oBAAoB,eAAe;AACtE,IAAM,OAAuB,oBAAoB,SAAS;AAC1D,IAAM,iBAAiC,eAAe,mBAAmB;AACzE,IAAM,aAA6B,eAAe,eAAe;AACjE,IAAM,YAA4B,eAAe,cAAc;AAC/D,IAAM,YAA4B,eAAe,cAAc;AAC/D,IAAM,YAA4B,eAAe,cAAc;AAC/D,IAAM,eAA+B,eAAe,iBAAiB;AACrE,IAAM,SAAyB,eAAe,WAAW;AACzD,IAAM,aAAa,MAAM;AACzB,IAAM,aAA6B,eAAe,eAAe;AACjE,IAAM,aAA6B,eAAe,eAAe;AACjE,IAAM,gBAAgC,eAAe,kBAAkB;AACvE,IAAM,YAA4B,eAAe,cAAc;AAC/D,IAAM,YAA4B,eAAe,cAAc;AAC/D,IAAM,gBAAgC,eAAe,kBAAkB;AACvE,IAAM,cAA8B,eAAe,gBAAgB;AACnE,IAAM,aAA6B,eAAe,eAAe;AACjE,IAAM,aAA6B,eAAe,eAAe;AACjE,IAAM,WAA2B,eAAe,aAAa;AAC7D,IAAM,cAA8B,eAAe,gBAAgB;AACnE,IAAM,YAA4B,eAAe,cAAc;AAC/D,IAAM,cAA8B,eAAe,gBAAgB;AACnE,IAAM,WAA2B,eAAe,aAAa;AAC7D,IAAM,cAA8B,eAAe,gBAAgB;AACnE,IAAM,cAA8B,eAAe,gBAAgB;AACnE,IAAM,WAA2B,eAAe,aAAa;AAC7D,IAAM,YAA4B,eAAe,cAAc;AAC/D,IAAM,eAA+B,eAAe,iBAAiB;AACrE,IAAM,eAA+B,eAAe,iBAAiB;AACrE,IAAM,aAA6B,eAAe,eAAe;AACjE,IAAM,SAAyB,eAAe,WAAW;AACzD,IAAM,YAA4B,eAAe,cAAc;AAC/D,IAAM,cAA8B,eAAe,gBAAgB;AACnE,IAAM,eAA+B,eAAe,iBAAiB;AACrE,IAAM,aAA6B,eAAe,eAAe;AACjE,IAAM,aAA6B,eAAe,eAAe;AACjE,IAAM,gBAAgC,eAAe,kBAAkB;AACvE,IAAM,YAA4B,eAAe,cAAc;AAC/D,IAAM,aAA6B,eAAe,eAAe;AACjE,IAAM,aAA6B,eAAe,eAAe;AACjE,IAAM,WAA2B,eAAe,aAAa;;;AF9FpE,IAAO,aAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA,YAAAC;AAAA,EACA;AAAA,EACA,OAAAC;AAAA,EACA;AAAA,EACA,OAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAAC;AAAA,EACA;AAAA,EACA,IAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA,MAAAC;AAAA,EACA;AAAA,EACA,OAAAC;AAAA,EACA;AAAA,EACA,SAAAC;AAAA,EACA;AAAA,EACA,OAAAC;AAAA,EACA;AAAA,EACA,SAAAC;AAAA,EACA;AAAA,EACA,MAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAAC;AAAA,EACA;AAAA,EACA,UAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAAC;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA,IAAAC;AAAA,EACA;AAAA,EACA,OAAAC;AAAA,EACA;AAAA,EACA,MAAAC;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA,SAAAC;AAAA,EACA;AAAA,EACA,UAAAC;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": ["access", "appendFile", "chown", "chmod", "copyFile", "cp", "lchown", "lchmod", "link", "lstat", "lutimes", "mkdir", "mkdtemp", "realpath", "open", "opendir", "readdir", "readFile", "readlink", "rename", "rm", "rmdir", "stat", "symlink", "truncate", "unlink", "utimes", "writeFile", "statfs", "access", "appendFile", "chmod", "chown", "copyFile", "cp", "lchmod", "lchown", "link", "lstat", "lutimes", "mkdir", "mkdtemp", "open", "opendir", "readFile", "readdir", "readlink", "realpath", "rename", "rm", "rmdir", "stat", "statfs", "symlink", "truncate", "unlink", "utimes", "writeFile"]}