{"expo": {"name": "Mientior - <PERSON><PERSON><PERSON>", "slug": "mientior-livraison-afrique", "version": "1.0.0", "main": "index.js", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "scheme": "mientior", "description": "Plateforme de livraison connectant clients, marchands et livreurs à travers l'Afrique", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#0DCAA8"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.mientior.livraison", "buildNumber": "1", "config": {"googleMapsApiKey": "AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#0DCAA8"}, "edgeToEdgeEnabled": true, "package": "com.mientior.livraison", "versionCode": 1, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "NOTIFICATIONS"], "config": {"googleMaps": {"apiKey": "AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s"}}}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-location", "expo-camera", "expo-notifications", ["react-native-maps", {"googleMapsApiKey": "AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s"}], ["expo-build-properties", {"android": {"enableProguardInReleaseBuilds": true, "enableShrinkResourcesInReleaseBuilds": true}}]], "extra": {"eas": {"projectId": "mientior-livraison-afrique"}}}}