{"version": 3, "sources": ["../../unenv/dist/runtime/node/os.mjs"], "sourcesContent": ["import { notImplemented } from \"../_internal/utils.mjs\";\nimport { UV_UDP_REUSEADDR, dlopen, errno, signals, priority } from \"./internal/os/constants.mjs\";\nexport const constants = {\n\tUV_UDP_REUSEADDR,\n\tdlopen,\n\terrno,\n\tsignals,\n\tpriority\n};\nconst NUM_CPUS = 8;\nexport const availableParallelism = () => NUM_CPUS;\nexport const arch = () => \"\";\nexport const machine = () => \"\";\nexport const endianness = () => \"LE\";\nexport const cpus = () => {\n\tconst info = {\n\t\tmodel: \"\",\n\t\tspeed: 0,\n\t\ttimes: {\n\t\t\tuser: 0,\n\t\t\tnice: 0,\n\t\t\tsys: 0,\n\t\t\tidle: 0,\n\t\t\tirq: 0\n\t\t}\n\t};\n\treturn Array.from({ length: NUM_CPUS }, () => info);\n};\nexport const getPriority = () => 0;\nexport const setPriority = /* @__PURE__ */ notImplemented(\"os.setPriority\");\nexport const homedir = () => \"/\";\nexport const tmpdir = () => \"/tmp\";\nexport const devNull = \"/dev/null\";\nexport const freemem = () => 0;\nexport const totalmem = () => 0;\nexport const loadavg = () => [\n\t0,\n\t0,\n\t0\n];\nexport const uptime = () => 0;\nexport const hostname = () => \"\";\nexport const networkInterfaces = () => {\n\treturn { lo0: [\n\t\t{\n\t\t\taddress: \"127.0.0.1\",\n\t\t\tnetmask: \"*********\",\n\t\t\tfamily: \"IPv4\",\n\t\t\tmac: \"00:00:00:00:00:00\",\n\t\t\tinternal: true,\n\t\t\tcidr: \"127.0.0.1/8\"\n\t\t},\n\t\t{\n\t\t\taddress: \"::1\",\n\t\t\tnetmask: \"ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff\",\n\t\t\tfamily: \"IPv6\",\n\t\t\tmac: \"00:00:00:00:00:00\",\n\t\t\tinternal: true,\n\t\t\tcidr: \"::1/128\",\n\t\t\tscopeid: 0\n\t\t},\n\t\t{\n\t\t\taddress: \"fe80::1\",\n\t\t\tnetmask: \"ffff:ffff:ffff:ffff::\",\n\t\t\tfamily: \"IPv6\",\n\t\t\tmac: \"00:00:00:00:00:00\",\n\t\t\tinternal: true,\n\t\t\tcidr: \"fe80::1/64\",\n\t\t\tscopeid: 1\n\t\t}\n\t] };\n};\nexport const platform = () => \"linux\";\nexport const type = () => \"Linux\";\nexport const release = () => \"\";\nexport const version = () => \"\";\nexport const userInfo = (opts) => {\n\tconst encode = (str) => {\n\t\tif (opts?.encoding) {\n\t\t\tconst buff = Buffer.from(str);\n\t\t\treturn opts.encoding === \"buffer\" ? buff : buff.toString(opts.encoding);\n\t\t}\n\t\treturn str;\n\t};\n\treturn {\n\t\tgid: 1e3,\n\t\tuid: 1e3,\n\t\thomedir: encode(\"/\"),\n\t\tshell: encode(\"/bin/sh\"),\n\t\tusername: encode(\"root\")\n\t};\n};\nexport const EOL = \"\\n\";\nexport default {\n\tarch,\n\tavailableParallelism,\n\tconstants,\n\tcpus,\n\tEOL,\n\tendianness,\n\tdevNull,\n\tfreemem,\n\tgetPriority,\n\thomedir,\n\thostname,\n\tloadavg,\n\tmachine,\n\tnetworkInterfaces,\n\tplatform,\n\trelease,\n\tsetPriority,\n\ttmpdir,\n\ttotalmem,\n\ttype,\n\tuptime,\n\tuserInfo,\n\tversion\n};\n"], "mappings": ";;;;;;;;;;;;;AAEO,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AACA,IAAM,WAAW;AACV,IAAM,uBAAuB,MAAM;AACnC,IAAM,OAAO,MAAM;AACnB,IAAM,UAAU,MAAM;AACtB,IAAM,aAAa,MAAM;AACzB,IAAM,OAAO,MAAM;AACzB,QAAM,OAAO;AAAA,IACZ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,IACN;AAAA,EACD;AACA,SAAO,MAAM,KAAK,EAAE,QAAQ,SAAS,GAAG,MAAM,IAAI;AACnD;AACO,IAAM,cAAc,MAAM;AAC1B,IAAM,cAA8B,eAAe,gBAAgB;AACnE,IAAM,UAAU,MAAM;AACtB,IAAM,SAAS,MAAM;AACrB,IAAM,UAAU;AAChB,IAAM,UAAU,MAAM;AACtB,IAAM,WAAW,MAAM;AACvB,IAAM,UAAU,MAAM;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AACD;AACO,IAAM,SAAS,MAAM;AACrB,IAAM,WAAW,MAAM;AACvB,IAAM,oBAAoB,MAAM;AACtC,SAAO,EAAE,KAAK;AAAA,IACb;AAAA,MACC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,IACP;AAAA,IACA;AAAA,MACC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACA;AAAA,MACC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACD,EAAE;AACH;AACO,IAAM,WAAW,MAAM;AACvB,IAAM,OAAO,MAAM;AACnB,IAAM,UAAU,MAAM;AACtB,IAAM,UAAU,MAAM;AACtB,IAAM,WAAW,CAAC,SAAS;AACjC,QAAM,SAAS,CAAC,QAAQ;AACvB,QAAI,MAAM,UAAU;AACnB,YAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,aAAO,KAAK,aAAa,WAAW,OAAO,KAAK,SAAS,KAAK,QAAQ;AAAA,IACvE;AACA,WAAO;AAAA,EACR;AACA,SAAO;AAAA,IACN,KAAK;AAAA,IACL,KAAK;AAAA,IACL,SAAS,OAAO,GAAG;AAAA,IACnB,OAAO,OAAO,SAAS;AAAA,IACvB,UAAU,OAAO,MAAM;AAAA,EACxB;AACD;AACO,IAAM,MAAM;AACnB,IAAO,aAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}