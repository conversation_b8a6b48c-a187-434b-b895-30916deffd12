import {
  access,
  appendFile,
  chmod,
  chown,
  copyFile,
  cp,
  lchmod,
  lchown,
  link,
  lstat,
  lutimes,
  mkdir,
  mkdtemp,
  open,
  opendir,
  readFile,
  readdir,
  readlink,
  realpath,
  rename,
  rm,
  rmdir,
  stat,
  statfs,
  symlink,
  truncate,
  unlink,
  utimes,
  writeFile
} from "./chunk-AIZST77Q.js";
import {
  F_OK,
  R_OK,
  W_OK,
  X_OK,
  constants_exports
} from "./chunk-6KHCSJPG.js";
import {
  notImplemented,
  notImplementedAsync,
  notImplementedClass
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/fs.mjs
import promises from "node:fs/promises";

// node_modules/unenv/dist/runtime/node/internal/fs/classes.mjs
var Dir = notImplementedClass("fs.Dir");
var Dirent = notImplementedClass("fs.Dirent");
var Stats = notImplementedClass("fs.Stats");
var ReadStream = notImplementedClass("fs.ReadStream");
var WriteStream = notImplementedClass("fs.WriteStream");
var FileReadStream = ReadStream;
var FileWriteStream = WriteStream;

// node_modules/unenv/dist/runtime/node/internal/fs/fs.mjs
function callbackify(fn) {
  const fnc = function(...args) {
    const cb = args.pop();
    fn().catch((error) => cb(error)).then((val) => cb(void 0, val));
  };
  fnc.__promisify__ = fn;
  fnc.native = fnc;
  return fnc;
}
var access2 = callbackify(access);
var appendFile2 = callbackify(appendFile);
var chown2 = callbackify(chown);
var chmod2 = callbackify(chmod);
var copyFile2 = callbackify(copyFile);
var cp2 = callbackify(cp);
var lchown2 = callbackify(lchown);
var lchmod2 = callbackify(lchmod);
var link2 = callbackify(link);
var lstat2 = callbackify(lstat);
var lutimes2 = callbackify(lutimes);
var mkdir2 = callbackify(mkdir);
var mkdtemp2 = callbackify(mkdtemp);
var realpath2 = callbackify(realpath);
var open2 = callbackify(open);
var opendir2 = callbackify(opendir);
var readdir2 = callbackify(readdir);
var readFile2 = callbackify(readFile);
var readlink2 = callbackify(readlink);
var rename2 = callbackify(rename);
var rm2 = callbackify(rm);
var rmdir2 = callbackify(rmdir);
var stat2 = callbackify(stat);
var symlink2 = callbackify(symlink);
var truncate2 = callbackify(truncate);
var unlink2 = callbackify(unlink);
var utimes2 = callbackify(utimes);
var writeFile2 = callbackify(writeFile);
var statfs2 = callbackify(statfs);
var close = notImplementedAsync("fs.close");
var createReadStream = notImplementedAsync("fs.createReadStream");
var createWriteStream = notImplementedAsync("fs.createWriteStream");
var exists = notImplementedAsync("fs.exists");
var fchown = notImplementedAsync("fs.fchown");
var fchmod = notImplementedAsync("fs.fchmod");
var fdatasync = notImplementedAsync("fs.fdatasync");
var fstat = notImplementedAsync("fs.fstat");
var fsync = notImplementedAsync("fs.fsync");
var ftruncate = notImplementedAsync("fs.ftruncate");
var futimes = notImplementedAsync("fs.futimes");
var lstatSync = notImplementedAsync("fs.lstatSync");
var read = notImplementedAsync("fs.read");
var readv = notImplementedAsync("fs.readv");
var realpathSync = notImplementedAsync("fs.realpathSync");
var statSync = notImplementedAsync("fs.statSync");
var unwatchFile = notImplementedAsync("fs.unwatchFile");
var watch = notImplementedAsync("fs.watch");
var watchFile = notImplementedAsync("fs.watchFile");
var write = notImplementedAsync("fs.write");
var writev = notImplementedAsync("fs.writev");
var _toUnixTimestamp = notImplementedAsync("fs._toUnixTimestamp");
var openAsBlob = notImplementedAsync("fs.openAsBlob");
var glob = notImplementedAsync("fs.glob");
var appendFileSync = notImplemented("fs.appendFileSync");
var accessSync = notImplemented("fs.accessSync");
var chownSync = notImplemented("fs.chownSync");
var chmodSync = notImplemented("fs.chmodSync");
var closeSync = notImplemented("fs.closeSync");
var copyFileSync = notImplemented("fs.copyFileSync");
var cpSync = notImplemented("fs.cpSync");
var existsSync = () => false;
var fchownSync = notImplemented("fs.fchownSync");
var fchmodSync = notImplemented("fs.fchmodSync");
var fdatasyncSync = notImplemented("fs.fdatasyncSync");
var fstatSync = notImplemented("fs.fstatSync");
var fsyncSync = notImplemented("fs.fsyncSync");
var ftruncateSync = notImplemented("fs.ftruncateSync");
var futimesSync = notImplemented("fs.futimesSync");
var lchownSync = notImplemented("fs.lchownSync");
var lchmodSync = notImplemented("fs.lchmodSync");
var linkSync = notImplemented("fs.linkSync");
var lutimesSync = notImplemented("fs.lutimesSync");
var mkdirSync = notImplemented("fs.mkdirSync");
var mkdtempSync = notImplemented("fs.mkdtempSync");
var openSync = notImplemented("fs.openSync");
var opendirSync = notImplemented("fs.opendirSync");
var readdirSync = notImplemented("fs.readdirSync");
var readSync = notImplemented("fs.readSync");
var readvSync = notImplemented("fs.readvSync");
var readFileSync = notImplemented("fs.readFileSync");
var readlinkSync = notImplemented("fs.readlinkSync");
var renameSync = notImplemented("fs.renameSync");
var rmSync = notImplemented("fs.rmSync");
var rmdirSync = notImplemented("fs.rmdirSync");
var symlinkSync = notImplemented("fs.symlinkSync");
var truncateSync = notImplemented("fs.truncateSync");
var unlinkSync = notImplemented("fs.unlinkSync");
var utimesSync = notImplemented("fs.utimesSync");
var writeFileSync = notImplemented("fs.writeFileSync");
var writeSync = notImplemented("fs.writeSync");
var writevSync = notImplemented("fs.writevSync");
var statfsSync = notImplemented("fs.statfsSync");
var globSync = notImplemented("fs.globSync");

// node_modules/unenv/dist/runtime/node/fs.mjs
var fs_default = {
  F_OK,
  R_OK,
  W_OK,
  X_OK,
  constants: constants_exports,
  promises,
  Dir,
  Dirent,
  FileReadStream,
  FileWriteStream,
  ReadStream,
  Stats,
  WriteStream,
  _toUnixTimestamp,
  access: access2,
  accessSync,
  appendFile: appendFile2,
  appendFileSync,
  chmod: chmod2,
  chmodSync,
  chown: chown2,
  chownSync,
  close,
  closeSync,
  copyFile: copyFile2,
  copyFileSync,
  cp: cp2,
  cpSync,
  createReadStream,
  createWriteStream,
  exists,
  existsSync,
  fchmod,
  fchmodSync,
  fchown,
  fchownSync,
  fdatasync,
  fdatasyncSync,
  fstat,
  fstatSync,
  fsync,
  fsyncSync,
  ftruncate,
  ftruncateSync,
  futimes,
  futimesSync,
  glob,
  lchmod: lchmod2,
  globSync,
  lchmodSync,
  lchown: lchown2,
  lchownSync,
  link: link2,
  linkSync,
  lstat: lstat2,
  lstatSync,
  lutimes: lutimes2,
  lutimesSync,
  mkdir: mkdir2,
  mkdirSync,
  mkdtemp: mkdtemp2,
  mkdtempSync,
  open: open2,
  openAsBlob,
  openSync,
  opendir: opendir2,
  opendirSync,
  read,
  readFile: readFile2,
  readFileSync,
  readSync,
  readdir: readdir2,
  readdirSync,
  readlink: readlink2,
  readlinkSync,
  readv,
  readvSync,
  realpath: realpath2,
  realpathSync,
  rename: rename2,
  renameSync,
  rm: rm2,
  rmSync,
  rmdir: rmdir2,
  rmdirSync,
  stat: stat2,
  statSync,
  statfs: statfs2,
  statfsSync,
  symlink: symlink2,
  symlinkSync,
  truncate: truncate2,
  truncateSync,
  unlink: unlink2,
  unlinkSync,
  unwatchFile,
  utimes: utimes2,
  utimesSync,
  watch,
  watchFile,
  write,
  writeFile: writeFile2,
  writeFileSync,
  writeSync,
  writev,
  writevSync
};
export {
  Dir,
  Dirent,
  F_OK,
  FileReadStream,
  FileWriteStream,
  R_OK,
  ReadStream,
  Stats,
  W_OK,
  WriteStream,
  X_OK,
  _toUnixTimestamp,
  access2 as access,
  accessSync,
  appendFile2 as appendFile,
  appendFileSync,
  chmod2 as chmod,
  chmodSync,
  chown2 as chown,
  chownSync,
  close,
  closeSync,
  constants_exports as constants,
  copyFile2 as copyFile,
  copyFileSync,
  cp2 as cp,
  cpSync,
  createReadStream,
  createWriteStream,
  fs_default as default,
  exists,
  existsSync,
  fchmod,
  fchmodSync,
  fchown,
  fchownSync,
  fdatasync,
  fdatasyncSync,
  fstat,
  fstatSync,
  fsync,
  fsyncSync,
  ftruncate,
  ftruncateSync,
  futimes,
  futimesSync,
  glob,
  globSync,
  lchmod2 as lchmod,
  lchmodSync,
  lchown2 as lchown,
  lchownSync,
  link2 as link,
  linkSync,
  lstat2 as lstat,
  lstatSync,
  lutimes2 as lutimes,
  lutimesSync,
  mkdir2 as mkdir,
  mkdirSync,
  mkdtemp2 as mkdtemp,
  mkdtempSync,
  open2 as open,
  openAsBlob,
  openSync,
  opendir2 as opendir,
  opendirSync,
  promises,
  read,
  readFile2 as readFile,
  readFileSync,
  readSync,
  readdir2 as readdir,
  readdirSync,
  readlink2 as readlink,
  readlinkSync,
  readv,
  readvSync,
  realpath2 as realpath,
  realpathSync,
  rename2 as rename,
  renameSync,
  rm2 as rm,
  rmSync,
  rmdir2 as rmdir,
  rmdirSync,
  stat2 as stat,
  statSync,
  statfs2 as statfs,
  statfsSync,
  symlink2 as symlink,
  symlinkSync,
  truncate2 as truncate,
  truncateSync,
  unlink2 as unlink,
  unlinkSync,
  unwatchFile,
  utimes2 as utimes,
  utimesSync,
  watch,
  watchFile,
  write,
  writeFile2 as writeFile,
  writeFileSync,
  writeSync,
  writev,
  writevSync
};
//# sourceMappingURL=unenv_node_fs.js.map
