{"version": 3, "names": ["React", "NativeModules", "RNMBXVectorSourceNativeComponent", "cloneReactChildrenWithProps", "isFunction", "copyPropertiesAsDeprecated", "AbstractSource", "jsx", "_jsx", "MapboxGL", "RNMBXModule", "NATIVE_MODULE_NAME", "VectorSource", "defaultProps", "id", "StyleSource", "DefaultSourceID", "constructor", "props", "_decodePayload", "payload", "JSON", "parse", "onPress", "event", "nativeEvent", "features", "coordinates", "point", "newEvent", "key", "console", "warn", "origNativeEvent", "render", "existing", "url", "tileUrlTemplates", "minZoomLevel", "maxZoomLevel", "tms", "attribution", "hitbox", "hasPressListener", "onMapboxVectorSourcePress", "bind", "undefined", "ref", "setNativeRef", "children", "sourceID"], "sourceRoot": "../../../src", "sources": ["components/VectorSource.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAA8B,cAAc;AAElE,OAAOC,gCAAgC,MAAM,2CAA2C;AACxF,SAASC,2BAA2B,EAAEC,UAAU,QAAQ,UAAU;AAClE,SAASC,0BAA0B,QAAQ,sBAAsB;AAGjE,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE9C,MAAMC,QAAQ,GAAGR,aAAa,CAACS,WAAW;AAE1C,OAAO,MAAMC,kBAAkB,GAAG,mBAAmB;;AA6ErD;;AAC0B;AAC1B;AACA;AACA;AACA;AACA,MAAMC,YAAY,SAASN,cAAc,CAAqB;EAC5D,OAAOO,YAAY,GAAG;IACpBC,EAAE,EAAEL,QAAQ,CAACM,WAAW,CAACC;EAC3B,CAAC;EAEDC,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;EACd;EAEAC,cAAcA,CAACC,OAA8B,EAAgB;IAC3D;IACA;IACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,OAAOC,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;IAC5B,CAAC,MAAM;MACL,OAAOA,OAAO;IAChB;EACF;EAEAG,OAAOA,CACLC,KAEE,EACF;IACA,MAAMJ,OAAO,GAAG,IAAI,CAACD,cAAc,CAACK,KAAK,CAACC,WAAW,CAACL,OAAO,CAAC;IAC9D,MAAM;MAAEM,QAAQ;MAAEC,WAAW;MAAEC;IAAM,CAAC,GAAGR,OAAO;IAChD,IAAIS,QAAQ,GAAG;MACbH,QAAQ;MACRC,WAAW;MACXC;IACF,CAAC;IACDC,QAAQ,GAAGxB,0BAA0B,CACnCmB,KAAK,EACLK,QAAQ,EACPC,GAAG,IAAK;MACPC,OAAO,CAACC,IAAI,CACV,SAASF,GAAG,mEACd,CAAC;IACH,CAAC,EACD;MACEL,WAAW,EAAGQ,eAAwB,KAAM;QAC1C,GAAIA,eAAgC;QACpCb,OAAO,EAAEM,QAAQ,CAAC,CAAC;MACrB,CAAC;IACH,CACF,CAAC;IACD,MAAM;MAAEH;IAAQ,CAAC,GAAG,IAAI,CAACL,KAAK;IAC9B,IAAIK,OAAO,EAAE;MACXA,OAAO,CAACM,QAAQ,CAAC;IACnB;EACF;EAEAK,MAAMA,CAAA,EAAG;IACP,MAAMhB,KAAK,GAAG;MACZJ,EAAE,EAAE,IAAI,CAACI,KAAK,CAACJ,EAAE;MACjBqB,QAAQ,EAAE,IAAI,CAACjB,KAAK,CAACiB,QAAQ;MAC7BC,GAAG,EAAE,IAAI,CAAClB,KAAK,CAACkB,GAAG;MACnBC,gBAAgB,EAAE,IAAI,CAACnB,KAAK,CAACmB,gBAAgB;MAC7CC,YAAY,EAAE,IAAI,CAACpB,KAAK,CAACoB,YAAY;MACrCC,YAAY,EAAE,IAAI,CAACrB,KAAK,CAACqB,YAAY;MACrCC,GAAG,EAAE,IAAI,CAACtB,KAAK,CAACsB,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACvB,KAAK,CAACuB,WAAW;MACnCC,MAAM,EAAE,IAAI,CAACxB,KAAK,CAACwB,MAAM;MACzBC,gBAAgB,EAAEvC,UAAU,CAAC,IAAI,CAACc,KAAK,CAACK,OAAO,CAAC;MAChDqB,yBAAyB,EAAE,IAAI,CAACrB,OAAO,CAACsB,IAAI,CAAC,IAAI,CAAC;MAClDtB,OAAO,EAAEuB;IACX,CAAC;IACD;MAAA;MACE;MACAtC,IAAA,CAACN,gCAAgC;QAAC6C,GAAG,EAAE,IAAI,CAACC,YAAa;QAAA,GAAK9B,KAAK;QAAA+B,QAAA,EAChE9C,2BAA2B,CAAC,IAAI,CAACe,KAAK,CAAC+B,QAAQ,EAAE;UAChDC,QAAQ,EAAE,IAAI,CAAChC,KAAK,CAACJ;QACvB,CAAC;MAAC,CAC8B;IAAC;EAEvC;AACF;AAEA,eAAeF,YAAY", "ignoreList": []}