import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import MapView, { PROVIDER_DEFAULT } from 'react-native-maps';

const { width, height } = Dimensions.get('window');

const SimpleMapTest: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Test de la carte</Text>
      <MapView
        provider={PROVIDER_DEFAULT}
        style={styles.map}
        initialRegion={{
          latitude: 5.348,
          longitude: -4.007,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        }}
        onMapReady={() => console.log('Carte prête!')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    paddingVertical: 20,
    backgroundColor: '#0DCAA8',
    color: '#fff',
  },
  map: {
    width: width,
    height: height - 100,
  },
});

export default SimpleMapTest;
