// Script pour peupler la base de données Supabase avec des données de test
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration Supabase
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Clé service pour les opérations admin
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Données de test pour les restaurants africains
const restaurantsData = [
  {
    business_name: 'Restaurant Africain Délice',
    business_type: 'restaurant',
    description: {
      fr: 'Cuisine africaine authentique avec des plats traditionnels du Bénin et de l\'Afrique de l\'Ouest',
      en: 'Authentic African cuisine with traditional dishes from Benin and West Africa'
    },
    address: 'Quartier Ganhi, Cotonou, Bénin',
    coordinates: `POINT(2.3912 6.3702)`, // PostGIS format
    phone: '+229 97 00 00 01',
    email: '<EMAIL>',
    is_verified: true,
    is_open: true,
    opening_hours: {
      lundi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
      mardi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
      mercredi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
      jeudi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
      vendredi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
      samedi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
      dimanche: { ouvert: true, heures: [{ ouverture: '10:00', fermeture: '20:00' }] }
    },
    average_preparation_time: 25,
    minimum_order_amount: 1500,
    base_delivery_fee: 500,
    delivery_radius_km: 8,
    rating: 4.5,
    total_orders: 127
  },
  {
    business_name: 'Épicerie du Marché',
    business_type: 'epicerie',
    description: {
      fr: 'Produits frais et locaux, épices africaines, légumes du terroir béninois',
      en: 'Fresh and local products, African spices, local Beninese vegetables'
    },
    address: 'Marché de Dantokpa, Cotonou, Bénin',
    coordinates: `POINT(2.4183 6.3654)`,
    phone: '+229 97 00 00 02',
    email: '<EMAIL>',
    is_verified: true,
    is_open: true,
    opening_hours: {
      lundi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
      mardi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
      mercredi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
      jeudi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
      vendredi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
      samedi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
      dimanche: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '18:00' }] }
    },
    average_preparation_time: 15,
    minimum_order_amount: 1000,
    base_delivery_fee: 300,
    delivery_radius_km: 5,
    rating: 4.2,
    total_orders: 89
  },
  {
    business_name: 'Pharmacie Santé Plus',
    business_type: 'pharmacie',
    description: {
      fr: 'Pharmacie moderne avec médicaments et produits de santé, service 24h/24',
      en: 'Modern pharmacy with medicines and health products, 24/7 service'
    },
    address: 'Avenue Steinmetz, Cotonou, Bénin',
    coordinates: `POINT(2.4019 6.3611)`,
    phone: '+229 97 00 00 03',
    email: '<EMAIL>',
    is_verified: true,
    is_open: true,
    opening_hours: {
      lundi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
      mardi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
      mercredi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
      jeudi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
      vendredi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
      samedi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
      dimanche: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '19:00' }] }
    },
    average_preparation_time: 10,
    minimum_order_amount: 500,
    base_delivery_fee: 400,
    delivery_radius_km: 10,
    rating: 4.7,
    total_orders: 156
  },
  {
    business_name: 'Supermarché Moderne',
    business_type: 'supermarche',
    description: {
      fr: 'Grand supermarché avec tous les produits du quotidien, produits importés et locaux',
      en: 'Large supermarket with all daily products, imported and local products'
    },
    address: 'Boulevard de la Marina, Cotonou, Bénin',
    coordinates: `POINT(2.4147 6.3573)`,
    phone: '+229 97 00 00 04',
    email: '<EMAIL>',
    is_verified: true,
    is_open: true,
    opening_hours: {
      lundi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
      mardi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
      mercredi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
      jeudi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
      vendredi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
      samedi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
      dimanche: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '20:00' }] }
    },
    average_preparation_time: 20,
    minimum_order_amount: 2000,
    base_delivery_fee: 600,
    delivery_radius_km: 12,
    rating: 4.3,
    total_orders: 203
  }
];

// Données de test pour les catégories
const categoriesData = [
  { name: { fr: 'Plats principaux', en: 'Main dishes' }, description: 'Plats traditionnels africains' },
  { name: { fr: 'Entrées', en: 'Appetizers' }, description: 'Entrées et amuse-bouches' },
  { name: { fr: 'Desserts', en: 'Desserts' }, description: 'Desserts et sucreries' },
  { name: { fr: 'Boissons', en: 'Beverages' }, description: 'Boissons chaudes et froides' },
  { name: { fr: 'Épices', en: 'Spices' }, description: 'Épices et condiments africains' },
  { name: { fr: 'Légumes', en: 'Vegetables' }, description: 'Légumes frais du marché' },
  { name: { fr: 'Médicaments', en: 'Medicines' }, description: 'Médicaments et produits de santé' },
  { name: { fr: 'Produits ménagers', en: 'Household products' }, description: 'Produits d\'entretien et ménagers' }
];

// Fonction principale pour peupler la base de données
async function seedDatabase() {
  try {
    console.log('🌱 Début du peuplement de la base de données Supabase...');

    // 1. Créer des utilisateurs de test
    console.log('👥 Création des utilisateurs de test...');
    const users = [];
    for (let i = 0; i < restaurantsData.length; i++) {
      const { data: user, error } = await supabase.auth.admin.createUser({
        email: restaurantsData[i].email,
        password: 'TestPassword123!',
        email_confirm: true,
        user_metadata: {
          full_name: restaurantsData[i].business_name,
          role: 'merchant'
        }
      });

      if (error) {
        console.error(`❌ Erreur création utilisateur ${restaurantsData[i].email}:`, error);
      } else {
        console.log(`✅ Utilisateur créé: ${user.user.email}`);
        users.push(user.user);
      }
    }

    // 2. Créer les catégories
    console.log('📂 Création des catégories...');
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .insert(categoriesData)
      .select();

    if (categoriesError) {
      console.error('❌ Erreur création catégories:', categoriesError);
    } else {
      console.log(`✅ ${categories.length} catégories créées`);
    }

    // 3. Créer les profils de restaurants
    console.log('🏪 Création des profils de restaurants...');
    const merchantProfiles = restaurantsData.map((restaurant, index) => ({
      ...restaurant,
      user_id: users[index]?.id
    }));

    const { data: merchants, error: merchantsError } = await supabase
      .from('merchant_profiles')
      .insert(merchantProfiles)
      .select();

    if (merchantsError) {
      console.error('❌ Erreur création restaurants:', merchantsError);
    } else {
      console.log(`✅ ${merchants.length} restaurants créés`);
    }

    // 4. Créer des produits de test
    console.log('🍽️ Création des produits de test...');
    const productsData = [];
    
    // Produits pour le restaurant
    if (merchants[0]) {
      productsData.push(
        {
          merchant_id: merchants[0].user_id,
          category_id: categories[0]?.id,
          name: { fr: 'Riz au gras', en: 'Jollof Rice' },
          description: { fr: 'Riz traditionnel béninois avec viande et légumes', en: 'Traditional Beninese rice with meat and vegetables' },
          price: 2500,
          promotional_price: null,
          images: ['https://images.unsplash.com/photo-1586190848861-99aa4a171e90?w=500&q=80'],
          is_available: true,
          preparation_time_minutes: 25,
          is_featured: true
        },
        {
          merchant_id: merchants[0].user_id,
          category_id: categories[0]?.id,
          name: { fr: 'Poisson braisé', en: 'Grilled Fish' },
          description: { fr: 'Poisson frais grillé aux épices locales', en: 'Fresh fish grilled with local spices' },
          price: 3000,
          promotional_price: 2700,
          images: ['https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=500&q=80'],
          is_available: true,
          preparation_time_minutes: 20,
          is_featured: false
        }
      );
    }

    // Produits pour l'épicerie
    if (merchants[1]) {
      productsData.push(
        {
          merchant_id: merchants[1].user_id,
          category_id: categories[4]?.id,
          name: { fr: 'Piment rouge africain', en: 'African Red Pepper' },
          description: { fr: 'Piment rouge séché du Bénin, très parfumé', en: 'Dried red pepper from Benin, very fragrant' },
          price: 500,
          images: ['https://images.unsplash.com/photo-1583623025817-d180a2221d0a?w=500&q=80'],
          is_available: true,
          preparation_time_minutes: 5,
          inventory_count: 50,
          track_inventory: true
        }
      );
    }

    if (productsData.length > 0) {
      const { data: products, error: productsError } = await supabase
        .from('products')
        .insert(productsData)
        .select();

      if (productsError) {
        console.error('❌ Erreur création produits:', productsError);
      } else {
        console.log(`✅ ${products.length} produits créés`);
      }
    }

    console.log('🎉 Peuplement de la base de données terminé avec succès !');
    console.log('📊 Résumé:');
    console.log(`   - ${users.length} utilisateurs créés`);
    console.log(`   - ${categories?.length || 0} catégories créées`);
    console.log(`   - ${merchants?.length || 0} restaurants créés`);
    console.log(`   - ${productsData.length} produits créés`);

  } catch (error) {
    console.error('❌ Erreur lors du peuplement:', error);
  }
}

// Fonction pour nettoyer la base de données
async function cleanDatabase() {
  try {
    console.log('🧹 Nettoyage de la base de données...');
    
    // Supprimer dans l'ordre inverse des dépendances
    await supabase.from('products').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('merchant_profiles').delete().neq('user_id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('categories').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    
    console.log('✅ Base de données nettoyée');
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  }
}

// Exécution du script
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'clean') {
    cleanDatabase();
  } else {
    seedDatabase();
  }
}

module.exports = { seedDatabase, cleanDatabase };
