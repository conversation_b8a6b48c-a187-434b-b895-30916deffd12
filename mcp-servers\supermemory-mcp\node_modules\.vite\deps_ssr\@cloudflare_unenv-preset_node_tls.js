import {
  createNotImplementedError,
  notImplemented
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/internal/tls/tls-socket.mjs
import { Socket } from "node:net";

// node_modules/unenv/dist/runtime/node/internal/tls/server.mjs
import { Server as _Server } from "node:net";
var Server = class extends _Server {
  constructor(arg1, arg2) {
    super(arg1, arg2);
  }
  addContext(hostname, context) {
  }
  setSecureContext(options) {
  }
  setTicketKeys(_keys) {
    throw createNotImplementedError("Server.setTicketKeys");
  }
  getTicketKeys() {
    throw createNotImplementedError("Server.getTicketKeys");
  }
};

// node_modules/unenv/dist/runtime/node/internal/tls/constants.mjs
var CLIENT_RENEG_LIMIT = 3;
var CLIENT_RENEG_WINDOW = 600;
var DEFAULT_CIPHERS = "";
var DEFAULT_ECDH_CURVE = "auto";
var DEFAULT_MIN_VERSION = "TLSv1.2";
var DEFAULT_MAX_VERSION = "TLSv1.3";

// node_modules/unenv/dist/runtime/node/tls.mjs
var createServer = function createServer2() {
  return new Server();
};
var checkServerIdentity = notImplemented("tls.checkServerIdentity");
var convertALPNProtocols = notImplemented("tls.convertALPNProtocols");
var createSecureContext = notImplemented("tls.createSecureContext");
var createSecurePair = notImplemented("tls.createSecurePair");
var getCiphers = notImplemented("tls.getCiphers");
var rootCertificates = [];

// node_modules/@cloudflare/unenv-preset/dist/runtime/node/tls.mjs
var workerdTls = process.getBuiltinModule("node:tls");
var {
  checkServerIdentity: checkServerIdentity2,
  connect,
  createSecureContext: createSecureContext2,
  // @ts-expect-error @types/node does not provide this function
  convertALPNProtocols: convertALPNProtocols2,
  // @ts-expect-error Node typings wrongly declare `SecureContext` as an interface
  SecureContext: SecureContext2,
  TLSSocket: TLSSocket2
} = workerdTls;
var tls_default = {
  CLIENT_RENEG_LIMIT,
  CLIENT_RENEG_WINDOW,
  DEFAULT_CIPHERS,
  DEFAULT_ECDH_CURVE,
  DEFAULT_MAX_VERSION,
  DEFAULT_MIN_VERSION,
  // @ts-expect-error
  SecureContext: SecureContext2,
  Server,
  TLSSocket: TLSSocket2,
  checkServerIdentity: checkServerIdentity2,
  connect,
  convertALPNProtocols: convertALPNProtocols2,
  createSecureContext: createSecureContext2,
  createSecurePair,
  createServer,
  getCiphers,
  rootCertificates
};
export {
  CLIENT_RENEG_LIMIT,
  CLIENT_RENEG_WINDOW,
  DEFAULT_CIPHERS,
  DEFAULT_ECDH_CURVE,
  DEFAULT_MAX_VERSION,
  DEFAULT_MIN_VERSION,
  SecureContext2 as SecureContext,
  Server,
  TLSSocket2 as TLSSocket,
  checkServerIdentity2 as checkServerIdentity,
  connect,
  convertALPNProtocols2 as convertALPNProtocols,
  createSecureContext2 as createSecureContext,
  createSecurePair,
  createServer,
  tls_default as default,
  getCiphers,
  rootCertificates
};
//# sourceMappingURL=@cloudflare_unenv-preset_node_tls.js.map
