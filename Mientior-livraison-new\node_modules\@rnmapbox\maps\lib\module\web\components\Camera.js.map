{"version": 3, "names": ["Component", "MapContext", "Fragment", "_Fragment", "jsx", "_jsx", "isArray", "value", "length", "undefined", "buildMapboxGlPadding", "padding", "left", "right", "top", "bottom", "Camera", "contextType", "UserTrackingModes", "componentDidMount", "map", "context", "props", "minZoomLevel", "setMinZoom", "maxZoomLevel", "setMaxZoom", "zoomLevel", "setZoom", "centerCoordinate", "flyTo", "center", "slice", "duration", "fitBounds", "northEastCoordinates", "southWestCoordinates", "animationDuration", "moveTo", "easeTo", "zoomTo", "zoom", "setCamera", "bounds", "heading", "pitch", "options", "getZoom", "bearing", "getBearing", "get<PERSON><PERSON>", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "ne", "sw", "newCameraTransform", "cameraForBounds", "animationMode", "jumpTo", "render"], "sourceRoot": "../../../../src", "sources": ["web/components/Camera.tsx"], "mappings": ";;AAAA,SAASA,SAAS,QAAqB,OAAO;AAI9C,OAAOC,UAAU,MAAM,eAAe;AAAC,SAAAC,QAAA,IAAAC,SAAA,EAAAC,GAAA,IAAAC,IAAA;AAEvC,SAASC,OAAOA,CAAIC,KAAuB,EAAyB;EAClE,OAAQA,KAAK,CAAkBC,MAAM,KAAKC,SAAS;AACrD;AAEA,SAASC,oBAAoBA,CAC3BC,OAA2B,EACmB;EAC9C,IAAIA,OAAO,KAAKF,SAAS,EAAE;IACzB;IACA,OAAOA,SAAS;EAClB,CAAC,MAAM,IAAI,CAACH,OAAO,CAACK,OAAO,CAAC,EAAE;IAC5B;IACA,OAAOA,OAAO;EAChB,CAAC,MAAM;IACL;IACA,IAAIA,OAAO,CAACH,MAAM,KAAK,CAAC,EAAE;MACxB;MACA,OAAOC,SAAS;IAClB,CAAC,MAAM,IAAIE,OAAO,CAACH,MAAM,GAAG,CAAC,EAAE;MAC7B;MACA,OAAOG,OAAO,CAAC,CAAC,CAAC;IACnB,CAAC,MAAM,IAAIA,OAAO,CAACH,MAAM,GAAG,CAAC,EAAE;MAC7B;MACA,OAAO;QACLI,IAAI,EAAED,OAAO,CAAC,CAAC,CAAC;QAChBE,KAAK,EAAEF,OAAO,CAAC,CAAC,CAAC;QACjBG,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC;QACfI,MAAM,EAAEJ,OAAO,CAAC,CAAC;MACnB,CAAC;IACH,CAAC,MAAM;MACL;MACA,OAAO;QACLG,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC;QACfE,KAAK,EAAEF,OAAO,CAAC,CAAC,CAAC;QACjBI,MAAM,EAAEJ,OAAO,CAAC,CAAC,CAAC;QAClBC,IAAI,EAAED,OAAO,CAAC,CAAC;MACjB,CAAC;IACH;EACF;AACF;AAEA,MAAMK,MAAM,SACFhB,SAAS,CAOnB;EAGE,OAAOiB,WAAW,GAAGhB,UAAU;EAC/B,OAAOiB,iBAAiB,GAAG,EAAE;EAE7BC,iBAAiBA,CAAA,EAAG;IAClB,MAAM;MAAEC;IAAI,CAAC,GAAG,IAAI,CAACC,OAAO;IAC5B,IAAI,CAACD,GAAG,EAAE;MACR;IACF;;IAEA;IACA,IAAI,IAAI,CAACE,KAAK,CAACC,YAAY,KAAKd,SAAS,EAAE;MACzCW,GAAG,CAACI,UAAU,CAAC,IAAI,CAACF,KAAK,CAACC,YAAY,CAAC;IACzC;;IAEA;IACA,IAAI,IAAI,CAACD,KAAK,CAACG,YAAY,KAAKhB,SAAS,EAAE;MACzCW,GAAG,CAACM,UAAU,CAAC,IAAI,CAACJ,KAAK,CAACG,YAAY,CAAC;IACzC;;IAEA;IACA,IAAI,IAAI,CAACH,KAAK,CAACK,SAAS,KAAKlB,SAAS,EAAE;MACtCW,GAAG,CAACQ,OAAO,CAAC,IAAI,CAACN,KAAK,CAACK,SAAS,CAAC;IACnC;;IAEA;IACA,IAAI,IAAI,CAACL,KAAK,CAACO,gBAAgB,KAAKpB,SAAS,EAAE;MAC7CW,GAAG,CAACU,KAAK,CAAC;QACRC,MAAM,EAAE,IAAI,CAACT,KAAK,CAACO,gBAAgB,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAqB;QACnEC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF;EAEAC,SAASA,CACPC,oBAA8B,EAC9BC,oBAA8B,EAC9BzB,OAA0B,GAAG,CAAC,EAC9B0B,iBAAiB,GAAG,CAAC,EACrB;IACA,MAAM;MAAEjB;IAAI,CAAC,GAAG,IAAI,CAACC,OAAO;IAC5B,IAAID,GAAG,EAAE;MACPA,GAAG,CAACc,SAAS,CACX,CACEC,oBAAoB,CAACH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAChCI,oBAAoB,CAACJ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACjC,EACD;QACErB,OAAO,EAAED,oBAAoB,CAACC,OAAO,CAAC;QACtCsB,QAAQ,EAAEI;MACZ,CACF,CAAC;IACH;EACF;EAEAP,KAAKA,CAACD,gBAA0B,EAAEQ,iBAAiB,GAAG,IAAI,EAAE;IAC1D,MAAM;MAAEjB;IAAI,CAAC,GAAG,IAAI,CAACC,OAAO;IAC5B,IAAID,GAAG,EAAE;MACPA,GAAG,CAACU,KAAK,CAAC;QACRC,MAAM,EAAEF,gBAAgB,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAqB;QACxDC,QAAQ,EAAEI;MACZ,CAAC,CAAC;IACJ;EACF;EAEAC,MAAMA,CAACT,gBAA0B,EAAEQ,iBAAiB,GAAG,CAAC,EAAE;IACxD,MAAM;MAAEjB;IAAI,CAAC,GAAG,IAAI,CAACC,OAAO;IAC5B,IAAID,GAAG,EAAE;MACPA,GAAG,CAACmB,MAAM,CAAC;QACTR,MAAM,EAAEF,gBAAgB,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAqB;QACxDC,QAAQ,EAAEI;MACZ,CAAC,CAAC;IACJ;EACF;EAEAG,MAAMA,CAACb,SAAiB,EAAEU,iBAAiB,GAAG,IAAI,EAAE;IAClD,MAAM;MAAEjB;IAAI,CAAC,GAAG,IAAI,CAACC,OAAO;IAC5B,IAAID,GAAG,EAAE;MACPA,GAAG,CAACU,KAAK,CAAC;QACRW,IAAI,EAAEd,SAAS;QACfM,QAAQ,EAAEI;MACZ,CAAC,CAAC;IACJ;EACF;EAEAK,SAASA,CAACpB,KAAiB,EAAE;IAC3B,MAAM;MAAEF;IAAI,CAAC,GAAG,IAAI,CAACC,OAAO;IAC5B,IAAI,CAACD,GAAG,EAAE;MACR;IACF;IACA,MAAM;MACJS,gBAAgB;MAChBc,MAAM;MACNhB,SAAS;MACTiB,OAAO;MACPC,KAAK;MACLlC,OAAO;MACP0B,iBAAiB,GAAG;IACtB,CAAC,GAAGf,KAAK;IAET,IAAIwB,OAA+B,GAAG;MACpCf,MAAM,EAAEF,gBAAgB,EAAEG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAqB;MACzDS,IAAI,EAAEd,SAAS,IAAIP,GAAG,CAAC2B,OAAO,CAAC,CAAC;MAChCC,OAAO,EAAEJ,OAAO,IAAIxB,GAAG,CAAC6B,UAAU,CAAC,CAAC;MACpCJ,KAAK,EAAEA,KAAK,IAAIzB,GAAG,CAAC8B,QAAQ,CAAC;IAC/B,CAAC;IAED,IACEvC,OAAO,EAAEwC,UAAU,IACnBxC,OAAO,EAAEyC,YAAY,IACrBzC,OAAO,EAAE0C,aAAa,IACtB1C,OAAO,EAAE2C,WAAW,EACpB;MACAR,OAAO,CAACnC,OAAO,GAAGD,oBAAoB,CAAC,CACrCC,OAAO,CAACwC,UAAU,EAClBxC,OAAO,CAACyC,YAAY,EACpBzC,OAAO,CAAC0C,aAAa,EACrB1C,OAAO,CAAC2C,WAAW,CACpB,CAAC;IACJ;IAEA,IAAIX,MAAM,EAAEY,EAAE,IAAIZ,MAAM,EAAEa,EAAE,EAAE;MAC5B,MAAMC,kBAAkB,GAAGrC,GAAG,CAACsC,eAAe,CAC5C,CAACf,MAAM,CAACY,EAAE,EAAyBZ,MAAM,CAACa,EAAE,CAAwB,EACpEV,OACF,CAAC;MACDA,OAAO,GAAG;QAAE,GAAGA,OAAO;QAAE,GAAGW;MAAmB,CAAC;IACjD;IAEA,QAAQnC,KAAK,CAACqC,aAAa;MACzB;MACA,KAAK,QAAQ;MACb,KAAK,UAAU;QACbvC,GAAG,CAACmB,MAAM,CAAC;UAAE,GAAGO,OAAO;UAAEb,QAAQ,EAAEI;QAAkB,CAAC,CAAC;QACvD;MACF,KAAK,OAAO;QACVjB,GAAG,CAACU,KAAK,CAAC;UAAE,GAAGgB,OAAO;UAAEb,QAAQ,EAAEI;QAAkB,CAAC,CAAC;QACtD;MACF,KAAK,QAAQ;MACb,KAAK,MAAM;QACTjB,GAAG,CAACwC,MAAM,CAACd,OAAO,CAAC;QACnB;IACJ;EACF;EAEAe,MAAMA,CAAA,EAAG;IACP,oBAAOxD,IAAA,CAAAF,SAAA,IAAI,CAAC;EACd;AACF;AAEA,SAASa,MAAM;AACf,eAAeA,MAAM", "ignoreList": []}