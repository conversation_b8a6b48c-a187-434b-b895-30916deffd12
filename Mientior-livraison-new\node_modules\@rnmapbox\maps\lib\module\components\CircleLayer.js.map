{"version": 3, "names": ["React", "NativeModules", "RNMBXCircleLayerNativeComponent", "AbstractLayer", "jsx", "_jsx", "Mapbox", "RNMBXModule", "<PERSON><PERSON><PERSON><PERSON>", "defaultProps", "sourceID", "StyleSource", "DefaultSourceID", "render", "props", "baseProps", "sourceLayerID", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../../src", "sources": ["components/CircleLayer.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,cAAc;AAI5C,OAAOC,+BAA+B,MAAM,0CAA0C;AAEtF,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE5C,MAAMC,MAAM,GAAGL,aAAa,CAACM,WAAW;;AAExC;;AA+DA;;AAaA;AACA;AACA;AACA,MAAMC,WAAW,SAASL,aAAa,CAAyB;EAC9D,OAAOM,YAAY,GAAG;IACpBC,QAAQ,EAAEJ,MAAM,CAACK,WAAW,CAACC;EAC/B,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAMC,KAAK,GAAG;MACZ,GAAG,IAAI,CAACA,KAAK;MACb,GAAG,IAAI,CAACC,SAAS;MACjBC,aAAa,EAAE,IAAI,CAACF,KAAK,CAACE;IAC5B,CAAC;IACD;MAAA;MACE;MACAX,IAAA,CAACH,+BAA+B;QAACe,GAAG,EAAE,IAAI,CAACC,cAAe;QAAA,GAAKJ;MAAK,CAAG;IAAC;EAE5E;AACF;AAEA,eAAeN,WAAW", "ignoreList": []}