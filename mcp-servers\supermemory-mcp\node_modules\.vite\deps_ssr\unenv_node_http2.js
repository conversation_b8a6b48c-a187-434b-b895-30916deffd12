import {
  notImplemented,
  notImplementedClass
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/internal/http2/constants.mjs
var NGHTTP2_ERR_FRAME_SIZE_ERROR = -522;
var NGHTTP2_SESSION_SERVER = 0;
var NGHTTP2_SESSION_CLIENT = 1;
var NGHTTP2_STREAM_STATE_IDLE = 1;
var NGHTTP2_STREAM_STATE_OPEN = 2;
var NGHTTP2_STREAM_STATE_RESERVED_LOCAL = 3;
var NGHTTP2_STREAM_STATE_RESERVED_REMOTE = 4;
var NGHTTP2_STREAM_STATE_HALF_CLOSED_LOCAL = 5;
var NGHTTP2_STREAM_STATE_HALF_CLOSED_REMOTE = 6;
var NGHTTP2_STREAM_STATE_CLOSED = 7;
var NGHTTP2_FLAG_NONE = 0;
var NGHTTP2_FLAG_END_STREAM = 1;
var NGHTTP2_FLAG_END_HEADERS = 4;
var NGHTTP2_FLAG_ACK = 1;
var NGHTTP2_FLAG_PADDED = 8;
var NGHTTP2_FLAG_PRIORITY = 32;
var DEFAULT_SETTINGS_HEADER_TABLE_SIZE = 4096;
var DEFAULT_SETTINGS_ENABLE_PUSH = 1;
var DEFAULT_SETTINGS_MAX_CONCURRENT_STREAMS = 4294967295;
var DEFAULT_SETTINGS_INITIAL_WINDOW_SIZE = 65535;
var DEFAULT_SETTINGS_MAX_FRAME_SIZE = 16384;
var DEFAULT_SETTINGS_MAX_HEADER_LIST_SIZE = 65535;
var DEFAULT_SETTINGS_ENABLE_CONNECT_PROTOCOL = 0;
var MAX_MAX_FRAME_SIZE = 16777215;
var MIN_MAX_FRAME_SIZE = 16384;
var MAX_INITIAL_WINDOW_SIZE = 2147483647;
var NGHTTP2_SETTINGS_HEADER_TABLE_SIZE = 1;
var NGHTTP2_SETTINGS_ENABLE_PUSH = 2;
var NGHTTP2_SETTINGS_MAX_CONCURRENT_STREAMS = 3;
var NGHTTP2_SETTINGS_INITIAL_WINDOW_SIZE = 4;
var NGHTTP2_SETTINGS_MAX_FRAME_SIZE = 5;
var NGHTTP2_SETTINGS_MAX_HEADER_LIST_SIZE = 6;
var NGHTTP2_SETTINGS_ENABLE_CONNECT_PROTOCOL = 8;
var PADDING_STRATEGY_NONE = 0;
var PADDING_STRATEGY_ALIGNED = 1;
var PADDING_STRATEGY_MAX = 2;
var PADDING_STRATEGY_CALLBACK = 1;
var NGHTTP2_NO_ERROR = 0;
var NGHTTP2_PROTOCOL_ERROR = 1;
var NGHTTP2_INTERNAL_ERROR = 2;
var NGHTTP2_FLOW_CONTROL_ERROR = 3;
var NGHTTP2_SETTINGS_TIMEOUT = 4;
var NGHTTP2_STREAM_CLOSED = 5;
var NGHTTP2_FRAME_SIZE_ERROR = 6;
var NGHTTP2_REFUSED_STREAM = 7;
var NGHTTP2_CANCEL = 8;
var NGHTTP2_COMPRESSION_ERROR = 9;
var NGHTTP2_CONNECT_ERROR = 10;
var NGHTTP2_ENHANCE_YOUR_CALM = 11;
var NGHTTP2_INADEQUATE_SECURITY = 12;
var NGHTTP2_HTTP_1_1_REQUIRED = 13;
var NGHTTP2_DEFAULT_WEIGHT = 16;
var HTTP2_HEADER_STATUS = ":status";
var HTTP2_HEADER_METHOD = ":method";
var HTTP2_HEADER_AUTHORITY = ":authority";
var HTTP2_HEADER_SCHEME = ":scheme";
var HTTP2_HEADER_PATH = ":path";
var HTTP2_HEADER_PROTOCOL = ":protocol";
var HTTP2_HEADER_ACCEPT_ENCODING = "accept-encoding";
var HTTP2_HEADER_ACCEPT_LANGUAGE = "accept-language";
var HTTP2_HEADER_ACCEPT_RANGES = "accept-ranges";
var HTTP2_HEADER_ACCEPT = "accept";
var HTTP2_HEADER_ACCESS_CONTROL_ALLOW_CREDENTIALS = "access-control-allow-credentials";
var HTTP2_HEADER_ACCESS_CONTROL_ALLOW_HEADERS = "access-control-allow-headers";
var HTTP2_HEADER_ACCESS_CONTROL_ALLOW_METHODS = "access-control-allow-methods";
var HTTP2_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN = "access-control-allow-origin";
var HTTP2_HEADER_ACCESS_CONTROL_EXPOSE_HEADERS = "access-control-expose-headers";
var HTTP2_HEADER_ACCESS_CONTROL_REQUEST_HEADERS = "access-control-request-headers";
var HTTP2_HEADER_ACCESS_CONTROL_REQUEST_METHOD = "access-control-request-method";
var HTTP2_HEADER_AGE = "age";
var HTTP2_HEADER_AUTHORIZATION = "authorization";
var HTTP2_HEADER_CACHE_CONTROL = "cache-control";
var HTTP2_HEADER_CONNECTION = "connection";
var HTTP2_HEADER_CONTENT_DISPOSITION = "content-disposition";
var HTTP2_HEADER_CONTENT_ENCODING = "content-encoding";
var HTTP2_HEADER_CONTENT_LENGTH = "content-length";
var HTTP2_HEADER_CONTENT_TYPE = "content-type";
var HTTP2_HEADER_COOKIE = "cookie";
var HTTP2_HEADER_DATE = "date";
var HTTP2_HEADER_ETAG = "etag";
var HTTP2_HEADER_FORWARDED = "forwarded";
var HTTP2_HEADER_HOST = "host";
var HTTP2_HEADER_IF_MODIFIED_SINCE = "if-modified-since";
var HTTP2_HEADER_IF_NONE_MATCH = "if-none-match";
var HTTP2_HEADER_IF_RANGE = "if-range";
var HTTP2_HEADER_LAST_MODIFIED = "last-modified";
var HTTP2_HEADER_LINK = "link";
var HTTP2_HEADER_LOCATION = "location";
var HTTP2_HEADER_RANGE = "range";
var HTTP2_HEADER_REFERER = "referer";
var HTTP2_HEADER_SERVER = "server";
var HTTP2_HEADER_SET_COOKIE = "set-cookie";
var HTTP2_HEADER_STRICT_TRANSPORT_SECURITY = "strict-transport-security";
var HTTP2_HEADER_TRANSFER_ENCODING = "transfer-encoding";
var HTTP2_HEADER_TE = "te";
var HTTP2_HEADER_UPGRADE_INSECURE_REQUESTS = "upgrade-insecure-requests";
var HTTP2_HEADER_UPGRADE = "upgrade";
var HTTP2_HEADER_USER_AGENT = "user-agent";
var HTTP2_HEADER_VARY = "vary";
var HTTP2_HEADER_X_CONTENT_TYPE_OPTIONS = "x-content-type-options";
var HTTP2_HEADER_X_FRAME_OPTIONS = "x-frame-options";
var HTTP2_HEADER_KEEP_ALIVE = "keep-alive";
var HTTP2_HEADER_PROXY_CONNECTION = "proxy-connection";
var HTTP2_HEADER_X_XSS_PROTECTION = "x-xss-protection";
var HTTP2_HEADER_ALT_SVC = "alt-svc";
var HTTP2_HEADER_CONTENT_SECURITY_POLICY = "content-security-policy";
var HTTP2_HEADER_EARLY_DATA = "early-data";
var HTTP2_HEADER_EXPECT_CT = "expect-ct";
var HTTP2_HEADER_ORIGIN = "origin";
var HTTP2_HEADER_PURPOSE = "purpose";
var HTTP2_HEADER_TIMING_ALLOW_ORIGIN = "timing-allow-origin";
var HTTP2_HEADER_X_FORWARDED_FOR = "x-forwarded-for";
var HTTP2_HEADER_PRIORITY = "priority";
var HTTP2_HEADER_ACCEPT_CHARSET = "accept-charset";
var HTTP2_HEADER_ACCESS_CONTROL_MAX_AGE = "access-control-max-age";
var HTTP2_HEADER_ALLOW = "allow";
var HTTP2_HEADER_CONTENT_LANGUAGE = "content-language";
var HTTP2_HEADER_CONTENT_LOCATION = "content-location";
var HTTP2_HEADER_CONTENT_MD5 = "content-md5";
var HTTP2_HEADER_CONTENT_RANGE = "content-range";
var HTTP2_HEADER_DNT = "dnt";
var HTTP2_HEADER_EXPECT = "expect";
var HTTP2_HEADER_EXPIRES = "expires";
var HTTP2_HEADER_FROM = "from";
var HTTP2_HEADER_IF_MATCH = "if-match";
var HTTP2_HEADER_IF_UNMODIFIED_SINCE = "if-unmodified-since";
var HTTP2_HEADER_MAX_FORWARDS = "max-forwards";
var HTTP2_HEADER_PREFER = "prefer";
var HTTP2_HEADER_PROXY_AUTHENTICATE = "proxy-authenticate";
var HTTP2_HEADER_PROXY_AUTHORIZATION = "proxy-authorization";
var HTTP2_HEADER_REFRESH = "refresh";
var HTTP2_HEADER_RETRY_AFTER = "retry-after";
var HTTP2_HEADER_TRAILER = "trailer";
var HTTP2_HEADER_TK = "tk";
var HTTP2_HEADER_VIA = "via";
var HTTP2_HEADER_WARNING = "warning";
var HTTP2_HEADER_WWW_AUTHENTICATE = "www-authenticate";
var HTTP2_HEADER_HTTP2_SETTINGS = "http2-settings";
var HTTP2_METHOD_ACL = "ACL";
var HTTP2_METHOD_BASELINE_CONTROL = "BASELINE-CONTROL";
var HTTP2_METHOD_BIND = "BIND";
var HTTP2_METHOD_CHECKIN = "CHECKIN";
var HTTP2_METHOD_CHECKOUT = "CHECKOUT";
var HTTP2_METHOD_CONNECT = "CONNECT";
var HTTP2_METHOD_COPY = "COPY";
var HTTP2_METHOD_DELETE = "DELETE";
var HTTP2_METHOD_GET = "GET";
var HTTP2_METHOD_HEAD = "HEAD";
var HTTP2_METHOD_LABEL = "LABEL";
var HTTP2_METHOD_LINK = "LINK";
var HTTP2_METHOD_LOCK = "LOCK";
var HTTP2_METHOD_MERGE = "MERGE";
var HTTP2_METHOD_MKACTIVITY = "MKACTIVITY";
var HTTP2_METHOD_MKCALENDAR = "MKCALENDAR";
var HTTP2_METHOD_MKCOL = "MKCOL";
var HTTP2_METHOD_MKREDIRECTREF = "MKREDIRECTREF";
var HTTP2_METHOD_MKWORKSPACE = "MKWORKSPACE";
var HTTP2_METHOD_MOVE = "MOVE";
var HTTP2_METHOD_OPTIONS = "OPTIONS";
var HTTP2_METHOD_ORDERPATCH = "ORDERPATCH";
var HTTP2_METHOD_PATCH = "PATCH";
var HTTP2_METHOD_POST = "POST";
var HTTP2_METHOD_PRI = "PRI";
var HTTP2_METHOD_PROPFIND = "PROPFIND";
var HTTP2_METHOD_PROPPATCH = "PROPPATCH";
var HTTP2_METHOD_PUT = "PUT";
var HTTP2_METHOD_REBIND = "REBIND";
var HTTP2_METHOD_REPORT = "REPORT";
var HTTP2_METHOD_SEARCH = "SEARCH";
var HTTP2_METHOD_TRACE = "TRACE";
var HTTP2_METHOD_UNBIND = "UNBIND";
var HTTP2_METHOD_UNCHECKOUT = "UNCHECKOUT";
var HTTP2_METHOD_UNLINK = "UNLINK";
var HTTP2_METHOD_UNLOCK = "UNLOCK";
var HTTP2_METHOD_UPDATE = "UPDATE";
var HTTP2_METHOD_UPDATEREDIRECTREF = "UPDATEREDIRECTREF";
var HTTP2_METHOD_VERSION_CONTROL = "VERSION-CONTROL";
var HTTP_STATUS_CONTINUE = 100;
var HTTP_STATUS_SWITCHING_PROTOCOLS = 101;
var HTTP_STATUS_PROCESSING = 102;
var HTTP_STATUS_EARLY_HINTS = 103;
var HTTP_STATUS_OK = 200;
var HTTP_STATUS_CREATED = 201;
var HTTP_STATUS_ACCEPTED = 202;
var HTTP_STATUS_NON_AUTHORITATIVE_INFORMATION = 203;
var HTTP_STATUS_NO_CONTENT = 204;
var HTTP_STATUS_RESET_CONTENT = 205;
var HTTP_STATUS_PARTIAL_CONTENT = 206;
var HTTP_STATUS_MULTI_STATUS = 207;
var HTTP_STATUS_ALREADY_REPORTED = 208;
var HTTP_STATUS_IM_USED = 226;
var HTTP_STATUS_MULTIPLE_CHOICES = 300;
var HTTP_STATUS_MOVED_PERMANENTLY = 301;
var HTTP_STATUS_FOUND = 302;
var HTTP_STATUS_SEE_OTHER = 303;
var HTTP_STATUS_NOT_MODIFIED = 304;
var HTTP_STATUS_USE_PROXY = 305;
var HTTP_STATUS_TEMPORARY_REDIRECT = 307;
var HTTP_STATUS_PERMANENT_REDIRECT = 308;
var HTTP_STATUS_BAD_REQUEST = 400;
var HTTP_STATUS_UNAUTHORIZED = 401;
var HTTP_STATUS_PAYMENT_REQUIRED = 402;
var HTTP_STATUS_FORBIDDEN = 403;
var HTTP_STATUS_NOT_FOUND = 404;
var HTTP_STATUS_METHOD_NOT_ALLOWED = 405;
var HTTP_STATUS_NOT_ACCEPTABLE = 406;
var HTTP_STATUS_PROXY_AUTHENTICATION_REQUIRED = 407;
var HTTP_STATUS_REQUEST_TIMEOUT = 408;
var HTTP_STATUS_CONFLICT = 409;
var HTTP_STATUS_GONE = 410;
var HTTP_STATUS_LENGTH_REQUIRED = 411;
var HTTP_STATUS_PRECONDITION_FAILED = 412;
var HTTP_STATUS_PAYLOAD_TOO_LARGE = 413;
var HTTP_STATUS_URI_TOO_LONG = 414;
var HTTP_STATUS_UNSUPPORTED_MEDIA_TYPE = 415;
var HTTP_STATUS_RANGE_NOT_SATISFIABLE = 416;
var HTTP_STATUS_EXPECTATION_FAILED = 417;
var HTTP_STATUS_TEAPOT = 418;
var HTTP_STATUS_MISDIRECTED_REQUEST = 421;
var HTTP_STATUS_UNPROCESSABLE_ENTITY = 422;
var HTTP_STATUS_LOCKED = 423;
var HTTP_STATUS_FAILED_DEPENDENCY = 424;
var HTTP_STATUS_TOO_EARLY = 425;
var HTTP_STATUS_UPGRADE_REQUIRED = 426;
var HTTP_STATUS_PRECONDITION_REQUIRED = 428;
var HTTP_STATUS_TOO_MANY_REQUESTS = 429;
var HTTP_STATUS_REQUEST_HEADER_FIELDS_TOO_LARGE = 431;
var HTTP_STATUS_UNAVAILABLE_FOR_LEGAL_REASONS = 451;
var HTTP_STATUS_INTERNAL_SERVER_ERROR = 500;
var HTTP_STATUS_NOT_IMPLEMENTED = 501;
var HTTP_STATUS_BAD_GATEWAY = 502;
var HTTP_STATUS_SERVICE_UNAVAILABLE = 503;
var HTTP_STATUS_GATEWAY_TIMEOUT = 504;
var HTTP_STATUS_HTTP_VERSION_NOT_SUPPORTED = 505;
var HTTP_STATUS_VARIANT_ALSO_NEGOTIATES = 506;
var HTTP_STATUS_INSUFFICIENT_STORAGE = 507;
var HTTP_STATUS_LOOP_DETECTED = 508;
var HTTP_STATUS_BANDWIDTH_LIMIT_EXCEEDED = 509;
var HTTP_STATUS_NOT_EXTENDED = 510;
var HTTP_STATUS_NETWORK_AUTHENTICATION_REQUIRED = 511;

// node_modules/unenv/dist/runtime/node/http2.mjs
var constants = {
  NGHTTP2_ERR_FRAME_SIZE_ERROR,
  NGHTTP2_SESSION_SERVER,
  NGHTTP2_SESSION_CLIENT,
  NGHTTP2_STREAM_STATE_IDLE,
  NGHTTP2_STREAM_STATE_OPEN,
  NGHTTP2_STREAM_STATE_RESERVED_LOCAL,
  NGHTTP2_STREAM_STATE_RESERVED_REMOTE,
  NGHTTP2_STREAM_STATE_HALF_CLOSED_LOCAL,
  NGHTTP2_STREAM_STATE_HALF_CLOSED_REMOTE,
  NGHTTP2_STREAM_STATE_CLOSED,
  NGHTTP2_FLAG_NONE,
  NGHTTP2_FLAG_END_STREAM,
  NGHTTP2_FLAG_END_HEADERS,
  NGHTTP2_FLAG_ACK,
  NGHTTP2_FLAG_PADDED,
  NGHTTP2_FLAG_PRIORITY,
  DEFAULT_SETTINGS_HEADER_TABLE_SIZE,
  DEFAULT_SETTINGS_ENABLE_PUSH,
  DEFAULT_SETTINGS_MAX_CONCURRENT_STREAMS,
  DEFAULT_SETTINGS_INITIAL_WINDOW_SIZE,
  DEFAULT_SETTINGS_MAX_FRAME_SIZE,
  DEFAULT_SETTINGS_MAX_HEADER_LIST_SIZE,
  DEFAULT_SETTINGS_ENABLE_CONNECT_PROTOCOL,
  MAX_MAX_FRAME_SIZE,
  MIN_MAX_FRAME_SIZE,
  MAX_INITIAL_WINDOW_SIZE,
  NGHTTP2_SETTINGS_HEADER_TABLE_SIZE,
  NGHTTP2_SETTINGS_ENABLE_PUSH,
  NGHTTP2_SETTINGS_MAX_CONCURRENT_STREAMS,
  NGHTTP2_SETTINGS_INITIAL_WINDOW_SIZE,
  NGHTTP2_SETTINGS_MAX_FRAME_SIZE,
  NGHTTP2_SETTINGS_MAX_HEADER_LIST_SIZE,
  NGHTTP2_SETTINGS_ENABLE_CONNECT_PROTOCOL,
  PADDING_STRATEGY_NONE,
  PADDING_STRATEGY_ALIGNED,
  PADDING_STRATEGY_MAX,
  PADDING_STRATEGY_CALLBACK,
  NGHTTP2_NO_ERROR,
  NGHTTP2_PROTOCOL_ERROR,
  NGHTTP2_INTERNAL_ERROR,
  NGHTTP2_FLOW_CONTROL_ERROR,
  NGHTTP2_SETTINGS_TIMEOUT,
  NGHTTP2_STREAM_CLOSED,
  NGHTTP2_FRAME_SIZE_ERROR,
  NGHTTP2_REFUSED_STREAM,
  NGHTTP2_CANCEL,
  NGHTTP2_COMPRESSION_ERROR,
  NGHTTP2_CONNECT_ERROR,
  NGHTTP2_ENHANCE_YOUR_CALM,
  NGHTTP2_INADEQUATE_SECURITY,
  NGHTTP2_HTTP_1_1_REQUIRED,
  NGHTTP2_DEFAULT_WEIGHT,
  HTTP2_HEADER_STATUS,
  HTTP2_HEADER_METHOD,
  HTTP2_HEADER_AUTHORITY,
  HTTP2_HEADER_SCHEME,
  HTTP2_HEADER_PATH,
  HTTP2_HEADER_PROTOCOL,
  HTTP2_HEADER_ACCEPT_ENCODING,
  HTTP2_HEADER_ACCEPT_LANGUAGE,
  HTTP2_HEADER_ACCEPT_RANGES,
  HTTP2_HEADER_ACCEPT,
  HTTP2_HEADER_ACCESS_CONTROL_ALLOW_CREDENTIALS,
  HTTP2_HEADER_ACCESS_CONTROL_ALLOW_HEADERS,
  HTTP2_HEADER_ACCESS_CONTROL_ALLOW_METHODS,
  HTTP2_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN,
  HTTP2_HEADER_ACCESS_CONTROL_EXPOSE_HEADERS,
  HTTP2_HEADER_ACCESS_CONTROL_REQUEST_HEADERS,
  HTTP2_HEADER_ACCESS_CONTROL_REQUEST_METHOD,
  HTTP2_HEADER_AGE,
  HTTP2_HEADER_AUTHORIZATION,
  HTTP2_HEADER_CACHE_CONTROL,
  HTTP2_HEADER_CONNECTION,
  HTTP2_HEADER_CONTENT_DISPOSITION,
  HTTP2_HEADER_CONTENT_ENCODING,
  HTTP2_HEADER_CONTENT_LENGTH,
  HTTP2_HEADER_CONTENT_TYPE,
  HTTP2_HEADER_COOKIE,
  HTTP2_HEADER_DATE,
  HTTP2_HEADER_ETAG,
  HTTP2_HEADER_FORWARDED,
  HTTP2_HEADER_HOST,
  HTTP2_HEADER_IF_MODIFIED_SINCE,
  HTTP2_HEADER_IF_NONE_MATCH,
  HTTP2_HEADER_IF_RANGE,
  HTTP2_HEADER_LAST_MODIFIED,
  HTTP2_HEADER_LINK,
  HTTP2_HEADER_LOCATION,
  HTTP2_HEADER_RANGE,
  HTTP2_HEADER_REFERER,
  HTTP2_HEADER_SERVER,
  HTTP2_HEADER_SET_COOKIE,
  HTTP2_HEADER_STRICT_TRANSPORT_SECURITY,
  HTTP2_HEADER_TRANSFER_ENCODING,
  HTTP2_HEADER_TE,
  HTTP2_HEADER_UPGRADE_INSECURE_REQUESTS,
  HTTP2_HEADER_UPGRADE,
  HTTP2_HEADER_USER_AGENT,
  HTTP2_HEADER_VARY,
  HTTP2_HEADER_X_CONTENT_TYPE_OPTIONS,
  HTTP2_HEADER_X_FRAME_OPTIONS,
  HTTP2_HEADER_KEEP_ALIVE,
  HTTP2_HEADER_PROXY_CONNECTION,
  HTTP2_HEADER_X_XSS_PROTECTION,
  HTTP2_HEADER_ALT_SVC,
  HTTP2_HEADER_CONTENT_SECURITY_POLICY,
  HTTP2_HEADER_EARLY_DATA,
  HTTP2_HEADER_EXPECT_CT,
  HTTP2_HEADER_ORIGIN,
  HTTP2_HEADER_PURPOSE,
  HTTP2_HEADER_TIMING_ALLOW_ORIGIN,
  HTTP2_HEADER_X_FORWARDED_FOR,
  HTTP2_HEADER_PRIORITY,
  HTTP2_HEADER_ACCEPT_CHARSET,
  HTTP2_HEADER_ACCESS_CONTROL_MAX_AGE,
  HTTP2_HEADER_ALLOW,
  HTTP2_HEADER_CONTENT_LANGUAGE,
  HTTP2_HEADER_CONTENT_LOCATION,
  HTTP2_HEADER_CONTENT_MD5,
  HTTP2_HEADER_CONTENT_RANGE,
  HTTP2_HEADER_DNT,
  HTTP2_HEADER_EXPECT,
  HTTP2_HEADER_EXPIRES,
  HTTP2_HEADER_FROM,
  HTTP2_HEADER_IF_MATCH,
  HTTP2_HEADER_IF_UNMODIFIED_SINCE,
  HTTP2_HEADER_MAX_FORWARDS,
  HTTP2_HEADER_PREFER,
  HTTP2_HEADER_PROXY_AUTHENTICATE,
  HTTP2_HEADER_PROXY_AUTHORIZATION,
  HTTP2_HEADER_REFRESH,
  HTTP2_HEADER_RETRY_AFTER,
  HTTP2_HEADER_TRAILER,
  HTTP2_HEADER_TK,
  HTTP2_HEADER_VIA,
  HTTP2_HEADER_WARNING,
  HTTP2_HEADER_WWW_AUTHENTICATE,
  HTTP2_HEADER_HTTP2_SETTINGS,
  HTTP2_METHOD_ACL,
  HTTP2_METHOD_BASELINE_CONTROL,
  HTTP2_METHOD_BIND,
  HTTP2_METHOD_CHECKIN,
  HTTP2_METHOD_CHECKOUT,
  HTTP2_METHOD_CONNECT,
  HTTP2_METHOD_COPY,
  HTTP2_METHOD_DELETE,
  HTTP2_METHOD_GET,
  HTTP2_METHOD_HEAD,
  HTTP2_METHOD_LABEL,
  HTTP2_METHOD_LINK,
  HTTP2_METHOD_LOCK,
  HTTP2_METHOD_MERGE,
  HTTP2_METHOD_MKACTIVITY,
  HTTP2_METHOD_MKCALENDAR,
  HTTP2_METHOD_MKCOL,
  HTTP2_METHOD_MKREDIRECTREF,
  HTTP2_METHOD_MKWORKSPACE,
  HTTP2_METHOD_MOVE,
  HTTP2_METHOD_OPTIONS,
  HTTP2_METHOD_ORDERPATCH,
  HTTP2_METHOD_PATCH,
  HTTP2_METHOD_POST,
  HTTP2_METHOD_PRI,
  HTTP2_METHOD_PROPFIND,
  HTTP2_METHOD_PROPPATCH,
  HTTP2_METHOD_PUT,
  HTTP2_METHOD_REBIND,
  HTTP2_METHOD_REPORT,
  HTTP2_METHOD_SEARCH,
  HTTP2_METHOD_TRACE,
  HTTP2_METHOD_UNBIND,
  HTTP2_METHOD_UNCHECKOUT,
  HTTP2_METHOD_UNLINK,
  HTTP2_METHOD_UNLOCK,
  HTTP2_METHOD_UPDATE,
  HTTP2_METHOD_UPDATEREDIRECTREF,
  HTTP2_METHOD_VERSION_CONTROL,
  HTTP_STATUS_CONTINUE,
  HTTP_STATUS_SWITCHING_PROTOCOLS,
  HTTP_STATUS_PROCESSING,
  HTTP_STATUS_EARLY_HINTS,
  HTTP_STATUS_OK,
  HTTP_STATUS_CREATED,
  HTTP_STATUS_ACCEPTED,
  HTTP_STATUS_NON_AUTHORITATIVE_INFORMATION,
  HTTP_STATUS_NO_CONTENT,
  HTTP_STATUS_RESET_CONTENT,
  HTTP_STATUS_PARTIAL_CONTENT,
  HTTP_STATUS_MULTI_STATUS,
  HTTP_STATUS_ALREADY_REPORTED,
  HTTP_STATUS_IM_USED,
  HTTP_STATUS_MULTIPLE_CHOICES,
  HTTP_STATUS_MOVED_PERMANENTLY,
  HTTP_STATUS_FOUND,
  HTTP_STATUS_SEE_OTHER,
  HTTP_STATUS_NOT_MODIFIED,
  HTTP_STATUS_USE_PROXY,
  HTTP_STATUS_TEMPORARY_REDIRECT,
  HTTP_STATUS_PERMANENT_REDIRECT,
  HTTP_STATUS_BAD_REQUEST,
  HTTP_STATUS_UNAUTHORIZED,
  HTTP_STATUS_PAYMENT_REQUIRED,
  HTTP_STATUS_FORBIDDEN,
  HTTP_STATUS_NOT_FOUND,
  HTTP_STATUS_METHOD_NOT_ALLOWED,
  HTTP_STATUS_NOT_ACCEPTABLE,
  HTTP_STATUS_PROXY_AUTHENTICATION_REQUIRED,
  HTTP_STATUS_REQUEST_TIMEOUT,
  HTTP_STATUS_CONFLICT,
  HTTP_STATUS_GONE,
  HTTP_STATUS_LENGTH_REQUIRED,
  HTTP_STATUS_PRECONDITION_FAILED,
  HTTP_STATUS_PAYLOAD_TOO_LARGE,
  HTTP_STATUS_URI_TOO_LONG,
  HTTP_STATUS_UNSUPPORTED_MEDIA_TYPE,
  HTTP_STATUS_RANGE_NOT_SATISFIABLE,
  HTTP_STATUS_EXPECTATION_FAILED,
  HTTP_STATUS_TEAPOT,
  HTTP_STATUS_MISDIRECTED_REQUEST,
  HTTP_STATUS_UNPROCESSABLE_ENTITY,
  HTTP_STATUS_LOCKED,
  HTTP_STATUS_FAILED_DEPENDENCY,
  HTTP_STATUS_TOO_EARLY,
  HTTP_STATUS_UPGRADE_REQUIRED,
  HTTP_STATUS_PRECONDITION_REQUIRED,
  HTTP_STATUS_TOO_MANY_REQUESTS,
  HTTP_STATUS_REQUEST_HEADER_FIELDS_TOO_LARGE,
  HTTP_STATUS_UNAVAILABLE_FOR_LEGAL_REASONS,
  HTTP_STATUS_INTERNAL_SERVER_ERROR,
  HTTP_STATUS_NOT_IMPLEMENTED,
  HTTP_STATUS_BAD_GATEWAY,
  HTTP_STATUS_SERVICE_UNAVAILABLE,
  HTTP_STATUS_GATEWAY_TIMEOUT,
  HTTP_STATUS_HTTP_VERSION_NOT_SUPPORTED,
  HTTP_STATUS_VARIANT_ALSO_NEGOTIATES,
  HTTP_STATUS_INSUFFICIENT_STORAGE,
  HTTP_STATUS_LOOP_DETECTED,
  HTTP_STATUS_BANDWIDTH_LIMIT_EXCEEDED,
  HTTP_STATUS_NOT_EXTENDED,
  HTTP_STATUS_NETWORK_AUTHENTICATION_REQUIRED
};
var createSecureServer = notImplemented("http2.createSecureServer");
var createServer = notImplemented("http2.createServer");
var connect = notImplemented("http2.connect");
var performServerHandshake = notImplemented("http2.performServerHandshake ");
var Http2ServerRequest = notImplementedClass("http2.Http2ServerRequest");
var Http2ServerResponse = notImplementedClass("http2.Http2ServerResponse");
var getDefaultSettings = function() {
  return /* @__PURE__ */ Object.create({
    headerTableSize: 4096,
    enablePush: true,
    initialWindowSize: 65535,
    maxFrameSize: 16384,
    maxConcurrentStreams: 4294967295,
    maxHeaderSize: 65535,
    maxHeaderListSize: 65535,
    enableConnectProtocol: false
  });
};
var getPackedSettings = function() {
  return Buffer.from("");
};
var getUnpackedSettings = function() {
  return /* @__PURE__ */ Object.create({});
};
var sensitiveHeaders = Symbol("nodejs.http2.sensitiveHeaders");
var http2_default = {
  constants,
  createSecureServer,
  createServer,
  Http2ServerRequest,
  Http2ServerResponse,
  connect,
  getDefaultSettings,
  getPackedSettings,
  getUnpackedSettings,
  performServerHandshake,
  sensitiveHeaders
};
export {
  Http2ServerRequest,
  Http2ServerResponse,
  connect,
  constants,
  createSecureServer,
  createServer,
  http2_default as default,
  getDefaultSettings,
  getPackedSettings,
  getUnpackedSettings,
  performServerHandshake,
  sensitiveHeaders
};
//# sourceMappingURL=unenv_node_http2.js.map
