# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# IDE
.vscode/
.idea/
*.swp
*.swo

# Temporary documentation files
*_TEMP.md
*_OLD.md
*_BACKUP.md
*_TEST.md
*_DEBUG.md
*_FIX.md
*_GUIDE.md
*_SETUP.md
*_CONFIG.md
*_MIGRATION.md
*_TROUBLESHOOT.md
*_WORKFLOW.md
*_IMPLEMENTATION.md
*_COMPLETE.md
*_SUMMARY.md
*_CORRECTION.md
*_DIAGNOSTIC.md

# Test files
test-*.js
verify-*.js
check-*.js
debug-*.js
quick-*.js
fix-*.js
restart-*.sh

# Temporary scripts
mock-*.js
remove-*.js

# Exceptions - fichiers essentiels à conserver
!gesture-handler.js
!global.js
