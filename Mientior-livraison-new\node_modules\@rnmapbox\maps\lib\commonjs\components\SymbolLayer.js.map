{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_RNMBXSymbolLayerNativeComponent", "_AbstractLayer", "_jsxRuntime", "e", "__esModule", "default", "NATIVE_MODULE_NAME", "exports", "Mapbox", "NativeModules", "RNMBXModule", "SymbolLayer", "AbstractLayer", "defaultProps", "sourceID", "StyleSource", "DefaultSourceID", "deprecationLogged", "snapshot", "_shouldSnapshot", "isSnapshot", "React", "Children", "count", "baseProps", "children", "for<PERSON>ach", "child", "type", "View", "console", "warn", "render", "props", "sourceLayerID", "jsx", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../../src", "sources": ["components/SymbolLayer.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAOA,IAAAE,gCAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAA4C,IAAAI,WAAA,GAAAJ,OAAA;AAAA,SAAAD,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAErC,MAAMG,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG,kBAAkB;AAEpD,MAAME,MAAM,GAAGC,0BAAa,CAACC,WAAW;;AAExC;;AA+DA;;AAoBA;AACA;AACA;AACO,MAAMC,WAAW,SAASC,sBAAa,CAAyB;EACrE,OAAOC,YAAY,GAAG;IACpBC,QAAQ,EAAEN,MAAM,CAACO,WAAW,CAACC;EAC/B,CAAC;EACDC,iBAAiB,GAA0B;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAE9DC,eAAeA,CAAA,EAAG;IAChB,IAAIC,UAAU,GAAG,KAAK;IAEtB,IAAIC,cAAK,CAACC,QAAQ,CAACC,KAAK,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACtD,OAAOL,UAAU;IACnB;IAEAC,cAAK,CAACC,QAAQ,CAACI,OAAO,CAAC,IAAI,CAACF,SAAS,CAACC,QAAQ,EAAGE,KAAK,IAAK;MACzD,IAAIA,KAAK,EAAEC,IAAI,KAAKC,iBAAI,EAAE;QACxBT,UAAU,GAAG,IAAI;MACnB;IACF,CAAC,CAAC;IACF,IAAIA,UAAU,IAAI,CAAC,IAAI,CAACH,iBAAiB,CAACC,QAAQ,EAAE;MAClDY,OAAO,CAACC,IAAI,CACV,uLACF,CAAC;MACD,IAAI,CAACd,iBAAiB,CAACC,QAAQ,GAAG,IAAI;IACxC;IAEA,OAAOE,UAAU;EACnB;EAEAY,MAAMA,CAAA,EAAG;IACP,MAAMC,KAAK,GAAG;MACZ,GAAG,IAAI,CAACT,SAAS;MACjBN,QAAQ,EAAE,IAAI,CAACC,eAAe,CAAC,CAAC;MAChCe,aAAa,EAAE,IAAI,CAACD,KAAK,CAACC;IAC5B,CAAC;IAED;MAAA;MACE;MACA,IAAAhC,WAAA,CAAAiC,GAAA,EAACnC,gCAAA,CAAAK,OAA+B;QAAC+B,GAAG,EAAE,IAAI,CAACC,cAAe;QAAA,GAAKJ,KAAK;QAAAR,QAAA,EACjE,IAAI,CAACQ,KAAK,CAACR;MAAQ,CACW;IAAC;EAEtC;AACF;AAAClB,OAAA,CAAAI,WAAA,GAAAA,WAAA", "ignoreList": []}