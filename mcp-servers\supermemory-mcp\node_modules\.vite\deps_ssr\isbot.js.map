{"version": 3, "sources": ["../../isbot/index.mjs"], "sourcesContent": ["// src/patterns.json\nvar patterns_default = [\n  \" daum[ /]\",\n  \" deusu/\",\n  \" yadirectfetcher\",\n  \"(?:^|[^g])news(?!sapphire)\",\n  \"(?<! (?:channel/|google/))google(?!(app|/google| pixel))\",\n  \"(?<! cu)bots?(?:\\\\b|_)\",\n  \"(?<!(?:lib))http\",\n  \"(?<![hg]m)score\",\n  \"(?<!cam)scan\",\n  \"@[a-z][\\\\w-]+\\\\.\",\n  \"\\\\(\\\\)\",\n  \"\\\\.com\\\\b\",\n  \"\\\\btime/\",\n  \"\\\\|\",\n  \"^<\",\n  \"^[\\\\w \\\\.\\\\-\\\\(?:\\\\):%]+(?:/v?\\\\d+(?:\\\\.\\\\d+)?(?:\\\\.\\\\d{1,10})*?)?(?:,|$)\",\n  \"^[^ ]{50,}$\",\n  \"^\\\\d+\\\\b\",\n  \"^\\\\w*search\\\\b\",\n  \"^\\\\w+/[\\\\w\\\\(\\\\)]*$\",\n  \"^active\",\n  \"^ad muncher\",\n  \"^amaya\",\n  \"^avsdevicesdk/\",\n  \"^biglotron\",\n  \"^bot\",\n  \"^bw/\",\n  \"^clamav[ /]\",\n  \"^client/\",\n  \"^cobweb/\",\n  \"^custom\",\n  \"^ddg[_-]android\",\n  \"^discourse\",\n  \"^dispatch/\\\\d\",\n  \"^downcast/\",\n  \"^duckduckgo\",\n  \"^email\",\n  \"^facebook\",\n  \"^getright/\",\n  \"^gozilla/\",\n  \"^hobbit\",\n  \"^hotzonu\",\n  \"^hwcdn/\",\n  \"^igetter/\",\n  \"^jeode/\",\n  \"^jetty/\",\n  \"^jigsaw\",\n  \"^microsoft bits\",\n  \"^movabletype\",\n  \"^mozilla/\\\\d\\\\.\\\\d\\\\s[\\\\w\\\\.-]+$\",\n  \"^mozilla/\\\\d\\\\.\\\\d\\\\s\\\\(compatible;?(?:\\\\s\\\\w+\\\\/\\\\d+\\\\.\\\\d+)?\\\\)$\",\n  \"^navermailapp\",\n  \"^netsurf\",\n  \"^offline\",\n  \"^openai/\",\n  \"^owler\",\n  \"^php\",\n  \"^postman\",\n  \"^python\",\n  \"^rank\",\n  \"^read\",\n  \"^reed\",\n  \"^rest\",\n  \"^rss\",\n  \"^snapchat\",\n  \"^space bison\",\n  \"^svn\",\n  \"^swcd \",\n  \"^taringa\",\n  \"^thumbor/\",\n  \"^track\",\n  \"^w3c\",\n  \"^webbandit/\",\n  \"^webcopier\",\n  \"^wget\",\n  \"^whatsapp\",\n  \"^wordpress\",\n  \"^xenu link sleuth\",\n  \"^yahoo\",\n  \"^yandex\",\n  \"^zdm/\\\\d\",\n  \"^zoom marketplace/\",\n  \"^{{.*}}$\",\n  \"analyzer\",\n  \"archive\",\n  \"ask jeeves/teoma\",\n  \"audit\",\n  \"bit\\\\.ly/\",\n  \"bluecoat drtr\",\n  \"browsex\",\n  \"burpcollaborator\",\n  \"capture\",\n  \"catch\",\n  \"check\\\\b\",\n  \"checker\",\n  \"chrome-lighthouse\",\n  \"chromeframe\",\n  \"classifier\",\n  \"cloudflare\",\n  \"convertify\",\n  \"crawl\",\n  \"cypress/\",\n  \"dareboost\",\n  \"datanyze\",\n  \"dejaclick\",\n  \"detect\",\n  \"dmbrowser\",\n  \"download\",\n  \"evc-batch/\",\n  \"exaleadcloudview\",\n  \"feed\",\n  \"firephp\",\n  \"functionize\",\n  \"gomezagent\",\n  \"grab\",\n  \"headless\",\n  \"httrack\",\n  \"hubspot marketing grader\",\n  \"hydra\",\n  \"ibisbrowser\",\n  \"infrawatch\",\n  \"insight\",\n  \"inspect\",\n  \"iplabel\",\n  \"ips-agent\",\n  \"java(?!;)\",\n  \"library\",\n  \"linkcheck\",\n  \"mail\\\\.ru/\",\n  \"manager\",\n  \"measure\",\n  \"neustar wpm\",\n  \"node\",\n  \"nutch\",\n  \"offbyone\",\n  \"onetrust\",\n  \"optimize\",\n  \"pageburst\",\n  \"pagespeed\",\n  \"parser\",\n  \"perl\",\n  \"phantomjs\",\n  \"pingdom\",\n  \"powermarks\",\n  \"preview\",\n  \"proxy\",\n  \"ptst[ /]\\\\d\",\n  \"retriever\",\n  \"rexx;\",\n  \"rigor\",\n  \"rss\\\\b\",\n  \"scrape\",\n  \"server\",\n  \"sogou\",\n  \"sparkler/\",\n  \"speedcurve\",\n  \"spider\",\n  \"splash\",\n  \"statuscake\",\n  \"supercleaner\",\n  \"synapse\",\n  \"synthetic\",\n  \"tools\",\n  \"torrent\",\n  \"transcoder\",\n  \"url\",\n  \"validator\",\n  \"virtuoso\",\n  \"wappalyzer\",\n  \"webglance\",\n  \"webkit2png\",\n  \"whatcms/\",\n  \"xtate/\"\n];\n\n// src/pattern.ts\nvar fullPattern = \" daum[ /]| deusu/| yadirectfetcher|(?:^|[^g])news(?!sapphire)|(?<! (?:channel/|google/))google(?!(app|/google| pixel))|(?<! cu)bots?(?:\\\\b|_)|(?<!(?:lib))http|(?<![hg]m)score|(?<!cam)scan|@[a-z][\\\\w-]+\\\\.|\\\\(\\\\)|\\\\.com\\\\b|\\\\btime/|\\\\||^<|^[\\\\w \\\\.\\\\-\\\\(?:\\\\):%]+(?:/v?\\\\d+(?:\\\\.\\\\d+)?(?:\\\\.\\\\d{1,10})*?)?(?:,|$)|^[^ ]{50,}$|^\\\\d+\\\\b|^\\\\w*search\\\\b|^\\\\w+/[\\\\w\\\\(\\\\)]*$|^active|^ad muncher|^amaya|^avsdevicesdk/|^biglotron|^bot|^bw/|^clamav[ /]|^client/|^cobweb/|^custom|^ddg[_-]android|^discourse|^dispatch/\\\\d|^downcast/|^duckduckgo|^email|^facebook|^getright/|^gozilla/|^hobbit|^hotzonu|^hwcdn/|^igetter/|^jeode/|^jetty/|^jigsaw|^microsoft bits|^movabletype|^mozilla/\\\\d\\\\.\\\\d\\\\s[\\\\w\\\\.-]+$|^mozilla/\\\\d\\\\.\\\\d\\\\s\\\\(compatible;?(?:\\\\s\\\\w+\\\\/\\\\d+\\\\.\\\\d+)?\\\\)$|^navermailapp|^netsurf|^offline|^openai/|^owler|^php|^postman|^python|^rank|^read|^reed|^rest|^rss|^snapchat|^space bison|^svn|^swcd |^taringa|^thumbor/|^track|^w3c|^webbandit/|^webcopier|^wget|^whatsapp|^wordpress|^xenu link sleuth|^yahoo|^yandex|^zdm/\\\\d|^zoom marketplace/|^{{.*}}$|analyzer|archive|ask jeeves/teoma|audit|bit\\\\.ly/|bluecoat drtr|browsex|burpcollaborator|capture|catch|check\\\\b|checker|chrome-lighthouse|chromeframe|classifier|cloudflare|convertify|crawl|cypress/|dareboost|datanyze|dejaclick|detect|dmbrowser|download|evc-batch/|exaleadcloudview|feed|firephp|functionize|gomezagent|grab|headless|httrack|hubspot marketing grader|hydra|ibisbrowser|infrawatch|insight|inspect|iplabel|ips-agent|java(?!;)|library|linkcheck|mail\\\\.ru/|manager|measure|neustar wpm|node|nutch|offbyone|onetrust|optimize|pageburst|pagespeed|parser|perl|phantomjs|pingdom|powermarks|preview|proxy|ptst[ /]\\\\d|retriever|rexx;|rigor|rss\\\\b|scrape|server|sogou|sparkler/|speedcurve|spider|splash|statuscake|supercleaner|synapse|synthetic|tools|torrent|transcoder|url|validator|virtuoso|wappalyzer|webglance|webkit2png|whatcms/|xtate/\";\n\n// src/index.ts\nvar naivePattern = /bot|crawl|http|lighthouse|scan|search|spider/i;\nvar pattern;\nfunction getPattern() {\n  if (pattern instanceof RegExp) {\n    return pattern;\n  }\n  try {\n    pattern = new RegExp(fullPattern, \"i\");\n  } catch (error) {\n    pattern = naivePattern;\n  }\n  return pattern;\n}\nvar list = patterns_default;\nvar isbotNaive = (userAgent) => Boolean(userAgent) && naivePattern.test(userAgent);\nfunction isbot(userAgent) {\n  return Boolean(userAgent) && getPattern().test(userAgent);\n}\nvar createIsbot = (customPattern) => (userAgent) => Boolean(userAgent) && customPattern.test(userAgent);\nvar createIsbotFromList = (list2) => {\n  const pattern2 = new RegExp(list2.join(\"|\"), \"i\");\n  return (userAgent) => Boolean(userAgent) && pattern2.test(userAgent);\n};\nvar isbotMatch = (userAgent) => {\n  var _a, _b;\n  return (_b = (_a = userAgent == null ? void 0 : userAgent.match(getPattern())) == null ? void 0 : _a[0]) != null ? _b : null;\n};\nvar isbotMatches = (userAgent) => list.map((part) => {\n  var _a;\n  return (_a = userAgent == null ? void 0 : userAgent.match(new RegExp(part, \"i\"))) == null ? void 0 : _a[0];\n}).filter(Boolean);\nvar isbotPattern = (userAgent) => {\n  var _a;\n  return userAgent ? (_a = list.find((pattern2) => new RegExp(pattern2, \"i\").test(userAgent))) != null ? _a : null : null;\n};\nvar isbotPatterns = (userAgent) => userAgent ? list.filter((pattern2) => new RegExp(pattern2, \"i\").test(userAgent)) : [];\nexport {\n  createIsbot,\n  createIsbotFromList,\n  getPattern,\n  isbot,\n  isbotMatch,\n  isbotMatches,\n  isbotNaive,\n  isbotPattern,\n  isbotPatterns,\n  list\n};\n"], "mappings": ";;;AACA,IAAI,mBAAmB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,cAAc;AAGlB,IAAI,eAAe;AACnB,IAAI;AACJ,SAAS,aAAa;AACpB,MAAI,mBAAmB,QAAQ;AAC7B,WAAO;AAAA,EACT;AACA,MAAI;AACF,cAAU,IAAI,OAAO,aAAa,GAAG;AAAA,EACvC,SAAS,OAAO;AACd,cAAU;AAAA,EACZ;AACA,SAAO;AACT;AACA,IAAI,OAAO;AACX,IAAI,aAAa,CAAC,cAAc,QAAQ,SAAS,KAAK,aAAa,KAAK,SAAS;AACjF,SAAS,MAAM,WAAW;AACxB,SAAO,QAAQ,SAAS,KAAK,WAAW,EAAE,KAAK,SAAS;AAC1D;AACA,IAAI,cAAc,CAAC,kBAAkB,CAAC,cAAc,QAAQ,SAAS,KAAK,cAAc,KAAK,SAAS;AACtG,IAAI,sBAAsB,CAAC,UAAU;AACnC,QAAM,WAAW,IAAI,OAAO,MAAM,KAAK,GAAG,GAAG,GAAG;AAChD,SAAO,CAAC,cAAc,QAAQ,SAAS,KAAK,SAAS,KAAK,SAAS;AACrE;AACA,IAAI,aAAa,CAAC,cAAc;AAC9B,MAAI,IAAI;AACR,UAAQ,MAAM,KAAK,aAAa,OAAO,SAAS,UAAU,MAAM,WAAW,CAAC,MAAM,OAAO,SAAS,GAAG,CAAC,MAAM,OAAO,KAAK;AAC1H;AACA,IAAI,eAAe,CAAC,cAAc,KAAK,IAAI,CAAC,SAAS;AACnD,MAAI;AACJ,UAAQ,KAAK,aAAa,OAAO,SAAS,UAAU,MAAM,IAAI,OAAO,MAAM,GAAG,CAAC,MAAM,OAAO,SAAS,GAAG,CAAC;AAC3G,CAAC,EAAE,OAAO,OAAO;AACjB,IAAI,eAAe,CAAC,cAAc;AAChC,MAAI;AACJ,SAAO,aAAa,KAAK,KAAK,KAAK,CAAC,aAAa,IAAI,OAAO,UAAU,GAAG,EAAE,KAAK,SAAS,CAAC,MAAM,OAAO,KAAK,OAAO;AACrH;AACA,IAAI,gBAAgB,CAAC,cAAc,YAAY,KAAK,OAAO,CAAC,aAAa,IAAI,OAAO,UAAU,GAAG,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC;", "names": []}