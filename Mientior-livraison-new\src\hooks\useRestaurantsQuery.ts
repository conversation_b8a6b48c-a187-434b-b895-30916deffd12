import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useRef } from 'react';
import { restaurantService } from '../services/database';
import { realTimeService, RealtimeSubscription } from '../services/realTimeService';
import { queryKeys, realtimeIntervals } from '../providers/QueryProvider';
import { merchant, FiltresRecherche, Location, ResultatRecherche } from '../types';
import { useAuthStore } from '../store/authStore';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Cache keys for offline storage
const RESTAURANTS_CACHE_KEY = 'restaurants_cache';
const RESTAURANT_DETAIL_CACHE_KEY = 'restaurant_detail_cache';

interface UseRestaurantsOptions {
  location?: Location;
  filters?: FiltresRecherche;
  enabled?: boolean;
  refetchInterval?: number;
}

/**
 * Enhanced hook for restaurant data with React Query
 * Provides caching, offline support, and real-time updates
 */
export const useRestaurantsQuery = (options: UseRestaurantsOptions = {}) => {
  const { location, filters, enabled = true, refetchInterval } = options;
  const queryClient = useQueryClient();
  const { isAuthenticated } = useAuthStore();
  const realtimeSubscription = useRef<RealtimeSubscription | null>(null);

  // Main restaurants query with real-time updates
  const restaurantsQuery = useQuery({
    queryKey: queryKeys.restaurants.list(filters),
    queryFn: async () => {
      try {
        console.log('🏪 Fetching restaurants from Supabase database...', { location, filters });
        const restaurants = await restaurantService.getAll(location, filters);

        // Cache results for offline use
        await AsyncStorage.setItem(
          `${RESTAURANTS_CACHE_KEY}_${JSON.stringify(filters || {})}`,
          JSON.stringify(restaurants)
        );

        console.log('✅ Restaurants fetched successfully from database:', restaurants.length);
        return restaurants;
      } catch (error) {
        console.error('❌ Error fetching restaurants from database:', error);

        // Try to load from cache as fallback
        try {
          const cached = await AsyncStorage.getItem(
            `${RESTAURANTS_CACHE_KEY}_${JSON.stringify(filters || {})}`
          );
          if (cached) {
            console.log('📱 Loading restaurants from offline cache');
            return JSON.parse(cached) as merchant[];
          }
        } catch (cacheError) {
          console.error('Cache error:', cacheError);
        }

        throw error;
      }
    },
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchInterval: refetchInterval || realtimeIntervals.restaurantAvailability,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: (failureCount, error: any) => {
      // Don't retry if it's a network error and we have cached data
      if (failureCount >= 2) return false;
      return true;
    },
    meta: {
      errorMessage: 'Erreur lors du chargement des restaurants'
    }
  });

  // Set up real-time subscription for restaurant updates
  useEffect(() => {
    if (enabled && restaurantsQuery.data) {
      console.log('🔄 Setting up real-time subscription for restaurants...');

      realtimeSubscription.current = realTimeService.subscribeToRestaurants(
        (payload) => {
          console.log('🏪 Real-time restaurant update received:', payload);
          // Invalidate and refetch restaurants when changes occur
          queryClient.invalidateQueries({
            queryKey: queryKeys.restaurants.lists()
          });
        },
        filters?.type_service ? { business_type: filters.type_service } : undefined
      );
    }

    return () => {
      if (realtimeSubscription.current) {
        console.log('🔌 Cleaning up restaurant real-time subscription');
        realtimeSubscription.current.unsubscribe();
        realtimeSubscription.current = null;
      }
    };
  }, [enabled, restaurantsQuery.data, filters?.type_service, queryClient]);

  // Nearby restaurants query (location-based)
  const nearbyRestaurantsQuery = useQuery({
    queryKey: queryKeys.restaurants.nearby(location),
    queryFn: () => restaurantService.getAll(location, { ...filters, rayon_km: 10 }),
    enabled: enabled && !!location,
    staleTime: 10 * 60 * 1000, // 10 minutes for location-based data
    gcTime: 60 * 60 * 1000, // 1 hour
  });

  // Search restaurants mutation
  const searchMutation = useMutation({
    mutationFn: async (searchQuery: string): Promise<ResultatRecherche> => {
      console.log('🔍 Searching restaurants:', searchQuery);
      return await restaurantService.search(searchQuery, location);
    },
    onSuccess: (data) => {
      console.log('✅ Search completed:', data.total, 'results');
      // Cache search results
      queryClient.setQueryData(
        queryKeys.restaurants.search(data.merchant_profiles[0]?.nom || ''),
        data
      );
    },
    onError: (error) => {
      console.error('❌ Search error:', error);
    },
    meta: {
      errorMessage: 'Erreur lors de la recherche'
    }
  });

  // Refresh restaurants
  const refresh = useCallback(async () => {
    console.log('🔄 Refreshing restaurants...');
    await queryClient.invalidateQueries({ 
      queryKey: queryKeys.restaurants.lists() 
    });
    return restaurantsQuery.refetch();
  }, [queryClient, restaurantsQuery]);

  // Load more restaurants (pagination)
  const loadMore = useCallback(async () => {
    // Implementation for pagination would go here
    console.log('📄 Loading more restaurants...');
    // For now, just refetch
    return refresh();
  }, [refresh]);

  // Get restaurant by ID with caching
  const getRestaurantById = useCallback(async (id: string): Promise<merchant | null> => {
    try {
      // Check if restaurant is in current list
      const currentRestaurants = restaurantsQuery.data || [];
      const found = currentRestaurants.find(r => r.id === id);
      if (found) {
        return found;
      }

      // Check cache
      try {
        const cached = await AsyncStorage.getItem(`${RESTAURANT_DETAIL_CACHE_KEY}_${id}`);
        if (cached) {
          const restaurant = JSON.parse(cached) as merchant;
          // Update query cache
          queryClient.setQueryData(queryKeys.restaurants.detail(id), restaurant);
          return restaurant;
        }
      } catch (cacheError) {
        console.error('Cache error:', cacheError);
      }

      // Fetch from database
      const restaurant = await restaurantService.getById(id);
      if (restaurant) {
        // Cache the result
        await AsyncStorage.setItem(
          `${RESTAURANT_DETAIL_CACHE_KEY}_${id}`,
          JSON.stringify(restaurant)
        );
        // Update query cache
        queryClient.setQueryData(queryKeys.restaurants.detail(id), restaurant);
      }
      return restaurant;
    } catch (error) {
      console.error('Error getting restaurant by ID:', error);
      return null;
    }
  }, [restaurantsQuery.data, queryClient]);

  // Search restaurants
  const searchRestaurants = useCallback(async (query: string) => {
    if (!query.trim()) {
      return { merchant_profiles: [], produits: [], total: 0 };
    }
    return searchMutation.mutateAsync(query);
  }, [searchMutation]);

  // Filter restaurants locally (for immediate feedback)
  const filterRestaurants = useCallback((localFilters: Partial<FiltresRecherche>) => {
    const restaurants = restaurantsQuery.data || [];
    return restaurants.filter(restaurant => {
      if (localFilters.type_service && restaurant.type_merchant !== localFilters.type_service) {
        return false;
      }
      if (localFilters.note_min && restaurant.note_moyenne < localFilters.note_min) {
        return false;
      }
      if (localFilters.prix_max && restaurant.frais_livraison_base > localFilters.prix_max) {
        return false;
      }
      return true;
    });
  }, [restaurantsQuery.data]);

  // Get restaurants by type
  const getRestaurantsByType = useCallback((type: merchant['type_merchant']) => {
    return filterRestaurants({ type_service: type });
  }, [filterRestaurants]);

  // Get featured restaurants
  const getFeaturedRestaurants = useCallback(() => {
    const restaurants = restaurantsQuery.data || [];
    return restaurants
      .filter(r => r.note_moyenne >= 4.0)
      .sort((a, b) => b.note_moyenne - a.note_moyenne)
      .slice(0, 5);
  }, [restaurantsQuery.data]);

  // Get restaurants open now
  const getOpenRestaurants = useCallback(() => {
    const restaurants = restaurantsQuery.data || [];
    const now = new Date();
    const currentDay = now.toLocaleLowerCase().substring(0, 3); // 'lun', 'mar', etc.
    const currentTime = now.toTimeString().substring(0, 5); // 'HH:MM'

    return restaurants.filter(restaurant => {
      if (!restaurant.is_active) return false;
      
      const daySchedule = restaurant.heures_ouverture[currentDay];
      if (!daySchedule || !daySchedule.ouvert) return false;
      
      if (daySchedule.heures && daySchedule.heures.length > 0) {
        return daySchedule.heures.some(horaire => 
          currentTime >= horaire.ouverture && currentTime <= horaire.fermeture
        );
      }
      
      return true; // Default to open if no specific hours
    });
  }, [restaurantsQuery.data]);

  return {
    // Data
    restaurants: restaurantsQuery.data || [],
    nearbyRestaurants: nearbyRestaurantsQuery.data || [],
    
    // Loading states
    loading: restaurantsQuery.isLoading,
    refreshing: restaurantsQuery.isFetching && !restaurantsQuery.isLoading,
    searching: searchMutation.isPending,
    loadingNearby: nearbyRestaurantsQuery.isLoading,
    
    // Error states
    error: restaurantsQuery.error?.message || null,
    searchError: searchMutation.error?.message || null,
    
    // Actions
    refresh,
    loadMore,
    searchRestaurants,
    getRestaurantById,
    
    // Filters and utilities
    filterRestaurants,
    getRestaurantsByType,
    getFeaturedRestaurants,
    getOpenRestaurants,
    
    // Query info
    isStale: restaurantsQuery.isStale,
    isFetching: restaurantsQuery.isFetching,
    dataUpdatedAt: restaurantsQuery.dataUpdatedAt,
    
    // Search results
    searchResults: searchMutation.data,
  };
};

/**
 * Hook for a single restaurant detail
 */
export const useRestaurantQuery = (restaurantId: string, enabled = true) => {
  const queryClient = useQueryClient();

  const restaurantQuery = useQuery({
    queryKey: queryKeys.restaurants.detail(restaurantId),
    queryFn: async () => {
      console.log('🏪 Fetching restaurant detail:', restaurantId);
      const restaurant = await restaurantService.getById(restaurantId);
      
      if (restaurant) {
        // Cache for offline use
        await AsyncStorage.setItem(
          `${RESTAURANT_DETAIL_CACHE_KEY}_${restaurantId}`,
          JSON.stringify(restaurant)
        );
      }
      
      return restaurant;
    },
    enabled: enabled && !!restaurantId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
    retry: 2,
  });

  const refresh = useCallback(async () => {
    return restaurantQuery.refetch();
  }, [restaurantQuery]);

  return {
    restaurant: restaurantQuery.data,
    loading: restaurantQuery.isLoading,
    error: restaurantQuery.error?.message || null,
    refresh,
    isStale: restaurantQuery.isStale,
  };
};
