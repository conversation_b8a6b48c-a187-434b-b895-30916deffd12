{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/fs/promises.mjs"], "sourcesContent": ["import { notImplemented } from \"../../../_internal/utils.mjs\";\nexport const access = /* @__PURE__ */ notImplemented(\"fs.access\");\nexport const copyFile = /* @__PURE__ */ notImplemented(\"fs.copyFile\");\nexport const cp = /* @__PURE__ */ notImplemented(\"fs.cp\");\nexport const open = /* @__PURE__ */ notImplemented(\"fs.open\");\nexport const opendir = /* @__PURE__ */ notImplemented(\"fs.opendir\");\nexport const rename = /* @__PURE__ */ notImplemented(\"fs.rename\");\nexport const truncate = /* @__PURE__ */ notImplemented(\"fs.truncate\");\nexport const rm = /* @__PURE__ */ notImplemented(\"fs.rm\");\nexport const rmdir = /* @__PURE__ */ notImplemented(\"fs.rmdir\");\nexport const mkdir = /* @__PURE__ */ notImplemented(\"fs.mkdir\");\nexport const readdir = /* @__PURE__ */ notImplemented(\"fs.readdir\");\nexport const readlink = /* @__PURE__ */ notImplemented(\"fs.readlink\");\nexport const symlink = /* @__PURE__ */ notImplemented(\"fs.symlink\");\nexport const lstat = /* @__PURE__ */ notImplemented(\"fs.lstat\");\nexport const stat = /* @__PURE__ */ notImplemented(\"fs.stat\");\nexport const link = /* @__PURE__ */ notImplemented(\"fs.link\");\nexport const unlink = /* @__PURE__ */ notImplemented(\"fs.unlink\");\nexport const chmod = /* @__PURE__ */ notImplemented(\"fs.chmod\");\nexport const lchmod = /* @__PURE__ */ notImplemented(\"fs.lchmod\");\nexport const lchown = /* @__PURE__ */ notImplemented(\"fs.lchown\");\nexport const chown = /* @__PURE__ */ notImplemented(\"fs.chown\");\nexport const utimes = /* @__PURE__ */ notImplemented(\"fs.utimes\");\nexport const lutimes = /* @__PURE__ */ notImplemented(\"fs.lutimes\");\nexport const realpath = /* @__PURE__ */ notImplemented(\"fs.realpath\");\nexport const mkdtemp = /* @__PURE__ */ notImplemented(\"fs.mkdtemp\");\nexport const writeFile = /* @__PURE__ */ notImplemented(\"fs.writeFile\");\nexport const appendFile = /* @__PURE__ */ notImplemented(\"fs.appendFile\");\nexport const readFile = /* @__PURE__ */ notImplemented(\"fs.readFile\");\nexport const watch = /* @__PURE__ */ notImplemented(\"fs.watch\");\nexport const statfs = /* @__PURE__ */ notImplemented(\"fs.statfs\");\nexport const glob = /* @__PURE__ */ notImplemented(\"fs.glob\");\n"], "mappings": ";;;;;AACO,IAAM,SAAyB,eAAe,WAAW;AACzD,IAAM,WAA2B,eAAe,aAAa;AAC7D,IAAM,KAAqB,eAAe,OAAO;AACjD,IAAM,OAAuB,eAAe,SAAS;AACrD,IAAM,UAA0B,eAAe,YAAY;AAC3D,IAAM,SAAyB,eAAe,WAAW;AACzD,IAAM,WAA2B,eAAe,aAAa;AAC7D,IAAM,KAAqB,eAAe,OAAO;AACjD,IAAM,QAAwB,eAAe,UAAU;AACvD,IAAM,QAAwB,eAAe,UAAU;AACvD,IAAM,UAA0B,eAAe,YAAY;AAC3D,IAAM,WAA2B,eAAe,aAAa;AAC7D,IAAM,UAA0B,eAAe,YAAY;AAC3D,IAAM,QAAwB,eAAe,UAAU;AACvD,IAAM,OAAuB,eAAe,SAAS;AACrD,IAAM,OAAuB,eAAe,SAAS;AACrD,IAAM,SAAyB,eAAe,WAAW;AACzD,IAAM,QAAwB,eAAe,UAAU;AACvD,IAAM,SAAyB,eAAe,WAAW;AACzD,IAAM,SAAyB,eAAe,WAAW;AACzD,IAAM,QAAwB,eAAe,UAAU;AACvD,IAAM,SAAyB,eAAe,WAAW;AACzD,IAAM,UAA0B,eAAe,YAAY;AAC3D,IAAM,WAA2B,eAAe,aAAa;AAC7D,IAAM,UAA0B,eAAe,YAAY;AAC3D,IAAM,YAA4B,eAAe,cAAc;AAC/D,IAAM,aAA6B,eAAe,eAAe;AACjE,IAAM,WAA2B,eAAe,aAAa;AAC7D,IAAM,QAAwB,eAAe,UAAU;AACvD,IAAM,SAAyB,eAAe,WAAW;AACzD,IAAM,OAAuB,eAAe,SAAS;", "names": []}