{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/punycode/punycode.mjs", "../../unenv/dist/runtime/node/punycode.mjs"], "sourcesContent": ["/**\nCopyright <PERSON> <https://mathiasbynens.be/>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n/** Highest positive signed 32-bit float value */\nconst maxInt = **********;\n/** Bootstring parameters */\nconst base = 36;\nconst tMin = 1;\nconst tMax = 26;\nconst skew = 38;\nconst damp = 700;\nconst initialBias = 72;\nconst initialN = 128;\nconst delimiter = \"-\";\n/** Regular expressions */\nconst regexPunycode = /^xn--/;\nconst regexNonASCII = /[^\\0-\\u007F]/;\nconst regexSeparators = /[.\\u3002\\uFF0E\\uFF61]/g;\n/** Error messages */\nconst errors = {\n\toverflow: \"Overflow: input needs wider integers to process\",\n\t\"not-basic\": \"Illegal input >= 0x80 (not a basic code point)\",\n\t\"invalid-input\": \"Invalid input\"\n};\n/** Convenience shortcuts */\nconst baseMinusTMin = base - tMin;\nconst floor = Math.floor;\nconst stringFromCharCode = String.fromCharCode;\n/**\n* A generic error utility function.\n* @private\n* @param {String} type The error type.\n* @returns {Error} Throws a `RangeError` with the applicable error message.\n*/\nfunction error(type) {\n\tthrow new RangeError(errors[type]);\n}\n/**\n* A generic `Array#map` utility function.\n* @private\n* @param {Array} array The array to iterate over.\n* @param {Function} callback The function that gets called for every array\n* item.\n* @returns {Array} A new array of values returned by the callback function.\n*/\nfunction map(array, callback) {\n\tconst result = [];\n\tlet length = array.length;\n\twhile (length--) {\n\t\tresult[length] = callback(array[length]);\n\t}\n\treturn result;\n}\n/**\n* A simple `Array#map`-like wrapper to work with domain name strings or email\n* addresses.\n* @private\n* @param {String} domain The domain name or email address.\n* @param {Function} callback The function that gets called for every\n* character.\n* @returns {String} A new string of characters returned by the callback\n* function.\n*/\nfunction mapDomain(domain, callback) {\n\tconst parts = domain.split(\"@\");\n\tlet result = \"\";\n\tif (parts.length > 1) {\n\t\tresult = parts[0] + \"@\";\n\t\tdomain = parts[1];\n\t}\n\tdomain = domain.replace(regexSeparators, \".\");\n\tconst labels = domain.split(\".\");\n\tconst encoded = map(labels, callback).join(\".\");\n\treturn result + encoded;\n}\n/**\n* Creates an array containing the numeric code points of each Unicode\n* character in the string. While JavaScript uses UCS-2 internally,\n* this function will convert a pair of surrogate halves (each of which\n* UCS-2 exposes as separate characters) into a single code point,\n* matching UTF-16.\n* @see `punycode.ucs2.encode`\n* @see <https://mathiasbynens.be/notes/javascript-encoding>\n* @memberOf punycode.ucs2\n* @name decode\n* @param {String} string The Unicode input string (UCS-2).\n* @returns {Array} The new array of code points.\n*/\nfunction ucs2decode(string) {\n\tconst output = [];\n\tlet counter = 0;\n\tconst length = string.length;\n\twhile (counter < length) {\n\t\tconst value = string.charCodeAt(counter++);\n\t\tif (value >= 55296 && value <= 56319 && counter < length) {\n\t\t\tconst extra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 64512) == 56320) {\n\t\t\t\toutput.push(((value & 1023) << 10) + (extra & 1023) + 65536);\n\t\t\t} else {\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n/**\n* Creates a string based on an array of numeric code points.\n* @see `punycode.ucs2.decode`\n* @memberOf punycode.ucs2\n* @name encode\n* @param {Array} codePoints The array of numeric code points.\n* @returns {String} The new Unicode string (UCS-2).\n*/\nconst ucs2encode = (codePoints) => String.fromCodePoint(...codePoints);\n/**\n* Converts a basic code point into a digit/integer.\n* @see `digitToBasic()`\n* @private\n* @param {Number} codePoint The basic numeric code point value.\n* @returns {Number} The numeric value of a basic code point (for use in\n* representing integers) in the range `0` to `base - 1`, or `base` if\n* the code point does not represent a value.\n*/\nconst basicToDigit = function(codePoint) {\n\tif (codePoint >= 48 && codePoint < 58) {\n\t\treturn 26 + (codePoint - 48);\n\t}\n\tif (codePoint >= 65 && codePoint < 91) {\n\t\treturn codePoint - 65;\n\t}\n\tif (codePoint >= 97 && codePoint < 123) {\n\t\treturn codePoint - 97;\n\t}\n\treturn base;\n};\n/**\n* Converts a digit/integer into a basic code point.\n* @see `basicToDigit()`\n* @private\n* @param {Number} digit The numeric value of a basic code point.\n* @returns {Number} The basic code point whose value (when used for\n* representing integers) is `digit`, which needs to be in the range\n* `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n* used; else, the lowercase form is used. The behavior is undefined\n* if `flag` is non-zero and `digit` has no uppercase form.\n*/\nconst digitToBasic = function(digit, flag) {\n\treturn digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n};\n/**\n* Bias adaptation function as per section 3.4 of RFC 3492.\n* https://tools.ietf.org/html/rfc3492#section-3.4\n* @private\n*/\nconst adapt = function(delta, numPoints, firstTime) {\n\tlet k = 0;\n\tdelta = firstTime ? floor(delta / damp) : delta >> 1;\n\tdelta += floor(delta / numPoints);\n\tfor (; delta > baseMinusTMin * tMax >> 1; k += base) {\n\t\tdelta = floor(delta / baseMinusTMin);\n\t}\n\treturn floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n};\n/**\n* Converts a Punycode string of ASCII-only symbols to a string of Unicode\n* symbols.\n* @memberOf punycode\n* @param {String} input The Punycode string of ASCII-only symbols.\n* @returns {String} The resulting string of Unicode symbols.\n*/\nconst decode = function(input) {\n\tconst output = [];\n\tconst inputLength = input.length;\n\tlet i = 0;\n\tlet n = initialN;\n\tlet bias = initialBias;\n\tlet basic = input.lastIndexOf(delimiter);\n\tif (basic < 0) {\n\t\tbasic = 0;\n\t}\n\tfor (let j = 0; j < basic; ++j) {\n\t\tif (input.charCodeAt(j) >= 128) {\n\t\t\terror(\"not-basic\");\n\t\t}\n\t\toutput.push(input.charCodeAt(j));\n\t}\n\tfor (let index = basic > 0 ? basic + 1 : 0; index < inputLength;) {\n\t\tconst oldi = i;\n\t\tfor (let w = 1, k = base;; k += base) {\n\t\t\tif (index >= inputLength) {\n\t\t\t\terror(\"invalid-input\");\n\t\t\t}\n\t\t\tconst digit = basicToDigit(input.charCodeAt(index++));\n\t\t\tif (digit >= base) {\n\t\t\t\terror(\"invalid-input\");\n\t\t\t}\n\t\t\tif (digit > floor((maxInt - i) / w)) {\n\t\t\t\terror(\"overflow\");\n\t\t\t}\n\t\t\ti += digit * w;\n\t\t\tconst t = k <= bias ? tMin : k >= bias + tMax ? tMax : k - bias;\n\t\t\tif (digit < t) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tconst baseMinusT = base - t;\n\t\t\tif (w > floor(maxInt / baseMinusT)) {\n\t\t\t\terror(\"overflow\");\n\t\t\t}\n\t\t\tw *= baseMinusT;\n\t\t}\n\t\tconst out = output.length + 1;\n\t\tbias = adapt(i - oldi, out, oldi === 0);\n\t\tif (floor(i / out) > maxInt - n) {\n\t\t\terror(\"overflow\");\n\t\t}\n\t\tn += floor(i / out);\n\t\ti %= out;\n\t\toutput.splice(i++, 0, n);\n\t}\n\treturn String.fromCodePoint(...output);\n};\n/**\n* Converts a string of Unicode symbols (e.g. a domain name label) to a\n* Punycode string of ASCII-only symbols.\n* @memberOf punycode\n* @param {String} input The string of Unicode symbols.\n* @returns {String} The resulting Punycode string of ASCII-only symbols.\n*/\nconst encode = function(_input) {\n\tconst output = [];\n\tconst input = ucs2decode(_input);\n\tconst inputLength = input.length;\n\tlet n = initialN;\n\tlet delta = 0;\n\tlet bias = initialBias;\n\tfor (const currentValue of input) {\n\t\tif (currentValue < 128) {\n\t\t\toutput.push(stringFromCharCode(currentValue));\n\t\t}\n\t}\n\tconst basicLength = output.length;\n\tlet handledCPCount = basicLength;\n\tif (basicLength) {\n\t\toutput.push(delimiter);\n\t}\n\twhile (handledCPCount < inputLength) {\n\t\tlet m = maxInt;\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue >= n && currentValue < m) {\n\t\t\t\tm = currentValue;\n\t\t\t}\n\t\t}\n\t\tconst handledCPCountPlusOne = handledCPCount + 1;\n\t\tif (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n\t\t\terror(\"overflow\");\n\t\t}\n\t\tdelta += (m - n) * handledCPCountPlusOne;\n\t\tn = m;\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue < n && ++delta > maxInt) {\n\t\t\t\terror(\"overflow\");\n\t\t\t}\n\t\t\tif (currentValue === n) {\n\t\t\t\tlet q = delta;\n\t\t\t\tfor (let k = base;; k += base) {\n\t\t\t\t\tconst t = k <= bias ? tMin : k >= bias + tMax ? tMax : k - bias;\n\t\t\t\t\tif (q < t) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tconst qMinusT = q - t;\n\t\t\t\t\tconst baseMinusT = base - t;\n\t\t\t\t\toutput.push(stringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0)));\n\t\t\t\t\tq = floor(qMinusT / baseMinusT);\n\t\t\t\t}\n\t\t\t\toutput.push(stringFromCharCode(digitToBasic(q, 0)));\n\t\t\t\tbias = adapt(delta, handledCPCountPlusOne, handledCPCount === basicLength);\n\t\t\t\tdelta = 0;\n\t\t\t\t++handledCPCount;\n\t\t\t}\n\t\t}\n\t\t++delta;\n\t\t++n;\n\t}\n\treturn output.join(\"\");\n};\n/**\n* Converts a Punycode string representing a domain name or an email address\n* to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n* it doesn't matter if you call it on a string that has already been\n* converted to Unicode.\n* @memberOf punycode\n* @param {String} input The Punycoded domain name or email address to\n* convert to Unicode.\n* @returns {String} The Unicode representation of the given Punycode\n* string.\n*/\nconst toUnicode = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexPunycode.test(string) ? decode(string.slice(4).toLowerCase()) : string;\n\t});\n};\n/**\n* Converts a Unicode string representing a domain name or an email address to\n* Punycode. Only the non-ASCII parts of the domain name will be converted,\n* i.e. it doesn't matter if you call it with a domain that's already in\n* ASCII.\n* @memberOf punycode\n* @param {String} input The domain name or email address to convert, as a\n* Unicode string.\n* @returns {String} The Punycode representation of the given domain name or\n* email address.\n*/\nconst toASCII = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexNonASCII.test(string) ? \"xn--\" + encode(string) : string;\n\t});\n};\n/**\n* A string representing the current Punycode.js version number.\n* @memberOf punycode\n* @type String\n*/\nconst version = \"2.3.1\";\n/**\n* An object of methods to convert from JavaScript's internal character\n* representation (UCS-2) to Unicode code points, and back.\n* @see <https://mathiasbynens.be/notes/javascript-encoding>\n* @memberOf punycode\n* @type Object\n*/\nconst ucs2 = {\n\tdecode: ucs2decode,\n\tencode: ucs2encode\n};\n/** Define the public API */\nconst punycode = {\n\tversion,\n\tucs2,\n\tdecode,\n\tencode,\n\ttoASCII,\n\ttoUnicode\n};\nexport { version, ucs2, decode, encode, toASCII, toUnicode };\nexport default punycode;\n", "import _punycode from \"./internal/punycode/punycode.mjs\";\nexport * from \"./internal/punycode/punycode.mjs\";\nexport default _punycode;\n"], "mappings": ";;;AAuBA,IAAM,SAAS;AAEf,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,cAAc;AACpB,IAAM,WAAW;AACjB,IAAM,YAAY;AAElB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AAExB,IAAM,SAAS;AAAA,EACd,UAAU;AAAA,EACV,aAAa;AAAA,EACb,iBAAiB;AAClB;AAEA,IAAM,gBAAgB,OAAO;AAC7B,IAAM,QAAQ,KAAK;AACnB,IAAM,qBAAqB,OAAO;AAOlC,SAAS,MAAM,MAAM;AACpB,QAAM,IAAI,WAAW,OAAO,IAAI,CAAC;AAClC;AASA,SAAS,IAAI,OAAO,UAAU;AAC7B,QAAM,SAAS,CAAC;AAChB,MAAI,SAAS,MAAM;AACnB,SAAO,UAAU;AAChB,WAAO,MAAM,IAAI,SAAS,MAAM,MAAM,CAAC;AAAA,EACxC;AACA,SAAO;AACR;AAWA,SAAS,UAAU,QAAQ,UAAU;AACpC,QAAM,QAAQ,OAAO,MAAM,GAAG;AAC9B,MAAI,SAAS;AACb,MAAI,MAAM,SAAS,GAAG;AACrB,aAAS,MAAM,CAAC,IAAI;AACpB,aAAS,MAAM,CAAC;AAAA,EACjB;AACA,WAAS,OAAO,QAAQ,iBAAiB,GAAG;AAC5C,QAAM,SAAS,OAAO,MAAM,GAAG;AAC/B,QAAM,UAAU,IAAI,QAAQ,QAAQ,EAAE,KAAK,GAAG;AAC9C,SAAO,SAAS;AACjB;AAcA,SAAS,WAAW,QAAQ;AAC3B,QAAM,SAAS,CAAC;AAChB,MAAI,UAAU;AACd,QAAM,SAAS,OAAO;AACtB,SAAO,UAAU,QAAQ;AACxB,UAAM,QAAQ,OAAO,WAAW,SAAS;AACzC,QAAI,SAAS,SAAS,SAAS,SAAS,UAAU,QAAQ;AACzD,YAAM,QAAQ,OAAO,WAAW,SAAS;AACzC,WAAK,QAAQ,UAAU,OAAO;AAC7B,eAAO,OAAO,QAAQ,SAAS,OAAO,QAAQ,QAAQ,KAAK;AAAA,MAC5D,OAAO;AACN,eAAO,KAAK,KAAK;AACjB;AAAA,MACD;AAAA,IACD,OAAO;AACN,aAAO,KAAK,KAAK;AAAA,IAClB;AAAA,EACD;AACA,SAAO;AACR;AASA,IAAM,aAAa,CAAC,eAAe,OAAO,cAAc,GAAG,UAAU;AAUrE,IAAM,eAAe,SAAS,WAAW;AACxC,MAAI,aAAa,MAAM,YAAY,IAAI;AACtC,WAAO,MAAM,YAAY;AAAA,EAC1B;AACA,MAAI,aAAa,MAAM,YAAY,IAAI;AACtC,WAAO,YAAY;AAAA,EACpB;AACA,MAAI,aAAa,MAAM,YAAY,KAAK;AACvC,WAAO,YAAY;AAAA,EACpB;AACA,SAAO;AACR;AAYA,IAAM,eAAe,SAAS,OAAO,MAAM;AAC1C,SAAO,QAAQ,KAAK,MAAM,QAAQ,QAAQ,QAAQ,MAAM;AACzD;AAMA,IAAM,QAAQ,SAAS,OAAO,WAAW,WAAW;AACnD,MAAI,IAAI;AACR,UAAQ,YAAY,MAAM,QAAQ,IAAI,IAAI,SAAS;AACnD,WAAS,MAAM,QAAQ,SAAS;AAChC,SAAO,QAAQ,gBAAgB,QAAQ,GAAG,KAAK,MAAM;AACpD,YAAQ,MAAM,QAAQ,aAAa;AAAA,EACpC;AACA,SAAO,MAAM,KAAK,gBAAgB,KAAK,SAAS,QAAQ,KAAK;AAC9D;AAQA,IAAM,SAAS,SAAS,OAAO;AAC9B,QAAM,SAAS,CAAC;AAChB,QAAM,cAAc,MAAM;AAC1B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,OAAO;AACX,MAAI,QAAQ,MAAM,YAAY,SAAS;AACvC,MAAI,QAAQ,GAAG;AACd,YAAQ;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC/B,QAAI,MAAM,WAAW,CAAC,KAAK,KAAK;AAC/B,YAAM,WAAW;AAAA,IAClB;AACA,WAAO,KAAK,MAAM,WAAW,CAAC,CAAC;AAAA,EAChC;AACA,WAAS,QAAQ,QAAQ,IAAI,QAAQ,IAAI,GAAG,QAAQ,eAAc;AACjE,UAAM,OAAO;AACb,aAAS,IAAI,GAAG,IAAI,QAAO,KAAK,MAAM;AACrC,UAAI,SAAS,aAAa;AACzB,cAAM,eAAe;AAAA,MACtB;AACA,YAAM,QAAQ,aAAa,MAAM,WAAW,OAAO,CAAC;AACpD,UAAI,SAAS,MAAM;AAClB,cAAM,eAAe;AAAA,MACtB;AACA,UAAI,QAAQ,OAAO,SAAS,KAAK,CAAC,GAAG;AACpC,cAAM,UAAU;AAAA,MACjB;AACA,WAAK,QAAQ;AACb,YAAM,IAAI,KAAK,OAAO,OAAO,KAAK,OAAO,OAAO,OAAO,IAAI;AAC3D,UAAI,QAAQ,GAAG;AACd;AAAA,MACD;AACA,YAAM,aAAa,OAAO;AAC1B,UAAI,IAAI,MAAM,SAAS,UAAU,GAAG;AACnC,cAAM,UAAU;AAAA,MACjB;AACA,WAAK;AAAA,IACN;AACA,UAAM,MAAM,OAAO,SAAS;AAC5B,WAAO,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC;AACtC,QAAI,MAAM,IAAI,GAAG,IAAI,SAAS,GAAG;AAChC,YAAM,UAAU;AAAA,IACjB;AACA,SAAK,MAAM,IAAI,GAAG;AAClB,SAAK;AACL,WAAO,OAAO,KAAK,GAAG,CAAC;AAAA,EACxB;AACA,SAAO,OAAO,cAAc,GAAG,MAAM;AACtC;AAQA,IAAM,SAAS,SAAS,QAAQ;AAC/B,QAAM,SAAS,CAAC;AAChB,QAAM,QAAQ,WAAW,MAAM;AAC/B,QAAM,cAAc,MAAM;AAC1B,MAAI,IAAI;AACR,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,aAAW,gBAAgB,OAAO;AACjC,QAAI,eAAe,KAAK;AACvB,aAAO,KAAK,mBAAmB,YAAY,CAAC;AAAA,IAC7C;AAAA,EACD;AACA,QAAM,cAAc,OAAO;AAC3B,MAAI,iBAAiB;AACrB,MAAI,aAAa;AAChB,WAAO,KAAK,SAAS;AAAA,EACtB;AACA,SAAO,iBAAiB,aAAa;AACpC,QAAI,IAAI;AACR,eAAW,gBAAgB,OAAO;AACjC,UAAI,gBAAgB,KAAK,eAAe,GAAG;AAC1C,YAAI;AAAA,MACL;AAAA,IACD;AACA,UAAM,wBAAwB,iBAAiB;AAC/C,QAAI,IAAI,IAAI,OAAO,SAAS,SAAS,qBAAqB,GAAG;AAC5D,YAAM,UAAU;AAAA,IACjB;AACA,cAAU,IAAI,KAAK;AACnB,QAAI;AACJ,eAAW,gBAAgB,OAAO;AACjC,UAAI,eAAe,KAAK,EAAE,QAAQ,QAAQ;AACzC,cAAM,UAAU;AAAA,MACjB;AACA,UAAI,iBAAiB,GAAG;AACvB,YAAI,IAAI;AACR,iBAAS,IAAI,QAAO,KAAK,MAAM;AAC9B,gBAAM,IAAI,KAAK,OAAO,OAAO,KAAK,OAAO,OAAO,OAAO,IAAI;AAC3D,cAAI,IAAI,GAAG;AACV;AAAA,UACD;AACA,gBAAM,UAAU,IAAI;AACpB,gBAAM,aAAa,OAAO;AAC1B,iBAAO,KAAK,mBAAmB,aAAa,IAAI,UAAU,YAAY,CAAC,CAAC,CAAC;AACzE,cAAI,MAAM,UAAU,UAAU;AAAA,QAC/B;AACA,eAAO,KAAK,mBAAmB,aAAa,GAAG,CAAC,CAAC,CAAC;AAClD,eAAO,MAAM,OAAO,uBAAuB,mBAAmB,WAAW;AACzE,gBAAQ;AACR,UAAE;AAAA,MACH;AAAA,IACD;AACA,MAAE;AACF,MAAE;AAAA,EACH;AACA,SAAO,OAAO,KAAK,EAAE;AACtB;AAYA,IAAM,YAAY,SAAS,OAAO;AACjC,SAAO,UAAU,OAAO,SAAS,QAAQ;AACxC,WAAO,cAAc,KAAK,MAAM,IAAI,OAAO,OAAO,MAAM,CAAC,EAAE,YAAY,CAAC,IAAI;AAAA,EAC7E,CAAC;AACF;AAYA,IAAM,UAAU,SAAS,OAAO;AAC/B,SAAO,UAAU,OAAO,SAAS,QAAQ;AACxC,WAAO,cAAc,KAAK,MAAM,IAAI,SAAS,OAAO,MAAM,IAAI;AAAA,EAC/D,CAAC;AACF;AAMA,IAAM,UAAU;AAQhB,IAAM,OAAO;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AACT;AAEA,IAAM,WAAW;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEA,IAAO,mBAAQ;;;AC5Wf,IAAOA,oBAAQ;", "names": ["punycode_default"]}