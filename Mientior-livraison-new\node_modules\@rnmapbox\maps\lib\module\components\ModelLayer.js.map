{"version": 3, "names": ["React", "NativeModules", "RNMBXModelLayerNativeComponent", "AbstractLayer", "jsx", "_jsx", "Mapbox", "RNMBXModule", "<PERSON><PERSON><PERSON><PERSON>", "defaultProps", "sourceID", "StyleSource", "DefaultSourceID", "render", "props", "baseProps", "sourceLayerID", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../../src", "sources": ["components/ModelLayer.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,cAAc;AAI5C,OAAOC,8BAA8B,MAAM,yCAAyC;AAEpF,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE5C,MAAMC,MAAM,GAAGL,aAAa,CAACM,WAAW;;AAExC;;AA+DA;;AAaA;AACA;AACA;AACA,MAAMC,UAAU,SAASL,aAAa,CAAyB;EAC7D,OAAOM,YAAY,GAAG;IACpBC,QAAQ,EAAEJ,MAAM,CAACK,WAAW,CAACC;EAC/B,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAMC,KAAK,GAAG;MACZ,GAAG,IAAI,CAACC,SAAS;MACjBC,aAAa,EAAE,IAAI,CAACF,KAAK,CAACE;IAC5B,CAAC;IACD,oBACEX,IAAA,CAACH,8BAA8B;MAACe,GAAG,EAAE,IAAI,CAACC,cAAe;MAAA,GAAKJ;IAAK,CAAG,CAAC;EAE3E;AACF;AAEA,eAAeN,UAAU", "ignoreList": []}