export declare const StyleTypes: {
    Constant: string;
    Color: string;
    Transition: string;
    Translation: string;
    Function: string;
    Image: string;
    Enum: string;
};
export declare function getStyleType(styleProp: keyof typeof styleExtras): string;
export declare const styleExtras: {
    iconTextFitPadding: {
        iosType: string;
    };
    iconOffset: {
        iosType: string;
    };
    textOffset: {
        iosType: string;
    };
    lineOffset: {
        iosType: string;
    };
    fillTranslate: {
        iosType: string;
    };
    lineTranslate: {
        iosType: string;
    };
    iconTranslate: {
        iosType: string;
    };
    textTranslate: {
        iosType: string;
    };
    circleTranslate: {
        iosType: string;
    };
    fillExtrusionTranslate: {
        iosType: string;
    };
};
//# sourceMappingURL=styleMap.d.ts.map