<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical|start"
    android:orientation="horizontal"
    android:paddingStart="?android:attr/dialogPreferredPadding"
    android:paddingTop="18dp"
    android:paddingEnd="?android:attr/dialogPreferredPadding"
    >

    <com.facebook.react.modules.dialog.DialogTitle
        android:id="@+id/alert_title"
        style="?android:attr/windowTitleStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:singleLine="true"
        android:textAlignment="viewStart"
        />

</LinearLayout>
