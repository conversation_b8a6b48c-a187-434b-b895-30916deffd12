{"version": 3, "names": ["React", "StyleSheet", "toJSONString", "isFunction", "checkRequiredProps", "makePoint", "RNMBXPointAnnotationNativeComponent", "NativeRNMBXPointAnnotationModule", "NativeBridgeComponent", "jsx", "_jsx", "NATIVE_MODULE_NAME", "styles", "create", "container", "alignItems", "justifyContent", "position", "PointAnnotation", "PureComponent", "defaultProps", "anchor", "x", "y", "draggable", "_nativeRef", "constructor", "props", "_onSelected", "bind", "_onDeselected", "_onDragStart", "_onDrag", "_onDragEnd", "_decodePayload", "payload", "JSON", "parse", "e", "onSelected", "nativeEvent", "onDese<PERSON>", "onDragStart", "onDrag", "onDragEnd", "_getCoordinate", "coordinate", "undefined", "refresh", "_runNativeMethod", "_setNativeRef", "nativeRef", "_runPendingNativeMethods", "render", "ref", "id", "title", "snippet", "selected", "style", "onMapboxPointAnnotationSelected", "onMapboxPointAnnotationDeselected", "onMapboxPointAnnotationDragStart", "onMapboxPointAnnotationDrag", "onMapboxPointAnnotationDragEnd", "children"], "sourceRoot": "../../../src", "sources": ["components/PointAnnotation.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAA0C,OAAO;AAC7D,SAASC,UAAU,QAAwB,cAAc;AAGzD,SAASC,YAAY,EAAEC,UAAU,QAAQ,UAAU;AACnD,OAAOC,kBAAkB,MAAM,6BAA6B;AAC5D,SAASC,SAAS,QAAQ,mBAAmB;AAG7C,OAAOC,mCAAmC,MAAM,8CAA8C;AAC9F,OAAOC,gCAAgC,MAAM,2CAA2C;AAExF,OAAOC,qBAAqB,MAA0B,yBAAyB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEhF,OAAO,MAAMC,kBAAkB,GAAG,sBAAsB;AAExD,MAAMC,MAAM,GAAGX,UAAU,CAACY,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AA4FF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASV,qBAAqB,CACjDR,KAAK,CAACmB,aAAa,EACnBZ,gCACF,CAAC,CAAC;EACA,OAAOa,YAAY,GAAG;IACpBC,MAAM,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC;IAC1BC,SAAS,EAAE;EACb,CAAC;EAEDC,UAAU,GAAoC,IAAI;EAElDC,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IACZvB,kBAAkB,CAAC,iBAAiB,EAAEuB,KAAK,EAAE,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IAClE,IAAI,CAACC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACE,YAAY,GAAG,IAAI,CAACA,YAAY,CAACF,IAAI,CAAC,IAAI,CAAC;IAChD,IAAI,CAACG,OAAO,GAAG,IAAI,CAACA,OAAO,CAACH,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACI,UAAU,GAAG,IAAI,CAACA,UAAU,CAACJ,IAAI,CAAC,IAAI,CAAC;EAC9C;EAEAK,cAAcA,CACZC,OAAuC,EAChB;IACvB;IACA;IACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,OAAOC,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;IAC5B,CAAC,MAAM;MACL,OAAOA,OAAO;IAChB;EACF;EAEAP,WAAWA,CAACU,CAAqD,EAAE;IACjE,IAAInC,UAAU,CAAC,IAAI,CAACwB,KAAK,CAACY,UAAU,CAAC,EAAE;MACrC,MAAMJ,OAAO,GAAG,IAAI,CAACD,cAAc,CAACI,CAAC,CAACE,WAAW,CAACL,OAAO,CAAC;MAC1D,IAAI,CAACR,KAAK,CAACY,UAAU,CAACJ,OAAO,CAAC;IAChC;EACF;EAEAL,aAAaA,CAACQ,CAAqD,EAAE;IACnE,IAAInC,UAAU,CAAC,IAAI,CAACwB,KAAK,CAACc,YAAY,CAAC,EAAE;MACvC,MAAMN,OAAO,GAAG,IAAI,CAACD,cAAc,CAACI,CAAC,CAACE,WAAW,CAACL,OAAO,CAAC;MAC1D,IAAI,CAACR,KAAK,CAACc,YAAY,CAACN,OAAO,CAAC;IAClC;EACF;EAEAJ,YAAYA,CAACO,CAAqD,EAAE;IAClE,IAAInC,UAAU,CAAC,IAAI,CAACwB,KAAK,CAACe,WAAW,CAAC,EAAE;MACtC,MAAMP,OAAO,GAAG,IAAI,CAACD,cAAc,CAACI,CAAC,CAACE,WAAW,CAACL,OAAO,CAAC;MAC1D,IAAI,CAACR,KAAK,CAACe,WAAW,CAACP,OAAO,CAAC;IACjC;EACF;EAEAH,OAAOA,CAACM,CAAqD,EAAE;IAC7D,IAAInC,UAAU,CAAC,IAAI,CAACwB,KAAK,CAACgB,MAAM,CAAC,EAAE;MACjC,MAAMR,OAAO,GAAG,IAAI,CAACD,cAAc,CAACI,CAAC,CAACE,WAAW,CAACL,OAAO,CAAC;MAC1D,IAAI,CAACR,KAAK,CAACgB,MAAM,CAACR,OAAO,CAAC;IAC5B;EACF;EAEAF,UAAUA,CAACK,CAAqD,EAAE;IAChE,IAAInC,UAAU,CAAC,IAAI,CAACwB,KAAK,CAACiB,SAAS,CAAC,EAAE;MACpC,MAAMT,OAAO,GAAG,IAAI,CAACD,cAAc,CAACI,CAAC,CAACE,WAAW,CAACL,OAAO,CAAC;MAC1D,IAAI,CAACR,KAAK,CAACiB,SAAS,CAACT,OAAO,CAAC;IAC/B;EACF;EAEAU,cAAcA,CAAA,EAAuB;IACnC,IAAI,CAAC,IAAI,CAAClB,KAAK,CAACmB,UAAU,EAAE;MAC1B,OAAOC,SAAS;IAClB;IACA,OAAO7C,YAAY,CAACG,SAAS,CAAC,IAAI,CAACsB,KAAK,CAACmB,UAAU,CAAC,CAAC;EACvD;;EAEA;AACF;AACA;AACA;AACA;EACEE,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACxB,UAAU,EAAE,EAAE,CAAC;EACvD;EAEAyB,aAAaA,CAACC,SAA0C,EAAE;IACxD,IAAI,CAAC1B,UAAU,GAAG0B,SAAS;IAC3B,KAAK,CAACC,wBAAwB,CAACD,SAAS,CAAC;EAC3C;EAEAE,MAAMA,CAAA,EAAG;IACP,MAAM1B,KAAK,GAAG;MACZ,GAAG,IAAI,CAACA,KAAK;MACb2B,GAAG,EAAGH,SAA0C,IAC9C,IAAI,CAACD,aAAa,CAACC,SAAS,CAAC;MAC/BI,EAAE,EAAE,IAAI,CAAC5B,KAAK,CAAC4B,EAAE;MACjBC,KAAK,EAAE,IAAI,CAAC7B,KAAK,CAAC6B,KAAK;MACvBC,OAAO,EAAE,IAAI,CAAC9B,KAAK,CAAC8B,OAAO;MAC3BpC,MAAM,EAAE,IAAI,CAACM,KAAK,CAACN,MAAM;MACzBqC,QAAQ,EAAE,IAAI,CAAC/B,KAAK,CAAC+B,QAAQ;MAC7BlC,SAAS,EAAE,IAAI,CAACG,KAAK,CAACH,SAAS;MAC/BmC,KAAK,EAAE,CAAC,IAAI,CAAChC,KAAK,CAACgC,KAAK,EAAE/C,MAAM,CAACE,SAAS,CAAC;MAC3C8C,+BAA+B,EAAE,IAAI,CAAChC,WAAW;MACjDiC,iCAAiC,EAAE,IAAI,CAAC/B,aAAa;MACrDgC,gCAAgC,EAAE,IAAI,CAAC/B,YAAY;MACnDgC,2BAA2B,EAAE,IAAI,CAAC/B,OAAO;MACzCgC,8BAA8B,EAAE,IAAI,CAAC/B,UAAU;MAC/Ca,UAAU,EAAE,IAAI,CAACD,cAAc,CAAC;IAClC,CAAC;IACD;MAAA;MACE;MACAnC,IAAA,CAACJ,mCAAmC;QAAA,GAAKqB,KAAK;QAAAsC,QAAA,EAC3C,IAAI,CAACtC,KAAK,CAACsC;MAAQ,CACe;IAAC;EAE1C;AACF;AAQA,eAAe/C,eAAe", "ignoreList": []}