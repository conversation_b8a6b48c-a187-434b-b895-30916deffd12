// Service pour les mises à jour en temps réel avec Supabase
import { supabase } from './supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface RealtimeSubscription {
  channel: RealtimeChannel;
  unsubscribe: () => void;
}

export class RealTimeService {
  private subscriptions: Map<string, RealtimeChannel> = new Map();

  /**
   * S'abonner aux mises à jour des restaurants en temps réel
   */
  subscribeToRestaurants(
    callback: (payload: any) => void,
    filters?: { business_type?: string; is_open?: boolean }
  ): RealtimeSubscription | null {
    if (!supabase) {
      console.warn('Supabase non disponible pour les mises à jour temps réel');
      return null;
    }

    const channelName = `restaurants_${Date.now()}`;
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'merchant_profiles',
          filter: filters ? this.buildFilter(filters) : undefined
        },
        (payload) => {
          console.log('🏪 Mise à jour restaurant temps réel:', payload);
          callback(payload);
        }
      )
      .subscribe();

    this.subscriptions.set(channelName, channel);

    return {
      channel,
      unsubscribe: () => this.unsubscribe(channelName)
    };
  }

  /**
   * S'abonner aux mises à jour des commandes en temps réel
   */
  subscribeToOrders(
    userId: string,
    role: 'client' | 'merchant' | 'livreur',
    callback: (payload: any) => void
  ): RealtimeSubscription | null {
    if (!supabase) {
      console.warn('Supabase non disponible pour les mises à jour temps réel');
      return null;
    }

    const channelName = `orders_${userId}_${role}_${Date.now()}`;
    let filter = '';

    switch (role) {
      case 'client':
        filter = `client_id=eq.${userId}`;
        break;
      case 'merchant':
        filter = `merchant_id=eq.${userId}`;
        break;
      case 'livreur':
        filter = `delivery_person_id=eq.${userId}`;
        break;
    }

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'orders',
          filter
        },
        (payload) => {
          console.log('📦 Mise à jour commande temps réel:', payload);
          callback(payload);
        }
      )
      .subscribe();

    this.subscriptions.set(channelName, channel);

    return {
      channel,
      unsubscribe: () => this.unsubscribe(channelName)
    };
  }

  /**
   * S'abonner aux mises à jour des produits en temps réel
   */
  subscribeToProducts(
    merchantId: string,
    callback: (payload: any) => void
  ): RealtimeSubscription | null {
    if (!supabase) {
      console.warn('Supabase non disponible pour les mises à jour temps réel');
      return null;
    }

    const channelName = `products_${merchantId}_${Date.now()}`;
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'products',
          filter: `merchant_id=eq.${merchantId}`
        },
        (payload) => {
          console.log('🍽️ Mise à jour produit temps réel:', payload);
          callback(payload);
        }
      )
      .subscribe();

    this.subscriptions.set(channelName, channel);

    return {
      channel,
      unsubscribe: () => this.unsubscribe(channelName)
    };
  }

  /**
   * S'abonner aux mises à jour de localisation des livreurs
   */
  subscribeToDeliveryTracking(
    deliveryId: string,
    callback: (payload: any) => void
  ): RealtimeSubscription | null {
    if (!supabase) {
      console.warn('Supabase non disponible pour les mises à jour temps réel');
      return null;
    }

    const channelName = `delivery_tracking_${deliveryId}_${Date.now()}`;
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'deliveries',
          filter: `id=eq.${deliveryId}`
        },
        (payload) => {
          console.log('🚚 Mise à jour livraison temps réel:', payload);
          callback(payload);
        }
      )
      .subscribe();

    this.subscriptions.set(channelName, channel);

    return {
      channel,
      unsubscribe: () => this.unsubscribe(channelName)
    };
  }

  /**
   * S'abonner aux notifications en temps réel
   */
  subscribeToNotifications(
    userId: string,
    callback: (payload: any) => void
  ): RealtimeSubscription | null {
    if (!supabase) {
      console.warn('Supabase non disponible pour les mises à jour temps réel');
      return null;
    }

    const channelName = `notifications_${userId}_${Date.now()}`;
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          console.log('🔔 Nouvelle notification temps réel:', payload);
          callback(payload);
        }
      )
      .subscribe();

    this.subscriptions.set(channelName, channel);

    return {
      channel,
      unsubscribe: () => this.unsubscribe(channelName)
    };
  }

  /**
   * Créer un canal de chat en temps réel
   */
  subscribeToChat(
    deliveryId: string,
    callback: (payload: any) => void
  ): RealtimeSubscription | null {
    if (!supabase) {
      console.warn('Supabase non disponible pour les mises à jour temps réel');
      return null;
    }

    const channelName = `chat_${deliveryId}_${Date.now()}`;
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `delivery_id=eq.${deliveryId}`
        },
        (payload) => {
          console.log('💬 Nouveau message chat temps réel:', payload);
          callback(payload);
        }
      )
      .subscribe();

    this.subscriptions.set(channelName, channel);

    return {
      channel,
      unsubscribe: () => this.unsubscribe(channelName)
    };
  }

  /**
   * Se désabonner d'un canal spécifique
   */
  private unsubscribe(channelName: string): void {
    const channel = this.subscriptions.get(channelName);
    if (channel) {
      supabase?.removeChannel(channel);
      this.subscriptions.delete(channelName);
      console.log(`🔌 Désabonnement du canal: ${channelName}`);
    }
  }

  /**
   * Se désabonner de tous les canaux
   */
  unsubscribeAll(): void {
    console.log('🔌 Désabonnement de tous les canaux temps réel...');
    this.subscriptions.forEach((channel, channelName) => {
      supabase?.removeChannel(channel);
    });
    this.subscriptions.clear();
  }

  /**
   * Construire un filtre pour les abonnements
   */
  private buildFilter(filters: Record<string, any>): string {
    return Object.entries(filters)
      .map(([key, value]) => `${key}=eq.${value}`)
      .join(',');
  }

  /**
   * Vérifier le statut de connexion temps réel
   */
  getConnectionStatus(): string {
    if (!supabase) return 'disconnected';
    
    // Supabase ne fournit pas directement le statut de connexion
    // On peut vérifier s'il y a des abonnements actifs
    return this.subscriptions.size > 0 ? 'connected' : 'idle';
  }

  /**
   * Obtenir le nombre d'abonnements actifs
   */
  getActiveSubscriptionsCount(): number {
    return this.subscriptions.size;
  }

  /**
   * Obtenir la liste des canaux actifs
   */
  getActiveChannels(): string[] {
    return Array.from(this.subscriptions.keys());
  }
}

// Export singleton instance
export const realTimeService = new RealTimeService();

// Types pour TypeScript
export interface RealtimePayload {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: any;
  old: any;
  schema: string;
  table: string;
  commit_timestamp: string;
}

export interface RealtimeCallback {
  (payload: RealtimePayload): void;
}

// Utilitaires pour les abonnements
export const createRealtimeSubscription = (
  table: string,
  filter?: string,
  callback?: RealtimeCallback
) => {
  if (!supabase || !callback) return null;

  const channel = supabase
    .channel(`${table}_${Date.now()}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table,
        filter
      },
      callback
    )
    .subscribe();

  return {
    channel,
    unsubscribe: () => supabase.removeChannel(channel)
  };
};
