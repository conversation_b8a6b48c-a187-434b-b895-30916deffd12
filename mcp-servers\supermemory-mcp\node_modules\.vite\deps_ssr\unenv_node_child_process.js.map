{"version": 3, "sources": ["../../unenv/dist/runtime/node/child_process.mjs"], "sourcesContent": ["import { notImplemented, notImplementedClass } from \"../_internal/utils.mjs\";\nexport const ChildProcess = /* @__PURE__ */ notImplementedClass(\"child_process.ChildProcess\");\nexport const _forkChild = /* @__PURE__ */ notImplemented(\"child_process.ChildProcess\");\nexport const exec = /* @__PURE__ */ notImplemented(\"child_process.exec\");\nexport const execFile = /* @__PURE__ */ notImplemented(\"child_process.execFile\");\nexport const execFileSync = /* @__PURE__ */ notImplemented(\"child_process.execFileSync\");\nexport const execSync = /* @__PURE__ */ notImplemented(\"child_process.execSyn\");\nexport const fork = /* @__PURE__ */ notImplemented(\"child_process.fork\");\nexport const spawn = /* @__PURE__ */ notImplemented(\"child_process.spawn\");\nexport const spawnSync = /* @__PURE__ */ notImplemented(\"child_process.spawnSync\");\nexport default {\n\tChildProcess,\n\t_forkChild,\n\texec,\n\texecFile,\n\texecFileSync,\n\texecSync,\n\tfork,\n\tspawn,\n\tspawnSync\n};\n"], "mappings": ";;;;;;;AACO,IAAM,eAA+B,oBAAoB,4BAA4B;AACrF,IAAM,aAA6B,eAAe,4BAA4B;AAC9E,IAAM,OAAuB,eAAe,oBAAoB;AAChE,IAAM,WAA2B,eAAe,wBAAwB;AACxE,IAAM,eAA+B,eAAe,4BAA4B;AAChF,IAAM,WAA2B,eAAe,uBAAuB;AACvE,IAAM,OAAuB,eAAe,oBAAoB;AAChE,IAAM,QAAwB,eAAe,qBAAqB;AAClE,IAAM,YAA4B,eAAe,yBAAyB;AACjF,IAAO,wBAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}