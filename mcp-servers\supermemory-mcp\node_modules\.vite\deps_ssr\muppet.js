import {
  CallToolRequestSchema,
  CompleteRequestSchema,
  ErrorCode,
  GetPromptRequestSchema,
  InitializeRequestSchema,
  LATEST_PROTOCOL_VERSION,
  ReadResourceRequestSchema,
  RequestSchema,
  SUPPORTED_PROTOCOL_VERSIONS
} from "./chunk-2JZEACTZ.js";
import "./chunk-T5ENI2NM.js";
import {
  Hono
} from "./chunk-YKCPFTPD.js";
import {
  decodeURIComponent_
} from "./chunk-5TF4N3S3.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/hono/dist/utils/cookie.js
var validCookieNameRegEx = /^[\w!#$%&'*.^`|~+-]+$/;
var validCookieValueRegEx = /^[ !#-:<-[\]-~]*$/;
var parse = (cookie, name) => {
  if (name && cookie.indexOf(name) === -1) {
    return {};
  }
  const pairs = cookie.trim().split(";");
  const parsedCookie = {};
  for (let pairStr of pairs) {
    pairStr = pairStr.trim();
    const valueStartPos = pairStr.indexOf("=");
    if (valueStartPos === -1) {
      continue;
    }
    const cookieName = pairStr.substring(0, valueStartPos).trim();
    if (name && name !== cookieName || !validCookieNameRegEx.test(cookieName)) {
      continue;
    }
    let cookieValue = pairStr.substring(valueStartPos + 1).trim();
    if (cookieValue.startsWith('"') && cookieValue.endsWith('"')) {
      cookieValue = cookieValue.slice(1, -1);
    }
    if (validCookieValueRegEx.test(cookieValue)) {
      parsedCookie[cookieName] = decodeURIComponent_(cookieValue);
      if (name) {
        break;
      }
    }
  }
  return parsedCookie;
};

// node_modules/hono/dist/helper/cookie/index.js
var getCookie = (c, key, prefix) => {
  const cookie = c.req.raw.headers.get("Cookie");
  if (typeof key === "string") {
    if (!cookie) {
      return void 0;
    }
    let finalKey = key;
    if (prefix === "secure") {
      finalKey = "__Secure-" + key;
    } else if (prefix === "host") {
      finalKey = "__Host-" + key;
    }
    const obj2 = parse(cookie, finalKey);
    return obj2[finalKey];
  }
  if (!cookie) {
    return {};
  }
  const obj = parse(cookie);
  return obj;
};

// node_modules/hono/dist/http-exception.js
var HTTPException = class extends Error {
  res;
  status;
  constructor(status = 500, options) {
    super(options?.message, { cause: options?.cause });
    this.res = options?.res;
    this.status = status;
  }
  getResponse() {
    if (this.res) {
      const newResponse = new Response(this.res.body, {
        status: this.status,
        headers: this.res.headers
      });
      return newResponse;
    }
    return new Response(this.message, {
      status: this.status
    });
  }
};

// node_modules/hono/dist/utils/buffer.js
var bufferToFormData = (arrayBuffer, contentType) => {
  const response = new Response(arrayBuffer, {
    headers: {
      "Content-Type": contentType
    }
  });
  return response.formData();
};

// node_modules/hono/dist/validator/validator.js
var jsonRegex = /^application\/([a-z-\.]+\+)?json(;\s*[a-zA-Z0-9\-]+\=([^;]+))*$/;
var multipartRegex = /^multipart\/form-data(;\s?boundary=[a-zA-Z0-9'"()+_,\-./:=?]+)?$/;
var urlencodedRegex = /^application\/x-www-form-urlencoded(;\s*[a-zA-Z0-9\-]+\=([^;]+))*$/;
var validator = (target, validationFunc) => {
  return async (c, next) => {
    let value = {};
    const contentType = c.req.header("Content-Type");
    switch (target) {
      case "json":
        if (!contentType || !jsonRegex.test(contentType)) {
          break;
        }
        try {
          value = await c.req.json();
        } catch {
          const message = "Malformed JSON in request body";
          throw new HTTPException(400, { message });
        }
        break;
      case "form": {
        if (!contentType || !(multipartRegex.test(contentType) || urlencodedRegex.test(contentType))) {
          break;
        }
        let formData;
        if (c.req.bodyCache.formData) {
          formData = await c.req.bodyCache.formData;
        } else {
          try {
            const arrayBuffer = await c.req.arrayBuffer();
            formData = await bufferToFormData(arrayBuffer, contentType);
            c.req.bodyCache.formData = formData;
          } catch (e) {
            let message = "Malformed FormData request.";
            message += e instanceof Error ? ` ${e.message}` : ` ${String(e)}`;
            throw new HTTPException(400, { message });
          }
        }
        const form = {};
        formData.forEach((value2, key) => {
          if (key.endsWith("[]")) {
            ;
            (form[key] ??= []).push(value2);
          } else if (Array.isArray(form[key])) {
            ;
            form[key].push(value2);
          } else if (key in form) {
            form[key] = [form[key], value2];
          } else {
            form[key] = value2;
          }
        });
        value = form;
        break;
      }
      case "query":
        value = Object.fromEntries(
          Object.entries(c.req.queries()).map(([k, v2]) => {
            return v2.length === 1 ? [k, v2[0]] : [k, v2];
          })
        );
        break;
      case "param":
        value = c.req.param();
        break;
      case "header":
        value = c.req.header();
        break;
      case "cookie":
        value = getCookie(c);
        break;
    }
    const res = await validationFunc(value, c);
    if (res instanceof Response) {
      return res;
    }
    c.req.addValidatedData(target, res);
    await next();
  };
};

// node_modules/@hono/standard-validator/dist/index.js
var sValidator = (target, schema, hook) => (
  // @ts-expect-error not typed well
  validator(target, async (value, c) => {
    const result = await schema["~standard"].validate(value);
    if (hook) {
      const hookResult = await hook(
        result.issues ? { data: value, error: result.issues, success: false, target } : { data: value, success: true, target },
        c
      );
      if (hookResult) {
        if (hookResult instanceof Response) {
          return hookResult;
        }
        if ("response" in hookResult) {
          return hookResult.response;
        }
      }
    }
    if (result.issues) {
      return c.json({ data: value, error: result.issues, success: false }, 400);
    }
    return result.value;
  })
);

// node_modules/muppet/dist/muppet-BdeHS-Ba.js
var P = Object.defineProperty;
var f = (i, n2) => P(i, "name", { value: n2, configurable: true });
var q = Symbol("muppet");
var h = { RESOURCES: "resources", TOOLS: "tools", PROMPTS: "prompts" };
async function _(i, n2) {
  const m2 = await L(i, n2.symbols);
  return R({ config: n2, specs: m2, app: i });
}
f(_, "muppet");
function R(i) {
  const { config: n2, specs: m2, app: c } = i, a2 = new Hono().use(async (e, t) => {
    if (!(h.TOOLS in e.get("specs"))) throw new Error("No tools available");
    await t();
  }).post("/list", (e) => e.json({ result: { tools: Object.values(e.get("specs").tools ?? {}).map(({ name: t, description: s3, inputSchema: p2 }) => ({ name: t, description: s3, inputSchema: p2 })) } })).post("/call", sValidator("json", CallToolRequestSchema), async (e) => {
    const { params: t } = e.req.valid("json"), s3 = e.get("specs").tools?.[t.name];
    if (!s3) throw new Error("Unable to find the path for the tool!");
    const o = await (await e.get("app").request(...b({ path: s3.path, method: s3.method, schema: s3.schema, args: t.arguments }), v(e))).json();
    return s3.resourceType === "text" ? e.json({ result: { content: [{ type: "text", text: typeof o == "string" ? o : JSON.stringify(o) }] } }) : Array.isArray(o) ? e.json({ result: { content: o } }) : e.json({ result: o });
  }), r = new Hono().use(async (e, t) => {
    if (!(h.PROMPTS in e.get("specs"))) throw new Error("No prompts available");
    await t();
  }).post("/list", (e) => e.json({ result: { prompts: Object.values(e.get("specs").prompts ?? {}).map(({ path: t, ...s3 }) => s3) } })).post("/get", sValidator("json", GetPromptRequestSchema), async (e) => {
    const { params: t } = e.req.valid("json"), s3 = e.get("specs").prompts?.[t.name];
    if (!s3) throw new Error("Unable to find the path for the prompt!");
    const o = await (await e.get("app").request(...b({ path: s3.path, method: s3.method, schema: s3.schema, args: t.arguments }), v(e))).json();
    return Array.isArray(o) ? e.json({ result: { description: s3.description, messages: o } }) : e.json({ result: o });
  }), u2 = new Hono().use(async (e, t) => {
    if (!(h.RESOURCES in e.get("specs"))) throw new Error("No resources available");
    await t();
  }).post("/list", async (e) => {
    const t = await S(e, (s3) => {
      if (s3.type !== "template") return { name: s3.name, description: s3.description, mimeType: s3.mimeType, uri: s3.uri };
    });
    return e.json({ result: { resources: t } });
  }).post("/templates/list", async (e) => {
    const t = await S(e, (s3) => {
      if (s3.type === "template") return { name: s3.name, description: s3.description, mimeType: s3.mimeType, uriTemplate: s3.uri };
    });
    return e.json({ result: { resourceTemplates: t } });
  }).post("/read", sValidator("json", ReadResourceRequestSchema), async (e) => {
    const { params: t } = e.req.valid("json"), s3 = t.uri.split(":")[0], p2 = e.get("muppet").resources?.[s3];
    if (!p2) throw new Error(`Unable to find the handler for ${s3} protocol!`);
    const o = await p2(t.uri);
    return Array.isArray(o) ? e.json({ result: { contents: o } }) : e.json({ result: o });
  });
  return new Hono().use(async (e, t) => {
    n2.logger?.info({ method: e.req.method, path: e.req.path }, "Incoming request"), e.set("muppet", n2), e.set("specs", m2), e.set("app", c), await t(), n2.logger?.info({ status: e.res.status }, "Outgoing response");
  }).post("/initialize", sValidator("json", InitializeRequestSchema), async (e) => {
    const { params: t } = e.req.valid("json"), { name: s3, version: p2 } = e.get("muppet"), o = e.get("specs"), l = h.TOOLS in o, j = h.PROMPTS in o, E = h.RESOURCES in o;
    return e.json({ result: { protocolVersion: SUPPORTED_PROTOCOL_VERSIONS.includes(t?.protocolVersion) ? t.protocolVersion : LATEST_PROTOCOL_VERSION, serverInfo: { name: s3, version: p2 }, capabilities: { tools: l ? {} : void 0, prompts: j ? {} : void 0, resources: E ? {} : void 0 } } });
  }).post("/notifications/:event", (e) => (e.get("muppet").events?.emit(e, `notifications/${e.req.param("event")}`, void 0), e.body(null, 204))).post("/ping", (e) => e.json({ result: {} })).route("/tools", a2).route("/prompts", r).route("/resources", u2).post("/completion/complete", sValidator("json", CompleteRequestSchema), async (e) => {
    const { params: t } = e.req.valid("json");
    let s3;
    if (t.ref.type === "ref/prompt" ? s3 = e.get("specs").prompts?.[t.ref.name].completion : t.ref.type === "ref/resource" && (s3 = await S(e, (o) => {
      if (o.type === "template" && o.uri === t.ref.uri) return o.completion;
    }).then((o) => o[0])), !s3) return e.json({ result: { completion: { values: [], total: 0, hasMore: false } } });
    const p2 = await s3(t.argument);
    return Array.isArray(p2) ? e.json({ result: { completion: { values: p2, total: p2.length, hasMore: false } } }) : e.json({ result: { completion: p2 } });
  }).notFound((e) => (e.get("muppet").logger?.info("Method not found"), e.json({ error: { code: ErrorCode.MethodNotFound, message: "Method not found" } }))).onError((e, t) => (t.get("muppet").logger?.error({ err: e }, "Internal error"), t.json({ error: { code: Number.isSafeInteger(e.code) ? e.code : ErrorCode.InternalError, message: e.message ?? "Internal error" } })));
}
f(R, "createMuppetServer");
async function L(i, n2 = []) {
  const m2 = {}, c = [...n2, q];
  for (const r of i.routes) {
    const u2 = c.find((j) => j in r.handler);
    if (!u2) continue;
    const { validationTarget: e, toJson: t, type: s3 } = r.handler[u2];
    let p2;
    typeof t == "function" ? p2 = await t() : p2 = t ?? {};
    const o = m2[r.path]?.[r.method];
    if (o?.type && s3 && o.type !== s3) throw new Error(`Conflicting types for ${r.path}: ${o.type} and ${s3}`);
    let l = { ...o ?? {}, type: s3 ?? o?.type };
    e && "schema" in p2 ? l.schema = { ...l.schema ?? {}, [e]: p2.schema } : l = { ...l, ...p2 }, m2[r.path] = { ...m2[r.path] ?? {}, [r.method]: l };
  }
  const a2 = {};
  for (const [r, u2] of Object.entries(m2)) if (u2) {
    for (const [e, t] of Object.entries(u2)) if (t) {
      if (!t.type) throw new Error(`Type not found for ${r}`);
      if (t.type === h.TOOLS) {
        a2.tools || (a2.tools = {});
        const s3 = t.name ?? g(e, r);
        a2.tools[s3] = { name: s3, description: t.description, resourceType: t.resourceType, inputSchema: O(t.schema) ?? {}, path: r, method: e, schema: t.schema };
      } else if (t.type === h.PROMPTS) {
        a2.prompts || (a2.prompts = {});
        const s3 = t.name ?? g(e, r), p2 = [], o = O(t.schema) ?? {};
        for (const l of Object.keys(o.properties ?? {})) p2.push({ name: l, description: o.properties?.[l]?.description, required: o.required?.includes(l) ?? false });
        a2.prompts[s3] = { name: s3, description: t.description, completion: t.completion, arguments: p2, path: r, method: e, schema: t.schema };
      } else t.type === h.RESOURCES && (a2.resources || (a2.resources = {}), a2.resources[g(e, r)] = { path: r, method: e });
    }
  }
  return a2;
}
f(L, "generateSpecs");
function O(i) {
  let n2;
  for (const m2 of Object.values(i ?? {})) {
    if (!n2) {
      n2 = m2;
      continue;
    }
    n2 = { ...n2, properties: { ...n2.properties, ...m2.properties }, required: [...n2.required ?? [], ...m2.required ?? []] };
  }
  return n2;
}
f(O, "mergeSchemas");
async function S(i, n2) {
  return (await Promise.all(Object.values(i.get("specs").resources ?? {}).map(async ({ path: c, method: a2 }) => i.get("app").request(c, { method: a2, headers: i.req.header() })))).flat(2).reduce((c, a2) => {
    const r = n2(a2);
    return r && c.push(r), c;
  }, []);
}
f(S, "findAllTheResources");
function g(i, n2) {
  return `${i}:${n2}`;
}
f(g, "generateKey");
function b(i) {
  const { path: n2, method: m2, schema: c, args: a2 } = i, r = {}, u2 = { method: m2 };
  for (const [s3, { properties: p2 }] of Object.entries(c ?? {})) p2 && (r[s3] = Object.keys(p2).reduce((o, l) => (a2?.[l] !== void 0 && (o[l] = a2?.[l]), o), {}));
  Object.values(r.header ?? {}).length > 0 && (u2.headers = r.header), Object.values(r.json ?? {}).length > 0 && (u2.body = JSON.stringify(r.json), u2.headers = { ...u2.headers, "content-type": "application/json" });
  const e = T(r.query);
  return [`${V(n2, r.param)}${e.length > 0 ? `?${e}` : ""}`, u2];
}
f(b, "getRequestInit");
function V(i, n2) {
  return i.split("/").map((m2) => {
    let c = m2;
    if (c.startsWith(":")) {
      const a2 = c.match(/^:([^{?]+)(?:{(.+)})?(\?)?$/);
      if (a2) {
        const r = a2[1], u2 = n2?.[r];
        u2 && (c = String(u2));
      } else c = c.slice(1, c.length), c.endsWith("?") && (c = c.slice(0, -1));
    }
    return c;
  }).join("/");
}
f(V, "placeParamValues");
function T(i, n2) {
  const { prefix: m2, separator: c = "__" } = n2 ?? {};
  return Object.entries(i ?? {}).reduce((a2, [r, u2]) => {
    const e = `${m2 ? `${m2}${c}` : ""}${r}`;
    return u2 && (Array.isArray(u2) ? a2.push(...u2.filter((t) => t !== void 0).map((t) => `${e}=${t}`)) : typeof u2 == "object" ? a2.push(T(u2, { prefix: e, separator: c })) : a2.push(`${e}=${u2}`)), a2;
  }, []).join("&");
}
f(T, "querySerializer");
function v(i) {
  return { ...i.env, muppet: { req: i.req } };
}
f(v, "createMuppetEnv");

// node_modules/@standard-community/standard-json/dist/index.js
var n = Object.defineProperty;
var a = (r, o) => n(r, "name", { value: o, configurable: true });
var s = a(async (r, o) => {
  const t = r["~standard"].vendor;
  let e;
  switch (t) {
    case "arktype":
      e = import("./arktype-CqF_-yop-X3RDNEHC.js");
      break;
    case "effect":
      e = import("./effect-pZiNZiGm-3L6RYGQS.js");
      break;
    case "valibot":
      e = import("./valibot-DOnkF2ES-XSPIN2JP.js");
      break;
    case "zod":
      e = import("./zod-DuHkA2Jp-MRBFWG26.js");
      break;
    default:
      throw new Error(`standard-json: Unsupported schema vendor "${t}"`);
  }
  return (await e).toJsonSchema(r, o);
}, "toJsonSchema");

// node_modules/muppet/dist/index.js
var u = Object.defineProperty;
var s2 = (e, o) => u(e, "name", { value: o, configurable: true });
function p(e) {
  return (o) => Object.assign(s2(async (n2, a2) => {
    await a2();
  }, "middleware"), { [q]: { toJson: o ?? {}, type: e } });
}
s2(p, "describeRoute");
var S2 = p(h.PROMPTS);
var h2 = p(h.TOOLS);
function w(e) {
  return Object.assign(e, { [q]: { type: h.RESOURCES } });
}
s2(w, "registerResources");
function O2(e, o, t) {
  const n2 = sValidator(e, o, t);
  return Object.assign(n2, { [q]: { validationTarget: e, toJson: s2(async () => ({ schema: await s(o) }), "toJson") } });
}
s2(O2, "mValidator");
function b2(e) {
  const { mcp: o, transport: t, logger: n2 } = e;
  let a2 = 0;
  return t.onmessage = async (i) => {
    const r = await m({ mcp: await o, message: i, logger: n2 });
    "method" in i && i.method === "initialize" && (a2 = -1), r && (a2++, await t.send({ ...r, id: a2 }).then(() => n2?.info("Sent response")).catch((l) => n2?.error(l, "Failed to send cancellation")));
  }, t.start();
}
s2(b2, "bridge");
async function m(e) {
  const { mcp: o, message: t, logger: n2 } = e;
  n2?.info({ message: t, string: JSON.stringify(t) }, "Received message");
  const a2 = RequestSchema.parse(t), i = await o.request(a2.method, { method: "POST", body: JSON.stringify(t), headers: { "content-type": "application/json" } });
  if (i.status === 204) return null;
  const r = await i.json();
  return r.jsonrpc = "2.0", n2?.info({ payload: r }, "Response payload"), r;
}
s2(m, "handleMessage");
export {
  h as McpPrimitives,
  b2 as bridge,
  S2 as describePrompt,
  h2 as describeTool,
  m as handleMessage,
  O2 as mValidator,
  _ as muppet,
  w as registerResources,
  q as uniqueSymbol
};
//# sourceMappingURL=muppet.js.map
