import {
  DH_CHECK_P_NOT_PRIME,
  DH_CHECK_P_NOT_SAFE_PRIME,
  DH_NOT_SUITABLE_GENERATOR,
  DH_UNABLE_TO_CHECK_GENERATOR,
  ENGINE_METHOD_ALL,
  ENGINE_METHOD_CIPHERS,
  ENGINE_METHOD_DH,
  ENGINE_METHOD_DIGESTS,
  ENGINE_METHOD_DSA,
  ENGINE_METHOD_EC,
  ENGINE_METHOD_NONE,
  ENGINE_METHOD_PKEY_ASN1_METHS,
  ENGINE_METHOD_PKEY_METHS,
  ENGINE_METHOD_RAND,
  ENGINE_METHOD_RSA,
  OPENSSL_VERSION_NUMBER,
  POINT_CONVERSION_COMPRESSED,
  POINT_CONVERSION_HYBRID,
  POINT_CONVERSION_UNCOMPRESSED,
  RSA_NO_PADDING,
  RSA_PKCS1_OAEP_PADDING,
  RSA_PKCS1_PADDING,
  RSA_PKCS1_PSS_PADDING,
  RSA_PSS_SALTLEN_AUTO,
  RSA_PSS_SALTLEN_DIGEST,
  RSA_PSS_SALTLEN_MAX_SIGN,
  RSA_X931_PADDING,
  SSL_OP_ALL,
  SSL_OP_ALLOW_NO_DHE_KEX,
  SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION,
  SSL_OP_CIPHER_SERVER_PREFERENCE,
  SSL_OP_CISCO_ANYCONNECT,
  SSL_OP_COOKIE_EXCHANGE,
  SSL_OP_CRYPTOPRO_TLSEXT_BUG,
  SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS,
  SSL_OP_LEGACY_SERVER_CONNECT,
  SSL_OP_NO_COMPRESSION,
  SSL_OP_NO_ENCRYPT_THEN_MAC,
  SSL_OP_NO_QUERY_MTU,
  SSL_OP_NO_RENEGOTIATION,
  SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION,
  SSL_OP_NO_SSLv2,
  SSL_OP_NO_SSLv3,
  SSL_OP_NO_TICKET,
  SSL_OP_NO_TLSv1,
  SSL_OP_NO_TLSv1_1,
  SSL_OP_NO_TLSv1_2,
  SSL_OP_NO_TLSv1_3,
  SSL_OP_PRIORITIZE_CHACHA,
  SSL_OP_TLS_ROLLBACK_BUG,
  TLS1_1_VERSION,
  TLS1_2_VERSION,
  TLS1_3_VERSION,
  TLS1_VERSION,
  defaultCipherList,
  defaultCoreCipherList
} from "./chunk-TKFP2B6M.js";
import {
  notImplemented,
  notImplementedClass
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/internal/crypto/web.mjs
var subtle = globalThis.crypto?.subtle;

// node_modules/unenv/dist/runtime/node/internal/crypto/node.mjs
var webcrypto = new Proxy(globalThis.crypto, { get(_, key) {
  if (key === "CryptoKey") {
    return globalThis.CryptoKey;
  }
  if (typeof globalThis.crypto[key] === "function") {
    return globalThis.crypto[key].bind(globalThis.crypto);
  }
  return globalThis.crypto[key];
} });
var checkPrime = notImplemented("crypto.checkPrime");
var checkPrimeSync = notImplemented("crypto.checkPrimeSync");
var createCipher = notImplemented("crypto.createCipher");
var createDecipher = notImplemented("crypto.createDecipher");
var pseudoRandomBytes = notImplemented("crypto.pseudoRandomBytes");
var createCipheriv = notImplemented("crypto.createCipheriv");
var createDecipheriv = notImplemented("crypto.createDecipheriv");
var createDiffieHellman = notImplemented("crypto.createDiffieHellman");
var createDiffieHellmanGroup = notImplemented("crypto.createDiffieHellmanGroup");
var createECDH = notImplemented("crypto.createECDH");
var createHash = notImplemented("crypto.createHash");
var createHmac = notImplemented("crypto.createHmac");
var createPrivateKey = notImplemented("crypto.createPrivateKey");
var createPublicKey = notImplemented("crypto.createPublicKey");
var createSecretKey = notImplemented("crypto.createSecretKey");
var createSign = notImplemented("crypto.createSign");
var createVerify = notImplemented("crypto.createVerify");
var diffieHellman = notImplemented("crypto.diffieHellman");
var generatePrime = notImplemented("crypto.generatePrime");
var generatePrimeSync = notImplemented("crypto.generatePrimeSync");
var getCiphers = notImplemented("crypto.getCiphers");
var getCipherInfo = notImplemented("crypto.getCipherInfo");
var getCurves = notImplemented("crypto.getCurves");
var getDiffieHellman = notImplemented("crypto.getDiffieHellman");
var getHashes = notImplemented("crypto.getHashes");
var hkdf = notImplemented("crypto.hkdf");
var hkdfSync = notImplemented("crypto.hkdfSync");
var pbkdf2 = notImplemented("crypto.pbkdf2");
var pbkdf2Sync = notImplemented("crypto.pbkdf2Sync");
var generateKeyPair = notImplemented("crypto.generateKeyPair");
var generateKeyPairSync = notImplemented("crypto.generateKeyPairSync");
var generateKey = notImplemented("crypto.generateKey");
var generateKeySync = notImplemented("crypto.generateKeySync");
var privateDecrypt = notImplemented("crypto.privateDecrypt");
var privateEncrypt = notImplemented("crypto.privateEncrypt");
var publicDecrypt = notImplemented("crypto.publicDecrypt");
var publicEncrypt = notImplemented("crypto.publicEncrypt");
var randomFill = notImplemented("crypto.randomFill");
var randomFillSync = notImplemented("crypto.randomFillSync");
var randomInt = notImplemented("crypto.randomInt");
var scrypt = notImplemented("crypto.scrypt");
var scryptSync = notImplemented("crypto.scryptSync");
var sign = notImplemented("crypto.sign");
var setEngine = notImplemented("crypto.setEngine");
var timingSafeEqual = notImplemented("crypto.timingSafeEqual");
var getFips = notImplemented("crypto.getFips");
var setFips = notImplemented("crypto.setFips");
var verify = notImplemented("crypto.verify");
var secureHeapUsed = notImplemented("crypto.secureHeapUsed");
var hash = notImplemented("crypto.hash");
var Certificate = notImplementedClass("crypto.Certificate");
var Cipher = notImplementedClass("crypto.Cipher");
var Cipheriv = notImplementedClass(
  "crypto.Cipheriv"
  // @ts-expect-error not typed yet
);
var Decipher = notImplementedClass("crypto.Decipher");
var Decipheriv = notImplementedClass(
  "crypto.Decipheriv"
  // @ts-expect-error not typed yet
);
var DiffieHellman = notImplementedClass("crypto.DiffieHellman");
var DiffieHellmanGroup = notImplementedClass("crypto.DiffieHellmanGroup");
var ECDH = notImplementedClass("crypto.ECDH");
var Hash = notImplementedClass("crypto.Hash");
var Hmac = notImplementedClass("crypto.Hmac");
var KeyObject = notImplementedClass("crypto.KeyObject");
var Sign = notImplementedClass("crypto.Sign");
var Verify = notImplementedClass("crypto.Verify");
var X509Certificate = notImplementedClass("crypto.X509Certificate");

// node_modules/unenv/dist/runtime/node/crypto.mjs
var constants = {
  OPENSSL_VERSION_NUMBER,
  SSL_OP_ALL,
  SSL_OP_ALLOW_NO_DHE_KEX,
  SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION,
  SSL_OP_CIPHER_SERVER_PREFERENCE,
  SSL_OP_CISCO_ANYCONNECT,
  SSL_OP_COOKIE_EXCHANGE,
  SSL_OP_CRYPTOPRO_TLSEXT_BUG,
  SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS,
  SSL_OP_LEGACY_SERVER_CONNECT,
  SSL_OP_NO_COMPRESSION,
  SSL_OP_NO_ENCRYPT_THEN_MAC,
  SSL_OP_NO_QUERY_MTU,
  SSL_OP_NO_RENEGOTIATION,
  SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION,
  SSL_OP_NO_SSLv2,
  SSL_OP_NO_SSLv3,
  SSL_OP_NO_TICKET,
  SSL_OP_NO_TLSv1,
  SSL_OP_NO_TLSv1_1,
  SSL_OP_NO_TLSv1_2,
  SSL_OP_NO_TLSv1_3,
  SSL_OP_PRIORITIZE_CHACHA,
  SSL_OP_TLS_ROLLBACK_BUG,
  ENGINE_METHOD_RSA,
  ENGINE_METHOD_DSA,
  ENGINE_METHOD_DH,
  ENGINE_METHOD_RAND,
  ENGINE_METHOD_EC,
  ENGINE_METHOD_CIPHERS,
  ENGINE_METHOD_DIGESTS,
  ENGINE_METHOD_PKEY_METHS,
  ENGINE_METHOD_PKEY_ASN1_METHS,
  ENGINE_METHOD_ALL,
  ENGINE_METHOD_NONE,
  DH_CHECK_P_NOT_SAFE_PRIME,
  DH_CHECK_P_NOT_PRIME,
  DH_UNABLE_TO_CHECK_GENERATOR,
  DH_NOT_SUITABLE_GENERATOR,
  RSA_PKCS1_PADDING,
  RSA_NO_PADDING,
  RSA_PKCS1_OAEP_PADDING,
  RSA_X931_PADDING,
  RSA_PKCS1_PSS_PADDING,
  RSA_PSS_SALTLEN_DIGEST,
  RSA_PSS_SALTLEN_MAX_SIGN,
  RSA_PSS_SALTLEN_AUTO,
  defaultCoreCipherList,
  TLS1_VERSION,
  TLS1_1_VERSION,
  TLS1_2_VERSION,
  TLS1_3_VERSION,
  POINT_CONVERSION_COMPRESSED,
  POINT_CONVERSION_UNCOMPRESSED,
  POINT_CONVERSION_HYBRID,
  defaultCipherList
};

// node_modules/@cloudflare/unenv-preset/dist/runtime/node/crypto.mjs
var workerdCrypto = process.getBuiltinModule("node:crypto");
var {
  Certificate: Certificate2,
  checkPrime: checkPrime2,
  checkPrimeSync: checkPrimeSync2,
  // @ts-expect-error
  Cipheriv: Cipheriv2,
  createCipheriv: createCipheriv2,
  createDecipheriv: createDecipheriv2,
  createDiffieHellman: createDiffieHellman2,
  createDiffieHellmanGroup: createDiffieHellmanGroup2,
  createECDH: createECDH2,
  createHash: createHash2,
  createHmac: createHmac2,
  createPrivateKey: createPrivateKey2,
  createPublicKey: createPublicKey2,
  createSecretKey: createSecretKey2,
  createSign: createSign2,
  createVerify: createVerify2,
  // @ts-expect-error
  Decipheriv: Decipheriv2,
  diffieHellman: diffieHellman2,
  DiffieHellman: DiffieHellman2,
  DiffieHellmanGroup: DiffieHellmanGroup2,
  ECDH: ECDH2,
  fips: fips2,
  generateKey: generateKey2,
  generateKeyPair: generateKeyPair2,
  generateKeyPairSync: generateKeyPairSync2,
  generateKeySync: generateKeySync2,
  generatePrime: generatePrime2,
  generatePrimeSync: generatePrimeSync2,
  getCipherInfo: getCipherInfo2,
  getCiphers: getCiphers2,
  getCurves: getCurves2,
  getDiffieHellman: getDiffieHellman2,
  getFips: getFips2,
  getHashes: getHashes2,
  getRandomValues: getRandomValues2,
  hash: hash2,
  Hash: Hash2,
  hkdf: hkdf2,
  hkdfSync: hkdfSync2,
  Hmac: Hmac2,
  KeyObject: KeyObject2,
  pbkdf2: pbkdf22,
  pbkdf2Sync: pbkdf2Sync2,
  privateDecrypt: privateDecrypt2,
  privateEncrypt: privateEncrypt2,
  publicDecrypt: publicDecrypt2,
  publicEncrypt: publicEncrypt2,
  randomBytes: randomBytes2,
  randomFill: randomFill2,
  randomFillSync: randomFillSync2,
  randomInt: randomInt2,
  randomUUID: randomUUID2,
  scrypt: scrypt2,
  scryptSync: scryptSync2,
  secureHeapUsed: secureHeapUsed2,
  setEngine: setEngine2,
  setFips: setFips2,
  sign: sign2,
  Sign: Sign2,
  subtle: subtle2,
  timingSafeEqual: timingSafeEqual2,
  verify: verify2,
  Verify: Verify2,
  X509Certificate: X509Certificate2
} = workerdCrypto;
var webcrypto2 = {
  // @ts-expect-error
  CryptoKey: webcrypto.CryptoKey,
  getRandomValues: getRandomValues2,
  randomUUID: randomUUID2,
  subtle: subtle2
};
var crypto_default = {
  /**
   * manually unroll unenv-polyfilled-symbols to make it tree-shakeable
   */
  Certificate: Certificate2,
  Cipher,
  Cipheriv: Cipheriv2,
  Decipher,
  Decipheriv: Decipheriv2,
  ECDH: ECDH2,
  Sign: Sign2,
  Verify: Verify2,
  X509Certificate: X509Certificate2,
  // @ts-expect-error @types/node is out of date - this is a bug in typings
  constants,
  createCipheriv: createCipheriv2,
  createDecipheriv: createDecipheriv2,
  createECDH: createECDH2,
  createSign: createSign2,
  createVerify: createVerify2,
  diffieHellman: diffieHellman2,
  getCipherInfo: getCipherInfo2,
  hash: hash2,
  privateDecrypt: privateDecrypt2,
  privateEncrypt: privateEncrypt2,
  publicDecrypt: publicDecrypt2,
  publicEncrypt: publicEncrypt2,
  scrypt: scrypt2,
  scryptSync: scryptSync2,
  sign: sign2,
  verify: verify2,
  // default-only export from unenv
  // @ts-expect-error unenv has unknown type
  createCipher,
  // @ts-expect-error unenv has unknown type
  createDecipher,
  // @ts-expect-error unenv has unknown type
  pseudoRandomBytes,
  /**
   * manually unroll workerd-polyfilled-symbols to make it tree-shakeable
   */
  DiffieHellman: DiffieHellman2,
  DiffieHellmanGroup: DiffieHellmanGroup2,
  Hash: Hash2,
  Hmac: Hmac2,
  KeyObject: KeyObject2,
  checkPrime: checkPrime2,
  checkPrimeSync: checkPrimeSync2,
  createDiffieHellman: createDiffieHellman2,
  createDiffieHellmanGroup: createDiffieHellmanGroup2,
  createHash: createHash2,
  createHmac: createHmac2,
  createPrivateKey: createPrivateKey2,
  createPublicKey: createPublicKey2,
  createSecretKey: createSecretKey2,
  generateKey: generateKey2,
  generateKeyPair: generateKeyPair2,
  generateKeyPairSync: generateKeyPairSync2,
  generateKeySync: generateKeySync2,
  generatePrime: generatePrime2,
  generatePrimeSync: generatePrimeSync2,
  getCiphers: getCiphers2,
  getCurves: getCurves2,
  getDiffieHellman: getDiffieHellman2,
  getFips: getFips2,
  getHashes: getHashes2,
  getRandomValues: getRandomValues2,
  hkdf: hkdf2,
  hkdfSync: hkdfSync2,
  pbkdf2: pbkdf22,
  pbkdf2Sync: pbkdf2Sync2,
  randomBytes: randomBytes2,
  randomFill: randomFill2,
  randomFillSync: randomFillSync2,
  randomInt: randomInt2,
  randomUUID: randomUUID2,
  secureHeapUsed: secureHeapUsed2,
  setEngine: setEngine2,
  setFips: setFips2,
  subtle: subtle2,
  timingSafeEqual: timingSafeEqual2,
  // default-only export from workerd
  fips: fips2,
  // special-cased deep merged symbols
  webcrypto: webcrypto2
};
export {
  Certificate2 as Certificate,
  Cipher,
  Cipheriv2 as Cipheriv,
  Decipher,
  Decipheriv2 as Decipheriv,
  DiffieHellman2 as DiffieHellman,
  DiffieHellmanGroup2 as DiffieHellmanGroup,
  ECDH2 as ECDH,
  Hash2 as Hash,
  Hmac2 as Hmac,
  KeyObject2 as KeyObject,
  Sign2 as Sign,
  Verify2 as Verify,
  X509Certificate2 as X509Certificate,
  checkPrime2 as checkPrime,
  checkPrimeSync2 as checkPrimeSync,
  constants,
  createCipheriv2 as createCipheriv,
  createDecipheriv2 as createDecipheriv,
  createDiffieHellman2 as createDiffieHellman,
  createDiffieHellmanGroup2 as createDiffieHellmanGroup,
  createECDH2 as createECDH,
  createHash2 as createHash,
  createHmac2 as createHmac,
  createPrivateKey2 as createPrivateKey,
  createPublicKey2 as createPublicKey,
  createSecretKey2 as createSecretKey,
  createSign2 as createSign,
  createVerify2 as createVerify,
  crypto_default as default,
  diffieHellman2 as diffieHellman,
  fips2 as fips,
  generateKey2 as generateKey,
  generateKeyPair2 as generateKeyPair,
  generateKeyPairSync2 as generateKeyPairSync,
  generateKeySync2 as generateKeySync,
  generatePrime2 as generatePrime,
  generatePrimeSync2 as generatePrimeSync,
  getCipherInfo2 as getCipherInfo,
  getCiphers2 as getCiphers,
  getCurves2 as getCurves,
  getDiffieHellman2 as getDiffieHellman,
  getFips2 as getFips,
  getHashes2 as getHashes,
  getRandomValues2 as getRandomValues,
  hash2 as hash,
  hkdf2 as hkdf,
  hkdfSync2 as hkdfSync,
  pbkdf22 as pbkdf2,
  pbkdf2Sync2 as pbkdf2Sync,
  privateDecrypt2 as privateDecrypt,
  privateEncrypt2 as privateEncrypt,
  publicDecrypt2 as publicDecrypt,
  publicEncrypt2 as publicEncrypt,
  randomBytes2 as randomBytes,
  randomFill2 as randomFill,
  randomFillSync2 as randomFillSync,
  randomInt2 as randomInt,
  randomUUID2 as randomUUID,
  scrypt2 as scrypt,
  scryptSync2 as scryptSync,
  secureHeapUsed2 as secureHeapUsed,
  setEngine2 as setEngine,
  setFips2 as setFips,
  sign2 as sign,
  subtle2 as subtle,
  timingSafeEqual2 as timingSafeEqual,
  verify2 as verify,
  webcrypto2 as webcrypto
};
//# sourceMappingURL=@cloudflare_unenv-preset_node_crypto.js.map
