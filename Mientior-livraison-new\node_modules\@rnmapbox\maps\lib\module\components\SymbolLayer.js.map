{"version": 3, "names": ["React", "View", "NativeModules", "RNMBXSymbolLayerNativeComponent", "AbstractLayer", "jsx", "_jsx", "NATIVE_MODULE_NAME", "Mapbox", "RNMBXModule", "SymbolLayer", "defaultProps", "sourceID", "StyleSource", "DefaultSourceID", "deprecationLogged", "snapshot", "_shouldSnapshot", "isSnapshot", "Children", "count", "baseProps", "children", "for<PERSON>ach", "child", "type", "console", "warn", "render", "props", "sourceLayerID", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../../src", "sources": ["components/SymbolLayer.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAe,OAAO;AAClC,SAASC,IAAI,EAAEC,aAAa,QAAQ,cAAc;AAOlD,OAAOC,+BAA+B,MAAM,0CAA0C;AAEtF,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE5C,OAAO,MAAMC,kBAAkB,GAAG,kBAAkB;AAEpD,MAAMC,MAAM,GAAGN,aAAa,CAACO,WAAW;;AAExC;;AA+DA;;AAoBA;AACA;AACA;AACA,OAAO,MAAMC,WAAW,SAASN,aAAa,CAAyB;EACrE,OAAOO,YAAY,GAAG;IACpBC,QAAQ,EAAEJ,MAAM,CAACK,WAAW,CAACC;EAC/B,CAAC;EACDC,iBAAiB,GAA0B;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAE9DC,eAAeA,CAAA,EAAG;IAChB,IAAIC,UAAU,GAAG,KAAK;IAEtB,IAAIlB,KAAK,CAACmB,QAAQ,CAACC,KAAK,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACtD,OAAOJ,UAAU;IACnB;IAEAlB,KAAK,CAACmB,QAAQ,CAACI,OAAO,CAAC,IAAI,CAACF,SAAS,CAACC,QAAQ,EAAGE,KAAK,IAAK;MACzD,IAAIA,KAAK,EAAEC,IAAI,KAAKxB,IAAI,EAAE;QACxBiB,UAAU,GAAG,IAAI;MACnB;IACF,CAAC,CAAC;IACF,IAAIA,UAAU,IAAI,CAAC,IAAI,CAACH,iBAAiB,CAACC,QAAQ,EAAE;MAClDU,OAAO,CAACC,IAAI,CACV,uLACF,CAAC;MACD,IAAI,CAACZ,iBAAiB,CAACC,QAAQ,GAAG,IAAI;IACxC;IAEA,OAAOE,UAAU;EACnB;EAEAU,MAAMA,CAAA,EAAG;IACP,MAAMC,KAAK,GAAG;MACZ,GAAG,IAAI,CAACR,SAAS;MACjBL,QAAQ,EAAE,IAAI,CAACC,eAAe,CAAC,CAAC;MAChCa,aAAa,EAAE,IAAI,CAACD,KAAK,CAACC;IAC5B,CAAC;IAED;MAAA;MACE;MACAxB,IAAA,CAACH,+BAA+B;QAAC4B,GAAG,EAAE,IAAI,CAACC,cAAe;QAAA,GAAKH,KAAK;QAAAP,QAAA,EACjE,IAAI,CAACO,KAAK,CAACP;MAAQ,CACW;IAAC;EAEtC;AACF", "ignoreList": []}