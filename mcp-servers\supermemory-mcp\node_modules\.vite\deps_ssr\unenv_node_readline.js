import {
  noop_default
} from "./chunk-DQN6H3OM.js";
import {
  Interface
} from "./chunk-NVGP5Q3A.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/readline.mjs
import promises from "node:readline/promises";
var clearLine = () => false;
var clearScreenDown = () => false;
var createInterface = () => new Interface();
var cursorTo = () => false;
var emitKeypressEvents = noop_default;
var moveCursor = () => false;
var readline_default = {
  clearLine,
  clearScreenDown,
  createInterface,
  cursorTo,
  emitKeypressEvents,
  moveCursor,
  Interface,
  promises
};
export {
  Interface,
  clearLine,
  clearScreenDown,
  createInterface,
  cursorTo,
  readline_default as default,
  emitKeypressEvents,
  moveCursor,
  promises
};
//# sourceMappingURL=unenv_node_readline.js.map
