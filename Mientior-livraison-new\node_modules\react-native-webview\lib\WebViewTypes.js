var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.NativeWebViewWindows=exports.NativeWebViewMacOS=void 0;var _createClass2=_interopRequireDefault(require("@babel/runtime/helpers/createClass"));var _classCallCheck2=_interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));var _possibleConstructorReturn2=_interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));var _getPrototypeOf2=_interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));var _inherits2=_interopRequireDefault(require("@babel/runtime/helpers/inherits"));var _react=require("react");function _callSuper(t,o,e){return o=(0,_getPrototypeOf2.default)(o),(0,_possibleConstructorReturn2.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,e||[],(0,_getPrototypeOf2.default)(t).constructor):o.apply(t,e));}function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}));}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t;})();}var NativeWebViewMacOS=exports.NativeWebViewMacOS=function(_NativeWebViewMacOSBa){(0,_inherits2.default)(NativeWebViewMacOS,_NativeWebViewMacOSBa);function NativeWebViewMacOS(){(0,_classCallCheck2.default)(this,NativeWebViewMacOS);return _callSuper(this,NativeWebViewMacOS,arguments);}return(0,_createClass2.default)(NativeWebViewMacOS);}(NativeWebViewMacOSBase);var NativeWebViewWindows=exports.NativeWebViewWindows=function(_NativeWebViewWindows){(0,_inherits2.default)(NativeWebViewWindows,_NativeWebViewWindows);function NativeWebViewWindows(){(0,_classCallCheck2.default)(this,NativeWebViewWindows);return _callSuper(this,NativeWebViewWindows,arguments);}return(0,_createClass2.default)(NativeWebViewWindows);}(NativeWebViewWindowsBase);