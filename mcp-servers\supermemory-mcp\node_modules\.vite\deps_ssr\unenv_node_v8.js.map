{"version": 3, "sources": ["../../unenv/dist/runtime/node/v8.mjs", "../../unenv/dist/runtime/node/internal/v8/deserializer.mjs", "../../unenv/dist/runtime/node/internal/v8/serializer.mjs", "../../unenv/dist/runtime/node/internal/v8/profiler.mjs"], "sourcesContent": ["import noop from \"../mock/noop.mjs\";\nimport { Readable } from \"node:stream\";\nimport { Deserializer, DefaultDeserializer } from \"./internal/v8/deserializer.mjs\";\nimport { Serializer, DefaultSerializer } from \"./internal/v8/serializer.mjs\";\nimport { <PERSON>CProfiler } from \"./internal/v8/profiler.mjs\";\nexport { Deserializer, DefaultDeserializer } from \"./internal/v8/deserializer.mjs\";\nexport { Serializer, DefaultSerializer } from \"./internal/v8/serializer.mjs\";\nexport { GCProfiler } from \"./internal/v8/profiler.mjs\";\nconst getMockHeapSpaceStats = (name) => ({\n\tspace_name: name,\n\tspace_size: 0,\n\tspace_used_size: 0,\n\tspace_available_size: 0,\n\tphysical_space_size: 0\n});\nexport const cachedDataVersionTag = () => 0;\nexport const deserialize = noop;\nexport const getHeapCodeStatistics = () => ({\n\tcode_and_metadata_size: 0,\n\tbytecode_and_metadata_size: 0,\n\texternal_script_source_size: 0,\n\tcpu_profiler_metadata_size: 0\n});\nexport const getHeapSpaceStatistics = () => [\n\t\"read_only_space\",\n\t\"new_space\",\n\t\"old_space\",\n\t\"code_space\",\n\t\"map_space\",\n\t\"large_object_space\",\n\t\"code_large_object_space\",\n\t\"new_large_object_space\"\n].map((space) => getMockHeapSpaceStats(space));\nexport const getHeapStatistics = () => ({\n\ttotal_heap_size: 0,\n\ttotal_heap_size_executable: 0,\n\ttotal_physical_size: 0,\n\ttotal_available_size: 0,\n\tused_heap_size: 0,\n\theap_size_limit: 0,\n\tmalloced_memory: 0,\n\tpeak_malloced_memory: 0,\n\tdoes_zap_garbage: 0,\n\tnumber_of_native_contexts: 0,\n\tnumber_of_detached_contexts: 0,\n\ttotal_global_handles_size: 0,\n\tused_global_handles_size: 0,\n\texternal_memory: 0\n});\nexport const getHeapSnapshot = () => {\n\treturn Readable.from(`{\n    snapshot: {},\n    nodes: [],\n    edges: [],\n    trace_function_infos: [],\n    trace_tree: [],\n    samples: [],\n    locations: [],\n    strings: [],\n  }`);\n};\nexport const promiseHooks = {\n\tonInit: () => noop,\n\tonSettled: () => noop,\n\tonBefore: () => noop,\n\tonAfter: () => noop,\n\tcreateHook: () => noop\n};\nexport const serialize = (value) => Buffer.from(value);\nexport const setFlagsFromString = noop;\nexport const setHeapSnapshotNearHeapLimit = noop;\nexport const startupSnapshot = {\n\taddDeserializeCallback: noop,\n\taddSerializeCallback: noop,\n\tsetDeserializeMainFunction: noop,\n\tisBuildingSnapshot: () => false\n};\nexport const stopCoverage = noop;\nexport const takeCoverage = noop;\nexport const writeHeapSnapshot = () => \"\";\nexport function queryObjects(_ctor, options) {\n\tif (options?.format === \"count\") {\n\t\treturn 0;\n\t}\n\treturn [];\n}\nexport default {\n\tDefaultDeserializer,\n\tDeserializer,\n\tGCProfiler,\n\tDefaultSerializer,\n\tSerializer,\n\tcachedDataVersionTag,\n\tdeserialize,\n\tgetHeapCodeStatistics,\n\tgetHeapSnapshot,\n\tgetHeapSpaceStatistics,\n\tgetHeapStatistics,\n\tpromiseHooks,\n\tserialize,\n\tsetFlagsFromString,\n\tsetHeapSnapshotNearHeapLimit,\n\tstartupSnapshot,\n\tstopCoverage,\n\ttakeCoverage,\n\twriteHeapSnapshot,\n\tqueryObjects\n};\n", "export class Deserializer {\n\treadHeader() {\n\t\treturn false;\n\t}\n\treadValue() {}\n\ttransferArrayBuffer(id, arrayBuffer) {}\n\tgetWireFormatVersion() {\n\t\treturn 0;\n\t}\n\treadUint32() {\n\t\treturn 0;\n\t}\n\treadUint64() {\n\t\treturn [0, 0];\n\t}\n\treadDouble() {\n\t\treturn 0;\n\t}\n\treadRawBytes(length) {\n\t\treturn Buffer.from(\"\");\n\t}\n}\nexport class DefaultDeserializer extends Deserializer {}\n", "export class Serializer {\n\twriteHeader() {}\n\twriteValue(val) {\n\t\treturn false;\n\t}\n\treleaseBuffer() {\n\t\treturn Buffer.from(\"\");\n\t}\n\ttransferArrayBuffer(id, arrayBuffer) {}\n\twriteDouble(value) {}\n\twriteUint32(value) {}\n\twriteUint64(hi, lo) {}\n\twriteRawBytes(buffer) {}\n}\nexport class DefaultSerializer extends Serializer {}\n", "export class GCProfiler {\n\tstart() {}\n\tstop() {\n\t\treturn {\n\t\t\tversion: 1,\n\t\t\tstartTime: 0,\n\t\t\tendTime: 0,\n\t\t\tstatistics: []\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;AACA,SAAS,gBAAgB;;;ACDlB,IAAM,eAAN,MAAmB;AAAA,EACzB,aAAa;AACZ,WAAO;AAAA,EACR;AAAA,EACA,YAAY;AAAA,EAAC;AAAA,EACb,oBAAoB,IAAI,aAAa;AAAA,EAAC;AAAA,EACtC,uBAAuB;AACtB,WAAO;AAAA,EACR;AAAA,EACA,aAAa;AACZ,WAAO;AAAA,EACR;AAAA,EACA,aAAa;AACZ,WAAO,CAAC,GAAG,CAAC;AAAA,EACb;AAAA,EACA,aAAa;AACZ,WAAO;AAAA,EACR;AAAA,EACA,aAAa,QAAQ;AACpB,WAAO,OAAO,KAAK,EAAE;AAAA,EACtB;AACD;AACO,IAAM,sBAAN,cAAkC,aAAa;AAAC;;;ACtBhD,IAAM,aAAN,MAAiB;AAAA,EACvB,cAAc;AAAA,EAAC;AAAA,EACf,WAAW,KAAK;AACf,WAAO;AAAA,EACR;AAAA,EACA,gBAAgB;AACf,WAAO,OAAO,KAAK,EAAE;AAAA,EACtB;AAAA,EACA,oBAAoB,IAAI,aAAa;AAAA,EAAC;AAAA,EACtC,YAAY,OAAO;AAAA,EAAC;AAAA,EACpB,YAAY,OAAO;AAAA,EAAC;AAAA,EACpB,YAAY,IAAI,IAAI;AAAA,EAAC;AAAA,EACrB,cAAc,QAAQ;AAAA,EAAC;AACxB;AACO,IAAM,oBAAN,cAAgC,WAAW;AAAC;;;ACd5C,IAAM,aAAN,MAAiB;AAAA,EACvB,QAAQ;AAAA,EAAC;AAAA,EACT,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,SAAS;AAAA,MACT,YAAY,CAAC;AAAA,IACd;AAAA,EACD;AACD;;;AHFA,IAAM,wBAAwB,CAAC,UAAU;AAAA,EACxC,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,qBAAqB;AACtB;AACO,IAAM,uBAAuB,MAAM;AACnC,IAAM,cAAc;AACpB,IAAM,wBAAwB,OAAO;AAAA,EAC3C,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAC7B;AACO,IAAM,yBAAyB,MAAM;AAAA,EAC3C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,EAAE,IAAI,CAAC,UAAU,sBAAsB,KAAK,CAAC;AACtC,IAAM,oBAAoB,OAAO;AAAA,EACvC,iBAAiB;AAAA,EACjB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,iBAAiB;AAClB;AACO,IAAM,kBAAkB,MAAM;AACpC,SAAO,SAAS,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASlB;AACJ;AACO,IAAM,eAAe;AAAA,EAC3B,QAAQ,MAAM;AAAA,EACd,WAAW,MAAM;AAAA,EACjB,UAAU,MAAM;AAAA,EAChB,SAAS,MAAM;AAAA,EACf,YAAY,MAAM;AACnB;AACO,IAAM,YAAY,CAAC,UAAU,OAAO,KAAK,KAAK;AAC9C,IAAM,qBAAqB;AAC3B,IAAM,+BAA+B;AACrC,IAAM,kBAAkB;AAAA,EAC9B,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,oBAAoB,MAAM;AAC3B;AACO,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,oBAAoB,MAAM;AAChC,SAAS,aAAa,OAAO,SAAS;AAC5C,MAAI,SAAS,WAAW,SAAS;AAChC,WAAO;AAAA,EACR;AACA,SAAO,CAAC;AACT;AACA,IAAO,aAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}