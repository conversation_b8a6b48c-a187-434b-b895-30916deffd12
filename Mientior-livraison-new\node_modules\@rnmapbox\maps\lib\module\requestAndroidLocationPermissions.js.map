{"version": 3, "names": ["PermissionsAndroid", "isAndroid", "requestAndroidLocationPermissions", "res", "requestMultiple", "PERMISSIONS", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "permissions", "Object", "keys", "permission", "RESULTS", "GRANTED", "Error"], "sourceRoot": "../../src", "sources": ["requestAndroidLocationPermissions.ts"], "mappings": ";;AAAA,SAAqBA,kBAAkB,QAAQ,cAAc;AAE7D,SAASC,SAAS,QAAQ,SAAS;AAEnC,OAAO,eAAeC,iCAAiCA,CAAA,EAAqB;EAC1E,IAAID,SAAS,CAAC,CAAC,EAAE;IACf,MAAME,GAAG,GAAG,MAAMH,kBAAkB,CAACI,eAAe,CAAC,CACnDJ,kBAAkB,CAACK,WAAW,CAACC,oBAAoB,EACnDN,kBAAkB,CAACK,WAAW,CAACE,sBAAsB,CACtD,CAAC;IAEF,IAAI,CAACJ,GAAG,EAAE;MACR,OAAO,KAAK;IACd;IAEA,MAAMK,WAAqB,GAAGC,MAAM,CAACC,IAAI,CAACP,GAAG,CAAC;IAC9C,KAAK,MAAMQ,UAAU,IAAIH,WAAW,EAAE;MACpC,IACEL,GAAG,CAACQ,UAAU,CAAe,KAAKX,kBAAkB,CAACY,OAAO,CAACC,OAAO,EACpE;QACA,OAAO,IAAI;MACb;IACF;IAEA,OAAO,KAAK;EACd;EAEA,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;AACjE", "ignoreList": []}