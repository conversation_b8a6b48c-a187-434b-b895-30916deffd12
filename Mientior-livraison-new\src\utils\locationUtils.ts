/**
 * Utilitaires pour la validation et manipulation sécurisée des données de localisation
 * Évite les erreurs "Cannot read property 'latitude' of undefined"
 */

import { Location } from '../types';

/**
 * Valide qu'un objet location a les propriétés requises et valides
 * @param location - L'objet location à valider
 * @returns true si la location est valide, false sinon
 */
export const isValidLocation = (location: any): location is Location => {
  if (!location || typeof location !== 'object') {
    return false;
  }

  const { latitude, longitude } = location;

  // Vérifier que latitude et longitude sont des nombres valides
  if (typeof latitude !== 'number' || typeof longitude !== 'number') {
    return false;
  }

  // Vérifier que les valeurs ne sont pas NaN
  if (isNaN(latitude) || isNaN(longitude)) {
    return false;
  }

  // Vérifier que les coordonnées sont dans les plages valides
  if (latitude < -90 || latitude > 90) {
    return false;
  }

  if (longitude < -180 || longitude > 180) {
    return false;
  }

  return true;
};

/**
 * Nettoie et valide un objet location, retourne null si invalide
 * @param location - L'objet location à nettoyer
 * @returns Location valide ou null
 */
export const sanitizeLocation = (location: any): Location | null => {
  if (!isValidLocation(location)) {
    return null;
  }

  return {
    latitude: location.latitude,
    longitude: location.longitude,
    altitude: typeof location.altitude === 'number' ? location.altitude : undefined,
    accuracy: typeof location.accuracy === 'number' ? location.accuracy : undefined,
    timestamp: typeof location.timestamp === 'number' ? location.timestamp : Date.now(),
  };
};

/**
 * Parse de manière sécurisée une chaîne JSON contenant des données de localisation
 * @param locationString - La chaîne JSON à parser
 * @returns Location valide ou null
 */
export const parseLocationFromString = (locationString: string | null): Location | null => {
  if (!locationString || typeof locationString !== 'string') {
    return null;
  }

  try {
    const parsed = JSON.parse(locationString);
    return sanitizeLocation(parsed);
  } catch (error) {
    console.error('Erreur parsing location string:', error);
    return null;
  }
};

/**
 * Convertit une location en chaîne JSON de manière sécurisée
 * @param location - L'objet location à sérialiser
 * @returns Chaîne JSON ou null si la location est invalide
 */
export const stringifyLocation = (location: any): string | null => {
  const validLocation = sanitizeLocation(location);
  if (!validLocation) {
    return null;
  }

  try {
    return JSON.stringify(validLocation);
  } catch (error) {
    console.error('Erreur stringify location:', error);
    return null;
  }
};

/**
 * Calcule la distance entre deux points géographiques (formule de Haversine)
 * @param location1 - Premier point
 * @param location2 - Deuxième point
 * @returns Distance en kilomètres, ou null si les locations sont invalides
 */
export const calculateDistance = (location1: any, location2: any): number | null => {
  const loc1 = sanitizeLocation(location1);
  const loc2 = sanitizeLocation(location2);

  if (!loc1 || !loc2) {
    return null;
  }

  const R = 6371; // Rayon de la Terre en kilomètres
  const dLat = toRad(loc2.latitude - loc1.latitude);
  const dLon = toRad(loc2.longitude - loc1.longitude);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(loc1.latitude)) * Math.cos(toRad(loc2.latitude)) * 
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;

  return distance;
};

/**
 * Convertit les degrés en radians
 * @param degrees - Valeur en degrés
 * @returns Valeur en radians
 */
const toRad = (degrees: number): number => {
  return degrees * (Math.PI / 180);
};

/**
 * Obtient une location par défaut pour l'Afrique de l'Ouest
 * @param country - Code pays optionnel pour une position plus précise
 * @returns Location par défaut
 */
export const getDefaultLocation = (country?: string): Location => {
  const defaultLocations: { [key: string]: Location } = {
    'ci': { latitude: 5.3600, longitude: -4.0083, accuracy: 1000, timestamp: Date.now() }, // Abidjan, Côte d'Ivoire
    'sn': { latitude: 14.6928, longitude: -17.4467, accuracy: 1000, timestamp: Date.now() }, // Dakar, Sénégal
    'gh': { latitude: 5.6037, longitude: -0.1870, accuracy: 1000, timestamp: Date.now() }, // Accra, Ghana
    'ng': { latitude: 6.5244, longitude: 3.3792, accuracy: 1000, timestamp: Date.now() }, // Lagos, Nigeria
    'bj': { latitude: 6.3702, longitude: 2.3912, accuracy: 1000, timestamp: Date.now() }, // Cotonou, Bénin
    'ml': { latitude: 12.6392, longitude: -8.0029, accuracy: 1000, timestamp: Date.now() }, // Bamako, Mali
    'bf': { latitude: 12.3714, longitude: -1.5197, accuracy: 1000, timestamp: Date.now() }, // Ouagadougou, Burkina Faso
  };

  return defaultLocations[country || 'ci'] || defaultLocations['ci'];
};

/**
 * Vérifie si une location est dans une zone géographique donnée
 * @param location - Location à vérifier
 * @param center - Centre de la zone
 * @param radiusKm - Rayon en kilomètres
 * @returns true si la location est dans la zone
 */
export const isLocationInRadius = (location: any, center: any, radiusKm: number): boolean => {
  const distance = calculateDistance(location, center);
  return distance !== null && distance <= radiusKm;
};

/**
 * Formate une location pour l'affichage
 * @param location - Location à formater
 * @param precision - Nombre de décimales (défaut: 4)
 * @returns Chaîne formatée ou 'Position invalide'
 */
export const formatLocationForDisplay = (location: any, precision: number = 4): string => {
  const validLocation = sanitizeLocation(location);
  if (!validLocation) {
    return 'Position invalide';
  }

  return `${validLocation.latitude.toFixed(precision)}, ${validLocation.longitude.toFixed(precision)}`;
};

/**
 * Crée une location de test pour le développement
 * @param baseLocation - Location de base (optionnelle)
 * @param variance - Variance maximale en degrés (défaut: 0.01)
 * @returns Location de test
 */
export const createTestLocation = (baseLocation?: Location, variance: number = 0.01): Location => {
  const base = baseLocation || getDefaultLocation('bj'); // Cotonou par défaut
  
  return {
    latitude: base.latitude + (Math.random() - 0.5) * variance,
    longitude: base.longitude + (Math.random() - 0.5) * variance,
    accuracy: 10 + Math.random() * 20,
    timestamp: Date.now(),
  };
};
