import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Animated,
  TextInput,
  Alert,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '../../store/authStore';
import { locationService, AddressSuggestion } from '../../services/locationService';
import { addressService } from '../../services/addressService';
import { Location as LocationType, Address } from '../../types';
import GoogleMapView from '../../components/GoogleMapView';



interface LocationScreenCleanProps {
  navigation: any;
}

const LocationScreenClean: React.FC<LocationScreenCleanProps> = ({ navigation }) => {
  // Store integration
  const {
    currentLocation,
    setCurrentLocation,
    userAddresses,
    addAddress,
    loadUserAddresses,
    user
  } = useAuthStore();

  // Local state
  const [selectedLocation, setSelectedLocation] = useState<LocationType | null>(currentLocation);
  const [address, setAddress] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [isLoadingAddress, setIsLoadingAddress] = useState(false);
  const [addressSuggestions, setAddressSuggestions] = useState<AddressSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    // Load user addresses
    if (user) {
      loadUserAddresses();
    }

    // Initialize with current location if available
    if (currentLocation) {
      setSelectedLocation(currentLocation);
      updateAddressFromLocation(currentLocation);
    }
  }, [user]);

  // Update address when location changes
  const updateAddressFromLocation = async (location: LocationType) => {
    setIsLoadingAddress(true);
    try {
      const result = await locationService.reverseGeocode(location.latitude, location.longitude);
      setAddress(result.address);
    } catch (error) {
      console.error('Erreur géocodage inverse:', error);
      setAddress(`${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`);
    } finally {
      setIsLoadingAddress(false);
    }
  };

  // Handle map location selection from Google Maps
  const handleMapLocationSelect = (location: LocationType) => {
    setSelectedLocation(location);
    updateAddressFromLocation(location);
  };

  const handleConfirmLocation = async () => {
    if (selectedLocation && user) {
      try {
        setIsLoadingLocation(true);

        // Update current location in store
        setCurrentLocation(selectedLocation);

        // Save as new address if user wants
        const addressData = {
          label: 'other' as const,
          nom_complet: address,
          adresse_ligne1: address,
          ville: 'Ville',
          quartier: 'Quartier',
          pays: 'Côte d\'Ivoire',
          coordonnees: selectedLocation,
          is_default: userAddresses.length === 0, // First address becomes default
        };

        await addAddress(addressData);

        Alert.alert(
          'Position confirmée',
          'Votre localisation a été enregistrée avec succès.',
          [
            {
              text: 'Continuer',
              onPress: () => navigation.goBack(),
            },
          ]
        );
      } catch (error) {
        console.error('Erreur sauvegarde localisation:', error);
        Alert.alert(
          'Erreur',
          'Impossible de sauvegarder votre localisation. Veuillez réessayer.',
          [{ text: 'OK' }]
        );
      } finally {
        setIsLoadingLocation(false);
      }
    }
  };

  const handleCurrentLocation = async () => {
    setIsLoadingLocation(true);
    try {
      const location = await locationService.getCurrentLocation({
        accuracy: 'high' as any,
        timeout: 15000,
      });

      setSelectedLocation(location);
      setCurrentLocation(location);
      await updateAddressFromLocation(location);

      Alert.alert('Position obtenue', 'Votre position actuelle a été détectée avec succès.');
    } catch (error) {
      console.error('Erreur géolocalisation:', error);
      locationService.handleLocationError(error);
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // Handle search input changes with enhanced address service
  const handleSearchChange = async (text: string) => {
    setSearchQuery(text);

    if (text.length > 2) {
      setShowSuggestions(true);
      try {
        // Use enhanced address service for better suggestions
        const results = await addressService.searchAddresses(text, selectedLocation || undefined);
        const suggestions: AddressSuggestion[] = results.map((result, index) => ({
          id: result.id || `suggestion-${index}`,
          description: result.description,
          latitude: result.latitude,
          longitude: result.longitude,
        }));
        setAddressSuggestions(suggestions);
      } catch (error) {
        console.error('Erreur recherche adresse:', error);
        // Fallback to basic geocoding
        try {
          const fallbackResults = await locationService.geocode(text);
          const fallbackSuggestions: AddressSuggestion[] = fallbackResults.map((result, index) => ({
            id: `fallback-${index}`,
            description: result.address,
            latitude: result.latitude,
            longitude: result.longitude,
          }));
          setAddressSuggestions(fallbackSuggestions);
        } catch (fallbackError) {
          setAddressSuggestions([]);
        }
      }
    } else {
      setShowSuggestions(false);
      setAddressSuggestions([]);
    }
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: AddressSuggestion) => {
    if (suggestion.latitude && suggestion.longitude) {
      const location: LocationType = {
        latitude: suggestion.latitude,
        longitude: suggestion.longitude,
        timestamp: Date.now(),
      };

      setSelectedLocation(location);
      setAddress(suggestion.description);
      setSearchQuery(suggestion.description);
      setShowSuggestions(false);
    }
  };

  // Handle saved address selection
  const handleSavedAddressSelect = (savedAddress: Address) => {
    const location: LocationType = {
      latitude: savedAddress.coordonnees.latitude,
      longitude: savedAddress.coordonnees.longitude,
      timestamp: Date.now(),
    };

    setSelectedLocation(location);
    setCurrentLocation(location);
    setAddress(savedAddress.adresse_ligne1);
    setSearchQuery(savedAddress.adresse_ligne1);
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Votre localisation</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color="#9CA3AF" style={styles.searchIcon} />
          <TextInput
            style={styles.searchPlaceholder}
            placeholder="Rechercher une adresse"
            placeholderTextColor="#9CA3AF"
            value={searchQuery}
            onChangeText={handleSearchChange}
            onFocus={() => setShowSuggestions(true)}
          />
          {isLoadingAddress && (
            <ActivityIndicator size="small" color="#0DCAA8" />
          )}
        </View>

        {/* Address Suggestions */}
        {showSuggestions && addressSuggestions.length > 0 && (
          <View style={styles.suggestionsContainer}>
            <FlatList
              data={addressSuggestions}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.suggestionItem}
                  onPress={() => handleSuggestionSelect(item)}
                >
                  <Ionicons name="location-outline" size={16} color="#6B7280" />
                  <Text style={styles.suggestionText}>{item.description}</Text>
                </TouchableOpacity>
              )}
              style={styles.suggestionsList}
              showsVerticalScrollIndicator={false}
            />
          </View>
        )}
      </View>

      {/* Google Maps Container */}
      <Animated.View style={[styles.mapContainer, { opacity: fadeAnim }]}>
        <GoogleMapView
          initialLocation={currentLocation || undefined}
          selectedLocation={selectedLocation}
          onLocationSelect={handleMapLocationSelect}
          onCurrentLocationPress={handleCurrentLocation}
          showCurrentLocationButton={true}
          isLoading={isLoadingLocation}
          style={styles.mapArea}
        />
      </Animated.View>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        <View style={styles.locationInfo}>
          <Text style={styles.locationCode}>QPPH+6J5</Text>
          <Text style={styles.locationAddress}>{address}</Text>
          <Text style={styles.coordinates}>
            Lat: {selectedLocation?.latitude.toFixed(4) || '6.3702'}, Long: {selectedLocation?.longitude.toFixed(4) || '2.3912'}
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.confirmButton, isLoadingLocation && styles.confirmButtonDisabled]}
          onPress={handleConfirmLocation}
          disabled={isLoadingLocation}
        >
          {isLoadingLocation ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.confirmButtonText}>Confirmer ma position</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.manualButton}
          onPress={() => {
            // Allow manual selection by tapping on map
            Alert.alert(
              'Sélection manuelle',
              'Appuyez sur la carte pour sélectionner une position précise.',
              [{ text: 'Compris' }]
            );
          }}
        >
          <Text style={styles.manualButtonText}>Sélectionner sur la carte</Text>
        </TouchableOpacity>

        {/* Saved Addresses */}
        <View style={styles.recentPlaces}>
          <Text style={styles.recentTitle}>Adresses sauvegardées</Text>

          {userAddresses.length > 0 ? (
            userAddresses.slice(0, 3).map((savedAddress) => (
              <TouchableOpacity
                key={savedAddress.id}
                style={styles.placeItem}
                onPress={() => handleSavedAddressSelect(savedAddress)}
              >
                <Ionicons
                  name={savedAddress.label === 'home' ? 'home' : savedAddress.label === 'work' ? 'business' : 'location'}
                  size={20}
                  color="#6B7280"
                />
                <View style={styles.placeInfo}>
                  <Text style={styles.placeName}>
                    {savedAddress.label === 'home' ? 'Domicile' :
                     savedAddress.label === 'work' ? 'Bureau' :
                     savedAddress.nom_complet}
                  </Text>
                  <Text style={styles.placeLocation}>{savedAddress.adresse_ligne1}</Text>
                </View>
                {savedAddress.is_default && (
                  <View style={styles.defaultBadge}>
                    <Text style={styles.defaultText}>Défaut</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))
          ) : (
            <Text style={styles.noAddressesText}>Aucune adresse sauvegardée</Text>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchPlaceholder: {
    fontSize: 16,
    color: '#9CA3AF',
    flex: 1,
  },
  mapContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: '#F8F9FA',
  },
  mapArea: {
    flex: 1,
  },
  bottomSheet: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  locationInfo: {
    marginBottom: 24,
  },
  locationCode: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 8,
  },
  coordinates: {
    fontSize: 14,
    color: '#9CA3AF',
  },
  confirmButton: {
    backgroundColor: '#10B981',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  manualButton: {
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 24,
  },
  manualButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#10B981',
  },
  recentPlaces: {
    gap: 16,
  },
  recentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  placeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  placeInfo: {
    flex: 1,
  },
  placeName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 2,
  },
  placeLocation: {
    fontSize: 14,
    color: '#6B7280',
  },
  // New styles for enhanced functionality
  suggestionsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginTop: 8,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  suggestionsList: {
    maxHeight: 200,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  suggestionText: {
    fontSize: 14,
    color: '#1F2937',
    marginLeft: 12,
    flex: 1,
  },
  confirmButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  defaultBadge: {
    backgroundColor: '#0DCAA8',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 8,
  },
  defaultText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  noAddressesText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 16,
  },
});

export default LocationScreenClean;
