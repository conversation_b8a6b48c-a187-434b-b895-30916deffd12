import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Animated,
  TextInput,
  Alert,
  Dimensions,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '../../store/authStore';
import { locationService, AddressSuggestion, GeocodingResult } from '../../services/locationService';
import { Location as LocationType, Address } from '../../types';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface LocationScreenCleanProps {
  navigation: any;
}

const LocationScreenClean: React.FC<LocationScreenCleanProps> = ({ navigation }) => {
  // Store integration
  const {
    currentLocation,
    setCurrentLocation,
    userAddresses,
    addAddress,
    loadUserAddresses,
    user
  } = useAuthStore();

  // Local state
  const [selectedLocation, setSelectedLocation] = useState<LocationType | null>(currentLocation);
  const [address, setAddress] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [isLoadingAddress, setIsLoadingAddress] = useState(false);
  const [addressSuggestions, setAddressSuggestions] = useState<AddressSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    // Load user addresses
    if (user) {
      loadUserAddresses();
    }

    // Initialize with current location if available
    if (currentLocation) {
      setSelectedLocation(currentLocation);
      updateAddressFromLocation(currentLocation);
    }
  }, [user]);

  // Update address when location changes
  const updateAddressFromLocation = async (location: LocationType) => {
    setIsLoadingAddress(true);
    try {
      const result = await locationService.reverseGeocode(location.latitude, location.longitude);
      setAddress(result.address);
    } catch (error) {
      console.error('Erreur géocodage inverse:', error);
      setAddress(`${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`);
    } finally {
      setIsLoadingAddress(false);
    }
  };

  const handleMapPress = () => {
    // Simulation de sélection de position sur la carte
    if (selectedLocation) {
      const newLat = selectedLocation.latitude + (Math.random() - 0.5) * 0.01;
      const newLng = selectedLocation.longitude + (Math.random() - 0.5) * 0.01;

      const newLocation: LocationType = {
        latitude: newLat,
        longitude: newLng,
        timestamp: Date.now(),
      };

      setSelectedLocation(newLocation);
      updateAddressFromLocation(newLocation);
    }
  };

  const handleConfirmLocation = async () => {
    if (selectedLocation && user) {
      try {
        setIsLoadingLocation(true);

        // Update current location in store
        setCurrentLocation(selectedLocation);

        // Save as new address if user wants
        const addressData = {
          label: 'other' as const,
          nom_complet: address,
          adresse_ligne1: address,
          ville: 'Ville',
          quartier: 'Quartier',
          pays: 'Côte d\'Ivoire',
          coordonnees: selectedLocation,
          is_default: userAddresses.length === 0, // First address becomes default
        };

        await addAddress(addressData);

        Alert.alert(
          'Position confirmée',
          'Votre localisation a été enregistrée avec succès.',
          [
            {
              text: 'Continuer',
              onPress: () => navigation.goBack(),
            },
          ]
        );
      } catch (error) {
        console.error('Erreur sauvegarde localisation:', error);
        Alert.alert(
          'Erreur',
          'Impossible de sauvegarder votre localisation. Veuillez réessayer.',
          [{ text: 'OK' }]
        );
      } finally {
        setIsLoadingLocation(false);
      }
    }
  };

  const handleCurrentLocation = async () => {
    setIsLoadingLocation(true);
    try {
      const location = await locationService.getCurrentLocation({
        accuracy: 'high' as any,
        timeout: 15000,
      });

      setSelectedLocation(location);
      setCurrentLocation(location);
      await updateAddressFromLocation(location);

      Alert.alert('Position obtenue', 'Votre position actuelle a été détectée avec succès.');
    } catch (error) {
      console.error('Erreur géolocalisation:', error);
      locationService.handleLocationError(error);
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // Handle search input changes
  const handleSearchChange = async (text: string) => {
    setSearchQuery(text);

    if (text.length > 2) {
      setShowSuggestions(true);
      try {
        // Simple geocoding for address suggestions
        const results = await locationService.geocode(text);
        const suggestions: AddressSuggestion[] = results.map((result, index) => ({
          id: `suggestion-${index}`,
          description: result.address,
          latitude: result.latitude,
          longitude: result.longitude,
        }));
        setAddressSuggestions(suggestions);
      } catch (error) {
        console.error('Erreur recherche adresse:', error);
        setAddressSuggestions([]);
      }
    } else {
      setShowSuggestions(false);
      setAddressSuggestions([]);
    }
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: AddressSuggestion) => {
    if (suggestion.latitude && suggestion.longitude) {
      const location: LocationType = {
        latitude: suggestion.latitude,
        longitude: suggestion.longitude,
        timestamp: Date.now(),
      };

      setSelectedLocation(location);
      setAddress(suggestion.description);
      setSearchQuery(suggestion.description);
      setShowSuggestions(false);
    }
  };

  // Handle saved address selection
  const handleSavedAddressSelect = (savedAddress: Address) => {
    const location: LocationType = {
      latitude: savedAddress.coordonnees.latitude,
      longitude: savedAddress.coordonnees.longitude,
      timestamp: Date.now(),
    };

    setSelectedLocation(location);
    setCurrentLocation(location);
    setAddress(savedAddress.adresse_ligne1);
    setSearchQuery(savedAddress.adresse_ligne1);
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Votre localisation</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color="#9CA3AF" style={styles.searchIcon} />
          <TextInput
            style={styles.searchPlaceholder}
            placeholder="Rechercher une adresse"
            placeholderTextColor="#9CA3AF"
            value={searchQuery}
            onChangeText={handleSearchChange}
            onFocus={() => setShowSuggestions(true)}
          />
          {isLoadingAddress && (
            <ActivityIndicator size="small" color="#0DCAA8" />
          )}
        </View>

        {/* Address Suggestions */}
        {showSuggestions && addressSuggestions.length > 0 && (
          <View style={styles.suggestionsContainer}>
            <FlatList
              data={addressSuggestions}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.suggestionItem}
                  onPress={() => handleSuggestionSelect(item)}
                >
                  <Ionicons name="location-outline" size={16} color="#6B7280" />
                  <Text style={styles.suggestionText}>{item.description}</Text>
                </TouchableOpacity>
              )}
              style={styles.suggestionsList}
              showsVerticalScrollIndicator={false}
            />
          </View>
        )}
      </View>

      {/* Map Container - Interface Exacte */}
      <Animated.View style={[styles.mapContainer, { opacity: fadeAnim }]}>
        <TouchableOpacity style={styles.mapArea} onPress={handleMapPress}>
          <View style={styles.mapBackground}>
            {/* Simulation de carte avec grille */}
            <View style={styles.mapGrid}>
              {Array.from({ length: 8 }).map((_, i) => (
                <View key={`h-${i}`} style={[styles.gridLine, { top: (i + 1) * 40 }]} />
              ))}
              {Array.from({ length: 6 }).map((_, i) => (
                <View key={`v-${i}`} style={[styles.gridLineVertical, { left: (i + 1) * 60 }]} />
              ))}
            </View>
            
            {/* Marqueur central */}
            {selectedLocation && (
              <View style={styles.markerContainer}>
                <View style={styles.marker}>
                  <View style={styles.markerInner} />
                </View>
              </View>
            )}
            
            {/* Bouton localisation */}
            <TouchableOpacity
              style={styles.locationButton}
              onPress={handleCurrentLocation}
              disabled={isLoadingLocation}
            >
              {isLoadingLocation ? (
                <ActivityIndicator size="small" color="#10B981" />
              ) : (
                <Ionicons name="locate" size={20} color="#10B981" />
              )}
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Animated.View>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        <View style={styles.locationInfo}>
          <Text style={styles.locationCode}>QPPH+6J5</Text>
          <Text style={styles.locationAddress}>{address}</Text>
          <Text style={styles.coordinates}>
            Lat: {selectedLocation?.latitude.toFixed(4) || '6.3702'}, Long: {selectedLocation?.longitude.toFixed(4) || '2.3912'}
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.confirmButton, isLoadingLocation && styles.confirmButtonDisabled]}
          onPress={handleConfirmLocation}
          disabled={isLoadingLocation}
        >
          {isLoadingLocation ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.confirmButtonText}>Confirmer ma position</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.manualButton}
          onPress={handleMapPress}
        >
          <Text style={styles.manualButtonText}>Sélectionner manuellement</Text>
        </TouchableOpacity>

        {/* Saved Addresses */}
        <View style={styles.recentPlaces}>
          <Text style={styles.recentTitle}>Adresses sauvegardées</Text>

          {userAddresses.length > 0 ? (
            userAddresses.slice(0, 3).map((savedAddress) => (
              <TouchableOpacity
                key={savedAddress.id}
                style={styles.placeItem}
                onPress={() => handleSavedAddressSelect(savedAddress)}
              >
                <Ionicons
                  name={savedAddress.label === 'home' ? 'home' : savedAddress.label === 'work' ? 'business' : 'location'}
                  size={20}
                  color="#6B7280"
                />
                <View style={styles.placeInfo}>
                  <Text style={styles.placeName}>
                    {savedAddress.label === 'home' ? 'Domicile' :
                     savedAddress.label === 'work' ? 'Bureau' :
                     savedAddress.nom_complet}
                  </Text>
                  <Text style={styles.placeLocation}>{savedAddress.adresse_ligne1}</Text>
                </View>
                {savedAddress.is_default && (
                  <View style={styles.defaultBadge}>
                    <Text style={styles.defaultText}>Défaut</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))
          ) : (
            <Text style={styles.noAddressesText}>Aucune adresse sauvegardée</Text>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchPlaceholder: {
    fontSize: 16,
    color: '#9CA3AF',
    flex: 1,
  },
  mapContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: '#F8F9FA',
  },
  mapArea: {
    flex: 1,
  },
  mapBackground: {
    flex: 1,
    backgroundColor: '#E8F5E8',
    position: 'relative',
  },
  mapGrid: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  gridLineVertical: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    width: 1,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  markerContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -16 }, { translateY: -16 }],
  },
  marker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#10B981',
    borderWidth: 3,
    borderColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  markerInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  locationButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bottomSheet: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  locationInfo: {
    marginBottom: 24,
  },
  locationCode: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 8,
  },
  coordinates: {
    fontSize: 14,
    color: '#9CA3AF',
  },
  confirmButton: {
    backgroundColor: '#10B981',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  manualButton: {
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 24,
  },
  manualButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#10B981',
  },
  recentPlaces: {
    gap: 16,
  },
  recentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  placeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  placeInfo: {
    flex: 1,
  },
  placeName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 2,
  },
  placeLocation: {
    fontSize: 14,
    color: '#6B7280',
  },
  // New styles for enhanced functionality
  suggestionsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginTop: 8,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  suggestionsList: {
    maxHeight: 200,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  suggestionText: {
    fontSize: 14,
    color: '#1F2937',
    marginLeft: 12,
    flex: 1,
  },
  confirmButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  defaultBadge: {
    backgroundColor: '#0DCAA8',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 8,
  },
  defaultText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  noAddressesText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 16,
  },
});

export default LocationScreenClean;
