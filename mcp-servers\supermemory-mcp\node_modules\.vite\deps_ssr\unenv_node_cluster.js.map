{"version": 3, "sources": ["../../unenv/dist/runtime/node/cluster.mjs"], "sourcesContent": ["import { EventEmitter } from \"node:events\";\nimport { notImplemented } from \"../_internal/utils.mjs\";\nexport const SCHED_NONE = 1;\nexport const SCHED_RR = 2;\nexport const isMaster = true;\nexport const isPrimary = true;\nexport const isWorker = false;\nexport const schedulingPolicy = SCHED_RR;\nexport const settings = {};\nexport const workers = {};\nexport const fork = /* @__PURE__ */ notImplemented(\"cluster.fork\");\nexport const disconnect = /* @__PURE__ */ notImplemented(\"cluster.disconnect\");\nexport const setupPrimary = /* @__PURE__ */ notImplemented(\"cluster.setupPrimary\");\nexport const setupMaster = /* @__PURE__ */ notImplemented(\"cluster.setupMaster\");\nexport const _events = [];\nexport const _eventsCount = 0;\nexport const _maxListeners = 0;\nexport class Worker extends EventEmitter {\n\t_connected = false;\n\tid = 0;\n\tget process() {\n\t\treturn globalThis.process;\n\t}\n\tget exitedAfterDisconnect() {\n\t\treturn this._connected;\n\t}\n\tisConnected() {\n\t\treturn this._connected;\n\t}\n\tisDead() {\n\t\treturn true;\n\t}\n\tsend(message, sendHandle, options, callback) {\n\t\treturn false;\n\t}\n\tkill(signal) {\n\t\tthis._connected = false;\n\t}\n\tdestroy(signal) {\n\t\tthis._connected = false;\n\t}\n\tdisconnect() {\n\t\tthis._connected = false;\n\t}\n}\nclass _Cluster extends EventEmitter {\n\tWorker = Worker;\n\tisMaster = isMaster;\n\tisPrimary = isPrimary;\n\tisWorker = isWorker;\n\tSCHED_NONE = SCHED_NONE;\n\tSCHED_RR = SCHED_RR;\n\tschedulingPolicy = SCHED_RR;\n\tsettings = settings;\n\tworkers = workers;\n\tsetupPrimary() {\n\t\treturn setupPrimary();\n\t}\n\tsetupMaster() {\n\t\treturn setupPrimary();\n\t}\n\tdisconnect() {\n\t\treturn disconnect();\n\t}\n\tfork() {\n\t\treturn fork();\n\t}\n}\nexport default new _Cluster();\n"], "mappings": ";;;;;;AAAA,SAAS,oBAAoB;AAEtB,IAAM,aAAa;AACnB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,YAAY;AAClB,IAAM,WAAW;AACjB,IAAM,mBAAmB;AACzB,IAAM,WAAW,CAAC;AAClB,IAAM,UAAU,CAAC;AACjB,IAAM,OAAuB,eAAe,cAAc;AAC1D,IAAM,aAA6B,eAAe,oBAAoB;AACtE,IAAM,eAA+B,eAAe,sBAAsB;AAC1E,IAAM,cAA8B,eAAe,qBAAqB;AACxE,IAAM,UAAU,CAAC;AACjB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,SAAN,cAAqB,aAAa;AAAA,EACxC,aAAa;AAAA,EACb,KAAK;AAAA,EACL,IAAI,UAAU;AACb,WAAO,WAAW;AAAA,EACnB;AAAA,EACA,IAAI,wBAAwB;AAC3B,WAAO,KAAK;AAAA,EACb;AAAA,EACA,cAAc;AACb,WAAO,KAAK;AAAA,EACb;AAAA,EACA,SAAS;AACR,WAAO;AAAA,EACR;AAAA,EACA,KAAK,SAAS,YAAY,SAAS,UAAU;AAC5C,WAAO;AAAA,EACR;AAAA,EACA,KAAK,QAAQ;AACZ,SAAK,aAAa;AAAA,EACnB;AAAA,EACA,QAAQ,QAAQ;AACf,SAAK,aAAa;AAAA,EACnB;AAAA,EACA,aAAa;AACZ,SAAK,aAAa;AAAA,EACnB;AACD;AACA,IAAM,WAAN,cAAuB,aAAa;AAAA,EACnC,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,eAAe;AACd,WAAO,aAAa;AAAA,EACrB;AAAA,EACA,cAAc;AACb,WAAO,aAAa;AAAA,EACrB;AAAA,EACA,aAAa;AACZ,WAAO,WAAW;AAAA,EACnB;AAAA,EACA,OAAO;AACN,WAAO,KAAK;AAAA,EACb;AACD;AACA,IAAO,kBAAQ,IAAI,SAAS;", "names": []}