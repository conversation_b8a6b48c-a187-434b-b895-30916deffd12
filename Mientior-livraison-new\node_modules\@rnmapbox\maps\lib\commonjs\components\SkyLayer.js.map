{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_RNMBXSkyLayerNativeComponent", "_AbstractLayer", "_jsxRuntime", "e", "__esModule", "default", "Mapbox", "NativeModules", "RNMBXModule", "SkyLayer", "AbstractLayer", "defaultProps", "sourceID", "StyleSource", "DefaultSourceID", "render", "jsx", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseProps", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/SkyLayer.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAIA,IAAAE,6BAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAA4C,IAAAI,WAAA,GAAAJ,OAAA;AAAA,SAAAD,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE5C,MAAMG,MAAM,GAAGC,0BAAa,CAACC,WAAW;AAgDxC;AACA;AACA;AACA,MAAMC,QAAQ,SAASC,sBAAa,CAAyB;EAC3D,OAAOC,YAAY,GAAG;IACpBC,QAAQ,EAAEN,MAAM,CAACO,WAAW,CAACC;EAC/B,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,oBACE,IAAAb,WAAA,CAAAc,GAAA,EAAChB,6BAAA,CAAAK;IACC;IAAA;MACAY,GAAG,EAAE,IAAI,CAACC,cAAe;MAAA,GACrB,IAAI,CAACC;IAAS,CACnB,CAAC;EAEN;AACF;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAhB,OAAA,GAEcI,QAAQ", "ignoreList": []}