import {
  notImplemented
} from "./chunk-AO3S7MWW.js";

// node_modules/unenv/dist/runtime/node/internal/fs/promises.mjs
var access = notImplemented("fs.access");
var copyFile = notImplemented("fs.copyFile");
var cp = notImplemented("fs.cp");
var open = notImplemented("fs.open");
var opendir = notImplemented("fs.opendir");
var rename = notImplemented("fs.rename");
var truncate = notImplemented("fs.truncate");
var rm = notImplemented("fs.rm");
var rmdir = notImplemented("fs.rmdir");
var mkdir = notImplemented("fs.mkdir");
var readdir = notImplemented("fs.readdir");
var readlink = notImplemented("fs.readlink");
var symlink = notImplemented("fs.symlink");
var lstat = notImplemented("fs.lstat");
var stat = notImplemented("fs.stat");
var link = notImplemented("fs.link");
var unlink = notImplemented("fs.unlink");
var chmod = notImplemented("fs.chmod");
var lchmod = notImplemented("fs.lchmod");
var lchown = notImplemented("fs.lchown");
var chown = notImplemented("fs.chown");
var utimes = notImplemented("fs.utimes");
var lutimes = notImplemented("fs.lutimes");
var realpath = notImplemented("fs.realpath");
var mkdtemp = notImplemented("fs.mkdtemp");
var writeFile = notImplemented("fs.writeFile");
var appendFile = notImplemented("fs.appendFile");
var readFile = notImplemented("fs.readFile");
var watch = notImplemented("fs.watch");
var statfs = notImplemented("fs.statfs");
var glob = notImplemented("fs.glob");

export {
  access,
  copyFile,
  cp,
  open,
  opendir,
  rename,
  truncate,
  rm,
  rmdir,
  mkdir,
  readdir,
  readlink,
  symlink,
  lstat,
  stat,
  link,
  unlink,
  chmod,
  lchmod,
  lchown,
  chown,
  utimes,
  lutimes,
  realpath,
  mkdtemp,
  writeFile,
  appendFile,
  readFile,
  watch,
  statfs,
  glob
};
//# sourceMappingURL=chunk-AIZST77Q.js.map
