import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE, Region, LatLng } from 'react-native-maps';
import { Ionicons } from '@expo/vector-icons';
import { Location as LocationType } from '../types';
import { locationService } from '../services/locationService';

interface GoogleMapViewProps {
  initialLocation?: LocationType;
  selectedLocation?: LocationType | null;
  onLocationSelect?: (location: LocationType) => void;
  onCurrentLocationPress?: () => void;
  showCurrentLocationButton?: boolean;
  markers?: Array<{
    id: string;
    coordinate: LatLng;
    title?: string;
    description?: string;
    pinColor?: string;
  }>;
  style?: any;
  isLoading?: boolean;
}

const GoogleMapView: React.FC<GoogleMapViewProps> = ({
  initialLocation,
  selectedLocation,
  onLocationSelect,
  onCurrentLocationPress,
  showCurrentLocationButton = true,
  markers = [],
  style,
  isLoading = false,
}) => {
  const mapRef = useRef<MapView>(null);
  const [region, setRegion] = useState<Region>({
    latitude: initialLocation?.latitude || 5.3600, // Default to Abidjan
    longitude: initialLocation?.longitude || -4.0083,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  const [isMapReady, setIsMapReady] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<LocationType | null>(initialLocation || null);

  useEffect(() => {
    if (initialLocation) {
      setCurrentLocation(initialLocation);
      setRegion({
        latitude: initialLocation.latitude,
        longitude: initialLocation.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    }
  }, [initialLocation]);

  const handleMapPress = (event: any) => {
    const { coordinate } = event.nativeEvent;
    const newLocation: LocationType = {
      latitude: coordinate.latitude,
      longitude: coordinate.longitude,
      timestamp: Date.now(),
    };
    
    if (onLocationSelect) {
      onLocationSelect(newLocation);
    }
  };

  const handleCurrentLocationPress = async () => {
    if (onCurrentLocationPress) {
      onCurrentLocationPress();
      return;
    }

    try {
      const location = await locationService.getCurrentLocation({
        accuracy: 'high' as any,
        timeout: 15000,
      });
      
      setCurrentLocation(location);
      
      // Animate to current location
      if (mapRef.current && isMapReady) {
        mapRef.current.animateToRegion({
          latitude: location.latitude,
          longitude: location.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }, 1000);
      }
      
      if (onLocationSelect) {
        onLocationSelect(location);
      }
    } catch (error) {
      console.error('Error getting current location:', error);
      Alert.alert(
        'Erreur de localisation',
        'Impossible d\'obtenir votre position actuelle. Veuillez vérifier que le GPS est activé.',
        [{ text: 'OK' }]
      );
    }
  };

  const animateToLocation = (location: LocationType) => {
    if (mapRef.current && isMapReady) {
      mapRef.current.animateToRegion({
        latitude: location.latitude,
        longitude: location.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      }, 1000);
    }
  };

  // Expose method to parent components
  React.useImperativeHandle(mapRef, () => ({
    animateToLocation,
  }));

  const mapStyle = [
    {
      featureType: 'poi',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'transit',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
  ];

  return (
    <View style={[styles.container, style]}>
      {/* Loading Overlay */}
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#0DCAA8" />
        </View>
      )}

      {/* Google Maps */}
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={region}
        onPress={handleMapPress}
        onMapReady={() => setIsMapReady(true)}
        showsUserLocation={false} // We'll handle this manually
        showsMyLocationButton={false}
        showsCompass={true}
        showsScale={true}
        customMapStyle={mapStyle}
        mapType="standard"
        pitchEnabled={true}
        rotateEnabled={true}
        scrollEnabled={true}
        zoomEnabled={true}
      >
        {/* Current Location Marker */}
        {currentLocation && (
          <Marker
            coordinate={{
              latitude: currentLocation.latitude,
              longitude: currentLocation.longitude,
            }}
            title="Ma position"
            description="Votre position actuelle"
            pinColor="#0DCAA8"
            identifier="current-location"
          >
            <View style={styles.currentLocationMarker}>
              <View style={styles.currentLocationInner} />
            </View>
          </Marker>
        )}

        {/* Selected Location Marker */}
        {selectedLocation && (
          <Marker
            coordinate={{
              latitude: selectedLocation.latitude,
              longitude: selectedLocation.longitude,
            }}
            title="Position sélectionnée"
            description="Adresse de livraison"
            pinColor="#FF6B6B"
            identifier="selected-location"
          >
            <View style={styles.selectedLocationMarker}>
              <Ionicons name="location" size={24} color="#FFFFFF" />
            </View>
          </Marker>
        )}

        {/* Additional Markers */}
        {markers.map((marker) => (
          <Marker
            key={marker.id}
            coordinate={marker.coordinate}
            title={marker.title}
            description={marker.description}
            pinColor={marker.pinColor || "#10B981"}
            identifier={marker.id}
          />
        ))}
      </MapView>

      {/* Current Location Button */}
      {showCurrentLocationButton && (
        <TouchableOpacity
          style={styles.currentLocationButton}
          onPress={handleCurrentLocationPress}
          activeOpacity={0.7}
        >
          <Ionicons name="locate" size={24} color="#0DCAA8" />
        </TouchableOpacity>
      )}

      {/* Map Type Toggle (Optional) */}
      <TouchableOpacity
        style={styles.mapTypeButton}
        onPress={() => {
          // Toggle between standard and satellite view
          // This can be implemented if needed
        }}
        activeOpacity={0.7}
      >
        <Ionicons name="layers" size={20} color="#6B7280" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
  },
  map: {
    flex: 1,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  currentLocationButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  mapTypeButton: {
    position: 'absolute',
    top: 72,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 3,
  },
  currentLocationMarker: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#0DCAA8',
    borderWidth: 3,
    borderColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  currentLocationInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  selectedLocationMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FF6B6B',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default GoogleMapView;
