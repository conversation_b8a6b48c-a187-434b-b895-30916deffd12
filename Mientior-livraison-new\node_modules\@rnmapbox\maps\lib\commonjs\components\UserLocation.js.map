{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_locationManager", "_Annotation", "_<PERSON><PERSON><PERSON><PERSON>", "_HeadingIndicator", "_LocationPuck", "_jsxRuntime", "e", "__esModule", "default", "mapboxBlue", "layerStyles", "normal", "pulse", "circleRadius", "circleColor", "circleOpacity", "circlePitchAlignment", "background", "foreground", "normalIcon", "showsUserHeadingIndicator", "heading", "jsx", "id", "style", "aboveLayerID", "HeadingIndicator", "key", "UserLocationRenderMode", "exports", "UserLocation", "React", "Component", "defaultProps", "animated", "visible", "requestsAlwaysUse", "minDisplacement", "renderMode", "Normal", "constructor", "props", "state", "shouldShowUserLocation", "coordinates", "_onLocationUpdate", "bind", "_isMounted", "undefined", "locationManagerRunning", "componentDidMount", "locationManager", "setMinDisplacement", "setLocationManager", "running", "needsLocationManagerRunning", "Native", "componentDidUpdate", "prevProps", "setRequestsAlwaysUse", "componentWillUnmount", "addListener", "location", "getLastKnownLocation", "removeListener", "onUpdate", "coords", "longitude", "latitude", "setState", "_renderNative", "androidRenderMode", "iosShowsUserHeadingIndicator", "render", "children", "onPress", "iconRotate", "_default"], "sourceRoot": "../../../src", "sources": ["components/UserLocation.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAIA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,YAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,iBAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,aAAA,GAAAN,sBAAA,CAAAC,OAAA;AAA0C,IAAAM,WAAA,GAAAN,OAAA;AAAA,SAAAD,uBAAAQ,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE1C,MAAMG,UAAU,GAAG,yBAAyB;AAE5C,MAAMC,WAA+D,GAAG;EACtEC,MAAM,EAAE;IACNC,KAAK,EAAE;MACLC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAEL,UAAU;MACvBM,aAAa,EAAE,GAAG;MAClBC,oBAAoB,EAAE;IACxB,CAAC;IACDC,UAAU,EAAE;MACVJ,YAAY,EAAE,CAAC;MACfC,WAAW,EAAE,MAAM;MACnBE,oBAAoB,EAAE;IACxB,CAAC;IACDE,UAAU,EAAE;MACVL,YAAY,EAAE,CAAC;MACfC,WAAW,EAAEL,UAAU;MACvBO,oBAAoB,EAAE;IACxB;EACF;AACF,CAAC;AAED,MAAMG,UAAU,GAAGA,CACjBC,yBAAmC,EACnCC,OAAuB,KACJ,cACnB,IAAAhB,WAAA,CAAAiB,GAAA,EAACpB,YAAA,CAAAM,OAAW;EAEVe,EAAE,EAAC,+BAA+B;EAClCC,KAAK,EAAEd,WAAW,CAACC,MAAM,CAACC;AAAM,GAF5B,+BAGL,CAAC,eACF,IAAAP,WAAA,CAAAiB,GAAA,EAACpB,YAAA,CAAAM,OAAW;EAEVe,EAAE,EAAC,+BAA+B;EAClCC,KAAK,EAAEd,WAAW,CAACC,MAAM,CAACM;AAAW,GAFjC,+BAGL,CAAC,eACF,IAAAZ,WAAA,CAAAiB,GAAA,EAACpB,YAAA,CAAAM,OAAW;EAEVe,EAAE,EAAC,8BAA8B;EACjCE,YAAY,EAAC,+BAA+B;EAC5CD,KAAK,EAAEd,WAAW,CAACC,MAAM,CAACO;AAAW,GAHjC,8BAIL,CAAC,EACF,IAAIE,yBAAyB,IAAI,OAAOC,OAAO,KAAK,QAAQ,GACxD,CAAC,IAAAK,yBAAgB,EAAC;EAAEL,OAAO;EAAEM,GAAG,EAAE;AAAqC,CAAC,CAAC,CAAC,GAC1E,EAAE,CAAC,CACR;AAAC,IAEUC,sBAAsB,GAAAC,OAAA,CAAAD,sBAAA,0BAAtBA,sBAAsB;EAAtBA,sBAAsB;EAAtBA,sBAAsB;EAAA,OAAtBA,sBAAsB;AAAA;AAwElC,MAAME,YAAY,SAASC,cAAK,CAACC,SAAS,CAA2B;EACnE,OAAOC,YAAY,GAAG;IACpBC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbf,yBAAyB,EAAE,KAAK;IAChCgB,iBAAiB,EAAE,KAAK;IACxBC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAEV,sBAAsB,CAACW;EACrC,CAAC;EAEDC,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IAEZ,IAAI,CAACC,KAAK,GAAG;MACXC,sBAAsB,EAAE,KAAK;MAC7BC,WAAW,EAAE,IAAI;MACjBvB,OAAO,EAAE;IACX,CAAC;IAED,IAAI,CAACwB,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC;EAC5D;;EAEA;EACA;EACAC,UAAU,GAAaC,SAAS;EAEhCC,sBAAsB,GAAa,KAAK;EAExC,MAAMC,iBAAiBA,CAAA,EAAG;IACxB,IAAI,CAACH,UAAU,GAAG,IAAI;IAEtBI,wBAAe,CAACC,kBAAkB,CAAC,IAAI,CAACX,KAAK,CAACJ,eAAe,IAAI,CAAC,CAAC;IAEnE,MAAM,IAAI,CAACgB,kBAAkB,CAAC;MAC5BC,OAAO,EAAE,IAAI,CAACC,2BAA2B,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAI,IAAI,CAACd,KAAK,CAACH,UAAU,KAAKV,sBAAsB,CAAC4B,MAAM,EAAE;MAC3D;IACF;EACF;EAEA,MAAMC,kBAAkBA,CAACC,SAAgB,EAAE;IACzC,MAAM,IAAI,CAACL,kBAAkB,CAAC;MAC5BC,OAAO,EAAE,IAAI,CAACC,2BAA2B,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAI,IAAI,CAACd,KAAK,CAACJ,eAAe,KAAKqB,SAAS,CAACrB,eAAe,EAAE;MAC5Dc,wBAAe,CAACC,kBAAkB,CAAC,IAAI,CAACX,KAAK,CAACJ,eAAe,IAAI,CAAC,CAAC;IACrE;IACA,IAAI,IAAI,CAACI,KAAK,CAACL,iBAAiB,KAAKsB,SAAS,CAACtB,iBAAiB,EAAE;MAChEe,wBAAe,CAACQ,oBAAoB,CAClC,IAAI,CAAClB,KAAK,CAACL,iBAAiB,IAAI,KAClC,CAAC;IACH;EACF;EAEA,MAAMwB,oBAAoBA,CAAA,EAAG;IAC3B,IAAI,CAACb,UAAU,GAAG,KAAK;IACvB,MAAM,IAAI,CAACM,kBAAkB,CAAC;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC;EACnD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMD,kBAAkBA,CAAC;IAAEC;EAA+B,CAAC,EAAE;IAC3D,IAAI,IAAI,CAACL,sBAAsB,KAAKK,OAAO,EAAE;MAC3C,IAAI,CAACL,sBAAsB,GAAGK,OAAO;MACrC,IAAIA,OAAO,EAAE;QACXH,wBAAe,CAACU,WAAW,CAAC,IAAI,CAAChB,iBAAiB,CAAC;QACnD,MAAMiB,QAAQ,GAAG,MAAMX,wBAAe,CAACY,oBAAoB,CAAC,CAAC;QAC7D,IAAI,CAAClB,iBAAiB,CAACiB,QAAQ,CAAC;MAClC,CAAC,MAAM;QACLX,wBAAe,CAACa,cAAc,CAAC,IAAI,CAACnB,iBAAiB,CAAC;MACxD;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEU,2BAA2BA,CAAA,EAAG;IAC5B,OACE,CAAC,CAAC,IAAI,CAACd,KAAK,CAACwB,QAAQ,IACpB,IAAI,CAACxB,KAAK,CAACH,UAAU,KAAKV,sBAAsB,CAACW,MAAM,IACtD,IAAI,CAACE,KAAK,CAACN,OAAQ;EAEzB;EAEAU,iBAAiBA,CAACiB,QAAyB,EAAE;IAC3C,IAAI,CAAC,IAAI,CAACf,UAAU,IAAI,CAACe,QAAQ,EAAE;MACjC;IACF;IACA,IAAIlB,WAAW,GAAG,IAAI;IACtB,IAAIvB,OAAO,GAAG,IAAI;IAElB,IAAIyC,QAAQ,IAAIA,QAAQ,CAACI,MAAM,EAAE;MAC/B,MAAM;QAAEC,SAAS;QAAEC;MAAS,CAAC,GAAGN,QAAQ,CAACI,MAAM;MAC/C,CAAC;QAAE7C;MAAQ,CAAC,GAAGyC,QAAQ,CAACI,MAAM;MAC9BtB,WAAW,GAAG,CAACuB,SAAS,EAAEC,QAAQ,CAAC;IACrC;IAEA,IAAI,CAACC,QAAQ,CAAC;MACZzB,WAAW;MACXvB,OAAO,EAAEA,OAAO,IAAI;IACtB,CAAC,CAAC;IAEF,IAAI,IAAI,CAACoB,KAAK,CAACwB,QAAQ,EAAE;MACvB,IAAI,CAACxB,KAAK,CAACwB,QAAQ,CAACH,QAAQ,CAAC;IAC/B;EACF;EAEAQ,aAAaA,CAAA,EAAG;IACd,MAAM;MAAEC,iBAAiB;MAAEnD;IAA0B,CAAC,GAAG,IAAI,CAACqB,KAAK;IAEnE,MAAMA,KAAK,GAAG;MACZ8B,iBAAiB;MACjBC,4BAA4B,EAAEpD;IAChC,CAAC;IACD,oBAAO,IAAAf,WAAA,CAAAiB,GAAA,EAAClB,aAAA,CAAAI,OAAY;MAAA,GAAKiC;IAAK,CAAG,CAAC;EACpC;EAEAgC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEpD,OAAO;MAAEuB;IAAY,CAAC,GAAG,IAAI,CAACF,KAAK;IAC3C,MAAM;MAAEgC,QAAQ;MAAEvC,OAAO;MAAEf,yBAAyB;MAAEuD,OAAO;MAAEzC;IAAS,CAAC,GACvE,IAAI,CAACO,KAAK;IAEZ,IAAI,CAACN,OAAO,EAAE;MACZ,OAAO,IAAI;IACb;IAEA,IAAI,IAAI,CAACM,KAAK,CAACH,UAAU,KAAKV,sBAAsB,CAAC4B,MAAM,EAAE;MAC3D,OAAO,IAAI,CAACc,aAAa,CAAC,CAAC;IAC7B;IAEA,IAAI,CAAC1B,WAAW,EAAE;MAChB,OAAO,IAAI;IACb;IAEA,oBACE,IAAAvC,WAAA,CAAAiB,GAAA,EAACrB,WAAA,CAAAO,OAAU;MACTe,EAAE,EAAC,oBAAoB;MACvBW,QAAQ,EAAEA,QAAS;MACnByC,OAAO,EAAEA,OAAQ;MACjB/B,WAAW,EAAEA,WAAY;MACzBpB,KAAK,EAAE;QACLoD,UAAU,EAAEvD;MACd,CAAE;MAAAqD,QAAA,EAEDA,QAAQ,IAAIvD,UAAU,CAACC,yBAAyB,EAAEC,OAAO;IAAC,CACjD,CAAC;EAEjB;AACF;AAAC,IAAAwD,QAAA,GAAAhD,OAAA,CAAArB,OAAA,GAEcsB,YAAY", "ignoreList": []}