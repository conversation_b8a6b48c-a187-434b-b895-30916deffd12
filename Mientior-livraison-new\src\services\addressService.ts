import { Address, Location as LocationType } from '../types';
import { locationService, AddressSuggestion } from './locationService';
import { useAuthStore } from '../store/authStore';

export interface AddressSearchResult {
  id: string;
  description: string;
  place_id?: string;
  latitude?: number;
  longitude?: number;
  structured_formatting?: {
    main_text: string;
    secondary_text: string;
  };
  types?: string[];
}

export interface AddressDetails {
  place_id: string;
  formatted_address: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  address_components: Array<{
    long_name: string;
    short_name: string;
    types: string[];
  }>;
}

class AddressService {
  private readonly GOOGLE_PLACES_API_KEY = process.env.EXPO_PUBLIC_GOOGLE_PLACES_API_KEY;
  private readonly MAPBOX_ACCESS_TOKEN = process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN;

  /**
   * Recherche d'adresses avec autocomplétion
   */
  async searchAddresses(query: string, location?: LocationType): Promise<AddressSearchResult[]> {
    if (query.length < 3) {
      return [];
    }

    try {
      // Essayer d'abord Google Places API si disponible
      if (this.GOOGLE_PLACES_API_KEY) {
        return await this.searchWithGooglePlaces(query, location);
      }
      
      // Fallback vers Mapbox Geocoding si disponible
      if (this.MAPBOX_ACCESS_TOKEN) {
        return await this.searchWithMapbox(query, location);
      }

      // Fallback vers géocodage local
      return await this.searchWithLocalGeocoding(query);
    } catch (error) {
      console.error('Erreur recherche adresses:', error);
      return await this.searchWithLocalGeocoding(query);
    }
  }

  /**
   * Recherche avec Google Places API
   */
  private async searchWithGooglePlaces(query: string, location?: LocationType): Promise<AddressSearchResult[]> {
    try {
      let url = `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(query)}&key=${this.GOOGLE_PLACES_API_KEY}&language=fr`;
      
      // Ajouter la localisation pour améliorer les résultats
      if (location) {
        url += `&location=${location.latitude},${location.longitude}&radius=50000`;
      }
      
      // Filtrer pour l'Afrique de l'Ouest
      url += '&components=country:ci|country:gh|country:ng|country:sn|country:ml|country:bf';

      const response = await fetch(url);
      const data = await response.json();

      if (data.status === 'OK') {
        return data.predictions.map((prediction: any) => ({
          id: prediction.place_id,
          description: prediction.description,
          place_id: prediction.place_id,
          structured_formatting: prediction.structured_formatting,
          types: prediction.types,
        }));
      }

      throw new Error(`Google Places API error: ${data.status}`);
    } catch (error) {
      console.error('Erreur Google Places:', error);
      throw error;
    }
  }

  /**
   * Recherche avec Mapbox Geocoding API
   */
  private async searchWithMapbox(query: string, location?: LocationType): Promise<AddressSearchResult[]> {
    try {
      let url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(query)}.json?access_token=${this.MAPBOX_ACCESS_TOKEN}&language=fr&limit=5`;
      
      // Ajouter la localisation pour améliorer les résultats
      if (location) {
        url += `&proximity=${location.longitude},${location.latitude}`;
      }
      
      // Filtrer pour l'Afrique de l'Ouest
      url += '&country=ci,gh,ng,sn,ml,bf';

      const response = await fetch(url);
      const data = await response.json();

      return data.features.map((feature: any) => ({
        id: feature.id,
        description: feature.place_name,
        latitude: feature.center[1],
        longitude: feature.center[0],
        structured_formatting: {
          main_text: feature.text,
          secondary_text: feature.place_name.replace(feature.text + ', ', ''),
        },
        types: feature.place_type,
      }));
    } catch (error) {
      console.error('Erreur Mapbox:', error);
      throw error;
    }
  }

  /**
   * Recherche avec géocodage local (fallback)
   */
  private async searchWithLocalGeocoding(query: string): Promise<AddressSearchResult[]> {
    try {
      const results = await locationService.geocode(query);
      
      return results.map((result, index) => ({
        id: `local-${index}`,
        description: result.address,
        latitude: result.latitude,
        longitude: result.longitude,
        structured_formatting: {
          main_text: result.address.split(',')[0] || result.address,
          secondary_text: result.address.split(',').slice(1).join(',').trim() || '',
        },
      }));
    } catch (error) {
      console.error('Erreur géocodage local:', error);
      return [];
    }
  }

  /**
   * Obtient les détails d'une adresse à partir de son place_id
   */
  async getAddressDetails(placeId: string): Promise<AddressDetails | null> {
    if (!this.GOOGLE_PLACES_API_KEY) {
      return null;
    }

    try {
      const url = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${this.GOOGLE_PLACES_API_KEY}&language=fr&fields=formatted_address,geometry,address_components`;
      
      const response = await fetch(url);
      const data = await response.json();

      if (data.status === 'OK') {
        return data.result;
      }

      throw new Error(`Google Places Details API error: ${data.status}`);
    } catch (error) {
      console.error('Erreur détails adresse:', error);
      return null;
    }
  }

  /**
   * Convertit un résultat de recherche en objet Address
   */
  async convertToAddress(
    searchResult: AddressSearchResult,
    label: 'home' | 'work' | 'other' = 'other',
    userId: string
  ): Promise<Omit<Address, 'id' | 'created_at' | 'updated_at'>> {
    let latitude = searchResult.latitude;
    let longitude = searchResult.longitude;
    let fullAddress = searchResult.description;

    // Si on a un place_id, obtenir plus de détails
    if (searchResult.place_id && this.GOOGLE_PLACES_API_KEY) {
      const details = await this.getAddressDetails(searchResult.place_id);
      if (details) {
        latitude = details.geometry.location.lat;
        longitude = details.geometry.location.lng;
        fullAddress = details.formatted_address;
      }
    }

    // Si pas de coordonnées, essayer le géocodage
    if (!latitude || !longitude) {
      try {
        const geocodeResults = await locationService.geocode(searchResult.description);
        if (geocodeResults.length > 0) {
          latitude = geocodeResults[0].latitude;
          longitude = geocodeResults[0].longitude;
        }
      } catch (error) {
        console.error('Erreur géocodage pour conversion:', error);
      }
    }

    // Extraire les composants de l'adresse
    const addressParts = fullAddress.split(',').map(part => part.trim());
    const mainAddress = addressParts[0] || fullAddress;
    const city = this.extractCity(addressParts);
    const country = this.extractCountry(addressParts);

    return {
      user_id: userId,
      label,
      nom_complet: fullAddress,
      adresse_ligne1: mainAddress,
      adresse_ligne2: addressParts.length > 1 ? addressParts[1] : undefined,
      ville: city,
      quartier: this.extractDistrict(addressParts),
      pays: country,
      coordonnees: {
        latitude: latitude || 0,
        longitude: longitude || 0,
      },
      is_default: false,
    };
  }

  /**
   * Extrait la ville de l'adresse
   */
  private extractCity(addressParts: string[]): string {
    // Chercher des villes connues d'Afrique de l'Ouest
    const knownCities = [
      'Abidjan', 'Bouaké', 'Daloa', 'Yamoussoukro', 'San-Pédro',
      'Accra', 'Kumasi', 'Tamale', 'Cape Coast',
      'Lagos', 'Kano', 'Ibadan', 'Kaduna', 'Port Harcourt',
      'Dakar', 'Thiès', 'Kaolack', 'Saint-Louis',
      'Bamako', 'Sikasso', 'Mopti', 'Ségou',
      'Ouagadougou', 'Bobo-Dioulasso', 'Koudougou'
    ];

    for (const part of addressParts) {
      for (const city of knownCities) {
        if (part.toLowerCase().includes(city.toLowerCase())) {
          return city;
        }
      }
    }

    // Fallback vers le deuxième élément s'il existe
    return addressParts.length > 1 ? addressParts[1] : 'Ville';
  }

  /**
   * Extrait le pays de l'adresse
   */
  private extractCountry(addressParts: string[]): string {
    const lastPart = addressParts[addressParts.length - 1]?.toLowerCase() || '';
    
    if (lastPart.includes('côte d\'ivoire') || lastPart.includes('ivory coast')) return 'Côte d\'Ivoire';
    if (lastPart.includes('ghana')) return 'Ghana';
    if (lastPart.includes('nigeria')) return 'Nigeria';
    if (lastPart.includes('sénégal') || lastPart.includes('senegal')) return 'Sénégal';
    if (lastPart.includes('mali')) return 'Mali';
    if (lastPart.includes('burkina faso')) return 'Burkina Faso';
    
    return 'Côte d\'Ivoire'; // Défaut
  }

  /**
   * Extrait le quartier de l'adresse
   */
  private extractDistrict(addressParts: string[]): string {
    // Le quartier est souvent le troisième élément
    return addressParts.length > 2 ? addressParts[2] : 'Quartier';
  }

  /**
   * Valide une adresse
   */
  validateAddress(address: Partial<Address>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!address.nom_complet || address.nom_complet.trim().length < 5) {
      errors.push('L\'adresse complète doit contenir au moins 5 caractères');
    }

    if (!address.coordonnees || !address.coordonnees.latitude || !address.coordonnees.longitude) {
      errors.push('Les coordonnées GPS sont requises');
    }

    if (!address.ville || address.ville.trim().length < 2) {
      errors.push('La ville est requise');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const addressService = new AddressService();
export default addressService;
