import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Dimensions,
  StatusBar,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { WebView } from 'react-native-webview';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
}

interface RecentLocation {
  id: string;
  name: string;
  address: string;
  icon: string;
}

const LocationScreenExact: React.FC = () => {
  const navigation = useNavigation();
  const webViewRef = useRef<WebView>(null);
  
  const [selectedLocation, setSelectedLocation] = useState<LocationData>({
    latitude: 6.3702,
    longitude: 2.3912,
    address: "123 Avenue de l'Indépendance"
  });
  
  const [searchText, setSearchText] = useState('');
  
  const recentLocations: RecentLocation[] = [
    {
      id: '1',
      name: '<PERSON><PERSON>',
      address: '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>',
      icon: 'storefront'
    },
    {
      id: '2',
      name: 'Restaurant Le Bénin',
      address: 'Porto-Novo, Bénin',
      icon: 'restaurant'
    }
  ];

  // HTML pour Google Maps avec API Key
  const mapHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body, html { 
          margin: 0; 
          padding: 0; 
          height: 100%; 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        #map { height: 100%; width: 100%; }
        .loading {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: rgba(255,255,255,0.9);
          padding: 20px;
          border-radius: 8px;
          text-align: center;
          z-index: 1000;
        }
      </style>
    </head>
    <body>
      <div id="map"></div>
      <div id="loading" class="loading">Chargement de la carte...</div>
      
      <script>
        let map;
        let marker;
        
        function initMap() {
          document.getElementById('loading').style.display = 'none';
          
          const initialPosition = { lat: ${selectedLocation.latitude}, lng: ${selectedLocation.longitude} };
          
          map = new google.maps.Map(document.getElementById("map"), {
            zoom: 16,
            center: initialPosition,
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
            zoomControl: true,
            zoomControlOptions: {
              position: google.maps.ControlPosition.RIGHT_TOP
            },
            styles: [
              {
                featureType: "poi",
                elementType: "labels",
                stylers: [{ visibility: "on" }]
              },
              {
                featureType: "road",
                elementType: "labels",
                stylers: [{ visibility: "on" }]
              }
            ]
          });
          
          // Marqueur personnalisé vert comme dans l'image
          marker = new google.maps.Marker({
            position: initialPosition,
            map: map,
            icon: {
              url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(\`
                <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="16" cy="16" r="14" fill="#00D4AA" stroke="white" stroke-width="3"/>
                  <circle cx="16" cy="16" r="6" fill="white"/>
                </svg>
              \`),
              scaledSize: new google.maps.Size(32, 32),
              anchor: new google.maps.Point(16, 16)
            },
            animation: google.maps.Animation.DROP
          });
          
          // Gérer les clics sur la carte
          map.addListener("click", (event) => {
            const lat = event.latLng.lat();
            const lng = event.latLng.lng();
            
            // Déplacer le marqueur
            marker.setPosition({ lat, lng });
            
            // Géocodage inverse pour obtenir l'adresse
            const geocoder = new google.maps.Geocoder();
            geocoder.geocode({ location: { lat, lng } }, (results, status) => {
              if (status === "OK" && results[0]) {
                const address = results[0].formatted_address;
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'locationSelected',
                  latitude: lat,
                  longitude: lng,
                  address: address
                }));
              } else {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'locationSelected',
                  latitude: lat,
                  longitude: lng,
                  address: \`Position: \${lat.toFixed(4)}, \${lng.toFixed(4)}\`
                }));
              }
            });
          });
        }
        
        function updateLocation(lat, lng) {
          if (map && marker) {
            const newPosition = { lat: lat, lng: lng };
            marker.setPosition(newPosition);
            map.setCenter(newPosition);
          }
        }
        
        function handleError() {
          document.getElementById('loading').innerHTML = 'Erreur de chargement de la carte';
        }
        
        window.initMap = initMap;
        window.updateLocation = updateLocation;
        window.addEventListener('error', handleError);
      </script>
      
      <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s&callback=initMap&libraries=geometry">
      </script>
    </body>
    </html>
  `;

  const handleWebViewMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === 'locationSelected') {
        setSelectedLocation({
          latitude: data.latitude,
          longitude: data.longitude,
          address: data.address || `${data.latitude.toFixed(4)}, ${data.longitude.toFixed(4)}`
        });
      }
    } catch (error) {
      console.log('Erreur parsing message:', error);
    }
  };

  const handleConfirmLocation = () => {
    console.log('Position confirmée:', selectedLocation);
    navigation.goBack();
  };

  const handleManualSelection = () => {
    console.log('Sélection manuelle demandée');
  };

  const handleRecentLocationPress = (location: RecentLocation) => {
    const coords = location.name.includes('Dantokpa') 
      ? { latitude: 6.3702, longitude: 2.3912 }
      : { latitude: 6.4969, longitude: 2.6283 };
    
    setSelectedLocation({
      ...coords,
      address: location.name
    });
    
    if (webViewRef.current) {
      webViewRef.current.postMessage(JSON.stringify({
        type: 'updateLocation',
        latitude: coords.latitude,
        longitude: coords.longitude
      }));
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Votre localisation</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#9CA3AF" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Rechercher une adresse"
          placeholderTextColor="#9CA3AF"
          value={searchText}
          onChangeText={setSearchText}
        />
      </View>

      {/* Map Container */}
      <View style={styles.mapContainer}>
        <WebView
          ref={webViewRef}
          source={{ html: mapHTML }}
          style={styles.map}
          onMessage={handleWebViewMessage}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          scalesPageToFit={true}
          allowsInlineMediaPlayback={true}
          mediaPlaybackRequiresUserAction={false}
        />
      </View>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        {/* Selected Location Info */}
        <View style={styles.locationInfo}>
          <Text style={styles.locationAddress}>{selectedLocation.address}</Text>
          <Text style={styles.locationCoords}>
            Lat: {selectedLocation.latitude.toFixed(4)}, Long: {selectedLocation.longitude.toFixed(4)}
          </Text>
        </View>

        {/* Action Buttons */}
        <TouchableOpacity 
          style={styles.confirmButton}
          onPress={handleConfirmLocation}
        >
          <Text style={styles.confirmButtonText}>Confirmer ma position</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.manualButton}
          onPress={handleManualSelection}
        >
          <Text style={styles.manualButtonText}>Sélectionner manuellement</Text>
        </TouchableOpacity>

        {/* Recent Locations */}
        <View style={styles.recentSection}>
          <Text style={styles.recentTitle}>Lieux récents</Text>
          
          {recentLocations.map((location) => (
            <TouchableOpacity
              key={location.id}
              style={styles.recentItem}
              onPress={() => handleRecentLocationPress(location)}
            >
              <View style={styles.recentIcon}>
                <Ionicons 
                  name={location.icon as any} 
                  size={20} 
                  color="#6B7280" 
                />
              </View>
              <View style={styles.recentInfo}>
                <Text style={styles.recentName}>{location.name}</Text>
                <Text style={styles.recentAddress}>{location.address}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  backButton: {
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#000000',
  },
  mapContainer: {
    flex: 1,
    marginHorizontal: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  map: {
    flex: 1,
  },
  bottomSheet: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 34,
  },
  locationInfo: {
    marginBottom: 20,
  },
  locationAddress: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  locationCoords: {
    fontSize: 14,
    color: '#6B7280',
  },
  confirmButton: {
    backgroundColor: '#00D4AA',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  manualButton: {
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 24,
  },
  manualButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#3B82F6',
  },
  recentSection: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 20,
  },
  recentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  recentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  recentInfo: {
    flex: 1,
  },
  recentName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 2,
  },
  recentAddress: {
    fontSize: 14,
    color: '#6B7280',
  },
});

export default LocationScreenExact;
