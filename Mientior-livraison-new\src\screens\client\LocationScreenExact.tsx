import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  StatusBar,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

// WebView désactivé temporairement pour éviter les erreurs de build
// Utilisation d'une interface simulée à la place
const WebView = null;

// Dimensions non utilisées supprimées

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
}

interface RecentLocation {
  id: string;
  name: string;
  address: string;
  icon: string;
}

const LocationScreenExact: React.FC = () => {
  const navigation = useNavigation();
  
  const [selectedLocation, setSelectedLocation] = useState<LocationData>({
    latitude: 6.3702,
    longitude: 2.3912,
    address: "123 Avenue de l'Indépendance"
  });
  
  const [searchText, setSearchText] = useState('');
  
  const recentLocations: RecentLocation[] = [
    {
      id: '1',
      name: '<PERSON><PERSON>',
      address: '<PERSON><PERSON><PERSON>, <PERSON>énin',
      icon: 'storefront'
    },
    {
      id: '2',
      name: 'Restaurant Le Bénin',
      address: 'Porto-Novo, Bénin',
      icon: 'restaurant'
    }
  ];

  // Interface simulée - pas besoin de WebView

  const handleConfirmLocation = () => {
    console.log('Position confirmée:', selectedLocation);
    navigation.goBack();
  };

  const handleManualSelection = () => {
    console.log('Sélection manuelle demandée');
  };

  const handleRecentLocationPress = (location: RecentLocation) => {
    const coords = location.name.includes('Dantokpa')
      ? { latitude: 6.3702, longitude: 2.3912 }
      : { latitude: 6.4969, longitude: 2.6283 };

    setSelectedLocation({
      ...coords,
      address: location.name
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Votre localisation</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#9CA3AF" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Rechercher une adresse"
          placeholderTextColor="#9CA3AF"
          value={searchText}
          onChangeText={setSearchText}
        />
      </View>

      {/* Map Container - Interface Simulée */}
      <View style={styles.mapContainer}>
        <View style={styles.simulatedMap}>
          <View style={styles.mapBackground}>
            {/* Grille simulée pour ressembler à une carte */}
            <View style={styles.mapGrid}>
              {Array.from({ length: 8 }).map((_, i) => (
                <View key={`h-${i}`} style={[styles.gridLine, { top: (i + 1) * 40 }]} />
              ))}
              {Array.from({ length: 6 }).map((_, i) => (
                <View key={`v-${i}`} style={[styles.gridLineVertical, { left: (i + 1) * 60 }]} />
              ))}
            </View>

            {/* Marqueur central */}
            <View style={styles.markerContainer}>
              <View style={styles.marker}>
                <View style={styles.markerInner} />
              </View>
            </View>

            {/* Bouton localisation */}
            <TouchableOpacity
              style={styles.locationButton}
              onPress={() => {
                const newLat = 6.3702 + (Math.random() - 0.5) * 0.01;
                const newLng = 2.3912 + (Math.random() - 0.5) * 0.01;
                setSelectedLocation({
                  latitude: newLat,
                  longitude: newLng,
                  address: `Position GPS: ${newLat.toFixed(4)}, ${newLng.toFixed(4)}`
                });
              }}
            >
              <Ionicons name="locate" size={20} color="#00D4AA" />
            </TouchableOpacity>

            {/* Zone cliquable pour sélection manuelle */}
            <TouchableOpacity
              style={styles.mapClickArea}
              onPress={() => {
                const newLat = 6.3702 + (Math.random() - 0.5) * 0.02;
                const newLng = 2.3912 + (Math.random() - 0.5) * 0.02;
                setSelectedLocation({
                  latitude: newLat,
                  longitude: newLng,
                  address: `Position sélectionnée: ${newLat.toFixed(4)}, ${newLng.toFixed(4)}`
                });
              }}
            />
          </View>
        </View>
      </View>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        {/* Selected Location Info */}
        <View style={styles.locationInfo}>
          <Text style={styles.locationAddress}>{selectedLocation.address}</Text>
          <Text style={styles.locationCoords}>
            Lat: {selectedLocation.latitude.toFixed(4)}, Long: {selectedLocation.longitude.toFixed(4)}
          </Text>
        </View>

        {/* Action Buttons */}
        <TouchableOpacity 
          style={styles.confirmButton}
          onPress={handleConfirmLocation}
        >
          <Text style={styles.confirmButtonText}>Confirmer ma position</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.manualButton}
          onPress={handleManualSelection}
        >
          <Text style={styles.manualButtonText}>Sélectionner manuellement</Text>
        </TouchableOpacity>

        {/* Recent Locations */}
        <View style={styles.recentSection}>
          <Text style={styles.recentTitle}>Lieux récents</Text>
          
          {recentLocations.map((location) => (
            <TouchableOpacity
              key={location.id}
              style={styles.recentItem}
              onPress={() => handleRecentLocationPress(location)}
            >
              <View style={styles.recentIcon}>
                <Ionicons 
                  name={location.icon as any} 
                  size={20} 
                  color="#6B7280" 
                />
              </View>
              <View style={styles.recentInfo}>
                <Text style={styles.recentName}>{location.name}</Text>
                <Text style={styles.recentAddress}>{location.address}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  backButton: {
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#000000',
  },
  mapContainer: {
    flex: 1,
    marginHorizontal: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  map: {
    flex: 1,
  },
  bottomSheet: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 34,
  },
  locationInfo: {
    marginBottom: 20,
  },
  locationAddress: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  locationCoords: {
    fontSize: 14,
    color: '#6B7280',
  },
  confirmButton: {
    backgroundColor: '#00D4AA',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  manualButton: {
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 24,
  },
  manualButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#3B82F6',
  },
  recentSection: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 20,
  },
  recentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  recentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  recentInfo: {
    flex: 1,
  },
  recentName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 2,
  },
  recentAddress: {
    fontSize: 14,
    color: '#6B7280',
  },
  webViewFallback: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  webViewFallbackContent: {
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  webViewFallbackTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  webViewFallbackText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  webViewFallbackButton: {
    backgroundColor: '#00D4AA',
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  webViewFallbackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  simulatedMap: {
    flex: 1,
  },
  mapBackground: {
    flex: 1,
    backgroundColor: '#E8F5E8',
    position: 'relative',
  },
  mapGrid: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: 'rgba(0, 212, 170, 0.1)',
  },
  gridLineVertical: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    width: 1,
    backgroundColor: 'rgba(0, 212, 170, 0.1)',
  },
  markerContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -16 }, { translateY: -16 }],
  },
  marker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#00D4AA',
    borderWidth: 3,
    borderColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  markerInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  locationButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  mapClickArea: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});

export default LocationScreenExact;
