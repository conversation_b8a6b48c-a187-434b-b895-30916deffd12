import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  Platform,
  Alert,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
// import { WebView } from 'react-native-webview'; // Temporairement désactivé
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface LocationScreenExactProps {
  navigation: any;
}

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
}

const LocationScreenExact: React.FC<LocationScreenExactProps> = ({ navigation }) => {
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [mapReady, setMapReady] = useState(false);
  const mapRef = useRef<MapView>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animation d'entrée
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    // Obtenir la position actuelle
    getCurrentLocation();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') return;

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const newLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      setSelectedLocation(newLocation);
      getAddressFromCoordinates(location.coords.latitude, location.coords.longitude);
    } catch (error) {
      console.log('Erreur localisation:', error);
    }
  };

  const getAddressFromCoordinates = async (latitude: number, longitude: number) => {
    try {
      const result = await Location.reverseGeocodeAsync({ latitude, longitude });
      if (result.length > 0) {
        const address = result[0];
        const formattedAddress = `${address.street || ''} ${address.name || ''}`.trim();
        setSelectedLocation(prev => prev ? { ...prev, address: formattedAddress } : null);
      }
    } catch (error) {
      console.log('Erreur géocodage:', error);
    }
  };

  // HTML pour la carte Google Maps
  const mapHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body, html { margin: 0; padding: 0; height: 100%; }
        #map { height: 100%; width: 100%; }
      </style>
    </head>
    <body>
      <div id="map"></div>
      <script>
        let map;
        let marker;
        
        function initMap() {
          map = new google.maps.Map(document.getElementById('map'), {
            center: { lat: ${selectedLocation?.latitude || 6.3702}, lng: ${selectedLocation?.longitude || 2.3912} },
            zoom: 15,
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
            zoomControl: false,
            styles: [
              {
                featureType: "poi",
                elementType: "labels",
                stylers: [{ visibility: "on" }]
              }
            ]
          });
          
          // Marqueur initial
          if (${selectedLocation ? 'true' : 'false'}) {
            marker = new google.maps.Marker({
              position: { lat: ${selectedLocation?.latitude || 6.3702}, lng: ${selectedLocation?.longitude || 2.3912} },
              map: map,
              icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(\`
                  <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="18" fill="#10B981" stroke="white" stroke-width="4"/>
                    <circle cx="20" cy="20" r="8" fill="white"/>
                  </svg>
                \`),
                scaledSize: new google.maps.Size(40, 40),
                anchor: new google.maps.Point(20, 20)
              }
            });
          }
          
          map.addListener('click', function(event) {
            const lat = event.latLng.lat();
            const lng = event.latLng.lng();
            
            if (marker) {
              marker.setMap(null);
            }
            
            marker = new google.maps.Marker({
              position: { lat: lat, lng: lng },
              map: map,
              icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(\`
                  <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="18" fill="#10B981" stroke="white" stroke-width="4"/>
                    <circle cx="20" cy="20" r="8" fill="white"/>
                  </svg>
                \`),
                scaledSize: new google.maps.Size(40, 40),
                anchor: new google.maps.Point(20, 20)
              }
            });
            
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'locationSelected',
              latitude: lat,
              longitude: lng
            }));
          });
        }
      </script>
      <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s&callback=initMap">
      </script>
    </body>
    </html>
  `;

  const handleWebViewMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === 'locationSelected') {
        setSelectedLocation({
          latitude: data.latitude,
          longitude: data.longitude,
        });
        getAddressFromCoordinates(data.latitude, data.longitude);
      }
    } catch (error) {
      console.log('Erreur parsing message:', error);
    }
  };

  const handleConfirmLocation = () => {
    if (!selectedLocation) {
      Alert.alert('Aucune position sélectionnée');
      return;
    }
    navigation.goBack();
  };

  const handleManualSelection = () => {
    // Logique pour sélection manuelle
    console.log('Sélection manuelle');
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#000000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Votre localisation</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color="#9CA3AF" />
          <Text style={styles.searchPlaceholder}>Rechercher une adresse</Text>
        </View>
      </View>

      {/* Map Container - Google Maps */}
      <Animated.View style={[styles.mapContainer, { opacity: fadeAnim }]}>
        <MapView
          ref={mapRef}
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          initialRegion={{
            latitude: selectedLocation?.latitude || 6.3702,
            longitude: selectedLocation?.longitude || 2.3912,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }}
          onPress={(event) => {
            const { latitude, longitude } = event.nativeEvent.coordinate;
            setSelectedLocation({ latitude, longitude });
          }}
          onMapReady={() => setMapReady(true)}
          showsUserLocation={true}
          showsMyLocationButton={false}
          loadingEnabled={true}
          loadingIndicatorColor="#10B981"
        >
          {selectedLocation && (
            <Marker
              coordinate={{
                latitude: selectedLocation.latitude,
                longitude: selectedLocation.longitude,
              }}
              title="Position sélectionnée"
              pinColor="#10B981"
            />
          )}
        </MapView>

        {/* Loading Overlay */}
        {!mapReady && (
          <View style={styles.mapLoadingOverlay}>
            <View style={styles.loadingSpinner}>
              <Ionicons name="globe-outline" size={48} color="#10B981" />
            </View>
            <Text style={styles.loadingText}>Chargement de la carte...</Text>
          </View>
        )}
      </Animated.View>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        {/* Selected Location Info */}
        {selectedLocation && (
          <View style={styles.locationInfo}>
            <Text style={styles.locationAddress}>
              {selectedLocation.address || '123 Avenue de l\'Indépendance'}
            </Text>
            <Text style={styles.locationCoordinates}>
              Lat: {selectedLocation.latitude.toFixed(4)}, Long: {selectedLocation.longitude.toFixed(4)}
            </Text>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={handleConfirmLocation}
          >
            <Text style={styles.confirmButtonText}>Confirmer ma position</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.manualButton}
            onPress={handleManualSelection}
          >
            <Text style={styles.manualButtonText}>Sélectionner manuellement</Text>
          </TouchableOpacity>
        </View>

        {/* Recent Places */}
        <View style={styles.recentPlaces}>
          <Text style={styles.recentTitle}>Lieux récents</Text>
          
          <TouchableOpacity style={styles.placeItem}>
            <View style={styles.placeIcon}>
              <Ionicons name="storefront" size={20} color="#6B7280" />
            </View>
            <View style={styles.placeDetails}>
              <Text style={styles.placeName}>Marché Dantokpa</Text>
              <Text style={styles.placeLocation}>Cotonou, Bénin</Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.placeItem}>
            <View style={styles.placeIcon}>
              <Ionicons name="restaurant" size={20} color="#6B7280" />
            </View>
            <View style={styles.placeDetails}>
              <Text style={styles.placeName}>Restaurant Le Bénin</Text>
              <Text style={styles.placeLocation}>Porto-Novo, Bénin</Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Bottom Indicator */}
        <View style={styles.bottomIndicator}>
          <View style={styles.indicator} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchPlaceholder: {
    fontSize: 16,
    color: '#9CA3AF',
    flex: 1,
  },
  mapContainer: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: '#F8F9FA',
  },
  map: {
    flex: 1,
  },
  mapLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  loadingSpinner: {
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
  },
  bottomSheet: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  locationInfo: {
    marginBottom: 20,
  },
  locationAddress: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  locationCoordinates: {
    fontSize: 14,
    color: '#6B7280',
  },
  actionButtons: {
    gap: 12,
    marginBottom: 24,
  },
  confirmButton: {
    backgroundColor: '#10B981',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  manualButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  manualButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#10B981',
  },
  recentPlaces: {
    marginBottom: 20,
  },
  recentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
  },
  placeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 12,
  },
  placeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeDetails: {
    flex: 1,
  },
  placeName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 2,
  },
  placeLocation: {
    fontSize: 14,
    color: '#6B7280',
  },
  bottomIndicator: {
    alignItems: 'center',
    paddingTop: 8,
  },
  indicator: {
    width: 36,
    height: 4,
    backgroundColor: '#D1D5DB',
    borderRadius: 2,
  },
});

export default LocationScreenExact;
