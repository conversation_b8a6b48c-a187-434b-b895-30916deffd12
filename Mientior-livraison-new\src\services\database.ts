// Enhanced database service with proper TypeScript types and error handling
import { supabase } from './supabase';
import { 
  User, 
  merchant, 
  Produit, 
  Commande, 
  Address, 
  Location,
  FiltresRecherche,
  ResultatRecherche 
} from '../types';

// Database types matching Supabase schema
export interface DatabaseRestaurant {
  user_id: string;
  business_name: string;
  business_type: 'restaurant' | 'epicerie' | 'pharmacie' | 'boutique' | 'supermarche';
  description: any; // JSONB
  logo_url?: string;
  cover_image_url?: string;
  address: string;
  coordinates: any; // GEOMETRY
  phone?: string;
  email?: string;
  is_verified: boolean;
  is_open: boolean;
  opening_hours: any; // JSONB
  average_preparation_time: number;
  minimum_order_amount: number;
  base_delivery_fee: number;
  delivery_radius_km: number;
  rating: number;
  total_orders: number;
  created_at: string;
  updated_at: string;
}

export interface DatabaseProduct {
  id: string;
  merchant_id: string;
  category_id?: string;
  sku?: string;
  name: any; // JSONB for multilingual support
  description: any; // JSONB
  images: any; // JSONB array of URLs
  price: number;
  promotional_price?: number;
  promotion_start_date?: string;
  promotion_end_date?: string;
  is_available: boolean;
  inventory_count?: number;
  track_inventory: boolean;
  preparation_time_minutes: number;
  weight_grams?: number;
  allergens: any; // JSONB
  nutritional_info: any; // JSONB
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

export interface DatabaseOrder {
  id: string;
  order_number: string;
  client_id: string;
  merchant_id: string;
  delivery_address_id: string;
  service_type: 'restaurant' | 'colis' | 'marchandises' | 'pharmacie' | 'epicerie';
  status: 'en_attente' | 'confirmee' | 'en_preparation' | 'prete' | 'en_livraison' | 'livree' | 'annulee';
  priority: 'normale' | 'elevee' | 'urgente' | 'express';
  subtotal: number;
  delivery_fee: number;
  service_fee: number;
  taxes: number;
  discount_amount: number;
  total_amount: number;
  payment_method: string;
  payment_status: 'en_attente' | 'paye' | 'echec' | 'rembourse';
  special_instructions?: string;
  estimated_preparation_time: number;
  estimated_delivery_time: number;
  actual_preparation_time?: number;
  actual_delivery_time?: number;
  created_at: string;
  updated_at: string;
}

// Enhanced restaurant service
export class RestaurantService {
  async getAll(location?: Location, filters?: FiltresRecherche): Promise<merchant[]> {
    try {
      if (!supabase) {
        // Fallback to mock data in offline mode
        return this.getMockRestaurants();
      }

      let query = supabase
        .from('merchant_profiles')
        .select(`
          *,
          user:users!user_id(id, email, phone, created_at)
        `)
        .eq('is_verified', true)
        .eq('is_open', true);

      // Apply filters based on real database schema
      if (filters?.type_service) {
        query = query.eq('business_type', filters.type_service);
      }

      if (filters?.type_merchant && filters.type_merchant.length > 0) {
        query = query.in('business_type', filters.type_merchant);
      }

      if (filters?.prix_min !== undefined) {
        query = query.gte('minimum_order_amount', filters.prix_min);
      }

      if (filters?.prix_max !== undefined) {
        query = query.lte('minimum_order_amount', filters.prix_max);
      }

      if (filters?.note_min !== undefined) {
        query = query.gte('rating', filters.note_min);
      }

      if (filters?.ouvert_maintenant) {
        query = query.eq('is_open', true);
      }

      // Location-based filtering
      if (location && filters?.rayon_km) {
        // Use PostGIS for distance calculation
        query = query.rpc('restaurants_within_radius', {
          lat: location.latitude,
          lng: location.longitude,
          radius_km: filters.rayon_km
        });
      }

      // Sorting based on real database fields
      switch (filters?.tri) {
        case 'distance':
          // Distance sorting handled by PostGIS function when location provided
          if (!location) {
            query = query.order('rating', { ascending: false });
          }
          break;
        case 'note':
          query = query.order('rating', { ascending: false });
          break;
        case 'temps_livraison':
          query = query.order('average_preparation_time', { ascending: true });
          break;
        case 'prix':
          query = query.order('minimum_order_amount', { ascending: true });
          break;
        case 'popularite':
          query = query.order('total_orders', { ascending: false });
          break;
        default:
          // Default sort by rating and total orders
          query = query.order('rating', { ascending: false })
                      .order('total_orders', { ascending: false });
      }

      const { data, error } = await query.limit(50);

      if (error) {
        console.error('Erreur récupération restaurants:', error);
        throw new Error(`Erreur base de données: ${error.message}`);
      }

      return this.transformDatabaseRestaurants(data || []);
    } catch (error) {
      console.error('Erreur service restaurants:', error);
      // Fallback to cached data or mock data
      return this.getMockRestaurants();
    }
  }

  async getById(id: string): Promise<merchant | null> {
    try {
      if (!supabase) {
        return this.getMockRestaurants().find(r => r.id === id) || null;
      }

      const { data, error } = await supabase
        .from('merchant_profiles')
        .select(`
          *,
          user:users!user_id(id, email, phone, created_at)
        `)
        .eq('user_id', id)
        .single();

      if (error) {
        console.error('Erreur récupération restaurant:', error);
        return null;
      }

      return this.transformDatabaseRestaurant(data);
    } catch (error) {
      console.error('Erreur service restaurant:', error);
      return null;
    }
  }

  async search(query: string, location?: Location): Promise<ResultatRecherche> {
    try {
      if (!supabase) {
        const mockRestaurants = this.getMockRestaurants();
        const filtered = mockRestaurants.filter(r =>
          r.nom.toLowerCase().includes(query.toLowerCase()) ||
          r.description?.toLowerCase().includes(query.toLowerCase())
        );
        return {
          merchant_profiles: filtered,
          produits: [],
          total: filtered.length,
          page: 1,
          pages_total: 1
        };
      }

      // Enhanced full-text search with multiple criteria
      const searchTerm = query.trim().toLowerCase();
      let restaurantQuery = supabase
        .from('merchant_profiles')
        .select(`
          *,
          user:users!user_id(id, email, phone, created_at)
        `)
        .eq('is_verified', true)
        .eq('is_open', true);

      // Search in multiple fields
      if (searchTerm) {
        restaurantQuery = restaurantQuery.or(
          `business_name.ilike.%${searchTerm}%,` +
          `description->>'fr'.ilike.%${searchTerm}%,` +
          `description->>'en'.ilike.%${searchTerm}%,` +
          `business_type.ilike.%${searchTerm}%`
        );
      }

      // Add location-based filtering if provided
      if (location) {
        restaurantQuery = restaurantQuery.rpc('restaurants_within_radius', {
          lat: location.latitude,
          lng: location.longitude,
          radius_km: 15 // 15km search radius
        });
      }

      const { data: restaurantData, error: restaurantError } = await restaurantQuery
        .order('rating', { ascending: false })
        .limit(20);

      if (restaurantError) {
        console.error('Erreur recherche restaurants:', restaurantError);
        throw new Error(`Erreur recherche restaurants: ${restaurantError.message}`);
      }

      // Search products as well
      let productData: any[] = [];
      if (searchTerm) {
        const { data: products, error: productError } = await supabase
          .from('products')
          .select(`
            *,
            merchant:merchant_profiles!merchant_id(business_name, is_open)
          `)
          .or(
            `name->>'fr'.ilike.%${searchTerm}%,` +
            `name->>'en'.ilike.%${searchTerm}%,` +
            `description->>'fr'.ilike.%${searchTerm}%,` +
            `description->>'en'.ilike.%${searchTerm}%`
          )
          .eq('is_available', true)
          .limit(10);

        if (!productError && products) {
          productData = products.filter(p => p.merchant?.is_open);
        }
      }

      const restaurants = this.transformDatabaseRestaurants(restaurantData || []);
      const produits = productData.map(p => this.transformDatabaseProduct(p));

      return {
        merchant_profiles: restaurants,
        produits,
        total: restaurants.length + produits.length,
        page: 1,
        pages_total: 1
      };
    } catch (error) {
      console.error('Erreur service recherche:', error);
      // Fallback to mock data search
      const mockRestaurants = this.getMockRestaurants();
      const filtered = mockRestaurants.filter(r =>
        r.nom.toLowerCase().includes(query.toLowerCase()) ||
        r.description?.toLowerCase().includes(query.toLowerCase())
      );
      return {
        merchant_profiles: filtered,
        produits: [],
        total: filtered.length,
        page: 1,
        pages_total: 1
      };
    }
  }

  // Helper method to transform database product for search results
  private transformDatabaseProduct(dbProduct: any): any {
    return {
      id: dbProduct.id,
      merchant_id: dbProduct.merchant_id,
      nom: typeof dbProduct.name === 'object'
        ? dbProduct.name?.fr || dbProduct.name?.en || 'Produit'
        : dbProduct.name || 'Produit',
      description: typeof dbProduct.description === 'object'
        ? dbProduct.description?.fr || dbProduct.description?.en || ''
        : dbProduct.description || '',
      prix: dbProduct.price,
      prix_reduit: dbProduct.promotional_price,
      devise: 'XOF',
      categorie: dbProduct.category_id || '',
      image_url: Array.isArray(dbProduct.images) && dbProduct.images.length > 0
        ? dbProduct.images[0] : undefined,
      is_disponible: dbProduct.is_available,
      is_recommande: dbProduct.is_featured,
      temps_preparation: dbProduct.preparation_time_minutes || 15,
      ingredients: [],
      allergenes: Array.isArray(dbProduct.allergens) ? dbProduct.allergens : [],
      valeurs_nutritionnelles: dbProduct.nutritional_info || {},
      nombre_commandes: 0,
      quantite_max: dbProduct.inventory_count
    };
  }

  private transformDatabaseRestaurant(dbRestaurant: any): merchant {
    return {
      id: dbRestaurant.user_id,
      nom: dbRestaurant.business_name,
      description: typeof dbRestaurant.description === 'object' 
        ? dbRestaurant.description?.fr || dbRestaurant.description?.en || ''
        : dbRestaurant.description || '',
      type_merchant: dbRestaurant.business_type,
      proprietaire_id: dbRestaurant.user_id,
      adresse: {
        id: dbRestaurant.user_id,
        user_id: dbRestaurant.user_id,
        label: 'Restaurant',
        address_line_1: dbRestaurant.address,
        address_line_2: '',
        coordinates: dbRestaurant.coordinates ? {
          latitude: dbRestaurant.coordinates.coordinates[1],
          longitude: dbRestaurant.coordinates.coordinates[0]
        } : { latitude: 0, longitude: 0 },
        is_default: true,
        created_at: dbRestaurant.created_at,
        updated_at: dbRestaurant.updated_at
      },
      telephone: dbRestaurant.phone || '',
      email: dbRestaurant.email,
      heures_ouverture: dbRestaurant.opening_hours || {},
      image_url: dbRestaurant.cover_image_url,
      note_moyenne: dbRestaurant.rating || 0,
      nombre_avis: dbRestaurant.total_orders || 0,
      is_active: dbRestaurant.is_open,
      zone_livraison_km: dbRestaurant.delivery_radius_km || 5,
      frais_livraison_base: dbRestaurant.base_delivery_fee || 0,
      temps_preparation_moyen: dbRestaurant.average_preparation_time || 30
    };
  }

  private transformDatabaseRestaurants(dbRestaurants: any[]): merchant[] {
    return dbRestaurants.map(restaurant => this.transformDatabaseRestaurant(restaurant));
  }

  private getMockRestaurants(): merchant[] {
    // Mock data for offline mode - représentant les vraies données de la base
    return [
      {
        id: '1',
        nom: 'Restaurant Africain Délice',
        description: 'Cuisine africaine authentique avec des plats traditionnels du Bénin',
        type_merchant: 'restaurant',
        proprietaire_id: '1',
        adresse: {
          id: '1',
          user_id: '1',
          label: 'home',
          nom_complet: 'Restaurant Africain Délice',
          adresse_ligne1: 'Quartier Ganhi',
          adresse_ligne2: 'Près du marché central',
          ville: 'Cotonou',
          quartier: 'Ganhi',
          code_postal: '01BP',
          pays: 'Bénin',
          instructions_livraison: 'Entrée principale du restaurant',
          coordonnees: { latitude: 6.3702, longitude: 2.3912 },
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        telephone: '+229 97 00 00 01',
        email: '<EMAIL>',
        heures_ouverture: {
          lundi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
          mardi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
          mercredi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
          jeudi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
          vendredi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
          samedi: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '22:00' }] },
          dimanche: { ouvert: true, heures: [{ ouverture: '10:00', fermeture: '20:00' }] }
        },
        image_url: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=500&q=80',
        note_moyenne: 4.5,
        nombre_avis: 127,
        is_active: true,
        zone_livraison_km: 8,
        frais_livraison_base: 500,
        temps_preparation_moyen: 25
      },
      {
        id: '2',
        nom: 'Épicerie du Marché',
        description: 'Produits frais et locaux, épices africaines, légumes du terroir',
        type_merchant: 'epicerie',
        proprietaire_id: '2',
        adresse: {
          id: '2',
          user_id: '2',
          label: 'work',
          nom_complet: 'Épicerie du Marché',
          adresse_ligne1: 'Marché de Dantokpa',
          adresse_ligne2: 'Secteur des épices',
          ville: 'Cotonou',
          quartier: 'Dantokpa',
          code_postal: '01BP',
          pays: 'Bénin',
          instructions_livraison: 'Boutique 45, allée des épices',
          coordonnees: { latitude: 6.3654, longitude: 2.4183 },
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        telephone: '+229 97 00 00 02',
        email: '<EMAIL>',
        heures_ouverture: {
          lundi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
          mardi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
          mercredi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
          jeudi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
          vendredi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
          samedi: { ouvert: true, heures: [{ ouverture: '06:00', fermeture: '20:00' }] },
          dimanche: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '18:00' }] }
        },
        image_url: 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=500&q=80',
        note_moyenne: 4.2,
        nombre_avis: 89,
        is_active: true,
        zone_livraison_km: 5,
        frais_livraison_base: 300,
        temps_preparation_moyen: 15
      },
      {
        id: '3',
        nom: 'Pharmacie Santé Plus',
        description: 'Pharmacie moderne avec médicaments et produits de santé',
        type_merchant: 'pharmacie',
        proprietaire_id: '3',
        adresse: {
          id: '3',
          user_id: '3',
          label: 'other',
          nom_complet: 'Pharmacie Santé Plus',
          adresse_ligne1: 'Avenue Steinmetz',
          adresse_ligne2: 'Face à la banque',
          ville: 'Cotonou',
          quartier: 'Centre-ville',
          code_postal: '01BP',
          pays: 'Bénin',
          instructions_livraison: 'Entrée principale, comptoir de garde',
          coordonnees: { latitude: 6.3611, longitude: 2.4019 },
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        telephone: '+229 97 00 00 03',
        email: '<EMAIL>',
        heures_ouverture: {
          lundi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
          mardi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
          mercredi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
          jeudi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
          vendredi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
          samedi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '21:00' }] },
          dimanche: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '19:00' }] }
        },
        image_url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=500&q=80',
        note_moyenne: 4.7,
        nombre_avis: 156,
        is_active: true,
        zone_livraison_km: 10,
        frais_livraison_base: 400,
        temps_preparation_moyen: 10
      },
      {
        id: '4',
        nom: 'Supermarché Moderne',
        description: 'Grand supermarché avec tous les produits du quotidien',
        type_merchant: 'supermarche',
        proprietaire_id: '4',
        adresse: {
          id: '4',
          user_id: '4',
          label: 'other',
          nom_complet: 'Supermarché Moderne',
          adresse_ligne1: 'Boulevard de la Marina',
          adresse_ligne2: 'Centre commercial Marina',
          ville: 'Cotonou',
          quartier: 'Marina',
          code_postal: '01BP',
          pays: 'Bénin',
          instructions_livraison: 'Parking principal, service livraison',
          coordonnees: { latitude: 6.3573, longitude: 2.4147 },
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        telephone: '+229 97 00 00 04',
        email: '<EMAIL>',
        heures_ouverture: {
          lundi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
          mardi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
          mercredi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
          jeudi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
          vendredi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
          samedi: { ouvert: true, heures: [{ ouverture: '07:00', fermeture: '22:00' }] },
          dimanche: { ouvert: true, heures: [{ ouverture: '08:00', fermeture: '20:00' }] }
        },
        image_url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500&q=80',
        note_moyenne: 4.3,
        nombre_avis: 203,
        is_active: true,
        zone_livraison_km: 12,
        frais_livraison_base: 600,
        temps_preparation_moyen: 20
      }
    ];
  }
}

// Enhanced product service
export class ProductService {
  async getByMerchant(merchantId: string, filters?: any): Promise<Produit[]> {
    try {
      if (!supabase) {
        return this.getMockProducts(merchantId);
      }

      let query = supabase
        .from('products')
        .select(`
          *,
          category:categories(id, name),
          merchant:merchant_profiles!merchant_id(business_name)
        `)
        .eq('merchant_id', merchantId)
        .eq('is_available', true);

      // Apply filters
      if (filters?.category) {
        query = query.eq('category_id', filters.category);
      }

      if (filters?.minPrice !== undefined) {
        query = query.gte('price', filters.minPrice);
      }

      if (filters?.maxPrice !== undefined) {
        query = query.lte('price', filters.maxPrice);
      }

      if (filters?.search) {
        query = query.or(`name->>'fr'.ilike.%${filters.search}%,description->>'fr'.ilike.%${filters.search}%`);
      }

      if (filters?.available !== undefined) {
        query = query.eq('is_available', filters.available);
      }

      // Sorting
      switch (filters?.sortBy) {
        case 'price_asc':
          query = query.order('price', { ascending: true });
          break;
        case 'price_desc':
          query = query.order('price', { ascending: false });
          break;
        case 'name':
          query = query.order('name', { ascending: true });
          break;
        case 'featured':
          query = query.order('is_featured', { ascending: false });
          break;
        default:
          query = query.order('created_at', { ascending: false });
      }

      const { data, error } = await query.limit(100);

      if (error) {
        console.error('Erreur récupération produits:', error);
        throw new Error(`Erreur base de données: ${error.message}`);
      }

      return this.transformDatabaseProducts(data || []);
    } catch (error) {
      console.error('Erreur service produits:', error);
      return this.getMockProducts(merchantId);
    }
  }

  async getById(id: string): Promise<Produit | null> {
    try {
      if (!supabase) {
        return this.getMockProducts().find(p => p.id === id) || null;
      }

      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          category:categories(id, name),
          merchant:merchant_profiles!merchant_id(business_name)
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error('Erreur récupération produit:', error);
        return null;
      }

      return this.transformDatabaseProduct(data);
    } catch (error) {
      console.error('Erreur service produit:', error);
      return null;
    }
  }

  async getCategories(merchantId: string): Promise<Array<{id: string, name: string, count: number}>> {
    try {
      if (!supabase) {
        return [
          { id: '1', name: 'Plats principaux', count: 12 },
          { id: '2', name: 'Entrées', count: 8 },
          { id: '3', name: 'Desserts', count: 6 },
          { id: '4', name: 'Boissons', count: 15 }
        ];
      }

      const { data, error } = await supabase
        .from('products')
        .select(`
          category_id,
          category:categories(id, name)
        `)
        .eq('merchant_id', merchantId)
        .eq('is_available', true);

      if (error) throw error;

      // Group by category and count
      const categoryMap = new Map();
      data?.forEach(item => {
        if (item.category) {
          const key = item.category.id;
          if (categoryMap.has(key)) {
            categoryMap.get(key).count++;
          } else {
            categoryMap.set(key, {
              id: item.category.id,
              name: item.category.name,
              count: 1
            });
          }
        }
      });

      return Array.from(categoryMap.values());
    } catch (error) {
      console.error('Erreur récupération catégories:', error);
      return [];
    }
  }

  private transformDatabaseProduct(dbProduct: any): Produit {
    return {
      id: dbProduct.id,
      nom: typeof dbProduct.name === 'object'
        ? dbProduct.name?.fr || dbProduct.name?.en || 'Produit'
        : dbProduct.name || 'Produit',
      description: typeof dbProduct.description === 'object'
        ? dbProduct.description?.fr || dbProduct.description?.en || ''
        : dbProduct.description || '',
      prix: dbProduct.price,
      prix_promo: dbProduct.promotional_price,
      devise: 'XOF',
      merchant_id: dbProduct.merchant_id,
      categorie_id: dbProduct.category_id,
      images: Array.isArray(dbProduct.images) ? dbProduct.images : [],
      is_available: dbProduct.is_available,
      temps_preparation: dbProduct.preparation_time_minutes || 15,
      ingredients: [],
      allergenes: Array.isArray(dbProduct.allergens) ? dbProduct.allergens : [],
      valeurs_nutritionnelles: dbProduct.nutritional_info || {},
      options: [],
      is_featured: dbProduct.is_featured || false,
      stock_quantity: dbProduct.inventory_count,
      track_inventory: dbProduct.track_inventory || false
    };
  }

  private transformDatabaseProducts(dbProducts: any[]): Produit[] {
    return dbProducts.map(product => this.transformDatabaseProduct(product));
  }

  private getMockProducts(merchantId?: string): Produit[] {
    const allProducts = [
      {
        id: '1',
        nom: 'Riz au gras',
        description: 'Riz traditionnel béninois avec viande et légumes',
        prix: 2500,
        prix_promo: undefined,
        devise: 'XOF' as const,
        merchant_id: '1',
        categorie_id: '1',
        images: ['https://images.unsplash.com/photo-1586190848861-99aa4a171e90?w=500'],
        is_available: true,
        temps_preparation: 25,
        ingredients: ['Riz', 'Viande', 'Tomates', 'Oignons', 'Épices'],
        allergenes: [],
        valeurs_nutritionnelles: { calories: 450, proteines: 25, glucides: 60 },
        options: [],
        is_featured: true,
        stock_quantity: undefined,
        track_inventory: false
      },
      {
        id: '2',
        nom: 'Poisson braisé',
        description: 'Poisson frais grillé aux épices locales',
        prix: 3000,
        prix_promo: 2700,
        devise: 'XOF' as const,
        merchant_id: '1',
        categorie_id: '1',
        images: ['https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=500'],
        is_available: true,
        temps_preparation: 20,
        ingredients: ['Poisson', 'Épices', 'Huile', 'Citron'],
        allergenes: ['Poisson'],
        valeurs_nutritionnelles: { calories: 320, proteines: 35, glucides: 5 },
        options: [],
        is_featured: false,
        stock_quantity: undefined,
        track_inventory: false
      }
    ];

    return merchantId ? allProducts.filter(p => p.merchant_id === merchantId) : allProducts;
  }
}

// Enhanced order service
export class OrderService {
  async getByUser(userId: string, role: string = 'client'): Promise<Commande[]> {
    try {
      if (!supabase) {
        return this.getMockOrders(userId);
      }

      let query = supabase
        .from('orders')
        .select(`
          *,
          client:users!client_id(id, full_name, phone),
          merchant:merchant_profiles!merchant_id(business_name, phone),
          delivery_address:addresses!delivery_address_id(*),
          order_items(
            *,
            product:products(id, name, price, images)
          )
        `);

      // Filter based on user role
      if (role === 'client') {
        query = query.eq('client_id', userId);
      } else if (role === 'merchant') {
        query = query.eq('merchant_id', userId);
      } else if (role === 'livreur') {
        // For delivery persons, get orders assigned to them
        query = query.not('delivery_person_id', 'is', null);
      }

      query = query.order('created_at', { ascending: false });

      const { data, error } = await query.limit(50);

      if (error) {
        console.error('Erreur récupération commandes:', error);
        throw new Error(`Erreur base de données: ${error.message}`);
      }

      return this.transformDatabaseOrders(data || []);
    } catch (error) {
      console.error('Erreur service commandes:', error);
      return this.getMockOrders(userId);
    }
  }

  async getById(orderId: string): Promise<Commande | null> {
    try {
      if (!supabase) {
        return this.getMockOrders().find(o => o.id === orderId) || null;
      }

      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          client:users!client_id(id, full_name, phone),
          merchant:merchant_profiles!merchant_id(business_name, phone),
          delivery_address:addresses!delivery_address_id(*),
          order_items(
            *,
            product:products(id, name, price, images)
          )
        `)
        .eq('id', orderId)
        .single();

      if (error) {
        console.error('Erreur récupération commande:', error);
        return null;
      }

      return this.transformDatabaseOrder(data);
    } catch (error) {
      console.error('Erreur service commande:', error);
      return null;
    }
  }

  async updateStatus(orderId: string, status: DatabaseOrder['status']): Promise<boolean> {
    try {
      if (!supabase) {
        console.log('Mode offline - simulation mise à jour statut:', { orderId, status });
        return true;
      }

      const { error } = await supabase
        .from('orders')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (error) {
        console.error('Erreur mise à jour statut commande:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erreur service mise à jour commande:', error);
      return false;
    }
  }

  private transformDatabaseOrder(dbOrder: any): Commande {
    return {
      id: dbOrder.id,
      client_id: dbOrder.client_id,
      merchant_id: dbOrder.merchant_id,
      livreur_id: dbOrder.delivery_person_id,
      statut: this.mapDatabaseStatus(dbOrder.status),
      type_livraison: 'standard',
      adresse_livraison: dbOrder.delivery_address ? {
        id: dbOrder.delivery_address.id,
        user_id: dbOrder.delivery_address.user_id,
        label: dbOrder.delivery_address.label || 'Livraison',
        nom_complet: dbOrder.delivery_address.full_name || '',
        adresse_ligne1: dbOrder.delivery_address.address_line_1,
        adresse_ligne2: dbOrder.delivery_address.address_line_2 || '',
        ville: dbOrder.delivery_address.city || '',
        quartier: dbOrder.delivery_address.district || '',
        code_postal: dbOrder.delivery_address.postal_code,
        pays: dbOrder.delivery_address.country || 'Bénin',
        instructions_livraison: dbOrder.delivery_address.delivery_instructions,
        coordonnees: dbOrder.delivery_address.coordinates ? {
          latitude: dbOrder.delivery_address.coordinates.coordinates[1],
          longitude: dbOrder.delivery_address.coordinates.coordinates[0]
        } : { latitude: 0, longitude: 0 },
        is_default: dbOrder.delivery_address.is_default || false,
        created_at: dbOrder.delivery_address.created_at,
        updated_at: dbOrder.delivery_address.updated_at
      } : {} as any,
      items: dbOrder.order_items?.map((item: any) => ({
        id: item.id,
        commande_id: dbOrder.id,
        produit_id: item.product_id,
        produit: item.product ? {
          id: item.product.id,
          merchant_id: dbOrder.merchant_id,
          nom: typeof item.product.name === 'object'
            ? item.product.name?.fr || item.product.name?.en || 'Produit'
            : item.product.name || 'Produit',
          description: '',
          prix: item.product.price,
          devise: 'XOF',
          categorie: '',
          image_url: Array.isArray(item.product.images) && item.product.images.length > 0
            ? item.product.images[0] : undefined,
          is_disponible: true,
          temps_preparation: 15,
          ingredients: [],
          allergenes: [],
          valeurs_nutritionnelles: {}
        } : {} as any,
        quantite: item.quantity,
        prix_unitaire: item.unit_price,
        prix_total: item.total_price,
        notes: item.notes,
        modifications: item.modifications || []
      })) || [],
      sous_total: dbOrder.subtotal,
      frais_livraison: dbOrder.delivery_fee,
      frais_service: dbOrder.service_fee,
      taxes: dbOrder.taxes,
      total: dbOrder.total_amount,
      devise: 'XOF',
      instructions_speciales: dbOrder.special_instructions,
      heure_souhaitee: dbOrder.preferred_delivery_time,
      temps_estime: dbOrder.estimated_delivery_time,
      created_at: dbOrder.created_at,
      updated_at: dbOrder.updated_at
    };
  }

  private transformDatabaseOrders(dbOrders: any[]): Commande[] {
    return dbOrders.map(order => this.transformDatabaseOrder(order));
  }

  private mapDatabaseStatus(dbStatus: string): StatutCommande {
    const statusMap: Record<string, StatutCommande> = {
      'en_attente': 'en_attente',
      'confirmee': 'confirmee',
      'en_preparation': 'en_preparation',
      'prete': 'prete',
      'en_livraison': 'en_cours_livraison',
      'livree': 'livree',
      'annulee': 'annulee'
    };
    return statusMap[dbStatus] || 'en_attente';
  }

  private getMockOrders(userId?: string): Commande[] {
    return [
      {
        id: '1',
        client_id: userId || '1',
        merchant_id: '1',
        livreur_id: '1',
        statut: 'en_cours_livraison',
        type_livraison: 'standard',
        adresse_livraison: {
          id: '1',
          user_id: userId || '1',
          label: 'home',
          nom_complet: 'John Doe',
          adresse_ligne1: 'Quartier Ganhi',
          adresse_ligne2: '',
          ville: 'Cotonou',
          quartier: 'Ganhi',
          pays: 'Bénin',
          coordonnees: { latitude: 6.3702, longitude: 2.3912 },
          is_default: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        items: [
          {
            id: '1',
            commande_id: '1',
            produit_id: '1',
            produit: {
              id: '1',
              merchant_id: '1',
              nom: 'Riz au gras',
              description: 'Riz traditionnel béninois',
              prix: 2500,
              devise: 'XOF',
              categorie: 'Plats principaux',
              is_disponible: true,
              temps_preparation: 25,
              ingredients: [],
              allergenes: [],
              valeurs_nutritionnelles: {}
            },
            quantite: 2,
            prix_unitaire: 2500,
            prix_total: 5000,
            notes: 'Bien épicé',
            modifications: []
          }
        ],
        sous_total: 5000,
        frais_livraison: 500,
        frais_service: 250,
        taxes: 0,
        total: 5750,
        devise: 'XOF',
        instructions_speciales: 'Sonner à la porte',
        temps_estime: 45,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
}

// Export singleton instances
export const restaurantService = new RestaurantService();
export const productService = new ProductService();
export const orderService = new OrderService();
