{"hash": "d6771222", "configHash": "ceffba1c", "lockfileHash": "f9dcf36f", "browserHash": "af4b655a", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "1d830299", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "c95abec5", "needsInterop": true}, "react-dom/server": {"src": "../../react-dom/server.edge.js", "file": "react-dom_server.js", "fileHash": "3546bb7d", "needsInterop": true}, "react-router": {"src": "../../react-router/dist/development/index.js", "file": "react-router.js", "fileHash": "37759ddf", "needsInterop": true}, "unenv/node/inspector/promises": {"src": "../../unenv/dist/runtime/node/inspector/promises.mjs", "file": "unenv_node_inspector_promises.js", "fileHash": "cb1f02ce", "needsInterop": false}, "unenv/node/readline/promises": {"src": "../../unenv/dist/runtime/node/readline/promises.mjs", "file": "unenv_node_readline_promises.js", "fileHash": "aea165d9", "needsInterop": false}, "unenv/node/fs/promises": {"src": "../../unenv/dist/runtime/node/fs/promises.mjs", "file": "unenv_node_fs_promises.js", "fileHash": "8a6639fe", "needsInterop": false}, "unenv/mock/proxy-cjs": {"src": "../../unenv/lib/mock.cjs", "file": "unenv_mock_proxy-cjs.js", "fileHash": "aaea5db1", "needsInterop": true}, "unenv/node/worker_threads": {"src": "../../unenv/dist/runtime/node/worker_threads.mjs", "file": "unenv_node_worker_threads.js", "fileHash": "254652d8", "needsInterop": false}, "unenv/node/child_process": {"src": "../../unenv/dist/runtime/node/child_process.mjs", "file": "unenv_node_child_process.js", "fileHash": "e8fae309", "needsInterop": false}, "unenv/node/trace_events": {"src": "../../unenv/dist/runtime/node/trace_events.mjs", "file": "unenv_node_trace_events.js", "fileHash": "633d119a", "needsInterop": false}, "@cloudflare/unenv-preset/node/async_hooks": {"src": "../../@cloudflare/unenv-preset/dist/runtime/node/async_hooks.mjs", "file": "@cloudflare_unenv-preset_node_async_hooks.js", "fileHash": "a9d4b32d", "needsInterop": false}, "unenv/node/perf_hooks": {"src": "../../unenv/dist/runtime/node/perf_hooks.mjs", "file": "unenv_node_perf_hooks.js", "fileHash": "2a145141", "needsInterop": false}, "unenv/node/constants": {"src": "../../unenv/dist/runtime/node/constants.mjs", "file": "unenv_node_constants.js", "fileHash": "8782120a", "needsInterop": false}, "unenv/node/inspector": {"src": "../../unenv/dist/runtime/node/inspector.mjs", "file": "unenv_node_inspector.js", "fileHash": "d0979337", "needsInterop": false}, "unenv/node/punycode": {"src": "../../unenv/dist/runtime/node/punycode.mjs", "file": "unenv_node_punycode.js", "fileHash": "07464d47", "needsInterop": false}, "unenv/node/readline": {"src": "../../unenv/dist/runtime/node/readline.mjs", "file": "unenv_node_readline.js", "fileHash": "4d7bb722", "needsInterop": false}, "unenv/node/cluster": {"src": "../../unenv/dist/runtime/node/cluster.mjs", "file": "unenv_node_cluster.js", "fileHash": "d314e22c", "needsInterop": false}, "@cloudflare/unenv-preset/node/console": {"src": "../../@cloudflare/unenv-preset/dist/runtime/node/console.mjs", "file": "@cloudflare_unenv-preset_node_console.js", "fileHash": "dc196b94", "needsInterop": false}, "@cloudflare/unenv-preset/node/process": {"src": "../../@cloudflare/unenv-preset/dist/runtime/node/process.mjs", "file": "@cloudflare_unenv-preset_node_process.js", "fileHash": "f711095f", "needsInterop": false}, "@cloudflare/unenv-preset/node/crypto": {"src": "../../@cloudflare/unenv-preset/dist/runtime/node/crypto.mjs", "file": "@cloudflare_unenv-preset_node_crypto.js", "fileHash": "4f37d536", "needsInterop": false}, "unenv/node/domain": {"src": "../../unenv/dist/runtime/node/domain.mjs", "file": "unenv_node_domain.js", "fileHash": "94ee503e", "needsInterop": false}, "@cloudflare/unenv-preset/node/module": {"src": "../../@cloudflare/unenv-preset/dist/runtime/node/module.mjs", "file": "@cloudflare_unenv-preset_node_module.js", "fileHash": "0e94d972", "needsInterop": false}, "unenv/node/sqlite": {"src": "../../unenv/dist/runtime/node/sqlite.mjs", "file": "unenv_node_sqlite.js", "fileHash": "e85246c6", "needsInterop": false}, "unenv/node/dgram": {"src": "../../unenv/dist/runtime/node/dgram.mjs", "file": "unenv_node_dgram.js", "fileHash": "b51a529c", "needsInterop": false}, "unenv/node/http2": {"src": "../../unenv/dist/runtime/node/http2.mjs", "file": "unenv_node_http2.js", "fileHash": "f8594040", "needsInterop": false}, "unenv/node/https": {"src": "../../unenv/dist/runtime/node/https.mjs", "file": "unenv_node_https.js", "fileHash": "81a749f3", "needsInterop": false}, "unenv/node/http": {"src": "../../unenv/dist/runtime/node/http.mjs", "file": "unenv_node_http.js", "fileHash": "2857a085", "needsInterop": false}, "unenv/node/repl": {"src": "../../unenv/dist/runtime/node/repl.mjs", "file": "unenv_node_repl.js", "fileHash": "c95b404c", "needsInterop": false}, "@cloudflare/unenv-preset/node/util": {"src": "../../@cloudflare/unenv-preset/dist/runtime/node/util.mjs", "file": "@cloudflare_unenv-preset_node_util.js", "fileHash": "177dc107", "needsInterop": false}, "unenv/node/wasi": {"src": "../../unenv/dist/runtime/node/wasi.mjs", "file": "unenv_node_wasi.js", "fileHash": "394599f3", "needsInterop": false}, "@cloudflare/unenv-preset/node/tls": {"src": "../../@cloudflare/unenv-preset/dist/runtime/node/tls.mjs", "file": "@cloudflare_unenv-preset_node_tls.js", "fileHash": "edad8ead", "needsInterop": false}, "unenv/node/tty": {"src": "../../unenv/dist/runtime/node/tty.mjs", "file": "unenv_node_tty.js", "fileHash": "c15a6ea1", "needsInterop": false}, "unenv/node/fs": {"src": "../../unenv/dist/runtime/node/fs.mjs", "file": "unenv_node_fs.js", "fileHash": "5fd4d147", "needsInterop": false}, "unenv/node/os": {"src": "../../unenv/dist/runtime/node/os.mjs", "file": "unenv_node_os.js", "fileHash": "535901bd", "needsInterop": false}, "unenv/node/v8": {"src": "../../unenv/dist/runtime/node/v8.mjs", "file": "unenv_node_v8.js", "fileHash": "67cec654", "needsInterop": false}, "unenv/node/vm": {"src": "../../unenv/dist/runtime/node/vm.mjs", "file": "unenv_node_vm.js", "fileHash": "b14117fd", "needsInterop": false}, "@cloudflare/unenv-preset/polyfill/performance": {"src": "../../@cloudflare/unenv-preset/dist/runtime/polyfill/performance.mjs", "file": "@cloudflare_unenv-preset_polyfill_performance.js", "fileHash": "8aaf9284", "needsInterop": false}, "hono": {"src": "../../hono/dist/index.js", "file": "hono.js", "fileHash": "836a1986", "needsInterop": false}, "hono/cors": {"src": "../../hono/dist/middleware/cors/index.js", "file": "hono_cors.js", "fileHash": "942693b3", "needsInterop": false}, "isbot": {"src": "../../isbot/index.mjs", "file": "isbot.js", "fileHash": "2c898007", "needsInterop": false}, "muppet": {"src": "../../muppet/dist/index.js", "file": "muppet.js", "fileHash": "f0e3dd87", "needsInterop": false}, "muppet/streaming": {"src": "../../muppet/dist/streaming.js", "file": "muppet_streaming.js", "fileHash": "5429fc21", "needsInterop": false}, "nanoid": {"src": "../../nanoid/index.browser.js", "file": "nanoid.js", "fileHash": "8b3649a5", "needsInterop": false}, "supermemory": {"src": "../../supermemory/index.mjs", "file": "supermemory.js", "fileHash": "d5379157", "needsInterop": false}, "zod": {"src": "../../zod/dist/esm/index.js", "file": "zod.js", "fileHash": "087be7c0", "needsInterop": false}}, "chunks": {"valibot-DOnkF2ES-XSPIN2JP": {"file": "valibot-DOnkF2ES-XSPIN2JP.js"}, "zod-DuHkA2Jp-MRBFWG26": {"file": "zod-DuHkA2Jp-MRBFWG26.js"}, "chunk-2JZEACTZ": {"file": "chunk-2JZEACTZ.js"}, "chunk-T5ENI2NM": {"file": "chunk-T5ENI2NM.js"}, "arktype-CqF_-yop-X3RDNEHC": {"file": "arktype-CqF_-yop-X3RDNEHC.js"}, "effect-pZiNZiGm-3L6RYGQS": {"file": "effect-pZiNZiGm-3L6RYGQS.js"}, "chunk-YKCPFTPD": {"file": "chunk-YKCPFTPD.js"}, "chunk-5TF4N3S3": {"file": "chunk-5TF4N3S3.js"}, "chunk-M5C2KFXU": {"file": "chunk-M5C2KFXU.js"}, "chunk-GX42U4MJ": {"file": "chunk-GX42U4MJ.js"}, "chunk-TKFP2B6M": {"file": "chunk-TKFP2B6M.js"}, "chunk-ARICXIOR": {"file": "chunk-ARICXIOR.js"}, "chunk-DQN6H3OM": {"file": "chunk-DQN6H3OM.js"}, "chunk-NVGP5Q3A": {"file": "chunk-NVGP5Q3A.js"}, "chunk-AIZST77Q": {"file": "chunk-AIZST77Q.js"}, "chunk-6KHCSJPG": {"file": "chunk-6KHCSJPG.js"}, "chunk-AO3S7MWW": {"file": "chunk-AO3S7MWW.js"}, "chunk-HKJ2B2AA": {"file": "chunk-HKJ2B2AA.js"}}}