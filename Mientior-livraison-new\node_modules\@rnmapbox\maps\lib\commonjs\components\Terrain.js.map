{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_StyleValue", "_RNMBXTerrainNativeComponent", "_interopRequireDefault", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "Terrain", "exports", "memo", "props", "style", "exaggeration", "console", "warn", "baseProps", "useMemo", "reactStyle", "transformStyle", "undefined", "jsx"], "sourceRoot": "../../../src", "sources": ["components/Terrain.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAGA,IAAAC,WAAA,GAAAD,OAAA;AAEA,IAAAE,4BAAA,GAAAC,sBAAA,CAAAH,OAAA;AAAgF,IAAAI,WAAA,GAAAJ,OAAA;AAAA,SAAAG,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAV,uBAAA,YAAAA,CAAAM,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAmBzE,MAAMgB,OAAO,GAAAC,OAAA,CAAAD,OAAA,gBAAG,IAAAE,WAAI,EAAEC,KAAY,IAAK;EAC5C,IAAI;IAAEC,KAAK,GAAG,CAAC;EAAE,CAAC,GAAGD,KAAK;EAE1B,IAAIA,KAAK,CAACE,YAAY,EAAE;IACtBC,OAAO,CAACC,IAAI,CACV,kFACF,CAAC;IACDH,KAAK,GAAG;MAAEC,YAAY,EAAEF,KAAK,CAACE,YAAY;MAAE,GAAGD;IAAM,CAAC;EACxD;EAEA,MAAMI,SAAS,GAAG,IAAAC,cAAO,EAAC,MAAM;IAC9B,OAAO;MACL,GAAGN,KAAK;MACRO,UAAU,EAAE,IAAAC,0BAAc,EAACP,KAAK,CAAC;MACjCA,KAAK,EAAEQ;IACT,CAAC;EACH,CAAC,EAAE,CAACT,KAAK,EAAEC,KAAK,CAAC,CAAC;EAElB,oBAAO,IAAAxB,WAAA,CAAAiC,GAAA,EAACnC,4BAAA,CAAAK,OAA4B;IAAA,GAAKyB;EAAS,CAAG,CAAC;AACxD,CAAC,CAAC", "ignoreList": []}