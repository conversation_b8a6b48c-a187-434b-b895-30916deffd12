import {
  UV_UDP_REUSEADDR,
  dlopen,
  errno,
  priority,
  signals
} from "./chunk-GX42U4MJ.js";
import {
  notImplemented
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/os.mjs
var constants = {
  UV_UDP_REUSEADDR,
  dlopen,
  errno,
  signals,
  priority
};
var NUM_CPUS = 8;
var availableParallelism = () => NUM_CPUS;
var arch = () => "";
var machine = () => "";
var endianness = () => "LE";
var cpus = () => {
  const info = {
    model: "",
    speed: 0,
    times: {
      user: 0,
      nice: 0,
      sys: 0,
      idle: 0,
      irq: 0
    }
  };
  return Array.from({ length: NUM_CPUS }, () => info);
};
var getPriority = () => 0;
var setPriority = notImplemented("os.setPriority");
var homedir = () => "/";
var tmpdir = () => "/tmp";
var devNull = "/dev/null";
var freemem = () => 0;
var totalmem = () => 0;
var loadavg = () => [
  0,
  0,
  0
];
var uptime = () => 0;
var hostname = () => "";
var networkInterfaces = () => {
  return { lo0: [
    {
      address: "127.0.0.1",
      netmask: "*********",
      family: "IPv4",
      mac: "00:00:00:00:00:00",
      internal: true,
      cidr: "127.0.0.1/8"
    },
    {
      address: "::1",
      netmask: "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff",
      family: "IPv6",
      mac: "00:00:00:00:00:00",
      internal: true,
      cidr: "::1/128",
      scopeid: 0
    },
    {
      address: "fe80::1",
      netmask: "ffff:ffff:ffff:ffff::",
      family: "IPv6",
      mac: "00:00:00:00:00:00",
      internal: true,
      cidr: "fe80::1/64",
      scopeid: 1
    }
  ] };
};
var platform = () => "linux";
var type = () => "Linux";
var release = () => "";
var version = () => "";
var userInfo = (opts) => {
  const encode = (str) => {
    if (opts?.encoding) {
      const buff = Buffer.from(str);
      return opts.encoding === "buffer" ? buff : buff.toString(opts.encoding);
    }
    return str;
  };
  return {
    gid: 1e3,
    uid: 1e3,
    homedir: encode("/"),
    shell: encode("/bin/sh"),
    username: encode("root")
  };
};
var EOL = "\n";
var os_default = {
  arch,
  availableParallelism,
  constants,
  cpus,
  EOL,
  endianness,
  devNull,
  freemem,
  getPriority,
  homedir,
  hostname,
  loadavg,
  machine,
  networkInterfaces,
  platform,
  release,
  setPriority,
  tmpdir,
  totalmem,
  type,
  uptime,
  userInfo,
  version
};
export {
  EOL,
  arch,
  availableParallelism,
  constants,
  cpus,
  os_default as default,
  devNull,
  endianness,
  freemem,
  getPriority,
  homedir,
  hostname,
  loadavg,
  machine,
  networkInterfaces,
  platform,
  release,
  setPriority,
  tmpdir,
  totalmem,
  type,
  uptime,
  userInfo,
  version
};
//# sourceMappingURL=unenv_node_os.js.map
