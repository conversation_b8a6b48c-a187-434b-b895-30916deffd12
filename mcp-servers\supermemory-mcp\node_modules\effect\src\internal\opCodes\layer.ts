/** @internal */
export const OP_EXTEND_SCOPE = "ExtendScope" as const

/** @internal */
export type OP_EXTEND_SCOPE = typeof OP_EXTEND_SCOPE

/** @internal */
export const OP_FOLD = "Fold" as const

/** @internal */
export type OP_FOLD = typeof OP_FOLD

/** @internal */
export const OP_FRESH = "Fresh" as const

/** @internal */
export type OP_FRESH = typeof OP_FRESH

/** @internal */
export const OP_FROM_EFFECT = "FromEffect" as const

/** @internal */
export type OP_FROM_EFFECT = typeof OP_FROM_EFFECT

/** @internal */
export const OP_SCOPED = "Scoped" as const

/** @internal */
export type OP_SCOPED = typeof OP_SCOPED

/** @internal */
export const OP_SUSPEND = "Suspend" as const

/** @internal */
export type OP_SUSPEND = typeof OP_SUSPEND

/** @internal */
export const OP_PROVIDE = "Provide" as const

/** @internal */
export type OP_PROVIDE = typeof OP_PROVIDE

/** @internal */
export const OP_PROVIDE_MERGE = "ProvideMerge" as const

/** @internal */
export type OP_PROVIDE_MERGE = typeof OP_PROVIDE_MERGE

/** @internal */
export const OP_ZIP_WITH = "ZipWith" as const

/** @internal */
export type OP_ZIP_WITH = typeof OP_ZIP_WITH
