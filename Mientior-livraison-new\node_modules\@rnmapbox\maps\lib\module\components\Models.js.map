{"version": 3, "names": ["React", "Image", "NativeModels", "jsx", "_jsx", "_resolveAssets", "models", "resolvedModels", "Object", "keys", "for<PERSON>ach", "key", "model", "url", "asset", "resolveAssetSource", "Error", "Models", "props", "restOfProps"], "sourceRoot": "../../../src", "sources": ["components/Models.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,cAAc;AAEpC,OAAOC,YAAY,MAAM,qCAAqC;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAe/D,SAASC,cAAcA,CAACC,MAAuB,EAE7C;EACA,MAAMC,cAAwC,GAAG,CAAC,CAAC;EACnDC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAAEC,GAAG,IAAK;IACnC,MAAMC,KAAK,GAAGN,MAAM,CAACK,GAAG,CAAC;IACzB,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MAC7BL,cAAc,CAACI,GAAG,CAAC,GAAG;QAAEE,GAAG,EAAED;MAAM,CAAC;IACtC,CAAC,MAAM;MACL,MAAME,KAAK,GAAGb,KAAK,CAACc,kBAAkB,CAACH,KAAK,CAAC;MAC7C,IAAI,CAACE,KAAK,EAAE;QACV,MAAM,IAAIE,KAAK,CAAC,kCAAkCJ,KAAK,EAAE,CAAC;MAC5D;MACAL,cAAc,CAACI,GAAG,CAAC,GAAGG,KAAK;IAC7B;EACF,CAAC,CAAC;EACF,OAAOP,cAAc;AACvB;;AAEA;AACA;AACA;AACA,eAAe,SAASU,MAAMA,CAACC,KAAY,EAAE;EAC3C,MAAM;IAAEZ,MAAM;IAAE,GAAGa;EAAY,CAAC,GAAGD,KAAK;EACxC,oBAAOd,IAAA,CAACF,YAAY;IAAA,GAAKiB,WAAW;IAAEb,MAAM,EAAED,cAAc,CAACC,MAAM;EAAE,CAAE,CAAC;AAC1E", "ignoreList": []}