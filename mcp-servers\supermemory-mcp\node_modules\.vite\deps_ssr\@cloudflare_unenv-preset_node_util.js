import {
  notImplemented
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/util.mjs
import types from "node:util/types";

// node_modules/unenv/dist/runtime/node/internal/util/promisify.mjs
var customSymbol = Symbol("customPromisify");
function _promisify(fn) {
  if (fn[customSymbol]) {
    return fn[customSymbol];
  }
  return function(...args) {
    return new Promise((resolve, reject) => {
      try {
        fn.call(this, ...args, (err, val) => {
          if (err) {
            return reject(err);
          }
          resolve(val);
        });
      } catch (error) {
        reject(error);
      }
    });
  };
}
var promisify = Object.assign(_promisify, { custom: customSymbol });

// node_modules/unenv/dist/runtime/node/internal/util/legacy-types.mjs
var isRegExp = (val) => val instanceof RegExp;
var isDate = (val) => val instanceof Date;
var isBoolean = (val) => typeof val === "boolean";
var isNull = (val) => val === null;
var isNullOrUndefined = (val) => val === null || val === void 0;
var isNumber = (val) => typeof val === "number";
var isString = (val) => typeof val === "string";
var isSymbol = (val) => typeof val === "symbol";
var isUndefined = (val) => val === void 0;
var isFunction = (val) => typeof val === "function";
var isBuffer = (val) => {
  return val && typeof val === "object" && typeof val.copy === "function" && typeof val.fill === "function" && typeof val.readUInt8 === "function";
};
var isObject = (val) => val !== null && typeof val === "object" && Object.getPrototypeOf(val).isPrototypeOf(Object);
var isError = (val) => val instanceof Error;
var isPrimitive = (val) => {
  if (typeof val === "object") {
    return val === null;
  }
  return typeof val !== "function";
};

// node_modules/unenv/dist/runtime/node/util.mjs
import { default as default2 } from "node:util/types";
var TextDecoder = globalThis.TextDecoder;
var TextEncoder = globalThis.TextEncoder;
var _errnoException = notImplemented("util._errnoException");
var _exceptionWithHostPort = notImplemented("util._exceptionWithHostPort");
var _extend = notImplemented("util._extend");
var aborted = notImplemented("util.aborted");
var callbackify = notImplemented("util.callbackify");
var getSystemErrorMap = notImplemented("util.getSystemErrorMap");
var getSystemErrorName = notImplemented("util.getSystemErrorName");
var toUSVString = notImplemented("util.toUSVString");
var stripVTControlCharacters = notImplemented("util.stripVTControlCharacters");
var transferableAbortController = notImplemented("util.transferableAbortController");
var transferableAbortSignal = notImplemented("util.transferableAbortSignal");
var parseArgs = notImplemented("util.parseArgs");
var parseEnv = notImplemented("util.parseEnv");
var styleText = notImplemented("util.styleText");
var getCallSite = notImplemented("util.getCallSite");
var getCallSites = notImplemented("util.getCallSites");
var getSystemErrorMessage = notImplemented("util.getSystemErrorMessage");

// node_modules/@cloudflare/unenv-preset/dist/runtime/node/util.mjs
var workerdUtil = process.getBuiltinModule("node:util");
var {
  MIMEParams: MIMEParams2,
  MIMEType: MIMEType2,
  TextDecoder: TextDecoder2,
  TextEncoder: TextEncoder2,
  // @ts-expect-error missing types?
  _extend: _extend2,
  aborted: aborted2,
  callbackify: callbackify2,
  debug: debug2,
  debuglog: debuglog2,
  deprecate,
  format: format2,
  formatWithOptions: formatWithOptions2,
  // @ts-expect-error unknown type
  getCallSite: getCallSite2,
  inherits: inherits2,
  inspect: inspect2,
  isArray: isArray2,
  isDeepStrictEqual: isDeepStrictEqual2,
  log: log2,
  parseArgs: parseArgs2,
  promisify: promisify2,
  stripVTControlCharacters: stripVTControlCharacters2,
  toUSVString: toUSVString2,
  transferableAbortController: transferableAbortController2,
  transferableAbortSignal: transferableAbortSignal2
} = workerdUtil;
var types2 = workerdUtil.types;
var util_default = {
  /**
   * manually unroll unenv-polyfilled-symbols to make it tree-shakeable
   */
  _errnoException,
  _exceptionWithHostPort,
  // @ts-expect-error unenv has unknown type
  getSystemErrorMap,
  // @ts-expect-error unenv has unknown type
  getSystemErrorName,
  isBoolean,
  isBuffer,
  isDate,
  isError,
  isFunction,
  isNull,
  isNullOrUndefined,
  isNumber,
  isObject,
  isPrimitive,
  isRegExp,
  isString,
  isSymbol,
  isUndefined,
  // @ts-expect-error unenv has unknown type
  parseEnv,
  // @ts-expect-error unenv has unknown type
  styleText,
  /**
   * manually unroll workerd-polyfilled-symbols to make it tree-shakeable
   */
  _extend: _extend2,
  aborted: aborted2,
  callbackify: callbackify2,
  debug: debug2,
  debuglog: debuglog2,
  deprecate,
  format: format2,
  formatWithOptions: formatWithOptions2,
  getCallSite: getCallSite2,
  inherits: inherits2,
  inspect: inspect2,
  isArray: isArray2,
  isDeepStrictEqual: isDeepStrictEqual2,
  log: log2,
  MIMEParams: MIMEParams2,
  MIMEType: MIMEType2,
  parseArgs: parseArgs2,
  promisify: promisify2,
  stripVTControlCharacters: stripVTControlCharacters2,
  TextDecoder: TextDecoder2,
  TextEncoder: TextEncoder2,
  toUSVString: toUSVString2,
  transferableAbortController: transferableAbortController2,
  transferableAbortSignal: transferableAbortSignal2,
  // special-cased deep merged symbols
  types: types2
};
export {
  MIMEParams2 as MIMEParams,
  MIMEType2 as MIMEType,
  TextDecoder2 as TextDecoder,
  TextEncoder2 as TextEncoder,
  _errnoException,
  _exceptionWithHostPort,
  _extend2 as _extend,
  aborted2 as aborted,
  callbackify2 as callbackify,
  debug2 as debug,
  debuglog2 as debuglog,
  util_default as default,
  deprecate,
  format2 as format,
  formatWithOptions2 as formatWithOptions,
  getCallSite2 as getCallSite,
  getSystemErrorMap,
  getSystemErrorName,
  inherits2 as inherits,
  inspect2 as inspect,
  isArray2 as isArray,
  isBoolean,
  isBuffer,
  isDate,
  isDeepStrictEqual2 as isDeepStrictEqual,
  isError,
  isFunction,
  isNull,
  isNullOrUndefined,
  isNumber,
  isObject,
  isPrimitive,
  isRegExp,
  isString,
  isSymbol,
  isUndefined,
  log2 as log,
  parseArgs2 as parseArgs,
  parseEnv,
  promisify2 as promisify,
  stripVTControlCharacters2 as stripVTControlCharacters,
  styleText,
  toUSVString2 as toUSVString,
  transferableAbortController2 as transferableAbortController,
  transferableAbortSignal2 as transferableAbortSignal,
  types2 as types
};
//# sourceMappingURL=@cloudflare_unenv-preset_node_util.js.map
