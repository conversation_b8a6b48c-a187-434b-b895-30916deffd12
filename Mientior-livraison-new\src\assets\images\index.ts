// Royalty-free images for Mientior Livraison app
// All images are optimized for mobile and follow the African design aesthetic
// Sources: Unsplash (CC0), Pexels (Free), Pixabay (Free), Original Designs
// See IMAGE_ATTRIBUTIONS.md for detailed licensing information

// App Logo and Branding - Original designs with African aesthetic
export const AppLogo = require('./logo/app-logo.png');
export const AppLogoWhite = require('./logo/app-logo-white.png');
export const AppIcon = require('./logo/app-icon.png');

// Onboarding Images - African-themed delivery illustrations (Unsplash CC0, Pexels Free)
export const FoodDeliveryImage = require('./onboarding/food-delivery.png');
export const PackageDeliveryImage = require('./onboarding/package-delivery.png');
export const ShoppingDeliveryImage = require('./onboarding/shopping-delivery.png');

// User Interface Images - Custom designs and royalty-free backgrounds
export const DefaultAvatar = require('./ui/default-avatar.png');
export const LocationBackground = require('./ui/location-background.png');
export const MapPlaceholder = require('./ui/map-placeholder.png');

// Category Images - African market and service imagery (Unsplash CC0, Pexels Free)
export const RestaurantCategory = require('./categories/restaurant.png');
export const GroceryCategory = require('./categories/grocery.png');
export const PharmacyCategory = require('./categories/pharmacy.png');
export const ShoppingCategory = require('./categories/shopping.png');

// Restaurant/Merchant Placeholders - Generic business imagery (Pixabay Free, Pexels Free)
export const RestaurantPlaceholder = require('./placeholders/restaurant-placeholder.png');
export const ProductPlaceholder = require('./placeholders/product-placeholder.png');
export const MerchantPlaceholder = require('./placeholders/merchant-placeholder.png');

// Delivery and Location - African delivery service imagery (Unsplash CC0, Original designs)
export const DeliveryTruck = require('./delivery/delivery-truck.png');
export const DeliveryPerson = require('./delivery/delivery-person.png');
export const LocationPin = require('./location/location-pin.png');

// Empty States - Custom illustrations with African design aesthetic
export const EmptyCart = require('./empty-states/empty-cart.png');
export const EmptyOrders = require('./empty-states/empty-orders.png');
export const NoResults = require('./empty-states/no-results.png');

// Status Icons - Custom SVG designs with African color scheme (#0DCAA8)
export const SuccessIcon = require('./states/success.png');
export const ErrorIcon = require('./states/error.png');
export const LoadingIcon = require('./states/loading.png');

// Export all images as a single object for easy access
export const Images = {
  // Logo and Branding
  AppLogo,
  AppLogoWhite,
  AppIcon,
  
  // Onboarding
  FoodDeliveryImage,
  PackageDeliveryImage,
  ShoppingDeliveryImage,
  
  // UI Elements
  DefaultAvatar,
  LocationBackground,
  MapPlaceholder,
  
  // Categories
  RestaurantCategory,
  GroceryCategory,
  PharmacyCategory,
  ShoppingCategory,
  
  // Placeholders
  RestaurantPlaceholder,
  ProductPlaceholder,
  MerchantPlaceholder,
  
  // Delivery
  DeliveryTruck,
  DeliveryPerson,
  LocationPin,
  
  // Empty States
  EmptyCart,
  EmptyOrders,
  NoResults,
  
  // States
  SuccessIcon,
  ErrorIcon,
  LoadingIcon,
};

export default Images;
