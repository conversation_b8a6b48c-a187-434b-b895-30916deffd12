import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
  Dimensions,
  StatusBar,
  Animated,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';

// Import Mapbox avec gestion d'erreur
let Mapbox: any = null;
try {
  Mapbox = require('@rnmapbox/maps').default;
} catch (error) {
  console.log('⚠️ Mapbox non disponible, utilisation du fallback');
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Configuration Mapbox - Token public de démonstration
if (Mapbox) {
  Mapbox.setAccessToken('pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw');
}

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
}

const LocationScreenMapbox: React.FC = () => {
  const navigation = useNavigation();

  // État local
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [mapRegion, setMapRegion] = useState({
    latitude: 5.348, // Côte d'Ivoire par défaut
    longitude: -4.007,
    zoom: 10,
  });
  const [loading, setLoading] = useState(false);
  const [mapReady, setMapReady] = useState(false);

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Références
  const mapRef = useRef<Mapbox.MapView>(null);
  const cameraRef = useRef<Mapbox.Camera>(null);

  useEffect(() => {
    initializeScreen();
  }, []);

  const initializeScreen = async () => {
    console.log('🗺️ Initialisation Mapbox...');
    
    // Animations d'entrée
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Demander les permissions
    await requestLocationPermission();
    
    // Marquer la carte comme prête après 2 secondes
    setTimeout(() => {
      console.log('✅ Mapbox prêt');
      setMapReady(true);
    }, 2000);
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission requise',
          'L\'accès à la localisation est nécessaire pour cette fonctionnalité.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.log('Erreur permission:', error);
    }
  };

  const handleMapPress = (feature: any) => {
    const coordinates = feature.geometry.coordinates;
    const latitude = coordinates[1];
    const longitude = coordinates[0];
    
    console.log('🗺️ Position sélectionnée sur Mapbox:', { latitude, longitude });
    setSelectedLocation({ latitude, longitude });
    getAddressFromCoordinates(latitude, longitude);
  };

  const getAddressFromCoordinates = async (latitude: number, longitude: number) => {
    try {
      const result = await Location.reverseGeocodeAsync({ latitude, longitude });
      if (result.length > 0) {
        const address = result[0];
        const formattedAddress = `${address.street || ''} ${address.name || ''}, ${address.city || ''}, ${address.country || ''}`.trim();
        console.log('📍 Adresse trouvée:', formattedAddress);
        setSelectedLocation(prev => prev ? { ...prev, address: formattedAddress } : null);
      }
    } catch (error) {
      console.log('Erreur géocodage inverse:', error);
    }
  };

  const handleCurrentLocationPress = async () => {
    setLoading(true);
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission requise',
          'L\'accès à la localisation est nécessaire pour cette fonctionnalité.',
          [{ text: 'OK' }]
        );
        return;
      }

      console.log('📍 Récupération de la position GPS...');
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const newLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      console.log('✅ Position GPS obtenue:', newLocation);
      setSelectedLocation(newLocation);

      // Animer vers la nouvelle position
      if (cameraRef.current) {
        cameraRef.current.setCamera({
          centerCoordinate: [location.coords.longitude, location.coords.latitude],
          zoomLevel: 16,
          animationDuration: 1000,
        });
      }

      // Obtenir l'adresse
      getAddressFromCoordinates(location.coords.latitude, location.coords.longitude);

    } catch (error) {
      console.log('❌ Erreur localisation:', error);
      Alert.alert(
        'Erreur de localisation',
        'Impossible d\'obtenir votre position actuelle. Vérifiez que le GPS est activé.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmLocation = () => {
    if (!selectedLocation) {
      Alert.alert('Aucune position sélectionnée', 'Veuillez sélectionner une position sur la carte.');
      return;
    }

    console.log('✅ Position confirmée:', selectedLocation);
    navigation.goBack();
  };

  const formatAddress = (address: string) => {
    if (address.length > 40) {
      return address.substring(0, 40) + '...';
    }
    return address;
  };

  // Si Mapbox n'est pas disponible, afficher un fallback
  if (!Mapbox) {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />

        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#1F2937" />
          </TouchableOpacity>

          <Text style={styles.headerTitle}>🗺️ Mapbox (Chargement...)</Text>
        </View>

        {/* Fallback Content */}
        <View style={styles.fallbackContainer}>
          <Ionicons name="map-outline" size={64} color="#0DCAA8" />
          <Text style={styles.fallbackTitle}>Mapbox en cours de chargement</Text>
          <Text style={styles.fallbackText}>
            La carte Mapbox se charge. Si le problème persiste, l'application utilisera une carte alternative.
          </Text>

          <TouchableOpacity
            style={styles.fallbackButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.fallbackButtonText}>Retour</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      {/* Header */}
      <Animated.View style={[
        styles.header,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>🗺️ Mapbox Interactive</Text>
      </Animated.View>

      {/* Search Bar */}
      <Animated.View style={[
        styles.searchContainer,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        <View style={styles.searchInputWrapper}>
          <Ionicons name="search" size={20} color="#9CA3AF" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher une adresse"
            placeholderTextColor="#9CA3AF"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </Animated.View>

      {/* Map Container */}
      <Animated.View style={[
        styles.mapContainer,
        {
          opacity: fadeAnim,
        }
      ]}>
        <Mapbox.MapView
          ref={mapRef}
          style={styles.map}
          onPress={handleMapPress}
          styleURL={Mapbox.StyleURL.Street}
          zoomEnabled={true}
          scrollEnabled={true}
          pitchEnabled={true}
          rotateEnabled={true}
          compassEnabled={true}
          scaleBarEnabled={true}
          logoEnabled={false}
          attributionEnabled={false}
        >
          <Mapbox.Camera
            ref={cameraRef}
            centerCoordinate={[mapRegion.longitude, mapRegion.latitude]}
            zoomLevel={mapRegion.zoom}
          />

          {selectedLocation && (
            <Mapbox.PointAnnotation
              id="selected-location"
              coordinate={[selectedLocation.longitude, selectedLocation.latitude]}
            >
              <View style={styles.markerContainer}>
                <View style={styles.marker}>
                  <Ionicons name="location" size={24} color="#FFFFFF" />
                </View>
              </View>
            </Mapbox.PointAnnotation>
          )}
        </Mapbox.MapView>

        {/* Map Status Indicator */}
        {!mapReady && (
          <View style={styles.mapLoadingOverlay}>
            <Ionicons name="map-outline" size={48} color="#0DCAA8" />
            <Text style={styles.mapLoadingText}>Chargement Mapbox...</Text>
          </View>
        )}

        {/* Current Location Button */}
        <TouchableOpacity
          style={styles.currentLocationButton}
          onPress={handleCurrentLocationPress}
          disabled={loading}
        >
          <Ionicons
            name="locate"
            size={24}
            color={loading ? "#9CA3AF" : "#0DCAA8"}
          />
        </TouchableOpacity>

        {/* Map Type Info */}
        <View style={styles.mapTypeInfo}>
          <Text style={styles.mapTypeText}>🗺️ Mapbox</Text>
        </View>
      </Animated.View>

      {/* Bottom Sheet */}
      <Animated.View style={[
        styles.bottomSheet,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}>
        {/* Selected Location Info */}
        {selectedLocation && (
          <View style={styles.locationInfo}>
            <View style={styles.locationHeader}>
              <Ionicons name="location" size={24} color="#0DCAA8" />
              <View style={styles.locationDetails}>
                <Text style={styles.locationTitle}>
                  {selectedLocation.address ? formatAddress(selectedLocation.address) : 'Position sélectionnée'}
                </Text>
                <Text style={styles.locationCoordinates}>
                  📍 Lat: {selectedLocation.latitude.toFixed(6)}, Long: {selectedLocation.longitude.toFixed(6)}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Instructions */}
        {!selectedLocation && (
          <View style={styles.instructions}>
            <Text style={styles.instructionsText}>
              🎯 Touchez la carte pour sélectionner une position ou utilisez le bouton GPS
            </Text>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.confirmButton,
              !selectedLocation && styles.confirmButtonDisabled
            ]}
            onPress={handleConfirmLocation}
            disabled={!selectedLocation}
          >
            <Text style={styles.confirmButtonText}>
              {selectedLocation ? '✅ Confirmer cette position' : 'Sélectionnez une position'}
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* Bottom Indicator */}
      <View style={styles.bottomIndicator}>
        <View style={styles.indicator} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  searchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: 12,
    minHeight: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    paddingVertical: 12,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  markerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  marker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#0DCAA8',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  mapLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  mapLoadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  currentLocationButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  mapTypeInfo: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: 'rgba(13, 202, 168, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  mapTypeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  bottomSheet: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 8,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    maxHeight: screenHeight * 0.4,
  },
  locationInfo: {
    marginBottom: 20,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  locationDetails: {
    flex: 1,
    marginLeft: 12,
  },
  locationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  locationCoordinates: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  instructions: {
    backgroundColor: '#F0FDF4',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#0DCAA8',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 20,
  },
  instructionsText: {
    fontSize: 16,
    color: '#0DCAA8',
    textAlign: 'center',
    fontWeight: '500',
  },
  actionButtons: {
    marginBottom: 24,
  },
  confirmButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    shadowColor: '#0DCAA8',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  confirmButtonDisabled: {
    backgroundColor: '#9CA3AF',
    shadowOpacity: 0.1,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  bottomIndicator: {
    alignItems: 'center',
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    paddingTop: 8,
    backgroundColor: '#FFFFFF',
  },
  indicator: {
    width: 134,
    height: 5,
    backgroundColor: '#000000',
    borderRadius: 3,
  },
  fallbackContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  fallbackTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1F2937',
    marginTop: 24,
    marginBottom: 16,
    textAlign: 'center',
  },
  fallbackText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  fallbackButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
  },
  fallbackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default LocationScreenMapbox;
