import {
  notImplemented,
  notImplementedClass
} from "./chunk-AO3S7MWW.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/repl.mjs
import { builtinModules as _builtinModules } from "node:module";
var writer = notImplementedClass("repl.writer");
var start = notImplemented("repl.start");
var Recoverable = notImplementedClass("repl.Recoverable");
var REPLServer = notImplementedClass("repl.REPLServer");
var REPL_MODE_SLOPPY = Symbol("repl-sloppy");
var REPL_MODE_STRICT = Symbol("repl-strict");
var builtinModules = _builtinModules.filter((m) => m[0] !== "_");
var _builtinLibs = builtinModules;
var repl_default = {
  writer,
  start,
  Recoverable,
  REPLServer,
  builtinModules,
  _builtinLibs,
  REPL_MODE_SLOPPY,
  REPL_MODE_STRICT
};
export {
  REPLServer,
  REPL_MODE_SLOPPY,
  REPL_MODE_STRICT,
  Recoverable,
  _builtinLibs,
  builtinModules,
  repl_default as default,
  start,
  writer
};
//# sourceMappingURL=unenv_node_repl.js.map
