import {
  noop_default
} from "./chunk-DQN6H3OM.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/internal/dgram/socket.mjs
import { EventEmitter } from "node:events";
var Socket = class extends EventEmitter {
  __unenv__ = true;
  bind() {
    return this;
  }
  close() {
    return this;
  }
  ref() {
    return this;
  }
  unref() {
    return this;
  }
  getRecvBufferSize() {
    return 1e5;
  }
  getSendBufferSize() {
    return 1e4;
  }
  getSendQueueSize() {
    return 0;
  }
  getSendQueueCount() {
    return 0;
  }
  setMulticastLoopback() {
    return false;
  }
  setMulticastTTL() {
    return 1;
  }
  setTTL() {
    return 1;
  }
  address() {
    return {
      address: "127.0.0.1",
      family: "IPv4",
      port: 1234
    };
  }
  remoteAddress() {
    throw new Error("ERR_SOCKET_DGRAM_NOT_CONNECTED");
  }
  [Symbol.asyncDispose]() {
    return Promise.resolve();
  }
  addMembership() {
  }
  addSourceSpecificMembership() {
  }
  connect() {
  }
  disconnect() {
  }
  dropMembership() {
  }
  dropSourceSpecificMembership() {
  }
  send() {
  }
  setSendBufferSize() {
  }
  setBroadcast() {
  }
  setRecvBufferSize() {
  }
  setMulticastInterface() {
  }
};

// node_modules/unenv/dist/runtime/node/dgram.mjs
var _createSocketHandle = noop_default;
var createSocket = function() {
  return new Socket();
};
var dgram_default = {
  Socket,
  _createSocketHandle,
  createSocket
};
export {
  Socket,
  _createSocketHandle,
  createSocket,
  dgram_default as default
};
//# sourceMappingURL=unenv_node_dgram.js.map
