{"version": 3, "sources": ["../../unenv/dist/runtime/node/internal/readline/interface.mjs"], "sourcesContent": ["import { EventEmitter } from \"node:events\";\nexport class Interface extends EventEmitter {\n\tterminal = false;\n\tline = \"\";\n\tcursor = 0;\n\tgetPrompt() {\n\t\treturn \"\";\n\t}\n\tsetPrompt(prompt) {}\n\tprompt(preserveCursor) {}\n\tquestion(query, options, callback) {\n\t\tcallback && typeof callback === \"function\" && callback(\"\");\n\t}\n\tresume() {\n\t\treturn this;\n\t}\n\tclose() {}\n\twrite(data, key) {}\n\tgetCursorPos() {\n\t\treturn {\n\t\t\trows: 0,\n\t\t\tcols: 0\n\t\t};\n\t}\n\tpause() {\n\t\treturn this;\n\t}\n\tasync *[Symbol.asyncIterator]() {\n\t\tyield \"\";\n\t}\n}\n"], "mappings": ";AAAA,SAAS,oBAAoB;AACtB,IAAM,YAAN,cAAwB,aAAa;AAAA,EAC3C,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,YAAY;AACX,WAAO;AAAA,EACR;AAAA,EACA,UAAU,QAAQ;AAAA,EAAC;AAAA,EACnB,OAAO,gBAAgB;AAAA,EAAC;AAAA,EACxB,SAAS,OAAO,SAAS,UAAU;AAClC,gBAAY,OAAO,aAAa,cAAc,SAAS,EAAE;AAAA,EAC1D;AAAA,EACA,SAAS;AACR,WAAO;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,EAAC;AAAA,EACT,MAAM,MAAM,KAAK;AAAA,EAAC;AAAA,EAClB,eAAe;AACd,WAAO;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACP;AAAA,EACD;AAAA,EACA,QAAQ;AACP,WAAO;AAAA,EACR;AAAA,EACA,QAAQ,OAAO,aAAa,IAAI;AAC/B,UAAM;AAAA,EACP;AACD;", "names": []}