import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Animated,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { theme } from '../../constants/theme';
import { useOnboardingFlow } from '../../store/onboardingStore';
import { PlaceholderImages } from '../../assets/images/placeholders';

const { width, height } = Dimensions.get('window');

interface OnboardingSlide {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  backgroundColor: string;
}

const slides: OnboardingSlide[] = [
  {
    id: 1,
    title: 'Commandez vos repas favoris',
    subtitle: 'Restaurants & Fast-food',
    description: 'Découvrez une large sélection de restaurants locaux et internationaux. Commandez en quelques clics et savourez vos plats préférés.',
    image: PlaceholderImages.FOOD_DELIVERY_BASE64,
    backgroundColor: '#FF6B6B',
  },
  {
    id: 2,
    title: 'Envoyez vos colis rapidement',
    subtitle: 'Livraison Express',
    description: 'Envoyez vos documents et colis en toute sécurité. Suivi en temps réel et livraison garantie dans les meilleurs délais.',
    image: PlaceholderImages.PACKAGE_DELIVERY_BASE64,
    backgroundColor: '#4ECDC4',
  },
  {
    id: 3,
    title: 'Recevez vos achats à domicile',
    subtitle: 'Shopping & Courses',
    description: 'Faites vos courses depuis chez vous. Produits frais, articles ménagers, tout ce dont vous avez besoin livré à votre porte.',
    image: PlaceholderImages.SHOPPING_DELIVERY_BASE64,
    backgroundColor: '#45B7D1',
  },
];

export const OnboardingCarouselScreen: React.FC = () => {
  const navigation = useNavigation();
  const { completeOnboarding } = useOnboardingFlow();
  const scrollViewRef = useRef<ScrollView>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;

  const handleScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / width);
    setCurrentIndex(index);
  };

  const goToNext = () => {
    if (currentIndex < slides.length - 1) {
      const nextIndex = currentIndex + 1;
      scrollViewRef.current?.scrollTo({
        x: nextIndex * width,
        animated: true,
      });
      setCurrentIndex(nextIndex);
    } else {
      handleFinish();
    }
  };

  const goToPrevious = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      scrollViewRef.current?.scrollTo({
        x: prevIndex * width,
        animated: true,
      });
      setCurrentIndex(prevIndex);
    }
  };

  const handleFinish = async () => {
    try {
      // Marquer l'onboarding comme terminé dans le store
      completeOnboarding();

      // Aussi sauvegarder dans AsyncStorage pour compatibilité
      await AsyncStorage.setItem('hasSeenOnboarding', 'true');

      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        navigation.navigate('LanguageSelection' as never);
      });
    } catch (error) {
      console.error('Erreur sauvegarde onboarding:', error);
      // Continuer même en cas d'erreur
      navigation.navigate('LanguageSelection' as never);
    }
  };

  const handleSkip = () => {
    handleFinish();
  };

  const renderSlide = (slide: OnboardingSlide, index: number) => (
    <View
      key={slide.id}
      style={[styles.slide, { backgroundColor: slide.backgroundColor }]}
    >
      <StatusBar
        backgroundColor={slide.backgroundColor}
        barStyle="light-content"
      />
      
      {/* Illustration */}
      <View style={styles.illustrationContainer}>
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: slide.image }}
            style={styles.slideImage}
            resizeMode="contain"
          />
        </View>
        <View style={styles.decorativeCircle1} />
        <View style={styles.decorativeCircle2} />
      </View>

      {/* Contenu */}
      <View style={styles.contentContainer}>
        <Text style={styles.title}>{slide.title}</Text>
        <Text style={styles.subtitle}>{slide.subtitle}</Text>
        <Text style={styles.description}>{slide.description}</Text>
      </View>
    </View>
  );

  const renderPagination = () => (
    <View style={styles.paginationContainer}>
      {slides.map((_, index) => (
        <View
          key={index}
          style={[
            styles.paginationDot,
            {
              backgroundColor:
                index === currentIndex
                  ? theme.colors.white
                  : 'rgba(255, 255, 255, 0.4)',
              width: index === currentIndex ? 24 : 8,
            },
          ]}
        />
      ))}
    </View>
  );

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {slides.map((slide, index) => renderSlide(slide, index))}
      </ScrollView>

      {/* Bouton Skip */}
      <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
        <Text style={styles.skipText}>Passer</Text>
      </TouchableOpacity>

      {/* Pagination */}
      {renderPagination()}

      {/* Boutons de navigation */}
      <View style={styles.navigationContainer}>
        {currentIndex > 0 && (
          <TouchableOpacity style={styles.backButton} onPress={goToPrevious}>
            <Text style={styles.backButtonText}>Précédent</Text>
          </TouchableOpacity>
        )}
        
        <View style={styles.spacer} />
        
        <TouchableOpacity style={styles.nextButton} onPress={goToNext}>
          <Text style={styles.nextButtonText}>
            {currentIndex === slides.length - 1 ? 'Commencer' : 'Suivant'}
          </Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  slide: {
    width,
    height,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  illustrationContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  imageContainer: {
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
    overflow: 'hidden',
  },
  slideImage: {
    width: 160,
    height: 160,
    borderRadius: 80,
  },
  decorativeCircle1: {
    position: 'absolute',
    top: 50,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  decorativeCircle2: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingTop: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.white,
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 18,
    color: theme.colors.white,
    opacity: 0.9,
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    color: theme.colors.white,
    opacity: 0.8,
    textAlign: 'center',
    lineHeight: 24,
  },
  skipButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    padding: 12,
  },
  skipText: {
    color: theme.colors.white,
    fontSize: 16,
    opacity: 0.8,
  },
  paginationContainer: {
    position: 'absolute',
    bottom: 120,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  navigationContainer: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  backButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    opacity: 0.8,
  },
  spacer: {
    flex: 1,
  },
  nextButton: {
    backgroundColor: theme.colors.white,
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  nextButtonText: {
    color: theme.colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
});
