{"version": 3, "names": ["NativeModules", "NativeEventEmitter", "AppState", "Platform", "NativeRNMBXLocationModule", "MapboxGL", "RNMBXModule", "MapboxGLLocationManager", "select", "ios", "RNMBXLocationModule", "android", "LocationModuleEventEmitter", "LocationManager", "constructor", "_listeners", "_lastKnownLocation", "_isListening", "_requestsAlwaysUse", "_onUpdate", "bind", "subscription", "_appStateListener", "addEventListener", "_handleAppStateChange", "getLastKnownLocation", "lastKnownLocation", "error", "console", "warn", "addListener", "listener", "start", "includes", "push", "removeListener", "filter", "l", "length", "stop", "removeAllListeners", "appState", "displacement", "validDisplacement", "undefined", "_minDisplacement", "OS", "LocationCallbackName", "Update", "onLocationUpdate", "location", "payload", "remove", "setMinDisplacement", "minDisplacement", "setRequestsAlwaysUse", "requestsAlwaysUse", "for<PERSON>ach", "_simulateHeading", "changesPerSecond", "increment", "simulateHeading", "setLocationEventThrottle", "throttleValue"], "sourceRoot": "../../../../src", "sources": ["modules/location/locationManager.ts"], "mappings": ";;AAAA,SACEA,aAAa,EACbC,kBAAkB,EAClBC,QAAQ,EAIRC,QAAQ,QAEH,cAAc;AAErB,OAAOC,yBAAyB,MAAM,uCAAuC;AAE7E,MAAMC,QAAQ,GAAGL,aAAa,CAACM,WAAW;AAC1C,MAAMC,uBAAyD,GAAGJ,QAAQ,CAACK,MAAM,CAAC;EAACC,GAAG,EAAET,aAAa,CAACU,mBAAmB;EAAEC,OAAO,EAAGP;AAAyB,CAAC,CAAC;AAEhK,OAAO,MAAMQ,0BAA0B,GAAG,IAAIX,kBAAkB;AAC9D;AACAM,uBACF,CAAC;;AAED;AACA;AACA;;AAMA;AACA;AACA;;AA0CA;AACA;AACA;AACA,OAAO,MAAMM,eAAe,CAAC;EAS3BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACC,YAAY,GAAG,IAAI;IAExB,IAAI,CAACC,iBAAiB,GAAGpB,QAAQ,CAACqB,gBAAgB,CAChD,QAAQ,EACR,IAAI,CAACC,qBAAqB,CAACJ,IAAI,CAAC,IAAI,CACtC,CAAC;EACH;EAEA,MAAMK,oBAAoBA,CAAA,EAAG;IAC3B,IAAI,CAAC,IAAI,CAACT,kBAAkB,EAAE;MAC5B,IAAIU,iBAAiB;;MAErB;MACA;MACA;MACA;MACA,IAAI;QACFA,iBAAiB,GACf,MAAMnB,uBAAuB,CAACkB,oBAAoB,CAAC,CAAC;MACxD,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,yBAAyB,EAAEF,KAAK,CAAC;MAChD;MAEA,IAAI,CAAC,IAAI,CAACX,kBAAkB,IAAIU,iBAAiB,EAAE;QACjD,IAAI,CAACV,kBAAkB,GAAGU,iBAAiB;MAC7C;IACF;IAEA,OAAO,IAAI,CAACV,kBAAkB;EAChC;EAEAc,WAAWA,CAACC,QAAsC,EAAE;IAClD,IAAI,CAAC,IAAI,CAACd,YAAY,EAAE;MACtB,IAAI,CAACe,KAAK,CAAC,CAAC;IACd;IACA,IAAI,CAAC,IAAI,CAACjB,UAAU,CAACkB,QAAQ,CAACF,QAAQ,CAAC,EAAE;MACvC,IAAI,CAAChB,UAAU,CAACmB,IAAI,CAACH,QAAQ,CAAC;MAE9B,IAAI,IAAI,CAACf,kBAAkB,EAAE;QAC3Be,QAAQ,CAAC,IAAI,CAACf,kBAAkB,CAAC;MACnC;IACF;EACF;EAEAmB,cAAcA,CAACJ,QAAsC,EAAE;IACrD,IAAI,CAAChB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACqB,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAKN,QAAQ,CAAC;IAC/D,IAAI,IAAI,CAAChB,UAAU,CAACuB,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACC,IAAI,CAAC,CAAC;IACb;EACF;EAEAC,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAACzB,UAAU,GAAG,EAAE;IACpB,IAAI,CAACwB,IAAI,CAAC,CAAC;EACb;EAEAf,qBAAqBA,CAACiB,QAAwB,EAAE;IAC9C,IAAI,CAAC,IAAI,CAACvB,kBAAkB,EAAE;MAC5B,IAAIuB,QAAQ,KAAK,YAAY,EAAE;QAC7B,IAAI,CAACF,IAAI,CAAC,CAAC;MACb,CAAC,MAAM,IAAIE,QAAQ,KAAK,QAAQ,EAAE;QAChC,IAAI,IAAI,CAAC1B,UAAU,CAACuB,MAAM,GAAG,CAAC,EAAE;UAC9B,IAAI,CAACN,KAAK,CAAC,CAAC;QACd;MACF;IACF;EACF;EAEAA,KAAKA,CAACU,YAAY,GAAG,CAAC,CAAC,EAAE;IACvB,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IACED,YAAY,KAAK,CAAC,CAAC,IACnBA,YAAY,KAAK,IAAI,IACrBA,YAAY,KAAKE,SAAS,EAC1B;MACAD,iBAAiB,GAAG,IAAI,CAACE,gBAAgB,IAAI,CAAC,CAAC;IACjD,CAAC,MAAM;MACLF,iBAAiB,GAAGD,YAAY;IAClC;IAEA,IAAI,CAAC,IAAI,CAACzB,YAAY,EAAE;MACtBV,uBAAuB,CAACyB,KAAK,CAACW,iBAAiB,CAAC;MAEhD,IAAIxC,QAAQ,CAAC2C,EAAE,KAAK,KAAK,EAAE;QACzB,IAAI,CAACzB,YAAY,GAAGT,0BAA0B,CAACkB,WAAW,CACxDzB,QAAQ,CAAC0C,oBAAoB,CAACC,MAAM,EACpC,IAAI,CAAC7B,SACP,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACE,YAAY,GAAGd,uBAAuB,CAAC0C,gBAAgB,CAAEC,QAAQ,IAAK;UACzE,IAAI,CAAC/B,SAAS,CAAC+B,QAAQ,CAACC,OAAO,CAAC;QAClC,CAAC,CAAC;MACJ;MAEA,IAAI,CAAClC,YAAY,GAAG,IAAI;IAC1B;EACF;EAEAsB,IAAIA,CAAA,EAAG;IACLhC,uBAAuB,CAACgC,IAAI,CAAC,CAAC;IAE9B,IAAI,IAAI,CAACtB,YAAY,IAAI,IAAI,CAACI,YAAY,EAAE;MAC1C,IAAI,CAACA,YAAY,CAAC+B,MAAM,CAAC,CAAC;IAC5B;IAEA,IAAI,CAACnC,YAAY,GAAG,KAAK;EAC3B;EAEAoC,kBAAkBA,CAACC,eAAuB,EAAE;IAC1C,IAAI,CAACT,gBAAgB,GAAGS,eAAe;IACvC/C,uBAAuB,CAAC8C,kBAAkB,CAACC,eAAe,CAAC;EAC7D;EAEAC,oBAAoBA,CAACC,iBAA0B,EAAE;IAC/CjD,uBAAuB,CAACgD,oBAAoB,CAACC,iBAAiB,CAAC;IAC/D,IAAI,CAACtC,kBAAkB,GAAGsC,iBAAiB;EAC7C;EAEArC,SAASA,CAAC+B,QAAkB,EAAE;IAC5B,IAAI,CAAClC,kBAAkB,GAAGkC,QAAQ;IAElC,IAAI,CAACnC,UAAU,CAAC0C,OAAO,CAAEpB,CAAC,IAAKA,CAAC,CAACa,QAAQ,CAAC,CAAC;EAC7C;;EAEA;AACF;AACA;EACEQ,gBAAgBA,CAACC,gBAAwB,EAAEC,SAAiB,EAAE;IAC5DrD,uBAAuB,CAACsD,eAAe,CAACF,gBAAgB,EAAEC,SAAS,CAAC;EACtE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,wBAAwBA,CAACC,aAAqB,EAAE;IAC9CxD,uBAAuB,CAACuD,wBAAwB,CAACC,aAAa,CAAC;EACjE;AACF;AAEA,eAAe,IAAIlD,eAAe,CAAC,CAAC", "ignoreList": []}