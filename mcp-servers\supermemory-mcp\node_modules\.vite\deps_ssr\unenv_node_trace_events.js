import "./chunk-HKJ2B2AA.js";

// node_modules/unenv/dist/runtime/node/internal/trace_events/tracing.mjs
var Tracing = class {
  categories = "";
  enabled = false;
  disable() {
    this.enabled = false;
  }
  enable() {
    this.enabled = true;
  }
};

// node_modules/unenv/dist/runtime/node/trace_events.mjs
var createTracing = function() {
  return new Tracing();
};
var getEnabledCategories = () => "";
var trace_events_default = {
  createTracing,
  getEnabledCategories
};
export {
  createTracing,
  trace_events_default as default,
  getEnabledCategories
};
//# sourceMappingURL=unenv_node_trace_events.js.map
