{"version": 3, "names": ["React", "NativeModules", "Platform", "RNMBXMakerViewContentCoponent", "NativeMarkerViewComponent", "toJSONString", "makePoint", "PointAnnotation", "jsx", "_jsx", "Mapbox", "RNMBXModule", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PureComponent", "defaultProps", "anchor", "x", "y", "allowOverlap", "allowOverlapWithPuck", "isSelected", "lastId", "_idForPointAnnotation", "__idForPointAnnotation", "undefined", "_getCoordinate", "coordinate", "render", "props", "console", "warn", "OS", "MapboxV10", "id", "RNMBXMarkerView", "style", "flex", "alignSelf", "Number", "onTouchEnd", "e", "stopPropagation", "children", "onStartShouldSetResponder", "_event"], "sourceRoot": "../../../src", "sources": ["components/MarkerView.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,QAAQ,QAAwB,cAAc;AAEtE,OAAOC,6BAA6B,MAAM,gDAAgD;AAC1F,OAAOC,yBAAyB,MAAM,yCAAyC;AAE/E,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,SAAS,QAAQ,mBAAmB;AAE7C,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEhD,MAAMC,MAAM,GAAGT,aAAa,CAACU,WAAW;AAuCxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,SAASZ,KAAK,CAACa,aAAa,CAAQ;EAClD,OAAOC,YAAY,GAAmB;IACpCC,MAAM,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC;IAC1BC,YAAY,EAAE,KAAK;IACnBC,oBAAoB,EAAE,KAAK;IAC3BC,UAAU,EAAE;EACd,CAAC;EAED,OAAOC,MAAM,GAAG,CAAC;EAGjBC,qBAAqBA,CAAA,EAAW;IAC9B,IAAI,IAAI,CAACC,sBAAsB,KAAKC,SAAS,EAAE;MAC7CZ,UAAU,CAACS,MAAM,GAAGT,UAAU,CAACS,MAAM,GAAG,CAAC;MACzC,IAAI,CAACE,sBAAsB,GAAG,MAAMX,UAAU,CAACS,MAAM,EAAE;IACzD;IACA,OAAO,IAAI,CAACE,sBAAsB;EACpC;EAEAE,cAAcA,CAACC,UAAoB,EAAsB;IACvD,IAAI,CAACA,UAAU,EAAE;MACf,OAAOF,SAAS;IAClB;IACA,OAAOnB,YAAY,CAACC,SAAS,CAACoB,UAAU,CAAC,CAAC;EAC5C;EAEAC,MAAMA,CAAA,EAAG;IACP,IACE,IAAI,CAACC,KAAK,CAACb,MAAM,CAACC,CAAC,GAAG,CAAC,IACvB,IAAI,CAACY,KAAK,CAACb,MAAM,CAACE,CAAC,GAAG,CAAC,IACvB,IAAI,CAACW,KAAK,CAACb,MAAM,CAACC,CAAC,GAAG,CAAC,IACvB,IAAI,CAACY,KAAK,CAACb,MAAM,CAACE,CAAC,GAAG,CAAC,EACvB;MACAY,OAAO,CAACC,IAAI,CACV,mCAAmC,IAAI,CAACF,KAAK,CAACb,MAAM,CAACC,CAAC,KAAK,IAAI,CAACY,KAAK,CAACb,MAAM,CAACE,CAAC,oDAChF,CAAC;IACH;IAEA,IAAIf,QAAQ,CAAC6B,EAAE,KAAK,KAAK,IAAI,CAACrB,MAAM,CAACsB,SAAS,EAAE;MAC9C,oBACEvB,IAAA,CAACF,eAAe;QAAC0B,EAAE,EAAE,IAAI,CAACX,qBAAqB,CAAC,CAAE;QAAA,GAAK,IAAI,CAACM;MAAK,CAAG,CAAC;IAEzE;IAEA,MAAM;MAAEb,MAAM,GAAG;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAI;IAAE,CAAC,GAAG,IAAI,CAACW,KAAK;IAElD,oBACEnB,IAAA,CAACyB,eAAe;MACdC,KAAK,EAAE,CACL;QACEC,IAAI,EAAE,CAAC;QACPC,SAAS,EAAE;MACb,CAAC,EACD,IAAI,CAACT,KAAK,CAACO,KAAK,CAChB;MACFT,UAAU,EAAE,CACVY,MAAM,CAAC,IAAI,CAACV,KAAK,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,EAChCY,MAAM,CAAC,IAAI,CAACV,KAAK,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,CAChC;MACFX,MAAM,EAAEA,MAAO;MACfG,YAAY,EAAE,IAAI,CAACU,KAAK,CAACV,YAAa;MACtCC,oBAAoB,EAAE,IAAI,CAACS,KAAK,CAACT,oBAAqB;MACtDC,UAAU,EAAE,IAAI,CAACQ,KAAK,CAACR,UAAW;MAClCmB,UAAU,EAAGC,CAAC,IAAK;QACjBA,CAAC,CAACC,eAAe,CAAC,CAAC;MACrB,CAAE;MAAAC,QAAA,eAEFjC,IAAA,CAACN,6BAA6B;QAC5BgC,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAa,CAAE;QAC5CM,yBAAyB,EAAGC,MAAM,IAAK;UACrC,OAAO,IAAI;QACb,CAAE;QACFL,UAAU,EAAGC,CAAC,IAAK;UACjBA,CAAC,CAACC,eAAe,CAAC,CAAC;QACrB,CAAE;QAAAC,QAAA,EAED,IAAI,CAACd,KAAK,CAACc;MAAQ,CACS;IAAC,CACjB,CAAC;EAEtB;AACF;AAEA,MAAMR,eAAe,GAAG9B,yBAAyB;AAEjD,eAAeQ,UAAU", "ignoreList": []}